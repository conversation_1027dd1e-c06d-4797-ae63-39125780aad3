package com.aiinterview.simulator.data.performance

import android.app.ActivityManager
import android.content.Context
import android.graphics.Bitmap
import android.os.Debug
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 内存优化管理器
 * 负责监控和优化应用内存使用，防止内存泄漏和OOM
 */
@Singleton
class MemoryOptimizationManager @Inject constructor(
    private val context: Context // 注入应用上下文
) {
    
    companion object {
        private const val LOW_MEMORY_THRESHOLD = 0.8 // 低内存阈值（80%）
        private const val CRITICAL_MEMORY_THRESHOLD = 0.9 // 临界内存阈值（90%）
        private const val GC_TRIGGER_INTERVAL = 30000L // GC触发间隔（毫秒）
    }
    
    // 活动管理器，用于获取内存信息
    private val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
    
    // 弱引用缓存，防止内存泄漏
    private val weakReferenceCache = ConcurrentHashMap<String, WeakReference<Any>>()
    
    // 上次GC时间
    private var lastGcTime = 0L
    
    /**
     * 获取当前内存使用情况
     * @return 内存使用信息
     */
    suspend fun getMemoryInfo(): MemoryInfo = withContext(Dispatchers.Default) {
        val memInfo = ActivityManager.MemoryInfo() // 创建内存信息对象
        activityManager.getMemoryInfo(memInfo) // 获取系统内存信息
        
        // 获取应用内存使用情况
        val runtime = Runtime.getRuntime() // 获取运行时对象
        val maxMemory = runtime.maxMemory() // 最大可用内存
        val totalMemory = runtime.totalMemory() // 已分配内存
        val freeMemory = runtime.freeMemory() // 空闲内存
        val usedMemory = totalMemory - freeMemory // 已使用内存
        
        // 获取原生内存使用情况
        val nativeHeapSize = Debug.getNativeHeapSize() // 原生堆大小
        val nativeHeapAllocatedSize = Debug.getNativeHeapAllocatedSize() // 原生堆已分配大小
        val nativeHeapFreeSize = Debug.getNativeHeapFreeSize() // 原生堆空闲大小
        
        MemoryInfo(
            totalSystemMemory = memInfo.totalMem, // 系统总内存
            availableSystemMemory = memInfo.availMem, // 系统可用内存
            isLowMemory = memInfo.lowMemory, // 是否处于低内存状态
            maxAppMemory = maxMemory, // 应用最大内存
            totalAppMemory = totalMemory, // 应用总内存
            usedAppMemory = usedMemory, // 应用已使用内存
            freeAppMemory = freeMemory, // 应用空闲内存
            memoryUsageRatio = usedMemory.toDouble() / maxMemory, // 内存使用率
            nativeHeapSize = nativeHeapSize, // 原生堆大小
            nativeHeapAllocated = nativeHeapAllocatedSize, // 原生堆已分配
            nativeHeapFree = nativeHeapFreeSize // 原生堆空闲
        )
    }
    
    /**
     * 检查是否需要进行内存清理
     * @return 是否需要清理内存
     */
    suspend fun shouldPerformMemoryCleanup(): Boolean = withContext(Dispatchers.Default) {
        val memoryInfo = getMemoryInfo() // 获取内存信息
        
        // 检查是否超过低内存阈值或系统处于低内存状态
        memoryInfo.memoryUsageRatio > LOW_MEMORY_THRESHOLD || memoryInfo.isLowMemory
    }
    
    /**
     * 执行内存清理操作
     */
    suspend fun performMemoryCleanup() = withContext(Dispatchers.Default) {
        try {
            // 清理弱引用缓存中的无效引用
            cleanupWeakReferences()
            
            // 清理图片缓存
            clearImageCache()
            
            // 建议进行垃圾回收
            suggestGarbageCollection()
            
            // 清理临时文件
            cleanupTempFiles()
            
        } catch (e: Exception) {
            e.printStackTrace() // 打印异常信息
        }
    }
    
    /**
     * 执行紧急内存清理（当内存使用率超过临界阈值时）
     */
    suspend fun performEmergencyMemoryCleanup() = withContext(Dispatchers.Default) {
        try {
            // 执行常规内存清理
            performMemoryCleanup()
            
            // 强制垃圾回收
            forceGarbageCollection()
            
            // 清理所有可清理的缓存
            clearAllCaches()
            
        } catch (e: Exception) {
            e.printStackTrace() // 打印异常信息
        }
    }
    
    /**
     * 添加对象到弱引用缓存
     * @param key 缓存键
     * @param value 要缓存的对象
     */
    fun addToWeakCache(key: String, value: Any) {
        weakReferenceCache[key] = WeakReference(value) // 使用弱引用存储对象
    }
    
    /**
     * 从弱引用缓存获取对象
     * @param key 缓存键
     * @return 缓存的对象，如果已被回收则返回null
     */
    fun getFromWeakCache(key: String): Any? {
        return weakReferenceCache[key]?.get() // 从弱引用获取对象
    }
    
    /**
     * 监控内存使用情况并在必要时触发清理
     */
    suspend fun monitorMemoryUsage() = withContext(Dispatchers.Default) {
        val memoryInfo = getMemoryInfo() // 获取当前内存信息
        
        when {
            // 内存使用率超过临界阈值，执行紧急清理
            memoryInfo.memoryUsageRatio > CRITICAL_MEMORY_THRESHOLD -> {
                performEmergencyMemoryCleanup()
            }
            // 内存使用率超过低内存阈值，执行常规清理
            memoryInfo.memoryUsageRatio > LOW_MEMORY_THRESHOLD -> {
                performMemoryCleanup()
            }
            // 定期建议垃圾回收
            System.currentTimeMillis() - lastGcTime > GC_TRIGGER_INTERVAL -> {
                suggestGarbageCollection()
            }
        }
    }
    
    /**
     * 清理弱引用缓存中的无效引用
     */
    private fun cleanupWeakReferences() {
        val iterator = weakReferenceCache.iterator() // 获取迭代器
        while (iterator.hasNext()) {
            val entry = iterator.next() // 获取下一个条目
            if (entry.value.get() == null) { // 如果引用已被回收
                iterator.remove() // 移除无效引用
            }
        }
    }
    
    /**
     * 清理图片缓存
     */
    private fun clearImageCache() {
        // 遍历弱引用缓存，回收Bitmap对象
        weakReferenceCache.values.forEach { weakRef ->
            val obj = weakRef.get()
            if (obj is Bitmap && !obj.isRecycled) { // 如果是未回收的Bitmap
                obj.recycle() // 回收Bitmap内存
            }
        }
    }
    
    /**
     * 建议进行垃圾回收
     */
    private fun suggestGarbageCollection() {
        System.gc() // 建议JVM进行垃圾回收
        lastGcTime = System.currentTimeMillis() // 更新最后GC时间
    }
    
    /**
     * 强制垃圾回收（仅在紧急情况下使用）
     */
    private fun forceGarbageCollection() {
        // 多次调用System.gc()增加垃圾回收的可能性
        repeat(3) {
            System.gc() // 建议垃圾回收
            System.runFinalization() // 运行终结器
            Thread.yield() // 让出CPU时间片
        }
        lastGcTime = System.currentTimeMillis() // 更新最后GC时间
    }
    
    /**
     * 清理临时文件
     */
    private suspend fun cleanupTempFiles() = withContext(Dispatchers.IO) {
        try {
            val cacheDir = context.cacheDir // 获取缓存目录
            val tempFiles = cacheDir.listFiles { file ->
                // 查找超过1小时的临时文件
                file.name.startsWith("temp_") && 
                System.currentTimeMillis() - file.lastModified() > 3600000L
            }
            
            // 删除过期的临时文件
            tempFiles?.forEach { file ->
                try {
                    file.delete() // 删除文件
                } catch (e: Exception) {
                    e.printStackTrace() // 打印删除失败的异常
                }
            }
        } catch (e: Exception) {
            e.printStackTrace() // 打印异常信息
        }
    }
    
    /**
     * 清理所有可清理的缓存
     */
    private fun clearAllCaches() {
        // 清理弱引用缓存
        weakReferenceCache.clear()
        
        // 清理系统缓存（如果可能）
        try {
            activityManager.clearApplicationUserData() // 清理应用用户数据（需要权限）
        } catch (e: Exception) {
            // 忽略权限异常，这是正常的
        }
    }
}

/**
 * 内存使用信息数据类
 */
data class MemoryInfo(
    val totalSystemMemory: Long, // 系统总内存（字节）
    val availableSystemMemory: Long, // 系统可用内存（字节）
    val isLowMemory: Boolean, // 是否处于低内存状态
    val maxAppMemory: Long, // 应用最大内存（字节）
    val totalAppMemory: Long, // 应用总内存（字节）
    val usedAppMemory: Long, // 应用已使用内存（字节）
    val freeAppMemory: Long, // 应用空闲内存（字节）
    val memoryUsageRatio: Double, // 内存使用率（0.0-1.0）
    val nativeHeapSize: Long, // 原生堆大小（字节）
    val nativeHeapAllocated: Long, // 原生堆已分配（字节）
    val nativeHeapFree: Long // 原生堆空闲（字节）
)