package com.aiinterview.simulator.domain.util

sealed class ApiException(message: String) : Exception(message) {
    object NetworkException : ApiException("网络连接失败，请检查网络设置")
    object TimeoutException : ApiException("请求超时，请稍后重试")
    object UnauthorizedException : ApiException("用户未登录或登录已过期")
    object ServerException : ApiException("服务器内部错误，请稍后重试")
    data class HttpException(val code: Int, val errorMessage: String) : ApiException(errorMessage)
    data class UnknownException(val errorMessage: String) : ApiException(errorMessage)
}

fun Throwable.toApiException(): ApiException {
    return when (this) {
        is java.net.SocketTimeoutException -> ApiException.TimeoutException
        is java.net.UnknownHostException -> ApiException.NetworkException
        is java.io.IOException -> ApiException.NetworkException
        is retrofit2.HttpException -> {
            when (code()) {
                401 -> ApiException.UnauthorizedException
                in 500..599 -> ApiException.ServerException
                else -> ApiException.HttpException(code(), message() ?: "HTTP错误")
            }
        }
        else -> ApiException.UnknownException(message ?: "未知错误")
    }
}