package com.aiinterview.simulator.presentation.screen.permission

import android.app.Activity
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.filled.Security
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.aiinterview.simulator.data.permission.PermissionStatus
import com.aiinterview.simulator.data.permission.PermissionType
import com.aiinterview.simulator.presentation.component.*
import com.aiinterview.simulator.presentation.viewmodel.PermissionViewModel

/**
 * 权限检查屏幕
 * 显示应用权限状态并允许用户授权权限
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PermissionScreen(
    onPermissionsGranted: () -> Unit,           // 权限授权完成回调
    onNavigateBack: () -> Unit,                 // 返回导航回调
    viewModel: PermissionViewModel = hiltViewModel() // 权限ViewModel
) {
    // 获取当前上下文和活动
    val context = LocalContext.current
    val activity = context as Activity
    
    // 收集权限状态
    val permissionState by viewModel.permissionState.collectAsStateWithLifecycle()
    
    // 在组件首次加载时检查权限
    LaunchedEffect(Unit) {
        viewModel.checkAllPermissions(activity)
    }
    
    // 监听权限状态变化，如果所有权限都已授权则回调
    LaunchedEffect(permissionState.allPermissionsGranted) {
        if (permissionState.allPermissionsGranted) {
            onPermissionsGranted()
        }
    }
    
    // 主界面布局
    Scaffold(
        topBar = {
            // 顶部应用栏
            TopAppBar(
                title = { 
                    Text("权限设置") 
                },
                navigationIcon = {
                    // 返回按钮
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.Security,
                            contentDescription = "返回"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        // 主内容区域
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
        ) {
            // 权限说明卡片
            PermissionExplanationCard()
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 权限状态列表
            if (permissionState.isLoading) {
                // 加载状态
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            } else {
                // 权限列表
                PermissionStatusList(
                    permissionResults = permissionState.permissionResults,
                    onRequestPermission = { permissionType ->
                        when (permissionType) {
                            PermissionType.MICROPHONE -> {
                                viewModel.requestMicrophonePermission(activity)
                            }
                            PermissionType.STORAGE -> {
                                viewModel.requestStoragePermission(activity)
                            }
                            else -> {
                                // 其他权限类型的处理
                            }
                        }
                    }
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 操作按钮
                PermissionActionButtons(
                    allPermissionsGranted = permissionState.allPermissionsGranted,
                    onRequestAllPermissions = {
                        viewModel.requestAllRequiredPermissions(activity)
                    },
                    onContinue = onPermissionsGranted,
                    onRefreshStatus = {
                        viewModel.checkAllPermissions(activity)
                    }
                )
            }
            
            // 错误信息显示
            permissionState.error?.let { error ->
                Spacer(modifier = Modifier.height(16.dp))
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Error,
                            contentDescription = "错误",
                            tint = MaterialTheme.colorScheme.error
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = error,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                    }
                }
            }
        }
    }
    
    // 权限对话框
    PermissionDialogs(
        permissionState = permissionState,
        viewModel = viewModel,
        activity = activity
    )
}

/**
 * 权限说明卡片
 * 向用户解释为什么需要这些权限
 */
@Composable
private fun PermissionExplanationCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 标题
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Security,
                    contentDescription = "安全",
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "权限说明",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 说明文字
            Text(
                text = "为了提供完整的AI面试体验，应用需要以下权限。我们承诺只在必要时使用这些权限，并严格保护您的隐私。",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
        }
    }
}

/**
 * 权限状态列表
 * 显示各个权限的当前状态
 */
@Composable
private fun PermissionStatusList(
    permissionResults: Map<String, com.aiinterview.simulator.data.permission.PermissionResult>,
    onRequestPermission: (PermissionType) -> Unit
) {
    // 获取所有权限类型
    val allPermissions = PermissionType.values().toList()
    
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(allPermissions) { permissionType ->
            val permissionResult = permissionResults[permissionType.permission]
            PermissionStatusItem(
                permissionType = permissionType,
                permissionResult = permissionResult,
                onRequestPermission = { onRequestPermission(permissionType) }
            )
        }
    }
}

/**
 * 权限状态项
 * 显示单个权限的状态和操作按钮
 */
@Composable
private fun PermissionStatusItem(
    permissionType: PermissionType,
    permissionResult: com.aiinterview.simulator.data.permission.PermissionResult?,
    onRequestPermission: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 权限图标
            Icon(
                imageVector = getPermissionIcon(permissionType),
                contentDescription = permissionType.title,
                modifier = Modifier.size(32.dp),
                tint = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 权限信息
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = permissionType.title,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = permissionType.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // 权限状态和操作
            when (permissionResult?.status) {
                PermissionStatus.GRANTED -> {
                    // 已授权状态
                    Icon(
                        imageVector = Icons.Default.CheckCircle,
                        contentDescription = "已授权",
                        tint = Color.Green
                    )
                }
                PermissionStatus.DENIED,
                PermissionStatus.PERMANENTLY_DENIED,
                PermissionStatus.NOT_REQUESTED -> {
                    // 未授权状态，显示请求按钮
                    OutlinedButton(
                        onClick = onRequestPermission,
                        modifier = Modifier.size(width = 80.dp, height = 36.dp)
                    ) {
                        Text(
                            text = "授权",
                            style = MaterialTheme.typography.labelSmall
                        )
                    }
                }
                null -> {
                    // 状态未知，显示检查按钮
                    OutlinedButton(
                        onClick = onRequestPermission,
                        modifier = Modifier.size(width = 80.dp, height = 36.dp)
                    ) {
                        Text(
                            text = "检查",
                            style = MaterialTheme.typography.labelSmall
                        )
                    }
                }
            }
        }
    }
}

/**
 * 权限操作按钮
 * 提供批量操作和继续按钮
 */
@Composable
private fun PermissionActionButtons(
    allPermissionsGranted: Boolean,
    onRequestAllPermissions: () -> Unit,
    onContinue: () -> Unit,
    onRefreshStatus: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        if (allPermissionsGranted) {
            // 所有权限已授权，显示继续按钮
            Button(
                onClick = onContinue,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("继续使用应用")
            }
        } else {
            // 还有权限未授权，显示请求按钮
            Button(
                onClick = onRequestAllPermissions,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("授权所有权限")
            }
        }
        
        // 刷新状态按钮
        OutlinedButton(
            onClick = onRefreshStatus,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("刷新权限状态")
        }
    }
}

/**
 * 权限对话框集合
 * 管理所有权限相关的对话框
 */
@Composable
private fun PermissionDialogs(
    permissionState: com.aiinterview.simulator.presentation.viewmodel.PermissionUiState,
    viewModel: PermissionViewModel,
    activity: Activity
) {
    // 录音权限对话框
    if (permissionState.showMicrophonePermissionDialog && 
        permissionState.currentRequestingPermission == PermissionType.MICROPHONE) {
        PermissionDialog(
            permissionType = PermissionType.MICROPHONE,
            isVisible = true,
            onGrantPermission = { viewModel.confirmGrantPermission(activity) },
            onDenyPermission = { viewModel.denyPermission() },
            onDismiss = { viewModel.hideAllDialogs() }
        )
    }
    
    // 存储权限对话框
    if (permissionState.showStoragePermissionDialog && 
        permissionState.currentRequestingPermission == PermissionType.STORAGE) {
        PermissionDialog(
            permissionType = PermissionType.STORAGE,
            isVisible = true,
            onGrantPermission = { viewModel.confirmGrantPermission(activity) },
            onDenyPermission = { viewModel.denyPermission() },
            onDismiss = { viewModel.hideAllDialogs() }
        )
    }
    
    // 多权限请求对话框
    if (permissionState.showMultiplePermissionsDialog) {
        MultiplePermissionsDialog(
            permissions = permissionState.pendingPermissions,
            isVisible = true,
            onGrantAll = { viewModel.confirmGrantAllPermissions(activity) },
            onDeny = { viewModel.denyPermission() },
            onDismiss = { viewModel.hideAllDialogs() }
        )
    }
    
    // 永久拒绝权限对话框
    if (permissionState.showPermanentlyDeniedDialog && 
        permissionState.permanentlyDeniedPermission != null) {
        PermissionPermanentlyDeniedDialog(
            permissionType = permissionState.permanentlyDeniedPermission,
            isVisible = true,
            onGoToSettings = { viewModel.goToAppSettings(activity) },
            onCancel = { viewModel.hideAllDialogs() },
            onDismiss = { viewModel.hideAllDialogs() }
        )
    }
}

/**
 * 根据权限类型获取对应的图标
 * @param permissionType 权限类型
 * @return 对应的图标
 */
private fun getPermissionIcon(permissionType: PermissionType): androidx.compose.ui.graphics.vector.ImageVector {
    return when (permissionType) {
        PermissionType.MICROPHONE -> Icons.Default.Security
        PermissionType.STORAGE -> Icons.Default.Security
        PermissionType.NETWORK_STATE,
        PermissionType.INTERNET -> Icons.Default.Security
    }
}