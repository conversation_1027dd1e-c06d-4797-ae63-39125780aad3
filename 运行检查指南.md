# AI面试模拟器 - 项目运行检查指南

## 项目状态概览

✅ **项目完整性**: 代码库完整，所有核心功能已实现  
✅ **架构设计**: 采用MVVM + Clean Architecture  
✅ **技术栈**: Kotlin + Jetpack Compose + Hilt + Room  
⚠️ **构建配置**: 需要配置Android SDK路径  

## 运行前准备工作

### 1. 开发环境要求

- **Android Studio**: Hedgehog (2023.1.1) 或更高版本
- **JDK**: Java 8 或更高版本
- **Android SDK**: API Level 24-34
- **Gradle**: 8.5 (项目已配置)
- **Kotlin**: 1.9.10 (项目已配置)

### 2. 必需的配置文件

#### 2.1 Android SDK配置 (local.properties)
```properties
# 修改为你的实际Android SDK路径
sdk.dir=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk
```

#### 2.2 签名配置 (keystore.properties) - 可选
```properties
# 发布版本签名配置（可选，调试版本不需要）
storeFile=your-keystore-file.jks
storePassword=your-store-password
keyAlias=your-key-alias
keyPassword=your-key-password
```

### 3. 第三方服务配置

项目集成了多个第三方服务，需要配置API密钥：

#### 3.1 语音识别服务
- **百度智能云ASR**: 需要配置APP_ID、API_KEY、SECRET_KEY
- **腾讯云ASR**: 需要配置SecretId、SecretKey
- **讯飞语音**: 需要配置APPID

#### 3.2 AI服务
- **文心一言**: 需要配置API_KEY、SECRET_KEY
- **通义千问**: 需要配置API_KEY

#### 3.3 TTS服务
- **百度TTS**: 需要配置相关密钥
- **腾讯TTS**: 需要配置相关密钥

## 构建和运行步骤

### 1. 检查构建环境
```bash
# 检查Gradle版本
./gradlew --version

# 清理项目
./gradlew clean
```

### 2. 编译项目
```bash
# 编译调试版本
./gradlew assembleDebug

# 编译所有版本
./gradlew build
```

### 3. 运行测试
```bash
# 运行单元测试
./gradlew test

# 运行Android测试（需要连接设备或模拟器）
./gradlew connectedAndroidTest
```

### 4. 安装到设备
```bash
# 安装调试版本到连接的设备
./gradlew installDebug

# 或者使用Android Studio的Run按钮
```

## 常见问题解决

### 1. SDK路径问题
**错误**: `SDK location not found`  
**解决**: 在`local.properties`中配置正确的Android SDK路径

### 2. 权限问题
**错误**: 录音功能无法使用  
**解决**: 确保在设备上授予录音权限

### 3. 网络连接问题
**错误**: API调用失败  
**解决**: 检查网络连接和API密钥配置

### 4. 依赖下载问题
**错误**: 依赖下载失败  
**解决**: 检查网络连接，或配置代理

## 功能测试清单

### 核心功能测试
- [ ] 用户登录注册
- [ ] 岗位选择
- [ ] 语音录制
- [ ] 语音识别
- [ ] AI问题生成
- [ ] AI评价生成
- [ ] 历史记录查看
- [ ] 进度分析

### 权限测试
- [ ] 录音权限申请
- [ ] 网络权限
- [ ] 存储权限

### 兼容性测试
- [ ] Android 7.0+ (API 24+)
- [ ] 不同屏幕尺寸
- [ ] 横竖屏切换

## 性能监控

### 内存使用
- 启动内存: < 100MB
- 运行内存: < 200MB
- 录音时内存: < 300MB

### 启动时间
- 冷启动: < 3秒
- 热启动: < 1秒

### 网络请求
- API响应时间: < 2秒
- 语音识别时间: < 5秒

## 发布准备

### 1. 代码检查
```bash
# 代码质量检查
./gradlew lint

# 安全检查
./gradlew dependencyCheckAnalyze
```

### 2. 构建发布版本
```bash
# 构建发布版本APK
./gradlew assembleRelease

# 构建AAB包（推荐用于Google Play）
./gradlew bundleRelease
```

### 3. 多渠道打包
```bash
# 构建所有渠道的发布版本
./gradlew assembleRelease

# 构建特定渠道
./gradlew assembleHuaweiRelease
./gradlew assembleXiaomiRelease
```

## 注意事项

1. **API密钥安全**: 不要将API密钥提交到版本控制系统
2. **权限说明**: 确保在应用商店描述中说明权限用途
3. **隐私政策**: 准备完整的隐私政策文档
4. **测试覆盖**: 在多种设备上进行充分测试
5. **性能优化**: 监控内存使用和电池消耗

## 技术支持

如果遇到问题，可以：
1. 查看Android Studio的Build输出
2. 检查Logcat日志
3. 参考项目中的测试用例
4. 查看相关依赖库的官方文档

---

**项目状态**: 生产就绪 ✅  
**最后更新**: 2024年12月  
**维护状态**: 活跃维护中