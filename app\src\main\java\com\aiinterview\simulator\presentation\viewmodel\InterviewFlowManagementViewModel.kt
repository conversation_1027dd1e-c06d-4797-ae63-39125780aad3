package com.aiinterview.simulator.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.aiinterview.simulator.data.handler.TechnicalIssueHandler
import com.aiinterview.simulator.data.manager.InterviewSessionManager
import com.aiinterview.simulator.data.model.InterviewSession
import com.aiinterview.simulator.data.model.Position
import com.aiinterview.simulator.data.service.InterviewTimerService
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class InterviewFlowManagementViewModel @Inject constructor(
    private val sessionManager: InterviewSessionManager,
    private val timerService: InterviewTimerService,
    private val technicalIssueHandler: TechnicalIssueHandler
) : ViewModel() {
    
    // 面试会话状态
    val activeSession: StateFlow<InterviewSession?> = sessionManager.activeSession
    val sessionProgress: StateFlow<InterviewSessionManager.SessionProgress> = sessionManager.sessionProgress
    
    // 计时器状态
    val timerState: StateFlow<InterviewTimerService.TimerState> = timerService.timerState
    val totalTimeRemaining: StateFlow<Long> = timerService.totalTimeRemaining
    val questionTimeRemaining: StateFlow<Long> = timerService.questionTimeRemaining
    val timeWarnings: StateFlow<List<InterviewTimerService.TimeWarning>> = timerService.timeWarnings
    
    // 技术问题状态
    val currentTechnicalIssue: StateFlow<TechnicalIssueHandler.TechnicalIssue?> = technicalIssueHandler.currentIssue
    val resolutionOptions: StateFlow<TechnicalIssueHandler.ResolutionOptions?> = technicalIssueHandler.resolutionOptions
    val isHandlingIssue: StateFlow<Boolean> = technicalIssueHandler.isHandlingIssue
    
    // UI状态
    private val _uiState = MutableStateFlow(InterviewFlowUiState())
    val uiState: StateFlow<InterviewFlowUiState> = _uiState.asStateFlow()
    
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    data class InterviewFlowUiState(
        val isLoading: Boolean = false,
        val canPauseResume: Boolean = true,
        val canProceedToNext: Boolean = false,
        val canCompleteInterview: Boolean = false,
        val showTimeWarning: Boolean = false,
        val showTimeCritical: Boolean = false,
        val hasTechnicalIssues: Boolean = false
    )
    
    init {
        // 监听状态变化并更新UI状态
        viewModelScope.launch {
            combine(
                sessionProgress,
                timerState,
                totalTimeRemaining,
                currentTechnicalIssue
            ) { progress, timer, timeRemaining, issue ->
                _uiState.value = _uiState.value.copy(
                    canProceedToNext = progress.currentQuestionIndex < progress.totalQuestions,
                    canCompleteInterview = progress.completedQuestions >= progress.totalQuestions,
                    showTimeWarning = timeRemaining <= 5 * 60 * 1000L && timeRemaining > 1 * 60 * 1000L,
                    showTimeCritical = timeRemaining <= 1 * 60 * 1000L && timeRemaining > 0,
                    hasTechnicalIssues = issue != null
                )
            }.collect()
        }
        
        // 监听时间警告并自动处理
        viewModelScope.launch {
            timeWarnings.collect { warnings ->
                warnings.filter { !it.acknowledged && it.isVoiceWarning }.forEach { warning ->
                    handleTimeWarning(warning)
                }
            }
        }
    }
    
    /**
     * 创建面试会话
     */
    fun createInterviewSession(
        userId: String,
        position: Position,
        useAIGeneration: Boolean = true
    ) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            val result = sessionManager.createSession(userId, position, useAIGeneration)
            
            result.fold(
                onSuccess = { session ->
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    // 启动计时器
                    timerService.startInterviewTimer(position.duration)
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    _errorMessage.value = "创建面试会话失败: ${error.message}"
                }
            )
        }
    }
    
    /**
     * 开始面试会话
     */
    fun startInterviewSession(sessionId: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            val result = sessionManager.startSession(sessionId)
            
            result.fold(
                onSuccess = { session ->
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    // 启动计时器
                    timerService.startInterviewTimer(30) // 默认30分钟
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    _errorMessage.value = "开始面试失败: ${error.message}"
                }
            )
        }
    }
    
    /**
     * 进入下一个问题
     */
    fun proceedToNextQuestion() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            val result = sessionManager.proceedToNextQuestion()
            
            result.fold(
                onSuccess = { question ->
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    // 开始问题计时
                    timerService.startQuestionTimer(question.timeLimit)
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    if (error.message?.contains("面试已完成") != true) {
                        _errorMessage.value = "进入下一题失败: ${error.message}"
                    }
                }
            )
        }
    }
    
    /**
     * 提交问题回答
     */
    fun submitAnswer(
        questionId: String,
        audioFile: java.io.File,
        transcription: String,
        duration: Int
    ) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            val result = sessionManager.submitAnswer(questionId, audioFile, transcription, duration)
            
            result.fold(
                onSuccess = {
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    // 停止当前问题计时
                    timerService.stopQuestionTimer()
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    _errorMessage.value = "提交回答失败: ${error.message}"
                }
            )
        }
    }
    
    /**
     * 暂停面试
     */
    fun pauseInterview() {
        viewModelScope.launch {
            val sessionResult = sessionManager.pauseSession()
            timerService.pauseTimers()
            
            sessionResult.fold(
                onSuccess = {
                    // 暂停成功
                },
                onFailure = { error ->
                    _errorMessage.value = "暂停面试失败: ${error.message}"
                }
            )
        }
    }
    
    /**
     * 恢复面试
     */
    fun resumeInterview() {
        viewModelScope.launch {
            val sessionResult = sessionManager.resumeSession()
            timerService.resumeTimers()
            
            sessionResult.fold(
                onSuccess = {
                    // 恢复成功
                },
                onFailure = { error ->
                    _errorMessage.value = "恢复面试失败: ${error.message}"
                }
            )
        }
    }
    
    /**
     * 完成面试
     */
    fun completeInterview() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            val result = sessionManager.completeSession()
            timerService.stopAllTimers()
            
            result.fold(
                onSuccess = { session ->
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    // 面试完成，可以导航到评价页面
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    _errorMessage.value = "完成面试失败: ${error.message}"
                }
            )
        }
    }
    
    /**
     * 取消面试
     */
    fun cancelInterview() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            val result = sessionManager.cancelSession()
            timerService.stopAllTimers()
            
            result.fold(
                onSuccess = {
                    _uiState.value = _uiState.value.copy(isLoading = false)
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    _errorMessage.value = "取消面试失败: ${error.message}"
                }
            )
        }
    }
    
    /**
     * 处理录音失败
     */
    fun handleRecordingFailure(questionId: String, errorMessage: String) {
        viewModelScope.launch {
            val result = technicalIssueHandler.handleRecordingFailed(
                questionId = questionId,
                audioFile = null,
                errorMessage = errorMessage
            )
            
            result.fold(
                onSuccess = { options ->
                    // 解决方案已准备好，UI会自动显示
                },
                onFailure = { error ->
                    _errorMessage.value = "处理录音问题失败: ${error.message}"
                }
            )
        }
    }
    
    /**
     * 处理语音识别失败
     */
    fun handleSpeechRecognitionFailure(
        questionId: String,
        audioFile: java.io.File,
        provider: String,
        errorMessage: String
    ) {
        viewModelScope.launch {
            val result = technicalIssueHandler.handleSpeechRecognitionFailed(
                questionId = questionId,
                audioFile = audioFile,
                provider = provider,
                errorMessage = errorMessage
            )
            
            result.fold(
                onSuccess = { options ->
                    // 解决方案已准备好
                },
                onFailure = { error ->
                    _errorMessage.value = "处理语音识别问题失败: ${error.message}"
                }
            )
        }
    }
    
    /**
     * 处理网络连接问题
     */
    fun handleNetworkIssue(description: String, questionId: String? = null) {
        viewModelScope.launch {
            val result = technicalIssueHandler.handleNetworkIssue(description, questionId)
            
            result.fold(
                onSuccess = { options ->
                    // 解决方案已准备好
                },
                onFailure = { error ->
                    _errorMessage.value = "处理网络问题失败: ${error.message}"
                }
            )
        }
    }
    
    /**
     * 执行技术问题解决方案
     */
    fun executeResolution(actionId: String) {
        viewModelScope.launch {
            val result = technicalIssueHandler.executeResolution(actionId)
            
            result.fold(
                onSuccess = { message ->
                    // 解决方案执行成功
                },
                onFailure = { error ->
                    _errorMessage.value = "执行解决方案失败: ${error.message}"
                }
            )
        }
    }
    
    /**
     * 确认时间警告
     */
    fun acknowledgeTimeWarning(warningId: String) {
        timerService.acknowledgeWarning(warningId)
    }
    
    /**
     * 清除当前技术问题
     */
    fun clearCurrentIssue() {
        technicalIssueHandler.clearCurrentIssue()
    }
    
    /**
     * 处理时间警告
     */
    private suspend fun handleTimeWarning(warning: InterviewTimerService.TimeWarning) {
        when (warning.type) {
            InterviewTimerService.TimeWarningType.INTERVIEW_TIME_UP -> {
                // 时间到，自动完成面试
                completeInterview()
            }
            InterviewTimerService.TimeWarningType.QUESTION_TIME_UP -> {
                // 问题时间到，可以提示用户
                // 这里可以添加自动跳转到下一题的逻辑
            }
            else -> {
                // 其他警告类型，只需要显示提醒
            }
        }
    }
    
    /**
     * 获取面试统计信息
     */
    fun getInterviewStatistics(): InterviewStatistics {
        val sessionStats = sessionManager.getSessionStatistics()
        val timerStats = timerService.getTimerStatistics()
        
        return InterviewStatistics(
            sessionId = sessionStats.sessionId,
            totalQuestions = sessionStats.totalQuestions,
            completedQuestions = sessionStats.completedQuestions,
            completionRate = sessionStats.completionRate,
            totalTimeSpent = timerStats.totalTimeElapsed,
            averageTimePerQuestion = sessionStats.averageTimePerQuestion,
            timeWarningsCount = timerStats.warningsTriggered,
            technicalIssuesCount = sessionStats.technicalIssuesCount,
            resolvedIssuesCount = sessionStats.resolvedIssuesCount
        )
    }
    
    /**
     * 检查是否可以进行下一步操作
     */
    fun canProceedToNext(): Boolean {
        val progress = sessionProgress.value
        return progress.currentQuestionIndex < progress.totalQuestions
    }
    
    /**
     * 检查面试是否完成
     */
    fun isInterviewCompleted(): Boolean {
        val progress = sessionProgress.value
        return progress.completedQuestions >= progress.totalQuestions
    }
    
    /**
     * 获取格式化的时间显示
     */
    fun getFormattedTotalTime(): String {
        return timerService.getFormattedTotalTime()
    }
    
    fun getFormattedQuestionTime(): String {
        return timerService.getFormattedQuestionTime()
    }
    
    /**
     * 获取面试进度百分比
     */
    fun getInterviewProgress(): Float {
        return timerService.getInterviewProgress()
    }
    
    /**
     * 清除错误消息
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }
    
    override fun onCleared() {
        super.onCleared()
        // 清理资源
        timerService.stopAllTimers()
    }
    
    data class InterviewStatistics(
        val sessionId: String?,
        val totalQuestions: Int,
        val completedQuestions: Int,
        val completionRate: Float,
        val totalTimeSpent: Long,
        val averageTimePerQuestion: Long,
        val timeWarningsCount: Int,
        val technicalIssuesCount: Int,
        val resolvedIssuesCount: Int
    )
}