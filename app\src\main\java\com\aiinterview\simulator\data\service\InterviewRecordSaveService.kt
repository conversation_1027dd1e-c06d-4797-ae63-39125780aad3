package com.aiinterview.simulator.data.service

import com.aiinterview.simulator.data.model.*
import com.aiinterview.simulator.data.repository.InterviewRecordRepository
import com.aiinterview.simulator.data.repository.InterviewRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 面试记录自动保存服务
 * 负责在面试完成后自动创建和保存面试记录
 */
@Singleton
class InterviewRecordSaveService @Inject constructor(
    private val interviewRecordRepository: InterviewRecordRepository,
    private val interviewRepository: InterviewRepository
) {
    
    /**
     * 面试完成后自动保存记录
     */
    suspend fun saveInterviewRecord(
        sessionId: String,
        audioFiles: List<File> = emptyList()
    ): Result<InterviewRecordModel> = withContext(Dispatchers.IO) {
        try {
            // 获取面试会话信息
            val session = interviewRepository.getInterviewSession(sessionId)
                ?: return@withContext Result.failure(Exception("面试会话不存在: $sessionId"))
            
            // 获取岗位信息
            val position = interviewRepository.getPositionById(session.positionId)
                ?: return@withContext Result.failure(Exception("岗位信息不存在: ${session.positionId}"))
            
            // 处理音频文件信息
            val audioFileInfos = processAudioFiles(audioFiles, session.answers)
            
            // 创建面试记录
            val record = interviewRecordRepository.createRecordFromSession(
                session = session,
                position = position,
                audioFiles = audioFileInfos
            )
            
            Result.success(record)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 从面试会话直接保存记录（用于面试流程服务调用）
     */
    suspend fun saveRecordFromSession(
        session: InterviewSession,
        position: Position,
        audioFiles: List<File> = emptyList()
    ): Result<InterviewRecordModel> = withContext(Dispatchers.IO) {
        try {
            // 处理音频文件信息
            val audioFileInfos = processAudioFiles(audioFiles, session.answers)
            
            // 创建面试记录
            val record = interviewRecordRepository.createRecordFromSession(
                session = session,
                position = position,
                audioFiles = audioFileInfos
            )
            
            Result.success(record)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 更新面试记录的评价信息
     */
    suspend fun updateRecordWithEvaluation(
        sessionId: String,
        evaluation: InterviewEvaluation
    ): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            // 获取现有记录
            val existingRecord = interviewRecordRepository.getRecordBySessionId(sessionId)
                ?: return@withContext Result.failure(Exception("面试记录不存在: $sessionId"))
            
            // 更新评价信息
            val updatedRecord = existingRecord.copy(
                evaluationId = evaluation.sessionId,
                overallScore = evaluation.overallScore
            )
            
            interviewRecordRepository.updateRecord(updatedRecord)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 批量保存历史面试记录
     */
    suspend fun batchSaveRecords(
        sessions: List<InterviewSession>,
        positions: Map<String, Position>
    ): Result<List<InterviewRecordModel>> = withContext(Dispatchers.IO) {
        try {
            val records = mutableListOf<InterviewRecordModel>()
            
            for (session in sessions) {
                val position = positions[session.positionId]
                if (position != null) {
                    val record = interviewRecordRepository.createRecordFromSession(
                        session = session,
                        position = position,
                        audioFiles = emptyList() // 历史记录可能没有本地音频文件
                    )
                    records.add(record)
                }
            }
            
            Result.success(records)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 处理音频文件信息
     */
    private fun processAudioFiles(
        audioFiles: List<File>,
        answers: List<Answer>
    ): List<AudioFileInfo> {
        return audioFiles.mapNotNull { file ->
            if (file.exists()) {
                // 根据文件名或路径匹配对应的问题ID
                val questionId = extractQuestionIdFromFileName(file.name, answers)
                
                AudioFileInfo(
                    filePath = file.absolutePath,
                    fileName = file.name,
                    fileSize = file.length(),
                    duration = extractAudioDuration(file),
                    questionId = questionId,
                    createdAt = file.lastModified(),
                    isUploaded = false,
                    uploadUrl = null
                )
            } else {
                null
            }
        }
    }
    
    /**
     * 从文件名提取问题ID
     */
    private fun extractQuestionIdFromFileName(fileName: String, answers: List<Answer>): String {
        // 尝试从文件名中提取问题ID
        // 假设文件名格式为: "question_[questionId]_[timestamp].mp3"
        val questionIdPattern = Regex("question_([^_]+)_")
        val matchResult = questionIdPattern.find(fileName)
        
        return if (matchResult != null) {
            matchResult.groupValues[1]
        } else {
            // 如果无法从文件名提取，尝试按顺序匹配
            val index = extractIndexFromFileName(fileName)
            if (index >= 0 && index < answers.size) {
                answers[index].questionId
            } else {
                answers.firstOrNull()?.questionId ?: ""
            }
        }
    }
    
    /**
     * 从文件名提取索引
     */
    private fun extractIndexFromFileName(fileName: String): Int {
        val indexPattern = Regex("_(\\d+)\\.")
        val matchResult = indexPattern.find(fileName)
        return matchResult?.groupValues?.get(1)?.toIntOrNull() ?: -1
    }
    
    /**
     * 提取音频时长（简化实现）
     */
    private fun extractAudioDuration(file: File): Int {
        // 这里应该使用音频处理库来获取真实的音频时长
        // 为了简化，这里使用文件大小估算（假设平均比特率）
        val fileSizeKB = file.length() / 1024
        val estimatedDurationSeconds = (fileSizeKB / 16).toInt() // 假设16KB/s的比特率
        return maxOf(1, estimatedDurationSeconds) // 至少1秒
    }
    
    /**
     * 清理过期的音频文件
     */
    suspend fun cleanupExpiredAudioFiles(
        userId: String,
        retentionDays: Int = 30
    ): Result<Int> = withContext(Dispatchers.IO) {
        try {
            val cutoffTime = System.currentTimeMillis() - (retentionDays * 24 * 60 * 60 * 1000L)
            
            // 获取过期的记录
            val expiredRecords = interviewRecordRepository.getRecordsByUser(userId)
            var deletedCount = 0
            
            expiredRecords.collect { records ->
                for (record in records) {
                    if (record.createdAt < cutoffTime) {
                        // 删除音频文件
                        for (audioFile in record.audioFiles) {
                            val file = File(audioFile.filePath)
                            if (file.exists() && file.delete()) {
                                deletedCount++
                            }
                        }
                    }
                }
            }
            
            Result.success(deletedCount)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 获取音频文件总大小
     */
    suspend fun getAudioFilesTotalSize(userId: String): Result<Long> = withContext(Dispatchers.IO) {
        try {
            var totalSize = 0L
            
            val records = interviewRecordRepository.getRecordsByUser(userId)
            records.collect { recordList ->
                for (record in recordList) {
                    for (audioFile in record.audioFiles) {
                        val file = File(audioFile.filePath)
                        if (file.exists()) {
                            totalSize += file.length()
                        }
                    }
                }
            }
            
            Result.success(totalSize)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 验证音频文件完整性
     */
    suspend fun validateAudioFiles(recordId: String): Result<AudioValidationResult> = withContext(Dispatchers.IO) {
        try {
            val record = interviewRecordRepository.getRecordById(recordId)
                ?: return@withContext Result.failure(Exception("记录不存在: $recordId"))
            
            val validFiles = mutableListOf<AudioFileInfo>()
            val missingFiles = mutableListOf<AudioFileInfo>()
            val corruptedFiles = mutableListOf<AudioFileInfo>()
            
            for (audioFile in record.audioFiles) {
                val file = File(audioFile.filePath)
                when {
                    !file.exists() -> missingFiles.add(audioFile)
                    file.length() != audioFile.fileSize -> corruptedFiles.add(audioFile)
                    else -> validFiles.add(audioFile)
                }
            }
            
            val result = AudioValidationResult(
                totalFiles = record.audioFiles.size,
                validFiles = validFiles,
                missingFiles = missingFiles,
                corruptedFiles = corruptedFiles
            )
            
            Result.success(result)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

/**
 * 音频文件验证结果
 */
data class AudioValidationResult(
    val totalFiles: Int,
    val validFiles: List<AudioFileInfo>,
    val missingFiles: List<AudioFileInfo>,
    val corruptedFiles: List<AudioFileInfo>
) {
    val isAllValid: Boolean
        get() = missingFiles.isEmpty() && corruptedFiles.isEmpty()
    
    val validCount: Int
        get() = validFiles.size
    
    val missingCount: Int
        get() = missingFiles.size
    
    val corruptedCount: Int
        get() = corruptedFiles.size
}