package com.aiinterview.simulator.data.model

/**
 * 进步分析数据模型
 * 用于展示用户面试表现的进步趋势和统计信息
 */

/**
 * 进步分析结果
 * @param overallTrend 总体趋势分析
 * @param scoreHistory 分数历史记录
 * @param categoryAnalysis 分类别分析
 * @param timeAnalysis 时间维度分析
 * @param recommendations 改进建议
 */
data class ProgressAnalysisResult(
    val overallTrend: OverallTrend,
    val scoreHistory: List<ScorePoint>,
    val categoryAnalysis: List<CategoryProgress>,
    val timeAnalysis: TimeAnalysis,
    val recommendations: List<String>
)

/**
 * 总体趋势分析
 * @param trendDirection 趋势方向（上升、下降、稳定）
 * @param improvementRate 改进率（百分比）
 * @param currentLevel 当前水平
 * @param description 趋势描述
 */
data class OverallTrend(
    val trendDirection: TrendDirection,
    val improvementRate: Double,
    val currentLevel: PerformanceLevel,
    val description: String
)

/**
 * 分数点数据（用于绘制趋势图）
 * @param timestamp 时间戳
 * @param score 分数
 * @param positionName 岗位名称
 * @param category 岗位类别
 */
data class ScorePoint(
    val timestamp: Long,
    val score: Double,
    val positionName: String,
    val category: String
)

/**
 * 分类别进步分析
 * @param category 岗位类别
 * @param averageScore 平均分数
 * @param bestScore 最高分数
 * @param interviewCount 面试次数
 * @param improvementRate 改进率
 * @param trend 趋势方向
 */
data class CategoryProgress(
    val category: String,
    val averageScore: Double,
    val bestScore: Double,
    val interviewCount: Int,
    val improvementRate: Double,
    val trend: TrendDirection
)

/**
 * 时间维度分析
 * @param weeklyStats 周统计
 * @param monthlyStats 月统计
 * @param totalPracticeTime 总练习时间（分钟）
 * @param averageSessionDuration 平均单次时长（分钟）
 */
data class TimeAnalysis(
    val weeklyStats: List<WeeklyStats>,
    val monthlyStats: List<MonthlyStats>,
    val totalPracticeTime: Int,
    val averageSessionDuration: Int
)

/**
 * 周统计数据
 * @param weekStart 周开始时间
 * @param interviewCount 面试次数
 * @param averageScore 平均分数
 * @param totalDuration 总时长（分钟）
 */
data class WeeklyStats(
    val weekStart: Long,
    val interviewCount: Int,
    val averageScore: Double,
    val totalDuration: Int
)

/**
 * 月统计数据
 * @param monthStart 月开始时间
 * @param interviewCount 面试次数
 * @param averageScore 平均分数
 * @param totalDuration 总时长（分钟）
 * @param improvementFromLastMonth 相比上月的改进率
 */
data class MonthlyStats(
    val monthStart: Long,
    val interviewCount: Int,
    val averageScore: Double,
    val totalDuration: Int,
    val improvementFromLastMonth: Double
)

/**
 * 趋势方向枚举
 */
enum class TrendDirection(val displayName: String, val description: String) {
    RISING("上升", "表现持续改善"),
    FALLING("下降", "表现有所下滑"),
    STABLE("稳定", "表现保持稳定"),
    FLUCTUATING("波动", "表现起伏较大")
}

/**
 * 表现水平枚举
 */
enum class PerformanceLevel(val displayName: String, val scoreRange: IntRange, val color: Long) {
    EXCELLENT("优秀", 90..100, 0xFF4CAF50),
    GOOD("良好", 80..89, 0xFF2196F3),
    AVERAGE("一般", 70..79, 0xFFFF9800),
    POOR("较差", 0..69, 0xFFF44336);
    
    companion object {
        /**
         * 根据分数获取表现水平
         */
        fun fromScore(score: Double): PerformanceLevel {
            val intScore = score.toInt()
            return values().find { intScore in it.scoreRange } ?: POOR
        }
    }
}

/**
 * 练习建议类型
 */
enum class RecommendationType(val displayName: String) {
    WEAK_CATEGORY("薄弱类别练习"),
    CONSISTENCY("保持稳定性"),
    TIME_MANAGEMENT("时间管理"),
    SPECIFIC_SKILL("特定技能提升"),
    REGULAR_PRACTICE("定期练习")
}

/**
 * 练习建议
 * @param type 建议类型
 * @param title 建议标题
 * @param description 详细描述
 * @param priority 优先级（1-5，5最高）
 * @param relatedCategory 相关类别（可选）
 */
data class PracticeRecommendation(
    val type: RecommendationType,
    val title: String,
    val description: String,
    val priority: Int,
    val relatedCategory: String? = null
)