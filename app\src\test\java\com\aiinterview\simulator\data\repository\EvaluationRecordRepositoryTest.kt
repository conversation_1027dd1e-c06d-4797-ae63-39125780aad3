package com.aiinterview.simulator.data.repository

import com.aiinterview.simulator.data.dao.EvaluationRecordDao
import com.aiinterview.simulator.data.model.EvaluationRecord
import com.aiinterview.simulator.data.model.EvaluationRecordModel
import com.aiinterview.simulator.data.model.DimensionScore
import com.aiinterview.simulator.data.service.EvaluationSyncService
import com.aiinterview.simulator.domain.util.Resource
import com.google.gson.Gson
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class EvaluationRecordRepositoryTest {

    @Mock
    private lateinit var evaluationRecordDao: EvaluationRecordDao

    @Mock
    private lateinit var evaluationSyncService: EvaluationSyncService

    private lateinit var gson: Gson
    private lateinit var repository: EvaluationRecordRepository

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        gson = Gson()
        repository = EvaluationRecordRepositoryImpl(
            evaluationRecordDao,
            evaluationSyncService,
            gson
        )
    }

    @Test
    fun `saveEvaluationRecord should save record successfully`() = runTest {
        // Given
        val record = createTestEvaluationRecordModel()
        
        // When
        val result = repository.saveEvaluationRecord(record).toList()
        
        // Then
        assertTrue(result.first() is Resource.Loading)
        assertTrue(result.last() is Resource.Success)
        verify(evaluationRecordDao).insertEvaluationRecord(org.mockito.kotlin.any())
    }

    @Test
    fun `getEvaluationRecordsByUser should return mapped records`() = runTest {
        // Given
        val userId = "test-user-id"
        val entityRecord = createTestEvaluationRecord()
        whenever(evaluationRecordDao.getEvaluationRecordsByUser(userId))
            .thenReturn(flowOf(listOf(entityRecord)))
        
        // When
        val result = repository.getEvaluationRecordsByUser(userId).toList()
        
        // Then
        assertEquals(1, result.size)
        assertEquals(1, result.first().size)
        assertEquals(entityRecord.id, result.first().first().id)
    }

    @Test
    fun `deleteEvaluationRecord should delete record successfully`() = runTest {
        // Given
        val recordId = "test-record-id"
        
        // When
        val result = repository.deleteEvaluationRecord(recordId).toList()
        
        // Then
        assertTrue(result.first() is Resource.Loading)
        assertTrue(result.last() is Resource.Success)
        verify(evaluationRecordDao).deleteEvaluationRecordById(recordId)
    }

    private fun createTestEvaluationRecordModel(): EvaluationRecordModel {
        return EvaluationRecordModel(
            id = "test-id",
            sessionId = "test-session-id",
            userId = "test-user-id",
            positionId = "test-position-id",
            positionName = "测试岗位",
            overallScore = 85.0,
            dimensionScores = mapOf(
                "content_completeness" to DimensionScore(85.0, 100.0, "内容完整"),
                "logic" to DimensionScore(80.0, 100.0, "逻辑清晰")
            ),
            feedback = "整体表现良好",
            suggestions = listOf("建议1", "建议2"),
            createdAt = System.currentTimeMillis(),
            isSynced = false,
            syncedAt = null
        )
    }

    private fun createTestEvaluationRecord(): EvaluationRecord {
        val dimensionScores = mapOf(
            "content_completeness" to DimensionScore(85.0, 100.0, "内容完整"),
            "logic" to DimensionScore(80.0, 100.0, "逻辑清晰")
        )
        val suggestions = listOf("建议1", "建议2")
        
        return EvaluationRecord(
            id = "test-id",
            sessionId = "test-session-id",
            userId = "test-user-id",
            positionId = "test-position-id",
            positionName = "测试岗位",
            overallScore = 85.0,
            dimensionScoresJson = gson.toJson(dimensionScores),
            feedback = "整体表现良好",
            suggestionsJson = gson.toJson(suggestions),
            createdAt = System.currentTimeMillis(),
            isSynced = false,
            syncedAt = null
        )
    }
}