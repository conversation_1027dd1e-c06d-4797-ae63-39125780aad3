package com.aiinterview.simulator.integration

import com.aiinterview.simulator.data.handler.TechnicalIssueHandler
import com.aiinterview.simulator.data.manager.InterviewSessionManager
import com.aiinterview.simulator.data.model.*
import com.aiinterview.simulator.data.repository.InterviewRepository
import com.aiinterview.simulator.data.service.AIQuestionGenerationService
import com.aiinterview.simulator.data.service.InterviewTimerService
import com.aiinterview.simulator.data.service.TextToSpeechService
import com.aiinterview.simulator.data.speech.SpeechRecognitionService
import com.aiinterview.simulator.data.audio.OfflineRecordingManager
import com.aiinterview.simulator.presentation.viewmodel.InterviewFlowManagementViewModel
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.*
import org.junit.Assert.*
import java.io.File

/**
 * 面试流程管理集成测试
 * 测试完整的面试流程，包括会话管理、计时器、技术问题处理等
 */
@ExperimentalCoroutinesApi
class InterviewFlowManagementIntegrationTest {
    
    @Mock
    private lateinit var interviewRepository: InterviewRepository
    
    @Mock
    private lateinit var aiQuestionService: AIQuestionGenerationService
    
    @Mock
    private lateinit var ttsService: TextToSpeechService
    
    @Mock
    private lateinit var speechRecognitionService: SpeechRecognitionService
    
    @Mock
    private lateinit var offlineRecordingManager: OfflineRecordingManager
    
    @Mock
    private lateinit var mockAudioFile: File
    
    private lateinit var sessionManager: InterviewSessionManager
    private lateinit var timerService: InterviewTimerService
    private lateinit var technicalIssueHandler: TechnicalIssueHandler
    private lateinit var viewModel: InterviewFlowManagementViewModel
    
    private val testPosition = Position(
        id = "civil-servant-position",
        name = "公务员岗位",
        category = "行政管理",
        level = "科员",
        description = "负责行政管理工作",
        duration = 30, // 30分钟面试
        questionCount = 5, // 5道题目
        questionTypes = "[\"COMPREHENSIVE_ANALYSIS\",\"PLANNING_ORGANIZATION\",\"INTERPERSONAL_RELATIONS\"]",
        timeWarnings = "[5,1]" // 5分钟和1分钟提醒
    )
    
    private val testQuestions = listOf(
        Question(
            id = "q1",
            type = QuestionType.COMPREHENSIVE_ANALYSIS,
            category = "综合分析",
            title = "社会现象分析",
            content = "近年来，网络直播带货成为一种新兴的销售模式，既带来了商业机遇，也出现了一些问题。请你谈谈对网络直播带货现象的看法。",
            timeLimit = 300, // 5分钟
            difficulty = 3
        ),
        Question(
            id = "q2",
            type = QuestionType.PLANNING_ORGANIZATION,
            category = "计划组织",
            title = "活动组织",
            content = "你所在的单位准备组织一次关爱空巢老人的志愿服务活动，领导让你负责此次活动的策划和组织工作。你会如何开展？",
            timeLimit = 300,
            difficulty = 3
        ),
        Question(
            id = "q3",
            type = QuestionType.INTERPERSONAL_RELATIONS,
            category = "人际关系",
            title = "人际关系处理",
            content = "在工作中，你和同事小李对某个方案有不同意见，小李坚持自己的观点，不愿意采纳你的建议，这影响了项目进度。你会怎么处理？",
            timeLimit = 300,
            difficulty = 3
        )
    )
    
    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        
        // Setup mocks
        setupRepositoryMocks()
        setupServiceMocks()
        
        // Create components
        sessionManager = InterviewSessionManager(interviewRepository, aiQuestionService, ttsService)
        timerService = InterviewTimerService(ttsService)
        technicalIssueHandler = TechnicalIssueHandler(
            sessionManager, ttsService, speechRecognitionService, offlineRecordingManager
        )
        
        viewModel = InterviewFlowManagementViewModel(
            sessionManager, timerService, technicalIssueHandler
        )
    }
    
    private fun setupRepositoryMocks() {
        whenever(interviewRepository.saveInterviewSession(any())).thenReturn(Unit)
        whenever(interviewRepository.getInterviewSession(any())).thenReturn(null)
    }
    
    private fun setupServiceMocks() {
        // AI Question Generation Service
        testQuestions.forEachIndexed { index, question ->
            val response = com.aiinterview.simulator.data.dto.response.QuestionGenerationResponse(
                success = true,
                question = com.aiinterview.simulator.data.dto.response.GeneratedQuestion(
                    id = question.id,
                    type = question.type.name,
                    category = question.category,
                    title = question.title,
                    content = question.content,
                    timeLimit = question.timeLimit,
                    difficulty = question.difficulty
                ),
                provider = "test"
            )
            whenever(aiQuestionService.generateQuestion(any(), eq(question.type), any(), any()))
                .thenReturn(response)
        }
        
        // TTS Service
        val ttsResponse = com.aiinterview.simulator.data.dto.response.TTSResponse(
            success = true,
            audioUrl = "test-audio-url",
            duration = 5000,
            provider = "test"
        )
        whenever(ttsService.synthesizeText(any(), any(), any())).thenReturn(ttsResponse)
        whenever(ttsService.playAudio(any())).thenReturn(true)
        whenever(ttsService.stopAudio()).thenReturn(Unit)
        whenever(ttsService.cleanupTempAudioFiles()).thenReturn(Unit)
        
        // Speech Recognition Service
        val recognitionResult = com.aiinterview.simulator.data.speech.SpeechRecognitionResult(
            success = true,
            text = "这是识别出的文本内容",
            confidence = 0.95f,
            provider = SpeechRecognitionService.Provider.BAIDU,
            duration = 30000L
        )
        whenever(speechRecognitionService.recognizeAudio(any(), any())).thenReturn(recognitionResult)
        
        // Mock file
        whenever(mockAudioFile.absolutePath).thenReturn("/test/audio/path.wav")
        whenever(mockAudioFile.exists()).thenReturn(true)
        whenever(mockAudioFile.delete()).thenReturn(true)
    }
    
    @Test
    fun `complete interview flow should work end to end`() = runTest {
        val userId = "test-user-123"
        
        // 1. 创建面试会话
        viewModel.createInterviewSession(userId, testPosition, useAIGeneration = true)
        
        // 验证会话创建成功
        delay(100) // 等待异步操作完成
        val activeSession = sessionManager.activeSession.first()
        assertNotNull("面试会话应该创建成功", activeSession)
        assertEquals("用户ID应该匹配", userId, activeSession?.userId)
        assertEquals("岗位ID应该匹配", testPosition.id, activeSession?.positionId)
        assertEquals("会话状态应该是CREATED", InterviewStatus.CREATED, activeSession?.status)
        
        // 2. 开始面试
        viewModel.startInterviewSession(activeSession!!.id)
        
        delay(100)
        val startedSession = sessionManager.activeSession.first()
        assertEquals("会话状态应该是STARTED", InterviewStatus.STARTED, startedSession?.status)
        
        // 验证计时器启动
        val timerState = timerService.timerState.first()
        assertEquals("计时器应该运行", InterviewTimerService.TimerState.RUNNING, timerState)
        
        // 3. 进入第一个问题
        viewModel.proceedToNextQuestion()
        
        delay(100)
        val progressAfterFirstQuestion = sessionManager.sessionProgress.first()
        assertEquals("当前问题索引应该是0", 0, progressAfterFirstQuestion.currentQuestionIndex)
        assertTrue("总问题数应该大于0", progressAfterFirstQuestion.totalQuestions > 0)
        
        // 4. 模拟回答第一个问题
        val firstQuestionId = startedSession.questions[0].id
        val transcription1 = "这是对第一个问题的回答内容，我认为网络直播带货是一个新兴的商业模式..."
        
        viewModel.submitAnswer(firstQuestionId, mockAudioFile, transcription1, 120)
        
        delay(100)
        val progressAfterFirstAnswer = sessionManager.sessionProgress.first()
        assertEquals("完成问题数应该是1", 1, progressAfterFirstAnswer.completedQuestions)
        assertEquals("当前问题索引应该是1", 1, progressAfterFirstAnswer.currentQuestionIndex)
        
        // 5. 进入第二个问题
        viewModel.proceedToNextQuestion()
        
        delay(100)
        val progressAfterSecondQuestion = sessionManager.sessionProgress.first()
        assertEquals("当前问题索引应该是1", 1, progressAfterSecondQuestion.currentQuestionIndex)
        
        // 6. 模拟技术问题 - 录音失败
        val secondQuestionId = startedSession.questions[1].id
        viewModel.handleRecordingFailure(secondQuestionId, "麦克风权限被拒绝")
        
        delay(100)
        val currentIssue = technicalIssueHandler.currentIssue.first()
        assertNotNull("应该有技术问题记录", currentIssue)
        assertEquals("问题类型应该是录音失败", 
            TechnicalIssueHandler.IssueType.RECORDING_PERMISSION_DENIED, currentIssue?.type)
        
        // 7. 解决技术问题 - 授予权限
        viewModel.executeResolution("grant_permission")
        
        delay(100)
        // 验证问题解决后可以继续
        
        // 8. 重新回答第二个问题
        val transcription2 = "这是对第二个问题的回答，关于组织关爱空巢老人的活动..."
        viewModel.submitAnswer(secondQuestionId, mockAudioFile, transcription2, 180)
        
        delay(100)
        val progressAfterSecondAnswer = sessionManager.sessionProgress.first()
        assertEquals("完成问题数应该是2", 2, progressAfterSecondAnswer.completedQuestions)
        
        // 9. 模拟时间警告
        timerService.startInterviewTimer(30) // 30分钟面试
        // 模拟时间流逝到剩余5分钟
        // 这里需要手动触发时间检查，因为在测试中Timer不会自动运行
        
        // 10. 暂停和恢复面试
        viewModel.pauseInterview()
        delay(100)
        val pausedTimerState = timerService.timerState.first()
        assertEquals("计时器应该暂停", InterviewTimerService.TimerState.PAUSED, pausedTimerState)
        
        viewModel.resumeInterview()
        delay(100)
        val resumedTimerState = timerService.timerState.first()
        assertEquals("计时器应该恢复", InterviewTimerService.TimerState.RUNNING, resumedTimerState)
        
        // 11. 完成剩余问题（快速完成）
        for (i in 2 until testQuestions.size) {
            viewModel.proceedToNextQuestion()
            delay(50)
            
            val questionId = startedSession.questions[i].id
            val transcription = "这是对第${i + 1}个问题的简短回答"
            viewModel.submitAnswer(questionId, mockAudioFile, transcription, 90)
            delay(50)
        }
        
        // 12. 完成面试
        viewModel.completeInterview()
        
        delay(100)
        val completedSession = sessionManager.activeSession.first()
        assertEquals("会话状态应该是COMPLETED", InterviewStatus.COMPLETED, completedSession?.status)
        assertNotNull("结束时间应该设置", completedSession?.endTime)
        
        val finalTimerState = timerService.timerState.first()
        assertEquals("计时器应该停止", InterviewTimerService.TimerState.STOPPED, finalTimerState)
        
        // 13. 验证最终统计信息
        val statistics = viewModel.getInterviewStatistics()
        assertEquals("会话ID应该匹配", completedSession?.id, statistics.sessionId)
        assertEquals("总问题数应该匹配", testQuestions.size, statistics.totalQuestions)
        assertEquals("完成问题数应该匹配", testQuestions.size, statistics.completedQuestions)
        assertEquals("完成率应该是100%", 1.0f, statistics.completionRate, 0.01f)
        assertTrue("总用时应该大于0", statistics.totalTimeSpent > 0)
        assertTrue("平均用时应该大于0", statistics.averageTimePerQuestion > 0)
        
        // 验证所有必要的服务调用
        verify(interviewRepository, atLeast(1)).saveInterviewSession(any())
        verify(aiQuestionService, atLeast(testQuestions.size)).generateQuestion(any(), any(), any(), any())
        verify(ttsService, atLeast(1)).stopAudio()
    }
    
    @Test
    fun `should handle network issues during interview`() = runTest {
        val userId = "test-user-network"
        
        // 创建并开始面试
        viewModel.createInterviewSession(userId, testPosition, useAIGeneration = true)
        delay(100)
        
        val activeSession = sessionManager.activeSession.first()
        viewModel.startInterviewSession(activeSession!!.id)
        delay(100)
        
        // 进入第一个问题
        viewModel.proceedToNextQuestion()
        delay(100)
        
        // 模拟网络问题
        val questionId = activeSession.questions[0].id
        viewModel.handleNetworkIssue("网络连接超时", questionId)
        
        delay(100)
        val networkIssue = technicalIssueHandler.currentIssue.first()
        assertNotNull("应该记录网络问题", networkIssue)
        assertEquals("问题类型应该是网络连接丢失", 
            TechnicalIssueHandler.IssueType.NETWORK_CONNECTION_LOST, networkIssue?.type)
        
        // 执行离线模式解决方案
        viewModel.executeResolution("offline_mode")
        delay(100)
        
        // 验证可以继续面试（离线模式）
        val transcription = "这是离线模式下的回答"
        viewModel.submitAnswer(questionId, mockAudioFile, transcription, 150)
        
        delay(100)
        val progress = sessionManager.sessionProgress.first()
        assertEquals("应该成功提交回答", 1, progress.completedQuestions)
    }
    
    @Test
    fun `should handle speech recognition failures with fallback options`() = runTest {
        val userId = "test-user-speech"
        
        // 创建并开始面试
        viewModel.createInterviewSession(userId, testPosition, useAIGeneration = true)
        delay(100)
        
        val activeSession = sessionManager.activeSession.first()
        viewModel.startInterviewSession(activeSession!!.id)
        viewModel.proceedToNextQuestion()
        delay(100)
        
        // 模拟语音识别失败
        val questionId = activeSession.questions[0].id
        viewModel.handleSpeechRecognitionFailure(
            questionId, mockAudioFile, "BAIDU", "识别服务暂时不可用"
        )
        
        delay(100)
        val speechIssue = technicalIssueHandler.currentIssue.first()
        assertNotNull("应该记录语音识别问题", speechIssue)
        assertEquals("问题类型应该是语音识别失败", 
            TechnicalIssueHandler.IssueType.SPEECH_RECOGNITION_FAILED, speechIssue?.type)
        
        // 尝试切换服务商
        viewModel.executeResolution("switch_provider")
        delay(100)
        
        // 如果仍然失败，使用手动编辑
        viewModel.executeResolution("manual_edit")
        delay(100)
        
        // 手动输入回答
        val manualTranscription = "这是手动输入的回答内容"
        viewModel.submitAnswer(questionId, mockAudioFile, manualTranscription, 0)
        
        delay(100)
        val progress = sessionManager.sessionProgress.first()
        assertEquals("应该成功提交手动回答", 1, progress.completedQuestions)
    }
    
    @Test
    fun `should handle interview cancellation properly`() = runTest {
        val userId = "test-user-cancel"
        
        // 创建并开始面试
        viewModel.createInterviewSession(userId, testPosition, useAIGeneration = true)
        delay(100)
        
        val activeSession = sessionManager.activeSession.first()
        viewModel.startInterviewSession(activeSession!!.id)
        viewModel.proceedToNextQuestion()
        delay(100)
        
        // 回答一个问题
        val questionId = activeSession.questions[0].id
        viewModel.submitAnswer(questionId, mockAudioFile, "部分回答", 60)
        delay(100)
        
        // 取消面试
        viewModel.cancelInterview()
        delay(100)
        
        val cancelledSession = sessionManager.activeSession.first()
        assertEquals("会话状态应该是CANCELLED", InterviewStatus.CANCELLED, cancelledSession?.status)
        
        val timerState = timerService.timerState.first()
        assertEquals("计时器应该停止", InterviewTimerService.TimerState.STOPPED, timerState)
        
        // 验证资源清理
        verify(ttsService).stopAudio()
        verify(ttsService).cleanupTempAudioFiles()
    }
    
    @Test
    fun `should track time warnings correctly`() = runTest {
        val userId = "test-user-time"
        
        // 创建面试会话
        viewModel.createInterviewSession(userId, testPosition, useAIGeneration = true)
        delay(100)
        
        val activeSession = sessionManager.activeSession.first()
        viewModel.startInterviewSession(activeSession!!.id)
        
        // 启动计时器（短时间用于测试）
        timerService.startInterviewTimer(1) // 1分钟面试用于快速测试
        
        // 模拟时间流逝和警告触发
        // 在实际测试中，这需要更复杂的时间模拟
        
        // 验证时间格式化功能
        val formattedTotal = viewModel.getFormattedTotalTime()
        assertNotNull("总时间格式化应该有结果", formattedTotal)
        assertTrue("时间格式应该正确", formattedTotal.matches(Regex("\\d{2}:\\d{2}")))
        
        val formattedQuestion = viewModel.getFormattedQuestionTime()
        assertNotNull("问题时间格式化应该有结果", formattedQuestion)
        
        val progress = viewModel.getInterviewProgress()
        assertTrue("进度应该在0-1之间", progress >= 0f && progress <= 1f)
    }
}