package com.aiinterview.simulator.data.dto.request

/**
 * TTS（文本转语音）请求DTO
 */
data class TTSRequest(
    val text: String,
    val voice: String = "default",
    val speed: Int = 5, // 语速 1-9
    val pitch: Int = 5, // 音调 1-9
    val volume: Int = 5, // 音量 1-9
    val format: String = "mp3", // 音频格式
    val sampleRate: Int = 16000 // 采样率
)

/**
 * 百度TTS请求格式
 */
data class BaiduTTSRequest(
    val tex: String, // 合成的文本
    val tok: String, // 开放平台获取到的开发者access_token
    val cuid: String, // 用户唯一标识
    val ctp: Int = 1, // 客户端类型选择，web端填写固定值1
    val lan: String = "zh", // 固定值zh。语言选择,目前只有中英文混合模式，填写固定值zh
    val spd: Int = 5, // 语速，取值0-15，默认为5中语速
    val pit: Int = 5, // 音调，取值0-15，默认为5中语调
    val vol: Int = 5, // 音量，取值0-15，默认为5中音量
    val per: Int = 0, // 发音人选择, 0为女声，1为男声，3为情感合成-度逍遥，4为情感合成-度丫丫
    val aue: Int = 3 // 3为mp3格式(默认)； 4为pcm-16k；5为pcm-8k；6为wav
)

/**
 * 腾讯云TTS请求格式
 */
data class TencentTTSRequest(
    val Action: String = "TextToVoice",
    val Version: String = "2019-08-23",
    val Region: String = "ap-beijing",
    val Text: String,
    val SessionId: String,
    val VoiceType: Int = 0, // 音色 0：亲和女声，1：亲和男声，2：成熟男声，4：温暖女声，5：情感男声，6：情感女声
    val Speed: Double = 0.0, // 语速，范围：[-2，6]，分别对应不同语速
    val Volume: Double = 0.0, // 音量大小，范围：[0，10]，分别对应11个等级的音量
    val SampleRate: Int = 16000, // 采样率，16000 或 8000
    val Codec: String = "mp3" // 返回音频格式，可取值：wav（默认），mp3，pcm
)

/**
 * 讯飞TTS请求格式
 */
data class iFlyTekTTSRequest(
    val common: iFlyTekCommon,
    val business: iFlyTekBusiness,
    val data: iFlyTekData
)

data class iFlyTekCommon(
    val app_id: String
)

data class iFlyTekBusiness(
    val aue: String = "mp3", // 音频编码，可选值：raw（未压缩的pcm或wav格式），mp3，speex-wb
    val auf: String = "audio/L16;rate=16000", // 音频采样率，可选值：audio/L16;rate=8000，audio/L16;rate=16000
    val vcn: String = "xiaoyan", // 发音人，可选值：xiaoyan（亲和女声），aisjiuxu（亲和男声），aisxping（亲和女声）
    val speed: Int = 50, // 语速，可选值：[0-100]，默认为50
    val volume: Int = 50, // 音量，可选值：[0-100]，默认为50
    val pitch: Int = 50, // 音调，可选值：[0-100]，默认为50
    val bgs: Int = 0, // 合成音频的背景音，0:无背景音（默认值），1:有背景音
    val tte: String = "UTF8" // 文本编码格式
)

data class iFlyTekData(
    val status: Int = 2, // 文本状态，固定为2
    val text: String // 待合成的文本，需要进行base64编码
)