package com.aiinterview.simulator.di

import android.content.Context
import androidx.room.Room
import com.aiinterview.simulator.data.database.AIInterviewDatabase
import com.aiinterview.simulator.data.dao.UserDao
import com.aiinterview.simulator.data.dao.PositionDao
import com.aiinterview.simulator.data.dao.InterviewSessionDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideAIInterviewDatabase(@ApplicationContext context: Context): AIInterviewDatabase {
        return Room.databaseBuilder(
            context,
            AIInterviewDatabase::class.java,
            AIInterviewDatabase.DATABASE_NAME
        ).build()
    }
    
    @Provides
    fun provideUserDao(database: AIInterviewDatabase): UserDao {
        return database.userDao()
    }
    
    @Provides
    fun providePositionDao(database: AIInterviewDatabase): PositionDao {
        return database.positionDao()
    }
    
    @Provides
    fun provideInterviewSessionDao(database: AIInterviewDatabase): InterviewSessionDao {
        return database.interviewSessionDao()
    }
    
    @Provides
    fun provideEvaluationRecordDao(database: AIInterviewDatabase): com.aiinterview.simulator.data.dao.EvaluationRecordDao {
        return database.evaluationRecordDao()
    }
}