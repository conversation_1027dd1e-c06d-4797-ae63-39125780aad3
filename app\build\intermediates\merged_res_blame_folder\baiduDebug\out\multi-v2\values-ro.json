{"logs": [{"outputFile": "com.aiinterview.simulator.app-mergeBaiduDebugResources-55:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fd044ab7a523edb1345beec00f65097\\transformed\\core-1.12.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "589,687,789,889,988,1090,1199,6490", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "682,784,884,983,1085,1194,1311,6586"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d78fa9dcb2040782f62e575b1696aadd\\transformed\\ui-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,383,485,573,651,738,829,911,983,1052,1140,1230,1303,1380,1447", "endColumns": "96,83,96,101,87,77,86,90,81,71,68,87,89,72,76,66,114", "endOffsets": "197,281,378,480,568,646,733,824,906,978,1047,1135,1225,1298,1375,1442,1557"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1316,1413,5161,5258,5432,5594,5672,5759,5850,5932,6004,6073,6161,6333,6688,6765,6832", "endColumns": "96,83,96,101,87,77,86,90,81,71,68,87,89,72,76,66,114", "endOffsets": "1408,1492,5253,5355,5515,5667,5754,5845,5927,5999,6068,6156,6246,6401,6760,6827,6942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\73f51c4b00c3d8b5bd0709c291d17c64\\transformed\\material3-1.1.2\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,181,304,417,539,616,709,820,953,1069,1208,1288,1383,1474,1568,1684,1807,1907,2040,2171,2308,2480,2613,2728,2848,2971,3063,3155,3278,3415,3511,3612,3719,3854,3996,4104,4203,4275,4349,4431,4515,4612,4690,4769,4864,4964,5055,5155,5238,5345,5441,5550,5671,5749,5860", "endColumns": "125,122,112,121,76,92,110,132,115,138,79,94,90,93,115,122,99,132,130,136,171,132,114,119,122,91,91,122,136,95,100,106,134,141,107,98,71,73,81,83,96,77,78,94,99,90,99,82,106,95,108,120,77,110,99", "endOffsets": "176,299,412,534,611,704,815,948,1064,1203,1283,1378,1469,1563,1679,1802,1902,2035,2166,2303,2475,2608,2723,2843,2966,3058,3150,3273,3410,3506,3607,3714,3849,3991,4099,4198,4270,4344,4426,4510,4607,4685,4764,4859,4959,5050,5150,5233,5340,5436,5545,5666,5744,5855,5955"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,231,354,467,1497,1574,1667,1778,1911,2027,2166,2246,2341,2432,2526,2642,2765,2865,2998,3129,3266,3438,3571,3686,3806,3929,4021,4113,4236,4373,4469,4570,4677,4812,4954,5062,5360,5520,6251,6406,6591,6947,7025,7104,7199,7299,7390,7490,7573,7680,7776,7885,8006,8084,8195", "endColumns": "125,122,112,121,76,92,110,132,115,138,79,94,90,93,115,122,99,132,130,136,171,132,114,119,122,91,91,122,136,95,100,106,134,141,107,98,71,73,81,83,96,77,78,94,99,90,99,82,106,95,108,120,77,110,99", "endOffsets": "226,349,462,584,1569,1662,1773,1906,2022,2161,2241,2336,2427,2521,2637,2760,2860,2993,3124,3261,3433,3566,3681,3801,3924,4016,4108,4231,4368,4464,4565,4672,4807,4949,5057,5156,5427,5589,6328,6485,6683,7020,7099,7194,7294,7385,7485,7568,7675,7771,7880,8001,8079,8190,8290"}}]}]}