package com.aiinterview.simulator.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.aiinterview.simulator.data.repository.InterviewRepository
import com.aiinterview.simulator.data.model.Position
import com.aiinterview.simulator.data.model.InterviewSession
import com.aiinterview.simulator.data.dto.response.InterviewSessionResponse
import com.aiinterview.simulator.data.dto.response.QuestionResponse
import com.aiinterview.simulator.data.dto.response.InterviewResultResponse
import com.aiinterview.simulator.domain.util.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class InterviewViewModel @Inject constructor(
    private val interviewRepository: InterviewRepository
) : ViewModel() {
    
    private val _positionsState = MutableStateFlow<Resource<List<Position>>?>(null)
    val positionsState: StateFlow<Resource<List<Position>>?> = _positionsState.asStateFlow()
    
    private val _selectedPosition = MutableStateFlow<Position?>(null)
    val selectedPosition: StateFlow<Position?> = _selectedPosition.asStateFlow()
    
    private val _interviewSessionState = MutableStateFlow<Resource<InterviewSessionResponse>?>(null)
    val interviewSessionState: StateFlow<Resource<InterviewSessionResponse>?> = _interviewSessionState.asStateFlow()
    
    private val _questionResponseState = MutableStateFlow<Resource<QuestionResponse>?>(null)
    val questionResponseState: StateFlow<Resource<QuestionResponse>?> = _questionResponseState.asStateFlow()
    
    private val _interviewResultState = MutableStateFlow<Resource<InterviewResultResponse>?>(null)
    val interviewResultState: StateFlow<Resource<InterviewResultResponse>?> = _interviewResultState.asStateFlow()
    
    private val _userInterviewSessions = MutableStateFlow<List<InterviewSession>>(emptyList())
    val userInterviewSessions: StateFlow<List<InterviewSession>> = _userInterviewSessions.asStateFlow()
    
    fun loadPositions() {
        viewModelScope.launch {
            interviewRepository.getPositions().collect { result ->
                _positionsState.value = result
            }
        }
    }
    
    fun selectPosition(position: Position) {
        _selectedPosition.value = position
    }
    
    fun loadPositionById(positionId: String) {
        viewModelScope.launch {
            interviewRepository.getPositionById(positionId).collect { result ->
                if (result is Resource.Success) {
                    _selectedPosition.value = result.data
                }
            }
        }
    }
    
    fun startInterview(userId: String, positionId: String) {
        viewModelScope.launch {
            interviewRepository.startInterview(userId, positionId).collect { result ->
                _interviewSessionState.value = result
            }
        }
    }
    
    fun submitAnswer(
        sessionId: String,
        questionId: String,
        audioUrl: String,
        transcription: String,
        duration: Int
    ) {
        viewModelScope.launch {
            interviewRepository.submitAnswer(sessionId, questionId, audioUrl, transcription, duration)
                .collect { result ->
                    _questionResponseState.value = result
                }
        }
    }
    
    fun getInterviewResult(sessionId: String) {
        viewModelScope.launch {
            interviewRepository.getInterviewResult(sessionId).collect { result ->
                _interviewResultState.value = result
            }
        }
    }
    
    fun loadUserInterviewSessions(userId: String) {
        viewModelScope.launch {
            interviewRepository.getUserInterviewSessions(userId).collect { sessions ->
                _userInterviewSessions.value = sessions
            }
        }
    }
    
    fun clearInterviewSession() {
        _interviewSessionState.value = null
    }
    
    fun clearQuestionResponse() {
        _questionResponseState.value = null
    }
    
    fun clearInterviewResult() {
        _interviewResultState.value = null
    }
}