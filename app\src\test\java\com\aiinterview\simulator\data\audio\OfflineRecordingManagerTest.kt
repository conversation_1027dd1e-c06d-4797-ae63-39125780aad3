package com.aiinterview.simulator.data.audio

import android.content.Context
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.whenever
import org.junit.Assert.*
import java.io.File

class OfflineRecordingManagerTest {
    
    @Mock
    private lateinit var context: Context
    
    @Mock
    private lateinit var filesDir: File
    
    @Mock
    private lateinit var audioFile: File
    
    private lateinit var offlineRecordingManager: OfflineRecordingManager
    
    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        whenever(context.filesDir).thenReturn(filesDir)
        whenever(filesDir.absolutePath).thenReturn("/mock/files")
        offlineRecordingManager = OfflineRecordingManager(context)
    }
    
    @Test
    fun `initial state should have empty pending uploads`() = runTest {
        // When
        val pendingUploads = offlineRecordingManager.pendingUploads.first()
        
        // Then
        assertTrue(pendingUploads.isEmpty())
    }
    
    @Test
    fun `getPendingRecordingsCount should return zero initially`() {
        // When
        val count = offlineRecordingManager.getPendingRecordingsCount()
        
        // Then
        assertEquals(0, count)
    }
    
    @Test
    fun `getFailedRecordingsCount should return zero initially`() {
        // When
        val count = offlineRecordingManager.getFailedRecordingsCount()
        
        // Then
        assertEquals(0, count)
    }
    
    @Test
    fun `getTotalPendingSize should return zero initially`() {
        // When
        val size = offlineRecordingManager.getTotalPendingSize()
        
        // Then
        assertEquals(0L, size)
    }
    
    @Test
    fun `saveOfflineRecording should generate unique recording ID`() = runTest {
        // Given
        val questionId = "test_question"
        val transcription = "测试转录文本"
        whenever(audioFile.copyTo(org.mockito.kotlin.any<File>(), org.mockito.kotlin.any())).thenReturn(audioFile)
        
        // When
        val recordingId1 = offlineRecordingManager.saveOfflineRecording(questionId, audioFile, transcription)
        val recordingId2 = offlineRecordingManager.saveOfflineRecording(questionId, audioFile, transcription)
        
        // Then
        assertNotEquals(recordingId1, recordingId2)
        assertTrue(recordingId1.startsWith("offline_"))
        assertTrue(recordingId2.startsWith("offline_"))
    }
}