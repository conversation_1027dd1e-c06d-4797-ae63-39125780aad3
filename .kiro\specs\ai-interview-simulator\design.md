# 设计文档

## 概述

AI线上面试模拟APP是一个基于Android平台的移动应用，采用前后端分离架构。前端使用Kotlin + Jetpack Compose构建现代化的用户界面，后端使用Spring Boot提供RESTful API服务。系统集成了语音识别、AI对话和自然语言处理技术，为用户提供真实的面试模拟体验。

## 架构设计

### 整体架构

```mermaid
graph TB
    A[Android客户端] --> B[API网关]
    B --> C[用户服务]
    B --> D[面试服务]
    B --> E[评价服务]
    B --> F[题库服务]
    
    C --> G[用户数据库]
    D --> H[面试记录数据库]
    E --> I[AI评价引擎]
    F --> J[题库数据库]
    
    D --> K[语音识别服务]
    D --> L[语音合成服务]
    E --> M[大语言模型API]
```

### 技术栈选择

**前端（Android）**
- 开发语言：Kotlin
- UI框架：Jetpack Compose
- 架构模式：MVVM + Repository
- 依赖注入：Hilt
- 网络请求：Retrofit + OkHttp
- 本地存储：Room Database
- 语音处理：MediaRecorder + ExoPlayer

**后端服务**
- 开发框架：Spring Boot 3.x
- 数据库：MySQL 8.0
- 缓存：Redis
- 消息队列：RabbitMQ
- 文件存储：阿里云OSS
- 语音识别：百度智能云ASR / 腾讯云ASR / 讯飞语音识别
- 语音合成：百度智能云TTS / 腾讯云TTS / 讯飞语音合成
- AI对话：文心一言API / 通义千问API / 腾讯混元API / 讯飞星火API

## 组件和接口设计

### 前端组件架构

```mermaid
graph TD
    A[MainActivity] --> B[AuthFragment]
    A --> C[HomeFragment]
    A --> D[InterviewFragment]
    A --> E[HistoryFragment]
    A --> F[ProfileFragment]
    
    C --> G[PositionSelectionScreen]
    D --> H[InterviewScreen]
    D --> I[VoiceRecordingScreen]
    D --> J[EvaluationScreen]
    
    K[ViewModels] --> L[AuthViewModel]
    K --> M[InterviewViewModel]
    K --> N[HistoryViewModel]
    
    O[Repositories] --> P[UserRepository]
    O --> Q[InterviewRepository]
    O --> R[QuestionRepository]
```

### 核心接口设计

**用户认证接口**
```kotlin
interface AuthApi {
    @POST("/api/auth/register")
    suspend fun register(@Body request: RegisterRequest): ApiResponse<AuthResponse>
    
    @POST("/api/auth/login")
    suspend fun login(@Body request: LoginRequest): ApiResponse<AuthResponse>
    
    @POST("/api/auth/refresh")
    suspend fun refreshToken(@Body request: RefreshTokenRequest): ApiResponse<AuthResponse>
}
```

**面试服务接口**
```kotlin
interface InterviewApi {
    @GET("/api/positions")
    suspend fun getPositions(): ApiResponse<List<Position>>
    
    @POST("/api/interview/start")
    suspend fun startInterview(@Body request: StartInterviewRequest): ApiResponse<InterviewSession>
    
    @POST("/api/interview/answer")
    suspend fun submitAnswer(@Body request: AnswerRequest): ApiResponse<QuestionResponse>
    
    @GET("/api/interview/{sessionId}/result")
    suspend fun getInterviewResult(@Path("sessionId") sessionId: String): ApiResponse<InterviewResult>
}
```

**语音处理接口**
```kotlin
interface VoiceApi {
    @Multipart
    @POST("/api/voice/recognize")
    suspend fun recognizeVoice(@Part audio: MultipartBody.Part): ApiResponse<VoiceRecognitionResult>
    
    @POST("/api/voice/synthesize")
    suspend fun synthesizeVoice(@Body request: VoiceSynthesisRequest): ApiResponse<VoiceSynthesisResult>
}
```

### 后端服务接口

**用户服务**
```java
@RestController
@RequestMapping("/api/auth")
public class AuthController {
    
    @PostMapping("/register")
    public ResponseEntity<AuthResponse> register(@RequestBody RegisterRequest request);
    
    @PostMapping("/login")
    public ResponseEntity<AuthResponse> login(@RequestBody LoginRequest request);
    
    @PostMapping("/refresh")
    public ResponseEntity<AuthResponse> refreshToken(@RequestBody RefreshTokenRequest request);
}
```

**面试服务**
```java
@RestController
@RequestMapping("/api/interview")
public class InterviewController {
    
    @PostMapping("/start")
    public ResponseEntity<InterviewSession> startInterview(@RequestBody StartInterviewRequest request);
    
    @PostMapping("/answer")
    public ResponseEntity<QuestionResponse> submitAnswer(@RequestBody AnswerRequest request);
    
    @GetMapping("/{sessionId}/result")
    public ResponseEntity<InterviewResult> getInterviewResult(@PathVariable String sessionId);
}
```

## 数据模型设计

### 用户相关模型

```kotlin
data class User(
    val id: String,
    val phoneNumber: String,
    val nickname: String?,
    val avatar: String?,
    val createdAt: Long,
    val updatedAt: Long
)

data class UserProfile(
    val userId: String,
    val realName: String?,
    val education: String?,
    val major: String?,
    val workExperience: Int?,
    val targetPosition: String?
)
```

### 面试相关模型

```kotlin
data class Position(
    val id: String,
    val name: String,
    val category: String,
    val level: String,
    val description: String,
    val requirements: List<String>,
    val interviewConfig: InterviewConfig
)

data class InterviewConfig(
    val duration: Int, // 面试总时长（分钟）
    val questionCount: Int, // 题目数量
    val questionTypes: List<QuestionType>, // 题目类型
    val timeWarnings: List<Int> // 时间提醒点（分钟）
)

data class Question(
    val id: String,
    val type: QuestionType,
    val category: String,
    val content: String,
    val backgroundInfo: String?,
    val keyPoints: List<String>,
    val timeLimit: Int,
    val difficulty: Int
)

enum class QuestionType {
    COMPREHENSIVE_ANALYSIS, // 综合分析
    PLANNING_ORGANIZATION, // 计划组织
    INTERPERSONAL_RELATIONS, // 人际关系
    EMERGENCY_RESPONSE, // 应急应变
    VERBAL_EXPRESSION // 言语表达
}
```

### 面试会话模型

```kotlin
data class InterviewSession(
    val id: String,
    val userId: String,
    val positionId: String,
    val status: InterviewStatus,
    val startTime: Long,
    val endTime: Long?,
    val currentQuestionIndex: Int,
    val questions: List<Question>,
    val answers: List<Answer>,
    val evaluation: InterviewEvaluation?
)

data class Answer(
    val questionId: String,
    val audioUrl: String,
    val transcription: String,
    val duration: Int,
    val submittedAt: Long
)

data class InterviewEvaluation(
    val sessionId: String,
    val overallScore: Double,
    val dimensions: Map<String, DimensionScore>,
    val feedback: String,
    val suggestions: List<String>,
    val createdAt: Long
)

data class DimensionScore(
    val score: Double,
    val maxScore: Double,
    val feedback: String
)

enum class InterviewStatus {
    CREATED, STARTED, IN_PROGRESS, COMPLETED, CANCELLED
}
```

## 错误处理设计

### 错误分类

```kotlin
sealed class AppError(val code: String, val message: String) {
    // 网络错误
    object NetworkError : AppError("NETWORK_ERROR", "网络连接失败")
    object TimeoutError : AppError("TIMEOUT_ERROR", "请求超时")
    
    // 认证错误
    object UnauthorizedError : AppError("UNAUTHORIZED", "用户未登录")
    object TokenExpiredError : AppError("TOKEN_EXPIRED", "登录已过期")
    
    // 业务错误
    object UserNotFoundError : AppError("USER_NOT_FOUND", "用户不存在")
    object InterviewNotFoundError : AppError("INTERVIEW_NOT_FOUND", "面试会话不存在")
    object VoiceRecognitionError : AppError("VOICE_RECOGNITION_ERROR", "语音识别失败")
    
    // 系统错误
    object ServerError : AppError("SERVER_ERROR", "服务器内部错误")
    object DatabaseError : AppError("DATABASE_ERROR", "数据库操作失败")
}
```

### 错误处理策略

```kotlin
class ErrorHandler {
    fun handleError(error: Throwable): AppError {
        return when (error) {
            is HttpException -> {
                when (error.code()) {
                    401 -> AppError.UnauthorizedError
                    404 -> AppError.UserNotFoundError
                    500 -> AppError.ServerError
                    else -> AppError.NetworkError
                }
            }
            is SocketTimeoutException -> AppError.TimeoutError
            is IOException -> AppError.NetworkError
            else -> AppError.ServerError
        }
    }
}
```

### 用户友好的错误提示

```kotlin
fun AppError.toUserMessage(): String {
    return when (this) {
        AppError.NetworkError -> "网络连接失败，请检查网络设置"
        AppError.TimeoutError -> "请求超时，请稍后重试"
        AppError.UnauthorizedError -> "请先登录"
        AppError.TokenExpiredError -> "登录已过期，请重新登录"
        AppError.VoiceRecognitionError -> "语音识别失败，请重新录制"
        else -> "操作失败，请稍后重试"
    }
}
```

## 测试策略

### 单元测试

**前端测试**
- ViewModel测试：使用JUnit + Mockito
- Repository测试：使用Room测试框架
- 工具类测试：使用JUnit

**后端测试**
- Service层测试：使用JUnit + Mockito
- Repository层测试：使用@DataJpaTest
- Controller层测试：使用@WebMvcTest

### 集成测试

**API集成测试**
```kotlin
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class InterviewControllerIntegrationTest {
    
    @Test
    fun `should start interview successfully`() {
        // 测试面试开始流程
    }
    
    @Test
    fun `should submit answer and get next question`() {
        // 测试答题流程
    }
}
```

**数据库集成测试**
```kotlin
@DataJpaTest
class InterviewRepositoryTest {
    
    @Test
    fun `should save and retrieve interview session`() {
        // 测试数据持久化
    }
}
```

### 端到端测试

**UI自动化测试**
```kotlin
@RunWith(AndroidJUnit4::class)
class InterviewFlowTest {
    
    @Test
    fun completeInterviewFlow() {
        // 1. 登录
        // 2. 选择岗位
        // 3. 开始面试
        // 4. 回答问题
        // 5. 查看评价
    }
}
```

### 性能测试

**语音处理性能**
- 语音识别响应时间 < 3秒
- 语音合成响应时间 < 2秒
- 音频文件上传时间 < 5秒

**AI评价性能**
- 单次评价生成时间 < 10秒
- 并发评价处理能力 > 100 QPS

### 兼容性测试

**Android版本兼容性**
- 最低支持：Android 7.0 (API 24)
- 目标版本：Android 14 (API 34)
- 测试设备：覆盖主流品牌和屏幕尺寸

**网络环境测试**
- 4G/5G网络环境
- WiFi网络环境
- 弱网络环境下的降级处理

## 安全设计

### 数据传输安全

**HTTPS通信**
- 所有API调用使用HTTPS协议
- 证书固定（Certificate Pinning）
- 请求签名验证

**API安全**
```kotlin
class ApiSecurityInterceptor : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request().newBuilder()
            .addHeader("Authorization", "Bearer ${getAccessToken()}")
            .addHeader("X-Request-ID", generateRequestId())
            .addHeader("X-Timestamp", System.currentTimeMillis().toString())
            .build()
        return chain.proceed(request)
    }
}
```

### 数据存储安全

**本地数据加密**
```kotlin
class SecurePreferences {
    private val masterKey = MasterKey.Builder(context)
        .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
        .build()
    
    private val encryptedPrefs = EncryptedSharedPreferences.create(
        context,
        "secure_prefs",
        masterKey,
        EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
        EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
    )
}
```

**音频文件安全**
- 录音文件本地加密存储
- 上传后服务端加密存储
- 定期清理临时文件

### 用户隐私保护

**权限管理**
```kotlin
class PermissionManager {
    fun requestMicrophonePermission(activity: Activity) {
        if (ContextCompat.checkSelfPermission(activity, Manifest.permission.RECORD_AUDIO) 
            != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(
                activity,
                arrayOf(Manifest.permission.RECORD_AUDIO),
                MICROPHONE_PERMISSION_REQUEST_CODE
            )
        }
    }
}
```

**数据脱敏**
- 日志中不记录敏感信息
- 错误报告中过滤个人数据
- 分析数据匿名化处理

## 性能优化设计

### 前端性能优化

**内存管理**
```kotlin
class AudioRecordManager {
    private var mediaRecorder: MediaRecorder? = null
    
    fun startRecording() {
        mediaRecorder = MediaRecorder().apply {
            setAudioSource(MediaRecorder.AudioSource.MIC)
            setOutputFormat(MediaRecorder.OutputFormat.AAC_ADTS)
            setAudioEncoder(MediaRecorder.AudioEncoder.AAC)
            setOutputFile(audioFile.absolutePath)
            prepare()
            start()
        }
    }
    
    fun stopRecording() {
        mediaRecorder?.apply {
            stop()
            release()
        }
        mediaRecorder = null
    }
}
```

**网络优化**
```kotlin
class NetworkOptimizer {
    // 请求缓存
    private val cacheInterceptor = CacheInterceptor()
    
    // 请求重试
    private val retryInterceptor = RetryInterceptor(maxRetries = 3)
    
    // 请求压缩
    private val gzipInterceptor = GzipRequestInterceptor()
}
```

### 后端性能优化

**数据库优化**
```sql
-- 面试会话表索引
CREATE INDEX idx_interview_session_user_id ON interview_sessions(user_id);
CREATE INDEX idx_interview_session_status ON interview_sessions(status);
CREATE INDEX idx_interview_session_created_at ON interview_sessions(created_at);

-- 题库表索引
CREATE INDEX idx_questions_type_category ON questions(type, category);
CREATE INDEX idx_questions_difficulty ON questions(difficulty);
```

**缓存策略**
```java
@Service
public class QuestionService {
    
    @Cacheable(value = "questions", key = "#positionId")
    public List<Question> getQuestionsByPosition(String positionId) {
        return questionRepository.findByPositionId(positionId);
    }
    
    @CacheEvict(value = "questions", allEntries = true)
    public void clearQuestionCache() {
        // 清除题库缓存
    }
}
```

### 第三方服务优化

**语音服务优化**
- 音频格式优化（AAC压缩）
- 分片上传大文件
- 并发请求限制

**AI服务优化**
- 请求批处理
- 结果缓存
- 降级策略

## 中国大陆地区适配

### 网络服务选择

**云服务提供商（推荐优先级）**
1. 阿里云：OSS存储、ECS服务器、RDS数据库
2. 腾讯云：COS存储、CVM服务器、TencentDB
3. 华为云：OBS存储、ECS服务器、RDS数据库

**语音识别服务**
```kotlin
// 多厂商语音识别适配
interface VoiceRecognitionService {
    suspend fun recognize(audioData: ByteArray): RecognitionResult
}

class BaiduASRService : VoiceRecognitionService {
    override suspend fun recognize(audioData: ByteArray): RecognitionResult {
        // 百度智能云ASR实现
    }
}

class TencentASRService : VoiceRecognitionService {
    override suspend fun recognize(audioData: ByteArray): RecognitionResult {
        // 腾讯云ASR实现
    }
}

class iFlyTekASRService : VoiceRecognitionService {
    override suspend fun recognize(audioData: ByteArray): RecognitionResult {
        // 讯飞语音识别实现
    }
}
```

**AI对话服务**
```kotlin
// 多厂商AI服务适配
interface AIConversationService {
    suspend fun generateEvaluation(answers: List<String>): EvaluationResult
}

class WenxinAIService : AIConversationService {
    override suspend fun generateEvaluation(answers: List<String>): EvaluationResult {
        // 文心一言API实现
    }
}

class TongyiAIService : AIConversationService {
    override suspend fun generateEvaluation(answers: List<String>): EvaluationResult {
        // 通义千问API实现
    }
}

class HunyuanAIService : AIConversationService {
    override suspend fun generateEvaluation(answers: List<String>): EvaluationResult {
        // 腾讯混元API实现
    }
}
```

### 合规性考虑

**数据安全合规**
- 遵循《网络安全法》
- 遵循《数据安全法》
- 遵循《个人信息保护法》
- 用户数据本地化存储

**内容审核**
```kotlin
class ContentModerationService {
    suspend fun moderateText(content: String): ModerationResult {
        // 使用阿里云/腾讯云内容安全服务
        // 检测违规内容、敏感信息等
    }
    
    suspend fun moderateAudio(audioUrl: String): ModerationResult {
        // 音频内容审核
    }
}
```

**用户隐私保护**
```kotlin
class PrivacyManager {
    fun showPrivacyPolicy() {
        // 显示隐私政策，符合中国法规要求
    }
    
    fun requestUserConsent() {
        // 请求用户授权，明确数据使用范围
    }
    
    fun handleDataDeletion(userId: String) {
        // 用户注销时的数据删除处理
    }
}
```

### 网络优化

**CDN加速**
- 使用阿里云CDN或腾讯云CDN
- 音频文件就近分发
- 静态资源缓存优化

**网络监控**
```kotlin
class NetworkMonitor {
    fun detectNetworkQuality(): NetworkQuality {
        // 检测网络质量，针对中国网络环境优化
        return when {
            isWifiConnected() -> NetworkQuality.EXCELLENT
            is4GConnected() -> NetworkQuality.GOOD
            is3GConnected() -> NetworkQuality.POOR
            else -> NetworkQuality.OFFLINE
        }
    }
}
```

### 应用商店适配

**应用分发渠道**
- 华为应用市场（AppGallery）
- 小米应用商店
- OPPO软件商店
- vivo应用商店
- 应用宝（腾讯）
- 360手机助手
- 百度手机助手

**权限申请优化**
```xml
<!-- 针对中国Android生态的权限配置 -->
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" 
    android:maxSdkVersion="28" />
```

这个设计文档现在完全适配了中国大陆地区的使用环境，包括合规的云服务选择、本土化的AI和语音服务、以及符合中国法规的数据安全要求。