{"logs": [{"outputFile": "com.aiinterview.simulator.app-mergeBaiduDebugResources-55:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fd044ab7a523edb1345beec00f65097\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,72,73,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,98,165,166,167,168,169,170,171,275,312,313,317,318,322,324,325,333,339,349,382,403,436", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,210,282,370,435,501,570,633,703,771,843,913,974,1048,1121,1182,1243,1305,1369,1431,1492,1560,1660,1720,1786,1859,1928,1985,2037,2099,2171,2247,4569,4604,4801,4856,4919,4974,5032,5090,5151,5214,5271,5322,5372,5433,5490,5556,5590,5625,5920,8438,8505,8577,8646,8715,8789,8861,15459,17600,17717,17918,18028,18229,18455,18527,18898,19101,19402,21133,21814,22496", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,72,73,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,98,165,166,167,168,169,170,171,275,312,316,317,321,322,324,325,338,348,381,402,435,441", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,277,365,430,496,565,628,698,766,838,908,969,1043,1116,1177,1238,1300,1364,1426,1487,1555,1655,1715,1781,1854,1923,1980,2032,2094,2166,2242,2307,4599,4634,4851,4914,4969,5027,5085,5146,5209,5266,5317,5367,5428,5485,5551,5585,5620,5655,5985,8500,8572,8641,8710,8784,8856,8944,15525,17712,17913,18023,18224,18353,18522,18589,19096,19397,21128,21809,22491,22658"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfd39d7f925e50e3e34236837813144\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "96", "startColumns": "4", "startOffsets": "5817", "endColumns": "49", "endOffsets": "5862"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\73f51c4b00c3d8b5bd0709c291d17c64\\transformed\\material3-1.1.2\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,221,298,378,426,487,566,668,750,866,916,981,1038,1103,1188,1279,1349,1442,1531,1625,1770,1857,1941,2033,2127,2187,2251,2334,2424,2487,2555,2623,2720,2825,2897,2962,3006,3052,3121,3174,3227,3295,3341,3391,3458,3525,3591,3656,3710,3782,3849,3919,4001,4047,4113", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "134,216,293,373,421,482,561,663,745,861,911,976,1033,1098,1183,1274,1344,1437,1526,1620,1765,1852,1936,2028,2122,2182,2246,2329,2419,2482,2550,2618,2715,2820,2892,2957,3001,3047,3116,3169,3222,3290,3336,3386,3453,3520,3586,3651,3705,3777,3844,3914,3996,4042,4108,4169"}, "to": {"startLines": "160,161,162,163,176,181,182,183,184,185,188,189,190,191,192,193,194,195,196,197,198,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,223,234,238,268,273,276,281,282,283,284,285,286,287,288,289,290,291,292,293,294", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8064,8148,8230,8307,9143,9420,9481,9560,9662,9744,9860,9910,9975,10032,10097,10182,10273,10343,10436,10525,10619,10764,10851,10935,11027,11121,11181,11245,11328,11418,11481,11549,11617,11714,11819,11891,12457,13091,13340,15076,15349,15530,15858,15904,15954,16021,16088,16154,16219,16273,16345,16412,16482,16564,16610,16676", "endLines": "160,161,162,163,176,181,182,183,184,187,188,189,190,191,192,193,194,195,196,197,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,223,234,238,268,273,276,281,282,283,284,285,286,287,288,289,290,291,292,293,294", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "8143,8225,8302,8382,9186,9476,9555,9657,9739,9855,9905,9970,10027,10092,10177,10268,10338,10431,10520,10614,10759,10846,10930,11022,11116,11176,11240,11323,11413,11476,11544,11612,11709,11814,11886,11951,12496,13132,13404,15124,15397,15593,15899,15949,16016,16083,16149,16214,16268,16340,16407,16477,16559,16605,16671,16732"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dbc7298f8ad072dc5569e2320bc12cad\\transformed\\navigation-common-2.7.5\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "442,455,461,467,476", "startColumns": "4,4,4,4,4", "startOffsets": "22663,23302,23546,23793,24156", "endLines": "454,460,466,469,480", "endColumns": "24,24,24,24,24", "endOffsets": "23297,23541,23788,23921,24333"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6d8cd924a9cf02f0063c8361a41a9ebf\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "76,94", "startColumns": "4,4", "startOffsets": "4759,5703", "endColumns": "41,59", "endOffsets": "4796,5758"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\88f400cf962815a13a8071ea1623f0c4\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "99", "startColumns": "4", "startOffsets": "5990", "endColumns": "82", "endOffsets": "6068"}}, {"source": "E:\\health-report-tracker\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "57", "endColumns": "97", "endOffsets": "150"}, "to": {"startLines": "323", "startColumns": "4", "startOffsets": "18358", "endColumns": "96", "endOffsets": "18450"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d78fa9dcb2040782f62e575b1696aadd\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,97,174,175,216,217,224,236,237,241,246,247,249,255,256,269,277,278,279,302,305,308", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2312,2371,2430,2490,2550,2610,2670,2730,2790,2850,2910,2970,3030,3089,3149,3209,3269,3329,3389,3449,3509,3569,3629,3689,3748,3808,3868,3927,3986,4045,4104,4163,4222,4296,4354,4409,4460,5867,9024,9089,11956,12022,12501,13228,13280,13519,13789,13843,13913,14337,14387,15129,15598,15645,15681,17182,17294,17405", "endLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,97,174,175,216,217,224,236,237,241,246,247,249,255,256,269,277,278,279,304,307,311", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "2366,2425,2485,2545,2605,2665,2725,2785,2845,2905,2965,3025,3084,3144,3204,3264,3324,3384,3444,3504,3564,3624,3684,3743,3803,3863,3922,3981,4040,4099,4158,4217,4291,4349,4404,4455,4510,5915,9084,9138,12017,12118,12554,13275,13335,13576,13838,13874,13942,14382,14436,15170,15640,15676,15766,17289,17400,17595"}}, {"source": "E:\\health-report-tracker\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "2,32,42,44,38,36,37,35,26,24,23,22,25,10,5,12,11,9,6,8,7,13,30,29,19,18,16,17,43,41,45,31", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "57,1471,1844,1918,1729,1603,1677,1548,1212,1076,1007,935,1145,421,128,554,498,354,186,293,240,615,1353,1301,855,799,696,750,1883,1806,1956,1413", "endColumns": "44,50,37,36,50,72,50,53,60,67,67,70,65,75,56,59,54,65,52,59,51,52,58,50,51,54,52,47,33,36,39,56", "endOffsets": "97,1517,1877,1950,1775,1671,1723,1597,1268,1139,1070,1001,1206,492,180,609,548,415,234,348,287,663,1407,1347,902,849,744,793,1912,1838,1991,1465"}, "to": {"startLines": "100,164,172,173,177,178,179,180,218,219,220,221,222,225,226,227,228,229,230,231,232,233,239,240,242,243,244,245,248,262,270,274", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6073,8387,8949,8987,9191,9242,9315,9366,12123,12184,12252,12320,12391,12559,12635,12692,12752,12807,12873,12926,12986,13038,13409,13468,13581,13633,13688,13741,13879,14699,15175,15402", "endColumns": "44,50,37,36,50,72,50,53,60,67,67,70,65,75,56,59,54,65,52,59,51,52,58,50,51,54,52,47,33,36,39,56", "endOffsets": "6113,8433,8982,9019,9237,9310,9361,9415,12179,12247,12315,12386,12452,12630,12687,12747,12802,12868,12921,12981,13033,13086,13463,13514,13628,13683,13736,13784,13908,14731,15210,15454"}}, {"source": "E:\\health-report-tracker\\app\\src\\main\\res\\values\\app_store_strings.xml", "from": {"startLines": "78,79,63,37,10,67,68,60,5,70,6,64,69,7,109,91,97,73,75,74,106,120,119,122,123,121,82,83,84,85,86,127,126,103,113,112,116,115,114,100,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2303,2357,1579,1005,321,1714,1773,1473,110,1935,162,1632,1858,233,3550,2860,3096,2054,2202,2128,3439,4018,3962,4135,4180,4092,2458,2527,2596,2667,2735,4304,4250,3323,3716,3658,3886,3839,3794,3212,2979", "endLines": "78,79,63,57,34,67,68,60,5,70,6,64,69,7,109,91,97,73,75,74,106,120,119,122,123,121,82,83,84,85,86,127,126,103,113,112,116,115,114,100,94", "endColumns": "52,72,51,13,13,57,83,79,50,92,69,54,75,61,81,90,85,72,74,72,82,72,54,43,43,41,67,67,69,66,66,80,52,86,76,56,47,45,43,85,86", "endOffsets": "2351,2425,1626,1443,974,1767,1852,1548,156,2023,227,1682,1929,290,3627,2946,3177,2122,2272,2196,3517,4086,4012,4174,4219,4129,2521,2590,2661,2729,2797,4380,4298,3405,3788,3710,3929,3880,3833,3293,3061"}, "to": {"startLines": "101,102,103,104,125,150,151,152,153,154,155,156,157,158,159,235,250,251,252,253,254,257,258,259,260,261,263,264,265,266,267,271,272,280,295,296,297,298,299,300,301", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6118,6171,6244,6296,6719,7353,7411,7495,7575,7626,7719,7789,7844,7920,7982,13137,13947,14033,14106,14181,14254,14441,14514,14569,14613,14657,14736,14804,14872,14942,15009,15215,15296,15771,16737,16814,16871,16919,16965,17009,17095", "endLines": "101,102,103,124,149,150,151,152,153,154,155,156,157,158,159,235,250,251,252,253,254,257,258,259,260,261,263,264,265,266,267,271,272,280,295,296,297,298,299,300,301", "endColumns": "52,72,51,13,13,57,83,79,50,92,69,54,75,61,81,90,85,72,74,72,82,72,54,43,43,41,67,67,69,66,66,80,52,86,76,56,47,45,43,85,86", "endOffsets": "6166,6239,6291,6714,7348,7406,7490,7570,7621,7714,7784,7839,7915,7977,8059,13223,14028,14101,14176,14249,14332,14509,14564,14608,14652,14694,14799,14867,14937,15004,15071,15291,15344,15853,16809,16866,16914,16960,17004,17090,17177"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b78c762ad8b933b1fdc5e729107e312e\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "93", "startColumns": "4", "startOffsets": "5660", "endColumns": "42", "endOffsets": "5698"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\279872a76e8eac0b8ce485635b4ca7e3\\transformed\\navigation-runtime-2.7.5\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "74,326,470,473", "startColumns": "4,4,4,4", "startOffsets": "4639,18594,23926,24041", "endLines": "74,332,472,475", "endColumns": "52,24,24,24", "endOffsets": "4687,18893,24036,24151"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\282c18d69cd69984610652f4a7d46511\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "71,75", "startColumns": "4,4", "startOffsets": "4515,4692", "endColumns": "53,66", "endOffsets": "4564,4754"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b9bf0c7298e94e20b9cad0d8517e71ef\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "95", "startColumns": "4", "startOffsets": "5763", "endColumns": "53", "endOffsets": "5812"}}]}]}