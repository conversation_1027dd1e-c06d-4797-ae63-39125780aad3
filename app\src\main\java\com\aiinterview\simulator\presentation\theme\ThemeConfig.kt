package com.aiinterview.simulator.presentation.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * 主题配置数据类
 * 定义应用的主题配置选项
 */
data class ThemeConfig(
    val isDarkTheme: Boolean = false,           // 是否为深色主题
    val useDynamicColors: Boolean = true,       // 是否使用动态颜色（Android 12+）
    val useHighContrast: Boolean = false,       // 是否使用高对比度模式
    val fontSize: FontSize = FontSize.MEDIUM,   // 字体大小设置
    val cornerRadius: CornerRadius = CornerRadius.MEDIUM // 圆角大小设置
)

/**
 * 字体大小枚举
 * 定义不同的字体大小选项
 */
enum class FontSize(val scale: Float, val displayName: String) {
    SMALL(0.85f, "小"),                         // 小字体 - 85%缩放
    MEDIUM(1.0f, "中"),                         // 中等字体 - 100%缩放（默认）
    LARGE(1.15f, "大"),                         // 大字体 - 115%缩放
    EXTRA_LARGE(1.3f, "超大")                   // 超大字体 - 130%缩放
}

/**
 * 圆角大小枚举
 * 定义不同的圆角大小选项
 */
enum class CornerRadius(val scale: Float, val displayName: String) {
    NONE(0f, "无圆角"),                         // 无圆角 - 0%缩放
    SMALL(0.5f, "小圆角"),                      // 小圆角 - 50%缩放
    MEDIUM(1.0f, "中等圆角"),                   // 中等圆角 - 100%缩放（默认）
    LARGE(1.5f, "大圆角"),                      // 大圆角 - 150%缩放
    EXTRA_LARGE(2.0f, "超大圆角")               // 超大圆角 - 200%缩放
}

/**
 * 屏幕尺寸类型枚举
 * 根据屏幕宽度定义不同的屏幕尺寸类型
 */
enum class ScreenSize {
    COMPACT,                                    // 紧凑屏幕 - 手机竖屏
    MEDIUM,                                     // 中等屏幕 - 手机横屏/小平板
    EXPANDED                                    // 扩展屏幕 - 大平板/桌面
}

/**
 * 窗口尺寸信息数据类
 * 包含当前窗口的尺寸信息
 */
data class WindowSizeInfo(
    val screenSize: ScreenSize,                 // 屏幕尺寸类型
    val screenWidth: Dp,                        // 屏幕宽度
    val screenHeight: Dp                        // 屏幕高度
)

/**
 * 主题配置的CompositionLocal
 * 用于在组件树中传递主题配置
 */
val LocalThemeConfig = staticCompositionLocalOf {
    ThemeConfig()                               // 提供默认的主题配置
}

/**
 * 窗口尺寸信息的CompositionLocal
 * 用于在组件树中传递窗口尺寸信息
 */
val LocalWindowSizeInfo = staticCompositionLocalOf {
    WindowSizeInfo(                             // 提供默认的窗口尺寸信息
        screenSize = ScreenSize.COMPACT,        // 默认为紧凑屏幕
        screenWidth = 360.dp,                   // 默认宽度
        screenHeight = 640.dp                   // 默认高度
    )
}

/**
 * 获取当前窗口尺寸信息的组合函数
 * 根据屏幕配置计算窗口尺寸信息
 */
@Composable
fun rememberWindowSizeInfo(): WindowSizeInfo {
    val configuration = LocalConfiguration.current  // 获取当前配置信息
    val screenWidth = configuration.screenWidthDp.dp // 获取屏幕宽度
    val screenHeight = configuration.screenHeightDp.dp // 获取屏幕高度
    
    // 根据屏幕宽度确定屏幕尺寸类型
    val screenSize = when {
        screenWidth < 600.dp -> ScreenSize.COMPACT      // 小于600dp为紧凑屏幕
        screenWidth < 840.dp -> ScreenSize.MEDIUM       // 600-840dp为中等屏幕
        else -> ScreenSize.EXPANDED                     // 大于840dp为扩展屏幕
    }
    
    return WindowSizeInfo(                      // 返回窗口尺寸信息
        screenSize = screenSize,                // 屏幕尺寸类型
        screenWidth = screenWidth,              // 屏幕宽度
        screenHeight = screenHeight             // 屏幕高度
    )
}

/**
 * 主题配置提供者组合函数
 * 为子组件提供主题配置和窗口尺寸信息
 */
@Composable
fun ThemeConfigProvider(
    config: ThemeConfig = ThemeConfig(          // 主题配置参数
        isDarkTheme = isSystemInDarkTheme()     // 默认跟随系统深色模式设置
    ),
    content: @Composable () -> Unit             // 子组件内容
) {
    val windowSizeInfo = rememberWindowSizeInfo() // 获取窗口尺寸信息
    
    // 提供主题配置和窗口尺寸信息给子组件
    CompositionLocalProvider(
        LocalThemeConfig provides config,       // 提供主题配置
        LocalWindowSizeInfo provides windowSizeInfo, // 提供窗口尺寸信息
        content = content                       // 渲染子组件
    )
}

/**
 * 获取当前主题配置的组合函数
 * 在组件中使用此函数获取当前的主题配置
 */
@Composable
fun currentThemeConfig(): ThemeConfig {
    return LocalThemeConfig.current             // 返回当前主题配置
}

/**
 * 获取当前窗口尺寸信息的组合函数
 * 在组件中使用此函数获取当前的窗口尺寸信息
 */
@Composable
fun currentWindowSizeInfo(): WindowSizeInfo {
    return LocalWindowSizeInfo.current          // 返回当前窗口尺寸信息
}

/**
 * 判断是否为紧凑屏幕的组合函数
 * 用于响应式布局判断
 */
@Composable
fun isCompactScreen(): Boolean {
    return currentWindowSizeInfo().screenSize == ScreenSize.COMPACT // 判断是否为紧凑屏幕
}

/**
 * 判断是否为中等屏幕的组合函数
 * 用于响应式布局判断
 */
@Composable
fun isMediumScreen(): Boolean {
    return currentWindowSizeInfo().screenSize == ScreenSize.MEDIUM // 判断是否为中等屏幕
}

/**
 * 判断是否为扩展屏幕的组合函数
 * 用于响应式布局判断
 */
@Composable
fun isExpandedScreen(): Boolean {
    return currentWindowSizeInfo().screenSize == ScreenSize.EXPANDED // 判断是否为扩展屏幕
}