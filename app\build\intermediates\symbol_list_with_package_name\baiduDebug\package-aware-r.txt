com.aiinterview.simulator
attr action
attr alpha
attr argType
attr data
attr dataPattern
attr destination
attr enterAnim
attr exitAnim
attr font
attr fontProviderAuthority
attr fontProviderCerts
attr fontProviderFetchStrategy
attr fontProviderFetchTimeout
attr fontProviderPackage
attr fontProviderQuery
attr fontProviderSystemFontFamily
attr fontStyle
attr fontVariationSettings
attr fontWeight
attr graph
attr lStar
attr launchSingleTop
attr mimeType
attr navGraph
attr nestedScrollViewStyle
attr nullable
attr popEnterAnim
attr popExitAnim
attr popUpTo
attr popUpToInclusive
attr popUpToSaveState
attr queryPatterns
attr restoreState
attr route
attr shortcutMatchRequired
attr startDestination
attr targetPackage
attr ttcIndex
attr uri
color androidx_core_ripple_material_light
color androidx_core_secondary_text_default_material_light
color call_notification_answer_color
color call_notification_decline_color
color notification_action_color_filter
color notification_icon_bg_color
color vector_tint_color
color vector_tint_theme_color
dimen compat_button_inset_horizontal_material
dimen compat_button_inset_vertical_material
dimen compat_button_padding_horizontal_material
dimen compat_button_padding_vertical_material
dimen compat_control_corner_material
dimen compat_notification_large_icon_max_height
dimen compat_notification_large_icon_max_width
dimen notification_action_icon_size
dimen notification_action_text_size
dimen notification_big_circle_margin
dimen notification_content_margin_start
dimen notification_large_icon_height
dimen notification_large_icon_width
dimen notification_main_column_padding_top
dimen notification_media_narrow_margin
dimen notification_right_icon_size
dimen notification_right_side_padding_top
dimen notification_small_icon_background_padding
dimen notification_small_icon_size_as_large
dimen notification_subtext_size
dimen notification_top_pad
dimen notification_top_pad_large_text
drawable ic_call_answer
drawable ic_call_answer_low
drawable ic_call_answer_video
drawable ic_call_answer_video_low
drawable ic_call_decline
drawable ic_call_decline_low
drawable notification_action_background
drawable notification_bg
drawable notification_bg_low
drawable notification_bg_low_normal
drawable notification_bg_low_pressed
drawable notification_bg_normal
drawable notification_bg_normal_pressed
drawable notification_icon_background
drawable notification_oversize_large_icon_bg
drawable notification_template_icon_bg
drawable notification_template_icon_low_bg
drawable notification_tile_bg
drawable notify_panel_notification_icon_bg
id accessibility_action_clickable_span
id accessibility_custom_action_0
id accessibility_custom_action_1
id accessibility_custom_action_10
id accessibility_custom_action_11
id accessibility_custom_action_12
id accessibility_custom_action_13
id accessibility_custom_action_14
id accessibility_custom_action_15
id accessibility_custom_action_16
id accessibility_custom_action_17
id accessibility_custom_action_18
id accessibility_custom_action_19
id accessibility_custom_action_2
id accessibility_custom_action_20
id accessibility_custom_action_21
id accessibility_custom_action_22
id accessibility_custom_action_23
id accessibility_custom_action_24
id accessibility_custom_action_25
id accessibility_custom_action_26
id accessibility_custom_action_27
id accessibility_custom_action_28
id accessibility_custom_action_29
id accessibility_custom_action_3
id accessibility_custom_action_30
id accessibility_custom_action_31
id accessibility_custom_action_4
id accessibility_custom_action_5
id accessibility_custom_action_6
id accessibility_custom_action_7
id accessibility_custom_action_8
id accessibility_custom_action_9
id action_container
id action_divider
id action_image
id action_text
id actions
id androidx_compose_ui_view_composition_context
id async
id blocking
id chronometer
id compose_view_saveable_id_tag
id consume_window_insets_tag
id dialog_button
id edit_text_id
id forever
id hide_ime_id
id hide_in_inspector_tag
id icon
id icon_group
id info
id inspection_slot_table_set
id is_pooling_container_tag
id italic
id line1
id line3
id nav_controller_view_tag
id normal
id notification_background
id notification_main_column
id notification_main_column_container
id pooling_container_listener_holder_tag
id report_drawn
id right_icon
id right_side
id tag_accessibility_actions
id tag_accessibility_clickable_spans
id tag_accessibility_heading
id tag_accessibility_pane_title
id tag_on_apply_window_listener
id tag_on_receive_content_listener
id tag_on_receive_content_mime_types
id tag_screen_reader_focusable
id tag_state_description
id tag_transition_group
id tag_unhandled_key_event_manager
id tag_unhandled_key_listeners
id tag_window_insets_animation_callback
id text
id text2
id time
id title
id view_tree_lifecycle_owner
id view_tree_on_back_pressed_dispatcher_owner
id view_tree_saved_state_registry_owner
id view_tree_view_model_store_owner
id wrapped_composition_tag
integer status_bar_notification_info_maxnum
layout custom_dialog
layout ime_base_split_test_activity
layout ime_secondary_split_test_activity
layout notification_action
layout notification_action_tombstone
layout notification_template_custom_big
layout notification_template_icon_group
layout notification_template_part_chronometer
layout notification_template_part_time
string androidx_startup
string app_name
string app_store_age_rating
string app_store_age_rating_description
string app_store_category
string app_store_changelog_v1_0_0
string app_store_description
string app_store_developer
string app_store_developer_website
string app_store_keywords
string app_store_name
string app_store_privacy_policy_url
string app_store_short_description
string app_store_subcategory
string app_store_support_email
string app_store_tagline
string baidu_app_description
string bottom_sheet_collapse_description
string bottom_sheet_dismiss_description
string bottom_sheet_drag_handle_description
string bottom_sheet_expand_description
string cache_clearing
string call_notification_answer_action
string call_notification_answer_video_action
string call_notification_decline_action
string call_notification_hang_up_action
string call_notification_incoming_text
string call_notification_ongoing_text
string call_notification_screening_text
string cancel
string close
string close_drawer
string close_sheet
string collapsed
string crash_report_cancel
string crash_report_message
string crash_report_send
string crash_report_title
string date_input_headline
string date_input_headline_description
string date_input_invalid_for_pattern
string date_input_invalid_not_allowed
string date_input_invalid_year_range
string date_input_label
string date_input_no_input_description
string date_input_title
string date_picker_headline
string date_picker_headline_description
string date_picker_navigate_to_year_description
string date_picker_no_selection_description
string date_picker_scroll_to_earlier_years
string date_picker_scroll_to_later_years
string date_picker_switch_to_calendar_mode
string date_picker_switch_to_day_selection
string date_picker_switch_to_input_mode
string date_picker_switch_to_next_month
string date_picker_switch_to_previous_month
string date_picker_switch_to_year_selection
string date_picker_title
string date_picker_today_description
string date_picker_year_picker_pane_title
string date_range_input_invalid_range_input
string date_range_input_title
string date_range_picker_day_in_range
string date_range_picker_end_headline
string date_range_picker_scroll_to_next_month
string date_range_picker_scroll_to_previous_month
string date_range_picker_start_headline
string date_range_picker_title
string default_error_message
string default_popup_window_title
string degradation_basic
string degradation_cache_only
string degradation_lightweight
string degradation_offline_mode
string degradation_simplified
string dialog
string dropdown_menu
string error_microphone_permission
string error_network
string error_no_internet
string error_server
string error_speech_recognition
string error_timeout
string error_token_expired
string error_unauthorized
string error_unknown
string expanded
string huawei_app_description
string in_progress
string indeterminate
string m3c_bottom_sheet_pane_title
string memory_critical
string memory_low
string navigation_menu
string network_connected
string network_connecting
string network_offline
string network_poor
string not_selected
string off
string ok
string on
string oppo_app_description
string permission_microphone_description
string permission_network_description
string permission_storage_description
string qihoo_app_description
string range_end
string range_start
string rate_app_message
string rate_app_title
string rate_later
string rate_never
string rate_now
string retry
string screenshot_1_description
string screenshot_2_description
string screenshot_3_description
string screenshot_4_description
string screenshot_5_description
string search_bar_search
string selected
string settings
string share_app_message
string share_app_title
string snackbar_dismiss
string startup_optimizing
string status_bar_notification_info_overflow
string suggestions_available
string switch_role
string tab
string template_percent
string tencent_app_description
string time_picker_am
string time_picker_hour
string time_picker_hour_24h_suffix
string time_picker_hour_selection
string time_picker_hour_suffix
string time_picker_hour_text_field
string time_picker_minute
string time_picker_minute_selection
string time_picker_minute_suffix
string time_picker_minute_text_field
string time_picker_period_toggle_description
string time_picker_pm
string tooltip_long_press_label
string tooltip_pane_description
string update_available_message
string update_available_title
string update_ignore
string update_later
string update_now
string vivo_app_description
string xiaomi_app_description
style DialogWindowTheme
style FloatingDialogTheme
style FloatingDialogWindowTheme
style TextAppearance_Compat_Notification
style TextAppearance_Compat_Notification_Info
style TextAppearance_Compat_Notification_Line2
style TextAppearance_Compat_Notification_Time
style TextAppearance_Compat_Notification_Title
style Theme_AIInterviewSimulator
style Widget_Compat_NotificationActionContainer
style Widget_Compat_NotificationActionText
styleable ActivityNavigator android_name action data dataPattern targetPackage
styleable Capability queryPatterns shortcutMatchRequired
styleable ColorStateListItem android_color android_alpha android_lStar alpha lStar
styleable FontFamily fontProviderAuthority fontProviderCerts fontProviderFetchStrategy fontProviderFetchTimeout fontProviderPackage fontProviderQuery fontProviderSystemFontFamily
styleable FontFamilyFont android_font android_fontWeight android_fontStyle android_ttcIndex android_fontVariationSettings font fontStyle fontVariationSettings fontWeight ttcIndex
styleable GradientColor android_startColor android_endColor android_type android_centerX android_centerY android_gradientRadius android_tileMode android_centerColor android_startX android_startY android_endX android_endY
styleable GradientColorItem android_color android_offset
styleable NavAction android_id destination enterAnim exitAnim launchSingleTop popEnterAnim popExitAnim popUpTo popUpToInclusive popUpToSaveState restoreState
styleable NavArgument android_name android_defaultValue argType nullable
styleable NavDeepLink android_autoVerify action mimeType uri
styleable NavGraphNavigator startDestination
styleable NavHost navGraph
styleable NavInclude graph
styleable Navigator android_label android_id route
xml backup_rules
xml data_extraction_rules
