package com.aiinterview.simulator.presentation.screen.interview

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.aiinterview.simulator.data.audio.AudioRecorder
import com.aiinterview.simulator.data.dto.response.RecognitionResult
import com.aiinterview.simulator.data.model.Question
import com.aiinterview.simulator.presentation.viewmodel.RecordingViewModel
import com.aiinterview.simulator.presentation.viewmodel.SpeechRecognitionViewModel
import com.aiinterview.simulator.domain.util.Resource
import java.io.File

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AnswerProcessingScreen(
    question: Question,
    onAnswerComplete: (audioFile: File, transcription: String) -> Unit,
    onNavigateBack: () -> Unit,
    recordingViewModel: RecordingViewModel = hiltViewModel(),
    speechViewModel: SpeechRecognitionViewModel = hiltViewModel()
) {
    val scrollState = rememberScrollState()
    
    // Recording states
    val recordingState by recordingViewModel.recordingState.collectAsState()
    val recordingDuration by recordingViewModel.recordingDuration.collectAsState()
    val currentRecordingFile by recordingViewModel.currentRecordingFile.collectAsState()
    val recordingError by recordingViewModel.errorMessage.collectAsState()
    
    // Speech recognition states
    val recognitionState by speechViewModel.recognitionState.collectAsState()
    val recognitionResult by speechViewModel.currentRecognitionResult.collectAsState()
    val isRecognizing by speechViewModel.isRecognizing.collectAsState()
    
    // UI states
    var currentStep by remember { mutableStateOf(ProcessingStep.RECORDING) }
    var editableText by remember { mutableStateOf("") }
    var isEditing by remember { mutableStateOf(false) }
    var showConfirmDialog by remember { mutableStateOf(false) }
    
    // Handle recording completion
    LaunchedEffect(currentRecordingFile, recordingState) {
        if (currentRecordingFile != null && recordingState == AudioRecorder.RecordingState.STOPPED) {
            currentStep = ProcessingStep.RECOGNIZING
            speechViewModel.recognizeAudio(currentRecordingFile!!)
        }
    }
    
    // Handle recognition completion
    LaunchedEffect(recognitionResult) {
        recognitionResult?.let { result ->
            if (currentStep == ProcessingStep.RECOGNIZING) {
                currentStep = ProcessingStep.REVIEWING
                editableText = result.text
            }
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(scrollState)
    ) {
        // Top App Bar
        TopAppBar(
            title = { Text("回答处理") },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            }
        )
        
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Question Card
            QuestionCard(question = question)
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Progress Indicator
            ProcessingProgressIndicator(
                currentStep = currentStep,
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Step Content
            when (currentStep) {
                ProcessingStep.RECORDING -> {
                    RecordingStepContent(
                        question = question,
                        recordingState = recordingState,
                        recordingDuration = recordingDuration,
                        errorMessage = recordingError,
                        onStartRecording = { recordingViewModel.startRecording(question.id) },
                        onStopRecording = { recordingViewModel.stopRecording() },
                        onPauseRecording = { recordingViewModel.pauseRecording() },
                        onResumeRecording = { recordingViewModel.resumeRecording() },
                        onCancelRecording = { recordingViewModel.cancelRecording() },
                        formatDuration = { recordingViewModel.formatDuration(it) }
                    )
                }
                
                ProcessingStep.RECOGNIZING -> {
                    RecognitionStepContent(
                        recognitionState = recognitionState,
                        isRecognizing = isRecognizing,
                        onRetryRecognition = {
                            currentRecordingFile?.let { file ->
                                speechViewModel.retryRecognition(file)
                            }
                        },
                        onSkipRecognition = {
                            currentStep = ProcessingStep.REVIEWING
                            editableText = ""
                        }
                    )
                }
                
                ProcessingStep.REVIEWING -> {
                    ReviewStepContent(
                        recognitionResult = recognitionResult,
                        editableText = editableText,
                        isEditing = isEditing,
                        onTextChange = { editableText = it },
                        onStartEditing = { isEditing = true },
                        onStopEditing = { isEditing = false },
                        onRetryRecording = {
                            currentStep = ProcessingStep.RECORDING
                            recordingViewModel.resetRecording()
                            speechViewModel.clearRecognitionResult()
                        },
                        onRetryRecognition = {
                            currentRecordingFile?.let { file ->
                                currentStep = ProcessingStep.RECOGNIZING
                                speechViewModel.retryRecognition(file)
                            }
                        },
                        onConfirmAnswer = { showConfirmDialog = true },
                        getQualityMessage = { speechViewModel.getRecognitionQualityMessage() },
                        getConfidenceDescription = { confidence ->
                            speechViewModel.getConfidenceDescription(confidence)
                        }
                    )
                }
            }
        }
    }
    
    // Confirmation Dialog
    if (showConfirmDialog) {
        AlertDialog(
            onDismissRequest = { showConfirmDialog = false },
            title = { Text("确认提交回答") },
            text = {
                Column {
                    Text("请确认您的回答内容：")
                    Spacer(modifier = Modifier.height(8.dp))
                    Card(
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surfaceVariant
                        )
                    ) {
                        Text(
                            text = editableText.ifBlank { "[无回答内容]" },
                            modifier = Modifier.padding(12.dp),
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            },
            confirmButton = {
                Button(
                    onClick = {
                        showConfirmDialog = false
                        currentRecordingFile?.let { file ->
                            onAnswerComplete(file, editableText)
                        }
                    }
                ) {
                    Text("确认提交")
                }
            },
            dismissButton = {
                TextButton(onClick = { showConfirmDialog = false }) {
                    Text("继续编辑")
                }
            }
        )
    }
}

enum class ProcessingStep {
    RECORDING, RECOGNIZING, REVIEWING
}

@Composable
fun ProcessingProgressIndicator(
    currentStep: ProcessingStep,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceEvenly,
        verticalAlignment = Alignment.CenterVertically
    ) {
        val steps = listOf(
            ProcessingStep.RECORDING to "录音",
            ProcessingStep.RECOGNIZING to "识别",
            ProcessingStep.REVIEWING to "确认"
        )
        
        steps.forEachIndexed { index, (step, label) ->
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                val isActive = step == currentStep
                val isCompleted = step.ordinal < currentStep.ordinal
                
                Box(
                    modifier = Modifier
                        .size(32.dp),
                    contentAlignment = Alignment.Center
                ) {
                    if (isCompleted) {
                        Icon(
                            Icons.Default.CheckCircle,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(32.dp)
                        )
                    } else {
                        Surface(
                            modifier = Modifier.size(32.dp),
                            shape = androidx.compose.foundation.shape.CircleShape,
                            color = if (isActive) {
                                MaterialTheme.colorScheme.primary
                            } else {
                                MaterialTheme.colorScheme.outline
                            }
                        ) {
                            Box(
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = (index + 1).toString(),
                                    color = if (isActive) {
                                        MaterialTheme.colorScheme.onPrimary
                                    } else {
                                        MaterialTheme.colorScheme.onSurface
                                    },
                                    style = MaterialTheme.typography.labelMedium
                                )
                            }
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = label,
                    style = MaterialTheme.typography.labelSmall,
                    color = if (isActive || isCompleted) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
            }
            
            if (index < steps.size - 1) {
                Divider(
                    modifier = Modifier
                        .weight(1f)
                        .padding(horizontal = 8.dp),
                    color = if (steps[index + 1].first.ordinal <= currentStep.ordinal) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.outline
                    }
                )
            }
        }
    }
}

@Composable
fun QuestionCard(question: Question) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = question.type.displayName,
                style = MaterialTheme.typography.labelMedium,
                color = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Text(
                text = question.title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            if (question.content != question.title) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = question.content,
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}

@Composable
fun RecordingStepContent(
    question: Question,
    recordingState: AudioRecorder.RecordingState,
    recordingDuration: Long,
    errorMessage: String?,
    onStartRecording: () -> Unit,
    onStopRecording: () -> Unit,
    onPauseRecording: () -> Unit,
    onResumeRecording: () -> Unit,
    onCancelRecording: () -> Unit,
    formatDuration: (Long) -> String
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "录制回答",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Recording Duration
            Text(
                text = formatDuration(recordingDuration),
                style = MaterialTheme.typography.headlineLarge,
                fontWeight = FontWeight.Bold,
                color = if (recordingState == AudioRecorder.RecordingState.RECORDING) {
                    MaterialTheme.colorScheme.primary
                } else {
                    MaterialTheme.colorScheme.onSurface
                }
            )
            
            Text(
                text = "建议回答时间：${question.timeLimit / 60}分钟",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Recording Waveform
            if (recordingState == AudioRecorder.RecordingState.RECORDING) {
                RecordingWaveform(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(60.dp)
                )
                Spacer(modifier = Modifier.height(24.dp))
            }
            
            // Recording Controls
            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Cancel button (if recording)
                if (recordingState != AudioRecorder.RecordingState.IDLE) {
                    IconButton(
                        onClick = onCancelRecording,
                        modifier = Modifier.size(48.dp)
                    ) {
                        Icon(
                            Icons.Default.Close,
                            contentDescription = "取消录音",
                            tint = MaterialTheme.colorScheme.error
                        )
                    }
                }
                
                // Main recording button
                FloatingActionButton(
                    onClick = {
                        when (recordingState) {
                            AudioRecorder.RecordingState.IDLE -> onStartRecording()
                            AudioRecorder.RecordingState.RECORDING -> onStopRecording()
                            AudioRecorder.RecordingState.PAUSED -> onResumeRecording()
                            else -> {}
                        }
                    },
                    containerColor = when (recordingState) {
                        AudioRecorder.RecordingState.RECORDING -> MaterialTheme.colorScheme.error
                        else -> MaterialTheme.colorScheme.primary
                    }
                ) {
                    Icon(
                        imageVector = when (recordingState) {
                            AudioRecorder.RecordingState.IDLE -> Icons.Default.Mic
                            AudioRecorder.RecordingState.RECORDING -> Icons.Default.Stop
                            AudioRecorder.RecordingState.PAUSED -> Icons.Default.PlayArrow
                            else -> Icons.Default.Mic
                        },
                        contentDescription = when (recordingState) {
                            AudioRecorder.RecordingState.IDLE -> "开始录音"
                            AudioRecorder.RecordingState.RECORDING -> "停止录音"
                            AudioRecorder.RecordingState.PAUSED -> "继续录音"
                            else -> "录音"
                        }
                    )
                }
                
                // Pause button (if recording)
                if (recordingState == AudioRecorder.RecordingState.RECORDING) {
                    IconButton(
                        onClick = onPauseRecording,
                        modifier = Modifier.size(48.dp)
                    ) {
                        Icon(
                            Icons.Default.Pause,
                            contentDescription = "暂停录音"
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Status Text
            Text(
                text = when (recordingState) {
                    AudioRecorder.RecordingState.IDLE -> "点击开始录音回答"
                    AudioRecorder.RecordingState.RECORDING -> "正在录音中，请开始回答..."
                    AudioRecorder.RecordingState.PAUSED -> "录音已暂停"
                    AudioRecorder.RecordingState.STOPPED -> "录音已完成"
                    AudioRecorder.RecordingState.ERROR -> "录音出现错误"
                },
                style = MaterialTheme.typography.bodyMedium,
                color = when (recordingState) {
                    AudioRecorder.RecordingState.ERROR -> MaterialTheme.colorScheme.error
                    AudioRecorder.RecordingState.RECORDING -> MaterialTheme.colorScheme.primary
                    else -> MaterialTheme.colorScheme.onSurfaceVariant
                }
            )
            
            // Error Message
            errorMessage?.let { message ->
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = message,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.error
                )
            }
        }
    }
}

@Composable
fun RecognitionStepContent(
    recognitionState: Resource<RecognitionResult>?,
    isRecognizing: Boolean,
    onRetryRecognition: () -> Unit,
    onSkipRecognition: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            when (recognitionState) {
                is Resource.Loading -> {
                    CircularProgressIndicator()
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "正在识别语音内容...",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = "请稍候，这可能需要几秒钟",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                is Resource.Error -> {
                    Icon(
                        Icons.Default.Error,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.error,
                        modifier = Modifier.size(48.dp)
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "语音识别失败",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.error
                    )
                    Text(
                        text = recognitionState.message ?: "未知错误",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        OutlinedButton(
                            onClick = onRetryRecognition,
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("重试识别")
                        }
                        
                        Button(
                            onClick = onSkipRecognition,
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("手动输入")
                        }
                    }
                }
                
                else -> {
                    if (isRecognizing) {
                        CircularProgressIndicator()
                        Spacer(modifier = Modifier.height(16.dp))
                        Text("正在处理...")
                    }
                }
            }
        }
    }
}

@Composable
fun ReviewStepContent(
    recognitionResult: RecognitionResult?,
    editableText: String,
    isEditing: Boolean,
    onTextChange: (String) -> Unit,
    onStartEditing: () -> Unit,
    onStopEditing: () -> Unit,
    onRetryRecording: () -> Unit,
    onRetryRecognition: () -> Unit,
    onConfirmAnswer: () -> Unit,
    getQualityMessage: () -> String,
    getConfidenceDescription: (Double) -> String
) {
    Column {
        // Recognition Quality Card
        recognitionResult?.let { result ->
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "识别质量",
                            style = MaterialTheme.typography.labelMedium
                        )
                        
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = when {
                                    result.confidence >= 0.8 -> Icons.Default.CheckCircle
                                    result.confidence >= 0.6 -> Icons.Default.Warning
                                    else -> Icons.Default.Error
                                },
                                contentDescription = null,
                                tint = when {
                                    result.confidence >= 0.8 -> MaterialTheme.colorScheme.primary
                                    result.confidence >= 0.6 -> MaterialTheme.colorScheme.tertiary
                                    else -> MaterialTheme.colorScheme.error
                                },
                                modifier = Modifier.size(16.dp)
                            )
                            
                            Spacer(modifier = Modifier.width(4.dp))
                            
                            Text(
                                text = "${(result.confidence * 100).toInt()}%",
                                style = MaterialTheme.typography.labelSmall
                            )
                        }
                    }
                    
                    Text(
                        text = getQualityMessage(),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
        }
        
        // Answer Content Card
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "回答内容",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                if (isEditing) {
                    OutlinedTextField(
                        value = editableText,
                        onValueChange = onTextChange,
                        modifier = Modifier.fillMaxWidth(),
                        minLines = 4,
                        maxLines = 10,
                        placeholder = { Text("请输入您的回答内容...") },
                        label = { Text("回答内容") }
                    )
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        OutlinedButton(
                            onClick = onStopEditing,
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("取消")
                        }
                        
                        Button(
                            onClick = onStopEditing,
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("保存")
                        }
                    }
                } else {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surfaceVariant
                        )
                    ) {
                        Text(
                            text = editableText.ifBlank { "暂无回答内容" },
                            modifier = Modifier.padding(12.dp),
                            style = MaterialTheme.typography.bodyMedium,
                            color = if (editableText.isNotBlank()) {
                                MaterialTheme.colorScheme.onSurfaceVariant
                            } else {
                                MaterialTheme.colorScheme.outline
                            }
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    // Action Buttons
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        OutlinedButton(
                            onClick = onStartEditing,
                            modifier = Modifier.weight(1f)
                        ) {
                            Icon(
                                Icons.Default.Edit,
                                contentDescription = null,
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text("编辑")
                        }
                        
                        OutlinedButton(
                            onClick = onRetryRecognition,
                            modifier = Modifier.weight(1f)
                        ) {
                            Icon(
                                Icons.Default.Refresh,
                                contentDescription = null,
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text("重新识别")
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    OutlinedButton(
                        onClick = onRetryRecording,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Icon(
                            Icons.Default.Mic,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("重新录制")
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Confirm Button
        Button(
            onClick = onConfirmAnswer,
            modifier = Modifier.fillMaxWidth(),
            enabled = editableText.isNotBlank()
        ) {
            Text("确认提交回答")
        }
    }
}