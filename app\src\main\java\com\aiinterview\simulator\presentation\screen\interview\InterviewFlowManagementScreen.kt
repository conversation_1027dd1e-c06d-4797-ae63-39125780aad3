package com.aiinterview.simulator.presentation.screen.interview

import androidx.compose.animation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.aiinterview.simulator.data.handler.TechnicalIssueHandler
import com.aiinterview.simulator.data.manager.InterviewSessionManager
import com.aiinterview.simulator.data.service.InterviewTimerService
import com.aiinterview.simulator.presentation.viewmodel.InterviewFlowManagementViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun InterviewFlowManagementScreen(
    onNavigateToQuestion: (String) -> Unit,
    onNavigateToEvaluation: () -> Unit,
    onExitInterview: () -> Unit,
    viewModel: InterviewFlowManagementViewModel = hiltViewModel()
) {
    val scrollState = rememberScrollState()
    
    // 观察ViewModel状态
    val sessionProgress by viewModel.sessionProgress.collectAsState()
    val timerState by viewModel.timerState.collectAsState()
    val totalTimeRemaining by viewModel.totalTimeRemaining.collectAsState()
    val questionTimeRemaining by viewModel.questionTimeRemaining.collectAsState()
    val currentIssue by viewModel.currentTechnicalIssue.collectAsState()
    val timeWarnings by viewModel.timeWarnings.collectAsState()
    val isHandlingIssue by viewModel.isHandlingIssue.collectAsState()
    
    var showExitDialog by remember { mutableStateOf(false) }
    var showIssueDialog by remember { mutableStateOf(false) }
    
    // 监听技术问题
    LaunchedEffect(currentIssue) {
        if (currentIssue != null) {
            showIssueDialog = true
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(scrollState)
    ) {
        // 顶部应用栏
        TopAppBar(
            title = { 
                Text(
                    text = "面试管理",
                    style = MaterialTheme.typography.titleMedium
                )
            },
            actions = {
                // 暂停/恢复按钮
                IconButton(
                    onClick = {
                        if (timerState == InterviewTimerService.TimerState.RUNNING) {
                            viewModel.pauseInterview()
                        } else {
                            viewModel.resumeInterview()
                        }
                    }
                ) {
                    Icon(
                        imageVector = if (timerState == InterviewTimerService.TimerState.RUNNING) {
                            Icons.Default.Pause
                        } else {
                            Icons.Default.PlayArrow
                        },
                        contentDescription = "暂停/恢复"
                    )
                }
                
                // 退出按钮
                IconButton(onClick = { showExitDialog = true }) {
                    Icon(Icons.Default.ExitToApp, contentDescription = "退出面试")
                }
            }
        )
        
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 时间显示区域
            TimeDisplaySection(
                totalTimeRemaining = totalTimeRemaining,
                questionTimeRemaining = questionTimeRemaining,
                timerState = timerState,
                onAcknowledgeWarning = { viewModel.acknowledgeTimeWarning(it) }
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 面试进度区域
            InterviewProgressSection(
                progress = sessionProgress,
                onNavigateToQuestion = onNavigateToQuestion
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 时间警告区域
            if (timeWarnings.isNotEmpty()) {
                TimeWarningsSection(
                    warnings = timeWarnings,
                    onAcknowledgeWarning = { viewModel.acknowledgeTimeWarning(it) }
                )
                
                Spacer(modifier = Modifier.height(16.dp))
            }
            
            // 技术问题状态
            if (currentIssue != null) {
                TechnicalIssueStatusCard(
                    issue = currentIssue,
                    isHandling = isHandlingIssue,
                    onShowDetails = { showIssueDialog = true }
                )
                
                Spacer(modifier = Modifier.height(16.dp))
            }
            
            // 面试控制按钮
            InterviewControlButtons(
                sessionProgress = sessionProgress,
                timerState = timerState,
                onPauseResume = {
                    if (timerState == InterviewTimerService.TimerState.RUNNING) {
                        viewModel.pauseInterview()
                    } else {
                        viewModel.resumeInterview()
                    }
                },
                onNextQuestion = { viewModel.proceedToNextQuestion() },
                onCompleteInterview = { viewModel.completeInterview() },
                onNavigateToEvaluation = onNavigateToEvaluation
            )
        }
    }
    
    // 退出确认对话框
    if (showExitDialog) {
        ExitInterviewConfirmDialog(
            onConfirm = {
                showExitDialog = false
                viewModel.cancelInterview()
                onExitInterview()
            },
            onDismiss = { showExitDialog = false }
        )
    }
    
    // 技术问题处理对话框
    if (showIssueDialog && currentIssue != null) {
        TechnicalIssueDialog(
            issue = currentIssue,
            resolutionOptions = viewModel.resolutionOptions.collectAsState().value,
            onExecuteResolution = { actionId ->
                viewModel.executeResolution(actionId)
            },
            onDismiss = { 
                showIssueDialog = false
                viewModel.clearCurrentIssue()
            }
        )
    }
}

@Composable
fun TimeDisplaySection(
    totalTimeRemaining: Long,
    questionTimeRemaining: Long,
    timerState: InterviewTimerService.TimerState,
    onAcknowledgeWarning: (String) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = when {
                totalTimeRemaining <= 1 * 60 * 1000L -> MaterialTheme.colorScheme.errorContainer
                totalTimeRemaining <= 5 * 60 * 1000L -> MaterialTheme.colorScheme.tertiaryContainer
                else -> MaterialTheme.colorScheme.primaryContainer
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 总时间
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "面试剩余时间",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                Text(
                    text = formatTime(totalTimeRemaining),
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold,
                    color = when {
                        totalTimeRemaining <= 1 * 60 * 1000L -> MaterialTheme.colorScheme.error
                        totalTimeRemaining <= 5 * 60 * 1000L -> MaterialTheme.colorScheme.tertiary
                        else -> MaterialTheme.colorScheme.primary
                    }
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 问题时间
            if (questionTimeRemaining > 0) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "当前题目时间",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    
                    Text(
                        text = formatTime(questionTimeRemaining),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 计时器状态
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = when (timerState) {
                        InterviewTimerService.TimerState.RUNNING -> Icons.Default.PlayArrow
                        InterviewTimerService.TimerState.PAUSED -> Icons.Default.Pause
                        InterviewTimerService.TimerState.STOPPED -> Icons.Default.Stop
                    },
                    contentDescription = null,
                    modifier = Modifier.size(16.dp)
                )
                
                Spacer(modifier = Modifier.width(4.dp))
                
                Text(
                    text = when (timerState) {
                        InterviewTimerService.TimerState.RUNNING -> "计时中"
                        InterviewTimerService.TimerState.PAUSED -> "已暂停"
                        InterviewTimerService.TimerState.STOPPED -> "已停止"
                    },
                    style = MaterialTheme.typography.labelMedium
                )
            }
        }
    }
}

@Composable
fun InterviewProgressSection(
    progress: InterviewSessionManager.SessionProgress,
    onNavigateToQuestion: (String) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "面试进度",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 进度条
            LinearProgressIndicator(
                progress = if (progress.totalQuestions > 0) {
                    progress.completedQuestions.toFloat() / progress.totalQuestions.toFloat()
                } else 0f,
                modifier = Modifier.fillMaxWidth(),
                color = MaterialTheme.colorScheme.primary,
                trackColor = MaterialTheme.colorScheme.surfaceVariant
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 进度信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "已完成 ${progress.completedQuestions}/${progress.totalQuestions} 题",
                    style = MaterialTheme.typography.bodyMedium
                )
                
                Text(
                    text = "${((progress.completedQuestions.toFloat() / progress.totalQuestions.toFloat()) * 100).toInt()}%",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 统计信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "平均用时: ${formatTime(progress.averageTimePerQuestion)}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Text(
                    text = "当前题目: ${progress.currentQuestionIndex + 1}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
fun TimeWarningsSection(
    warnings: List<InterviewTimerService.TimeWarning>,
    onAcknowledgeWarning: (String) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.tertiaryContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.Warning,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onTertiaryContainer
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "时间提醒",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onTertiaryContainer
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            warnings.filter { !it.acknowledged }.forEach { warning ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = warning.message,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onTertiaryContainer,
                        modifier = Modifier.weight(1f)
                    )
                    
                    TextButton(
                        onClick = { onAcknowledgeWarning(warning.id) }
                    ) {
                        Text("知道了")
                    }
                }
            }
        }
    }
}

@Composable
fun TechnicalIssueStatusCard(
    issue: TechnicalIssueHandler.TechnicalIssue,
    isHandling: Boolean,
    onShowDetails: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = when (issue.severity) {
                TechnicalIssueHandler.IssueSeverity.CRITICAL -> MaterialTheme.colorScheme.errorContainer
                TechnicalIssueHandler.IssueSeverity.HIGH -> MaterialTheme.colorScheme.tertiaryContainer
                else -> MaterialTheme.colorScheme.secondaryContainer
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.Error,
                        contentDescription = null,
                        tint = when (issue.severity) {
                            TechnicalIssueHandler.IssueSeverity.CRITICAL -> MaterialTheme.colorScheme.error
                            TechnicalIssueHandler.IssueSeverity.HIGH -> MaterialTheme.colorScheme.tertiary
                            else -> MaterialTheme.colorScheme.secondary
                        }
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = "技术问题",
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.Bold
                    )
                }
                
                if (isHandling) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = issue.description,
                style = MaterialTheme.typography.bodyMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Button(
                onClick = onShowDetails,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("查看解决方案")
            }
        }
    }
}

@Composable
fun InterviewControlButtons(
    sessionProgress: InterviewSessionManager.SessionProgress,
    timerState: InterviewTimerService.TimerState,
    onPauseResume: () -> Unit,
    onNextQuestion: () -> Unit,
    onCompleteInterview: () -> Unit,
    onNavigateToEvaluation: () -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // 暂停/恢复按钮
        Button(
            onClick = onPauseResume,
            modifier = Modifier.fillMaxWidth(),
            colors = if (timerState == InterviewTimerService.TimerState.RUNNING) {
                ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.tertiary)
            } else {
                ButtonDefaults.buttonColors()
            }
        ) {
            Icon(
                imageVector = if (timerState == InterviewTimerService.TimerState.RUNNING) {
                    Icons.Default.Pause
                } else {
                    Icons.Default.PlayArrow
                },
                contentDescription = null,
                modifier = Modifier.size(20.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                if (timerState == InterviewTimerService.TimerState.RUNNING) "暂停面试" else "继续面试"
            )
        }
        
        // 下一题按钮
        if (sessionProgress.currentQuestionIndex < sessionProgress.totalQuestions) {
            OutlinedButton(
                onClick = onNextQuestion,
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(
                    Icons.Default.SkipNext,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("进入下一题")
            }
        }
        
        // 完成面试按钮
        if (sessionProgress.completedQuestions >= sessionProgress.totalQuestions) {
            Button(
                onClick = onCompleteInterview,
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Icon(
                    Icons.Default.Check,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("完成面试")
            }
        }
        
        // 查看评价按钮（如果面试已完成）
        if (sessionProgress.completedQuestions >= sessionProgress.totalQuestions) {
            OutlinedButton(
                onClick = onNavigateToEvaluation,
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(
                    Icons.Default.Assessment,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("查看评价结果")
            }
        }
    }
}

@Composable
fun ExitInterviewConfirmDialog(
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { 
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.Warning,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.error
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("退出面试")
            }
        },
        text = {
            Text("确定要退出当前面试吗？退出后本次面试记录将被保存，但可能无法获得完整的评价结果。")
        },
        confirmButton = {
            Button(
                onClick = onConfirm,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.error
                )
            ) {
                Text("确定退出")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("继续面试")
            }
        }
    )
}

@Composable
fun TechnicalIssueDialog(
    issue: TechnicalIssueHandler.TechnicalIssue,
    resolutionOptions: TechnicalIssueHandler.ResolutionOptions?,
    onExecuteResolution: (String) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { 
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.Error,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.error
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("技术问题")
            }
        },
        text = {
            Column {
                Text(
                    text = issue.description,
                    style = MaterialTheme.typography.bodyMedium
                )
                
                if (resolutionOptions != null) {
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Text(
                        text = "建议解决方案：",
                        style = MaterialTheme.typography.labelMedium,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    resolutionOptions.primaryActions.forEach { action ->
                        OutlinedButton(
                            onClick = { onExecuteResolution(action.id) },
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Text(action.title)
                        }
                        Spacer(modifier = Modifier.height(4.dp))
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("关闭")
            }
        }
    )
}

private fun formatTime(timeMs: Long): String {
    val minutes = timeMs / (60 * 1000)
    val seconds = (timeMs % (60 * 1000)) / 1000
    return String.format("%02d:%02d", minutes, seconds)
}