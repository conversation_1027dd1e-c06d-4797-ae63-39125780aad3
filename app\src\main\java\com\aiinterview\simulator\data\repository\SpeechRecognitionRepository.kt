package com.aiinterview.simulator.data.repository

import com.aiinterview.simulator.data.speech.SpeechRecognitionService
import com.aiinterview.simulator.data.dto.response.RecognitionResult
import com.aiinterview.simulator.domain.util.Resource
import com.aiinterview.simulator.domain.util.ErrorHandler
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

interface SpeechRecognitionRepository {
    suspend fun recognizeAudio(audioFile: File, provider: SpeechRecognitionService.Provider = SpeechRecognitionService.Provider.AUTO): Flow<Resource<RecognitionResult>>
    suspend fun validateRecognitionQuality(result: RecognitionResult): Boolean
    fun getConfidenceDescription(confidence: Double): String
}

@Singleton
class SpeechRecognitionRepositoryImpl @Inject constructor(
    private val speechRecognitionService: SpeechRecognitionService
) : SpeechRecognitionRepository {
    
    override suspend fun recognizeAudio(
        audioFile: File, 
        provider: SpeechRecognitionService.Provider
    ): Flow<Resource<RecognitionResult>> = flow {
        try {
            emit(Resource.Loading())
            
            // 检查音频文件是否存在
            if (!audioFile.exists()) {
                emit(Resource.Error("音频文件不存在"))
                return@flow
            }
            
            // 检查音频文件大小
            if (audioFile.length() == 0L) {
                emit(Resource.Error("音频文件为空"))
                return@flow
            }
            
            // 检查音频文件大小是否超过限制（例如10MB）
            val maxFileSize = 10 * 1024 * 1024L // 10MB
            if (audioFile.length() > maxFileSize) {
                emit(Resource.Error("音频文件过大，请录制较短的音频"))
                return@flow
            }
            
            // 执行语音识别
            val result = speechRecognitionService.recognizeAudio(audioFile, provider)
            
            if (result.success) {
                emit(Resource.Success(result))
            } else {
                // 如果识别失败，尝试离线降级处理
                val fallbackResult = speechRecognitionService.createOfflineFallbackResult(audioFile)
                emit(Resource.Success(fallbackResult))
            }
            
        } catch (e: Exception) {
            // 网络异常或其他错误，提供离线降级
            val fallbackResult = speechRecognitionService.createOfflineFallbackResult(audioFile)
            emit(Resource.Success(fallbackResult))
        }
    }
    
    override suspend fun validateRecognitionQuality(result: RecognitionResult): Boolean {
        return speechRecognitionService.validateRecognitionResult(result)
    }
    
    override fun getConfidenceDescription(confidence: Double): String {
        return speechRecognitionService.getConfidenceLevel(confidence)
    }
}