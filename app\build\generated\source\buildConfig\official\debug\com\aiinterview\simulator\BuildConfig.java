/**
 * Automatically generated file. DO NOT MODIFY
 */
package com.aiinterview.simulator;

public final class BuildConfig {
  public static final boolean DEBUG = Boolean.parseBoolean("true");
  public static final String APPLICATION_ID = "com.aiinterview.simulator.debug";
  // Field from build type: debug
  public static final String BUILD_TYPE = "debug";
  public static final String FLAVOR = "official";
  public static final int VERSION_CODE = 1;
  public static final String VERSION_NAME = "1.0.0-debug";
  // Field from product flavor: official
  public static final String CHANNEL = "official";
  // Field from build type: debug
  public static final boolean ENABLE_LOGGING = true;
}
