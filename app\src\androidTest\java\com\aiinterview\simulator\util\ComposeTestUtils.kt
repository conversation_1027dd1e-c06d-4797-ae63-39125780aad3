package com.aiinterview.simulator.util

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.ComposeContentTestRule
import androidx.test.platform.app.InstrumentationRegistry
import kotlinx.coroutines.delay

/**
 * Compose UI测试工具类
 * 提供通用的UI测试辅助方法
 */
object ComposeTestUtils {
    
    /**
     * 等待指定时间（毫秒）
     */
    suspend fun waitFor(millis: Long) {
        delay(millis)
    }
    
    /**
     * 等待元素出现
     */
    fun ComposeContentTestRule.waitForElementToAppear(
        matcher: SemanticsMatcher,
        timeoutMillis: Long = 5000
    ) {
        waitUntil(timeoutMillis) {
            onAllNodes(matcher).fetchSemanticsNodes().isNotEmpty()
        }
    }
    
    /**
     * 等待元素消失
     */
    fun ComposeContentTestRule.waitForElementToDisappear(
        matcher: SemanticsMatcher,
        timeoutMillis: Long = 5000
    ) {
        waitUntil(timeoutMillis) {
            onAllNodes(matcher).fetchSemanticsNodes().isEmpty()
        }
    }
    
    /**
     * 等待加载完成（Loading状态消失）
     */
    fun ComposeContentTestRule.waitForLoadingToComplete(timeoutMillis: Long = 10000) {
        waitForElementToDisappear(
            hasText("加载中") or hasContentDescription("加载中"),
            timeoutMillis
        )
    }
    
    /**
     * 验证文本是否存在
     */
    fun ComposeContentTestRule.assertTextExists(text: String) {
        onNodeWithText(text).assertExists()
    }
    
    /**
     * 验证文本是否不存在
     */
    fun ComposeContentTestRule.assertTextNotExists(text: String) {
        onNodeWithText(text).assertDoesNotExist()
    }
    
    /**
     * 验证按钮是否可点击
     */
    fun ComposeContentTestRule.assertButtonClickable(text: String) {
        onNodeWithText(text)
            .assertExists()
            .assertIsEnabled()
            .assertHasClickAction()
    }
    
    /**
     * 验证按钮是否不可点击
     */
    fun ComposeContentTestRule.assertButtonNotClickable(text: String) {
        onNodeWithText(text)
            .assertExists()
            .assertIsNotEnabled()
    }
    
    /**
     * 输入文本到指定字段
     */
    fun ComposeContentTestRule.inputText(label: String, text: String) {
        onNodeWithText(label)
            .performTextInput(text)
    }
    
    /**
     * 点击按钮
     */
    fun ComposeContentTestRule.clickButton(text: String) {
        onNodeWithText(text)
            .assertExists()
            .assertIsEnabled()
            .performClick()
    }
    
    /**
     * 点击具有特定内容描述的元素
     */
    fun ComposeContentTestRule.clickByContentDescription(description: String) {
        onNodeWithContentDescription(description)
            .assertExists()
            .assertIsEnabled()
            .performClick()
    }
    
    /**
     * 验证错误消息显示
     */
    fun ComposeContentTestRule.assertErrorMessage(message: String) {
        onNodeWithText(message)
            .assertExists()
            .assertIsDisplayed()
    }
    
    /**
     * 验证成功消息显示
     */
    fun ComposeContentTestRule.assertSuccessMessage(message: String) {
        onNodeWithText(message)
            .assertExists()
            .assertIsDisplayed()
    }
    
    /**
     * 滚动到指定元素
     */
    fun ComposeContentTestRule.scrollToElement(matcher: SemanticsMatcher) {
        onNode(matcher).performScrollTo()
    }
    
    /**
     * 验证列表项数量
     */
    fun ComposeContentTestRule.assertListItemCount(testTag: String, expectedCount: Int) {
        onNodeWithTag(testTag)
            .onChildren()
            .assertCountEquals(expectedCount)
    }
    
    /**
     * 验证输入框的值
     */
    fun ComposeContentTestRule.assertTextFieldValue(label: String, expectedValue: String) {
        onNodeWithText(label)
            .assert(hasText(expectedValue))
    }
    
    /**
     * 清除输入框内容
     */
    fun ComposeContentTestRule.clearTextField(label: String) {
        onNodeWithText(label)
            .performTextClearance()
    }
    
    /**
     * 验证对话框是否显示
     */
    fun ComposeContentTestRule.assertDialogShown(title: String) {
        onNodeWithText(title)
            .assertExists()
            .assertIsDisplayed()
    }
    
    /**
     * 关闭对话框
     */
    fun ComposeContentTestRule.dismissDialog() {
        // 尝试点击取消按钮
        try {
            onNodeWithText("取消").performClick()
        } catch (e: AssertionError) {
            // 如果没有取消按钮，尝试点击确定按钮
            try {
                onNodeWithText("确定").performClick()
            } catch (e: AssertionError) {
                // 如果都没有，尝试点击关闭按钮
                onNodeWithContentDescription("关闭").performClick()
            }
        }
    }
    
    /**
     * 获取应用上下文
     */
    fun getApplicationContext() = InstrumentationRegistry.getInstrumentation().targetContext
    
    /**
     * 模拟网络延迟
     */
    suspend fun simulateNetworkDelay(millis: Long = 1000) {
        delay(millis)
    }
    
    /**
     * 验证进度条是否显示
     */
    fun ComposeContentTestRule.assertProgressBarShown() {
        onNode(hasProgressBarRangeInfo(ProgressBarRangeInfo.Indeterminate))
            .assertExists()
            .assertIsDisplayed()
    }
    
    /**
     * 验证进度条是否隐藏
     */
    fun ComposeContentTestRule.assertProgressBarHidden() {
        onNode(hasProgressBarRangeInfo(ProgressBarRangeInfo.Indeterminate))
            .assertDoesNotExist()
    }
    
    /**
     * 验证图片是否显示
     */
    fun ComposeContentTestRule.assertImageDisplayed(contentDescription: String) {
        onNodeWithContentDescription(contentDescription)
            .assertExists()
            .assertIsDisplayed()
    }
    
    /**
     * 执行下拉刷新
     */
    fun ComposeContentTestRule.performPullToRefresh(testTag: String) {
        onNodeWithTag(testTag)
            .performTouchInput {
                swipeDown(
                    startY = 0f,
                    endY = 500f
                )
            }
    }
    
    /**
     * 验证底部导航栏选中状态
     */
    fun ComposeContentTestRule.assertBottomNavSelected(label: String) {
        onNodeWithText(label)
            .assertExists()
            .assertIsSelected()
    }
    
    /**
     * 点击底部导航栏项目
     */
    fun ComposeContentTestRule.clickBottomNavItem(label: String) {
        onNodeWithText(label)
            .assertExists()
            .performClick()
    }
}