{"logs": [{"outputFile": "com.aiinterview.simulator.app-mergeOfficialDebugResources-55:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\73f51c4b00c3d8b5bd0709c291d17c64\\transformed\\material3-1.1.2\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,395,509,586,677,786,920,1032,1175,1255,1351,1440,1532,1644,1764,1865,2002,2135,2266,2451,2574,2694,2820,2938,3027,3124,3246,3372,3466,3567,3672,3812,3959,4063,4158,4229,4307,4389,4472,4567,4643,4725,4820,4920,5011,5107,5192,5295,5387,5485,5599,5675,5776", "endColumns": "113,111,113,113,76,90,108,133,111,142,79,95,88,91,111,119,100,136,132,130,184,122,119,125,117,88,96,121,125,93,100,104,139,146,103,94,70,77,81,82,94,75,81,94,99,90,95,84,102,91,97,113,75,100,101", "endOffsets": "164,276,390,504,581,672,781,915,1027,1170,1250,1346,1435,1527,1639,1759,1860,1997,2130,2261,2446,2569,2689,2815,2933,3022,3119,3241,3367,3461,3562,3667,3807,3954,4058,4153,4224,4302,4384,4467,4562,4638,4720,4815,4915,5006,5102,5187,5290,5382,5480,5594,5670,5771,5873"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,445,1463,1540,1631,1740,1874,1986,2129,2209,2305,2394,2486,2598,2718,2819,2956,3089,3220,3405,3528,3648,3774,3892,3981,4078,4200,4326,4420,4521,4626,4766,4913,5017,5315,5470,6203,6357,6541,6909,6985,7067,7162,7262,7353,7449,7534,7637,7729,7827,7941,8017,8118", "endColumns": "113,111,113,113,76,90,108,133,111,142,79,95,88,91,111,119,100,136,132,130,184,122,119,125,117,88,96,121,125,93,100,104,139,146,103,94,70,77,81,82,94,75,81,94,99,90,95,84,102,91,97,113,75,100,101", "endOffsets": "214,326,440,554,1535,1626,1735,1869,1981,2124,2204,2300,2389,2481,2593,2713,2814,2951,3084,3215,3400,3523,3643,3769,3887,3976,4073,4195,4321,4415,4516,4621,4761,4908,5012,5107,5381,5543,6280,6435,6631,6980,7062,7157,7257,7348,7444,7529,7632,7724,7822,7936,8012,8113,8215"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d78fa9dcb2040782f62e575b1696aadd\\transformed\\ui-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,384,485,569,651,740,828,910,980,1051,1136,1224,1296,1376,1446", "endColumns": "92,83,101,100,83,81,88,87,81,69,70,84,87,71,79,69,122", "endOffsets": "193,277,379,480,564,646,735,823,905,975,1046,1131,1219,1291,1371,1441,1564"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1286,1379,5112,5214,5386,5548,5630,5719,5807,5889,5959,6030,6115,6285,6636,6716,6786", "endColumns": "92,83,101,100,83,81,88,87,81,69,70,84,87,71,79,69,122", "endOffsets": "1374,1458,5209,5310,5465,5625,5714,5802,5884,5954,6025,6110,6198,6352,6711,6781,6904"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fd044ab7a523edb1345beec00f65097\\transformed\\core-1.12.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "559,659,761,862,963,1068,1173,6440", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "654,756,857,958,1063,1168,1281,6536"}}]}]}