package com.aiinterview.simulator.presentation.viewmodel

import com.aiinterview.simulator.data.model.*
import com.aiinterview.simulator.data.service.InterviewFlowService
import com.aiinterview.simulator.data.service.TextToSpeechService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.*
import org.junit.Assert.*
import java.io.File

@ExperimentalCoroutinesApi
class InterviewFlowViewModelTest {
    
    @Mock
    private lateinit var interviewFlowService: InterviewFlowService
    
    @Mock
    private lateinit var ttsService: TextToSpeechService
    
    @Mock
    private lateinit var mockFile: File
    
    private lateinit var viewModel: InterviewFlowViewModel
    private val testDispatcher = UnconfinedTestDispatcher()
    
    // Mock StateFlows
    private val mockCurrentSession = MutableStateFlow<InterviewSession?>(null)
    private val mockCurrentQuestion = MutableStateFlow<Question?>(null)
    private val mockFlowState = MutableStateFlow(InterviewFlowService.InterviewFlowState.IDLE)
    private val mockTimeRemaining = MutableStateFlow(300000L) // 5 minutes
    private val mockQuestionProgress = MutableStateFlow(InterviewFlowService.QuestionProgress(0, 0))
    
    private val testPosition = Position(
        id = "test-position",
        name = "测试岗位",
        category = "测试分类",
        level = "测试级别",
        description = "测试描述",
        duration = 30,
        questionCount = 3,
        questionTypes = "[]",
        timeWarnings = "[]"
    )
    
    private val testQuestion = Question(
        id = "test-question-1",
        type = QuestionType.COMPREHENSIVE_ANALYSIS,
        category = "综合分析",
        title = "测试题目",
        content = "这是一个测试问题的内容",
        timeLimit = 300,
        difficulty = 3
    )
    
    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        Dispatchers.setMain(testDispatcher)
        
        // Setup mock StateFlows
        whenever(interviewFlowService.currentSession).thenReturn(mockCurrentSession)
        whenever(interviewFlowService.currentQuestion).thenReturn(mockCurrentQuestion)
        whenever(interviewFlowService.flowState).thenReturn(mockFlowState)
        whenever(interviewFlowService.timeRemaining).thenReturn(mockTimeRemaining)
        whenever(interviewFlowService.questionProgress).thenReturn(mockQuestionProgress)
        
        whenever(ttsService.isPlaying()).thenReturn(false)
        
        viewModel = InterviewFlowViewModel(interviewFlowService, ttsService)
    }
    
    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }
    
    @Test
    fun `startInterview should call service and update UI state`() = runTest {
        // Given
        val userId = "test-user"
        val testSession = InterviewSession(
            id = "test-session",
            userId = userId,
            positionId = testPosition.id,
            status = InterviewStatus.STARTED,
            startTime = System.currentTimeMillis(),
            endTime = null,
            currentQuestionIndex = 0,
            questions = listOf(testQuestion),
            answers = emptyList(),
            evaluation = null
        )
        
        whenever(interviewFlowService.startInterview(userId, testPosition, true))
            .thenReturn(Result.success(testSession))
        whenever(interviewFlowService.startNextQuestion())
            .thenReturn(Result.success(testQuestion))
        
        // When
        viewModel.startInterview(userId, testPosition, true)
        
        // Then
        verify(interviewFlowService).startInterview(userId, testPosition, true)
        verify(interviewFlowService).startNextQuestion()
    }
    
    @Test
    fun `startInterview should handle failure`() = runTest {
        // Given
        val userId = "test-user"
        val error = Exception("Start interview failed")
        
        whenever(interviewFlowService.startInterview(userId, testPosition, true))
            .thenReturn(Result.failure(error))
        
        // When
        viewModel.startInterview(userId, testPosition, true)
        
        // Then
        verify(interviewFlowService).startInterview(userId, testPosition, true)
        assertEquals("开始面试失败: Start interview failed", viewModel.errorMessage.value)
    }
    
    @Test
    fun `readCurrentQuestion should start TTS and update playing state`() = runTest {
        // Given
        mockCurrentQuestion.value = testQuestion
        whenever(interviewFlowService.readCurrentQuestion())
            .thenReturn(Result.success(Unit))
        whenever(ttsService.isPlaying()).thenReturn(true, true, false)
        
        // When
        viewModel.readCurrentQuestion()
        
        // Then
        verify(interviewFlowService).readCurrentQuestion()
        // Playing state should eventually become false
    }
    
    @Test
    fun `readCurrentQuestion should handle failure`() = runTest {
        // Given
        val error = Exception("TTS failed")
        whenever(interviewFlowService.readCurrentQuestion())
            .thenReturn(Result.failure(error))
        
        // When
        viewModel.readCurrentQuestion()
        
        // Then
        verify(interviewFlowService).readCurrentQuestion()
        assertEquals("朗读问题失败: TTS failed", viewModel.errorMessage.value)
        assertFalse(viewModel.isPlayingAudio.value)
    }
    
    @Test
    fun `stopAudioPlayback should stop TTS and update state`() {
        // When
        viewModel.stopAudioPlayback()
        
        // Then
        verify(ttsService).stopAudio()
        assertFalse(viewModel.isPlayingAudio.value)
    }
    
    @Test
    fun `startAnswerRecording should call service`() = runTest {
        // Given
        whenever(interviewFlowService.startAnswerRecording())
            .thenReturn(Result.success(Unit))
        
        // When
        viewModel.startAnswerRecording()
        
        // Then
        verify(interviewFlowService).startAnswerRecording()
    }
    
    @Test
    fun `submitAnswer should call service and update UI state`() = runTest {
        // Given
        val transcription = "这是回答内容"
        val duration = 30
        
        whenever(interviewFlowService.submitAnswer(mockFile, transcription, duration))
            .thenReturn(Result.success(Unit))
        
        // When
        viewModel.submitAnswer(mockFile, transcription, duration)
        
        // Then
        verify(interviewFlowService).submitAnswer(mockFile, transcription, duration)
    }
    
    @Test
    fun `submitAnswer should handle failure`() = runTest {
        // Given
        val transcription = "这是回答内容"
        val duration = 30
        val error = Exception("Submit failed")
        
        whenever(interviewFlowService.submitAnswer(mockFile, transcription, duration))
            .thenReturn(Result.failure(error))
        
        // When
        viewModel.submitAnswer(mockFile, transcription, duration)
        
        // Then
        verify(interviewFlowService).submitAnswer(mockFile, transcription, duration)
        assertEquals("提交回答失败: Submit failed", viewModel.errorMessage.value)
    }
    
    @Test
    fun `skipCurrentQuestion should call service`() = runTest {
        // Given
        whenever(interviewFlowService.skipCurrentQuestion())
            .thenReturn(Result.success(Unit))
        
        // When
        viewModel.skipCurrentQuestion()
        
        // Then
        verify(interviewFlowService).skipCurrentQuestion()
    }
    
    @Test
    fun `pauseInterview should call service and stop audio`() = runTest {
        // Given
        whenever(interviewFlowService.pauseInterview())
            .thenReturn(Result.success(Unit))
        
        // When
        viewModel.pauseInterview()
        
        // Then
        verify(interviewFlowService).pauseInterview()
        verify(ttsService).stopAudio()
    }
    
    @Test
    fun `resumeInterview should call service`() = runTest {
        // Given
        whenever(interviewFlowService.resumeInterview())
            .thenReturn(Result.success(Unit))
        
        // When
        viewModel.resumeInterview()
        
        // Then
        verify(interviewFlowService).resumeInterview()
    }
    
    @Test
    fun `cancelInterview should call service and stop audio`() = runTest {
        // Given
        whenever(interviewFlowService.cancelInterview())
            .thenReturn(Result.success(Unit))
        
        // When
        viewModel.cancelInterview()
        
        // Then
        verify(interviewFlowService).cancelInterview()
        verify(ttsService).stopAudio()
    }
    
    @Test
    fun `UI state should update based on time remaining`() = runTest {
        // Test warning state (5 minutes remaining)
        mockTimeRemaining.value = 5 * 60 * 1000L
        assertTrue(viewModel.shouldShowTimeWarning())
        assertFalse(viewModel.shouldShowTimeCritical())
        
        // Test critical state (1 minute remaining)
        mockTimeRemaining.value = 1 * 60 * 1000L
        assertFalse(viewModel.shouldShowTimeWarning())
        assertTrue(viewModel.shouldShowTimeCritical())
        
        // Test normal state (10 minutes remaining)
        mockTimeRemaining.value = 10 * 60 * 1000L
        assertFalse(viewModel.shouldShowTimeWarning())
        assertFalse(viewModel.shouldShowTimeCritical())
    }
    
    @Test
    fun `UI state should update based on flow state`() = runTest {
        // Test waiting answer state
        mockFlowState.value = InterviewFlowService.InterviewFlowState.WAITING_ANSWER
        assertTrue(viewModel.uiState.value.canStartRecording)
        assertTrue(viewModel.uiState.value.canSkipQuestion)
        
        // Test recording state
        mockFlowState.value = InterviewFlowService.InterviewFlowState.RECORDING
        assertFalse(viewModel.uiState.value.canStartRecording)
        assertFalse(viewModel.uiState.value.canSkipQuestion)
        
        // Test processing state
        mockFlowState.value = InterviewFlowService.InterviewFlowState.PROCESSING
        assertTrue(viewModel.uiState.value.isLoading)
    }
    
    @Test
    fun `getCurrentQuestionInfo should return correct information`() {
        // Given
        mockCurrentQuestion.value = testQuestion
        mockQuestionProgress.value = InterviewFlowService.QuestionProgress(1, 3)
        
        // When
        val questionInfo = viewModel.getCurrentQuestionInfo()
        
        // Then
        assertNotNull(questionInfo)
        assertEquals(testQuestion.title, questionInfo?.title)
        assertEquals(testQuestion.type.displayName, questionInfo?.type)
        assertEquals(testQuestion.content, questionInfo?.content)
        assertEquals(1, questionInfo?.currentIndex)
        assertEquals(3, questionInfo?.totalCount)
    }
    
    @Test
    fun `getCurrentQuestionInfo should return null when no current question`() {
        // Given
        mockCurrentQuestion.value = null
        
        // When
        val questionInfo = viewModel.getCurrentQuestionInfo()
        
        // Then
        assertNull(questionInfo)
    }
    
    @Test
    fun `getFlowStateDescription should return correct descriptions`() {
        val testCases = mapOf(
            InterviewFlowService.InterviewFlowState.IDLE to "准备中",
            InterviewFlowService.InterviewFlowState.PREPARING to "正在准备面试",
            InterviewFlowService.InterviewFlowState.QUESTION_DISPLAY to "请查看题目",
            InterviewFlowService.InterviewFlowState.QUESTION_READING to "正在朗读题目",
            InterviewFlowService.InterviewFlowState.WAITING_ANSWER to "请开始回答",
            InterviewFlowService.InterviewFlowState.RECORDING to "正在录音",
            InterviewFlowService.InterviewFlowState.PROCESSING to "正在处理回答",
            InterviewFlowService.InterviewFlowState.COMPLETED to "面试完成",
            InterviewFlowService.InterviewFlowState.ERROR to "出现错误"
        )
        
        testCases.forEach { (state, expectedDescription) ->
            mockFlowState.value = state
            assertEquals(expectedDescription, viewModel.getFlowStateDescription())
        }
    }
    
    @Test
    fun `isInterviewCompleted should return correct status`() {
        // Test completed state
        mockFlowState.value = InterviewFlowService.InterviewFlowState.COMPLETED
        assertTrue(viewModel.isInterviewCompleted())
        
        // Test other states
        mockFlowState.value = InterviewFlowService.InterviewFlowState.WAITING_ANSWER
        assertFalse(viewModel.isInterviewCompleted())
    }
    
    @Test
    fun `isInterviewActive should return correct status`() {
        val activeStates = listOf(
            InterviewFlowService.InterviewFlowState.QUESTION_DISPLAY,
            InterviewFlowService.InterviewFlowState.QUESTION_READING,
            InterviewFlowService.InterviewFlowState.WAITING_ANSWER,
            InterviewFlowService.InterviewFlowState.RECORDING,
            InterviewFlowService.InterviewFlowState.PROCESSING
        )
        
        // Test active states
        activeStates.forEach { state ->
            mockFlowState.value = state
            assertTrue("State $state should be active", viewModel.isInterviewActive())
        }
        
        // Test inactive states
        mockFlowState.value = InterviewFlowService.InterviewFlowState.IDLE
        assertFalse(viewModel.isInterviewActive())
        
        mockFlowState.value = InterviewFlowService.InterviewFlowState.COMPLETED
        assertFalse(viewModel.isInterviewActive())
    }
    
    @Test
    fun `clearErrorMessage should clear error state`() {
        // Given
        viewModel.startInterview("user", testPosition) // This might set an error
        
        // When
        viewModel.clearErrorMessage()
        
        // Then
        assertNull(viewModel.errorMessage.value)
    }
    
    @Test
    fun `onCleared should cleanup resources`() {
        // When
        viewModel.onCleared()
        
        // Then
        verify(ttsService).stopAudio()
        verify(ttsService).cleanupTempAudioFiles()
    }
}