int attr action 0x7f010000
int attr alpha 0x7f010001
int attr argType 0x7f010002
int attr data 0x7f010003
int attr dataPattern 0x7f010004
int attr destination 0x7f010005
int attr enterAnim 0x7f010006
int attr exitAnim 0x7f010007
int attr font 0x7f010008
int attr fontProviderAuthority 0x7f010009
int attr fontProviderCerts 0x7f01000a
int attr fontProviderFetchStrategy 0x7f01000b
int attr fontProviderFetchTimeout 0x7f01000c
int attr fontProviderPackage 0x7f01000d
int attr fontProviderQuery 0x7f01000e
int attr fontProviderSystemFontFamily 0x7f01000f
int attr fontStyle 0x7f010010
int attr fontVariationSettings 0x7f010011
int attr fontWeight 0x7f010012
int attr graph 0x7f010013
int attr lStar 0x7f010014
int attr launchSingleTop 0x7f010015
int attr mimeType 0x7f010016
int attr navGraph 0x7f010017
int attr nestedScrollViewStyle 0x7f010018
int attr nullable 0x7f010019
int attr popEnterAnim 0x7f01001a
int attr popExitAnim 0x7f01001b
int attr popUpTo 0x7f01001c
int attr popUpToInclusive 0x7f01001d
int attr popUpToSaveState 0x7f01001e
int attr queryPatterns 0x7f01001f
int attr restoreState 0x7f010020
int attr route 0x7f010021
int attr shortcutMatchRequired 0x7f010022
int attr startDestination 0x7f010023
int attr targetPackage 0x7f010024
int attr ttcIndex 0x7f010025
int attr uri 0x7f010026
int color androidx_core_ripple_material_light 0x7f020000
int color androidx_core_secondary_text_default_material_light 0x7f020001
int color call_notification_answer_color 0x7f020002
int color call_notification_decline_color 0x7f020003
int color notification_action_color_filter 0x7f020004
int color notification_icon_bg_color 0x7f020005
int color vector_tint_color 0x7f020006
int color vector_tint_theme_color 0x7f020007
int dimen compat_button_inset_horizontal_material 0x7f030000
int dimen compat_button_inset_vertical_material 0x7f030001
int dimen compat_button_padding_horizontal_material 0x7f030002
int dimen compat_button_padding_vertical_material 0x7f030003
int dimen compat_control_corner_material 0x7f030004
int dimen compat_notification_large_icon_max_height 0x7f030005
int dimen compat_notification_large_icon_max_width 0x7f030006
int dimen notification_action_icon_size 0x7f030007
int dimen notification_action_text_size 0x7f030008
int dimen notification_big_circle_margin 0x7f030009
int dimen notification_content_margin_start 0x7f03000a
int dimen notification_large_icon_height 0x7f03000b
int dimen notification_large_icon_width 0x7f03000c
int dimen notification_main_column_padding_top 0x7f03000d
int dimen notification_media_narrow_margin 0x7f03000e
int dimen notification_right_icon_size 0x7f03000f
int dimen notification_right_side_padding_top 0x7f030010
int dimen notification_small_icon_background_padding 0x7f030011
int dimen notification_small_icon_size_as_large 0x7f030012
int dimen notification_subtext_size 0x7f030013
int dimen notification_top_pad 0x7f030014
int dimen notification_top_pad_large_text 0x7f030015
int drawable ic_call_answer 0x7f040000
int drawable ic_call_answer_low 0x7f040001
int drawable ic_call_answer_video 0x7f040002
int drawable ic_call_answer_video_low 0x7f040003
int drawable ic_call_decline 0x7f040004
int drawable ic_call_decline_low 0x7f040005
int drawable notification_action_background 0x7f040006
int drawable notification_bg 0x7f040007
int drawable notification_bg_low 0x7f040008
int drawable notification_bg_low_normal 0x7f040009
int drawable notification_bg_low_pressed 0x7f04000a
int drawable notification_bg_normal 0x7f04000b
int drawable notification_bg_normal_pressed 0x7f04000c
int drawable notification_icon_background 0x7f04000d
int drawable notification_oversize_large_icon_bg 0x7f04000e
int drawable notification_template_icon_bg 0x7f04000f
int drawable notification_template_icon_low_bg 0x7f040010
int drawable notification_tile_bg 0x7f040011
int drawable notify_panel_notification_icon_bg 0x7f040012
int id accessibility_action_clickable_span 0x7f050000
int id accessibility_custom_action_0 0x7f050001
int id accessibility_custom_action_1 0x7f050002
int id accessibility_custom_action_10 0x7f050003
int id accessibility_custom_action_11 0x7f050004
int id accessibility_custom_action_12 0x7f050005
int id accessibility_custom_action_13 0x7f050006
int id accessibility_custom_action_14 0x7f050007
int id accessibility_custom_action_15 0x7f050008
int id accessibility_custom_action_16 0x7f050009
int id accessibility_custom_action_17 0x7f05000a
int id accessibility_custom_action_18 0x7f05000b
int id accessibility_custom_action_19 0x7f05000c
int id accessibility_custom_action_2 0x7f05000d
int id accessibility_custom_action_20 0x7f05000e
int id accessibility_custom_action_21 0x7f05000f
int id accessibility_custom_action_22 0x7f050010
int id accessibility_custom_action_23 0x7f050011
int id accessibility_custom_action_24 0x7f050012
int id accessibility_custom_action_25 0x7f050013
int id accessibility_custom_action_26 0x7f050014
int id accessibility_custom_action_27 0x7f050015
int id accessibility_custom_action_28 0x7f050016
int id accessibility_custom_action_29 0x7f050017
int id accessibility_custom_action_3 0x7f050018
int id accessibility_custom_action_30 0x7f050019
int id accessibility_custom_action_31 0x7f05001a
int id accessibility_custom_action_4 0x7f05001b
int id accessibility_custom_action_5 0x7f05001c
int id accessibility_custom_action_6 0x7f05001d
int id accessibility_custom_action_7 0x7f05001e
int id accessibility_custom_action_8 0x7f05001f
int id accessibility_custom_action_9 0x7f050020
int id action_container 0x7f050021
int id action_divider 0x7f050022
int id action_image 0x7f050023
int id action_text 0x7f050024
int id actions 0x7f050025
int id androidx_compose_ui_view_composition_context 0x7f050026
int id async 0x7f050027
int id blocking 0x7f050028
int id chronometer 0x7f050029
int id compose_view_saveable_id_tag 0x7f05002a
int id consume_window_insets_tag 0x7f05002b
int id dialog_button 0x7f05002c
int id edit_text_id 0x7f05002d
int id forever 0x7f05002e
int id hide_ime_id 0x7f05002f
int id hide_in_inspector_tag 0x7f050030
int id icon 0x7f050031
int id icon_group 0x7f050032
int id info 0x7f050033
int id inspection_slot_table_set 0x7f050034
int id is_pooling_container_tag 0x7f050035
int id italic 0x7f050036
int id line1 0x7f050037
int id line3 0x7f050038
int id nav_controller_view_tag 0x7f050039
int id normal 0x7f05003a
int id notification_background 0x7f05003b
int id notification_main_column 0x7f05003c
int id notification_main_column_container 0x7f05003d
int id pooling_container_listener_holder_tag 0x7f05003e
int id report_drawn 0x7f05003f
int id right_icon 0x7f050040
int id right_side 0x7f050041
int id tag_accessibility_actions 0x7f050042
int id tag_accessibility_clickable_spans 0x7f050043
int id tag_accessibility_heading 0x7f050044
int id tag_accessibility_pane_title 0x7f050045
int id tag_on_apply_window_listener 0x7f050046
int id tag_on_receive_content_listener 0x7f050047
int id tag_on_receive_content_mime_types 0x7f050048
int id tag_screen_reader_focusable 0x7f050049
int id tag_state_description 0x7f05004a
int id tag_transition_group 0x7f05004b
int id tag_unhandled_key_event_manager 0x7f05004c
int id tag_unhandled_key_listeners 0x7f05004d
int id tag_window_insets_animation_callback 0x7f05004e
int id text 0x7f05004f
int id text2 0x7f050050
int id time 0x7f050051
int id title 0x7f050052
int id view_tree_lifecycle_owner 0x7f050053
int id view_tree_on_back_pressed_dispatcher_owner 0x7f050054
int id view_tree_saved_state_registry_owner 0x7f050055
int id view_tree_view_model_store_owner 0x7f050056
int id wrapped_composition_tag 0x7f050057
int integer status_bar_notification_info_maxnum 0x7f060000
int layout custom_dialog 0x7f070000
int layout ime_base_split_test_activity 0x7f070001
int layout ime_secondary_split_test_activity 0x7f070002
int layout notification_action 0x7f070003
int layout notification_action_tombstone 0x7f070004
int layout notification_template_custom_big 0x7f070005
int layout notification_template_icon_group 0x7f070006
int layout notification_template_part_chronometer 0x7f070007
int layout notification_template_part_time 0x7f070008
int string androidx_startup 0x7f080000
int string app_name 0x7f080001
int string app_store_age_rating 0x7f080002
int string app_store_age_rating_description 0x7f080003
int string app_store_category 0x7f080004
int string app_store_changelog_v1_0_0 0x7f080005
int string app_store_description 0x7f080006
int string app_store_developer 0x7f080007
int string app_store_developer_website 0x7f080008
int string app_store_keywords 0x7f080009
int string app_store_name 0x7f08000a
int string app_store_privacy_policy_url 0x7f08000b
int string app_store_short_description 0x7f08000c
int string app_store_subcategory 0x7f08000d
int string app_store_support_email 0x7f08000e
int string app_store_tagline 0x7f08000f
int string baidu_app_description 0x7f080010
int string bottom_sheet_collapse_description 0x7f080011
int string bottom_sheet_dismiss_description 0x7f080012
int string bottom_sheet_drag_handle_description 0x7f080013
int string bottom_sheet_expand_description 0x7f080014
int string cache_clearing 0x7f080015
int string call_notification_answer_action 0x7f080016
int string call_notification_answer_video_action 0x7f080017
int string call_notification_decline_action 0x7f080018
int string call_notification_hang_up_action 0x7f080019
int string call_notification_incoming_text 0x7f08001a
int string call_notification_ongoing_text 0x7f08001b
int string call_notification_screening_text 0x7f08001c
int string cancel 0x7f08001d
int string close 0x7f08001e
int string close_drawer 0x7f08001f
int string close_sheet 0x7f080020
int string collapsed 0x7f080021
int string crash_report_cancel 0x7f080022
int string crash_report_message 0x7f080023
int string crash_report_send 0x7f080024
int string crash_report_title 0x7f080025
int string date_input_headline 0x7f080026
int string date_input_headline_description 0x7f080027
int string date_input_invalid_for_pattern 0x7f080028
int string date_input_invalid_not_allowed 0x7f080029
int string date_input_invalid_year_range 0x7f08002a
int string date_input_label 0x7f08002b
int string date_input_no_input_description 0x7f08002c
int string date_input_title 0x7f08002d
int string date_picker_headline 0x7f08002e
int string date_picker_headline_description 0x7f08002f
int string date_picker_navigate_to_year_description 0x7f080030
int string date_picker_no_selection_description 0x7f080031
int string date_picker_scroll_to_earlier_years 0x7f080032
int string date_picker_scroll_to_later_years 0x7f080033
int string date_picker_switch_to_calendar_mode 0x7f080034
int string date_picker_switch_to_day_selection 0x7f080035
int string date_picker_switch_to_input_mode 0x7f080036
int string date_picker_switch_to_next_month 0x7f080037
int string date_picker_switch_to_previous_month 0x7f080038
int string date_picker_switch_to_year_selection 0x7f080039
int string date_picker_title 0x7f08003a
int string date_picker_today_description 0x7f08003b
int string date_picker_year_picker_pane_title 0x7f08003c
int string date_range_input_invalid_range_input 0x7f08003d
int string date_range_input_title 0x7f08003e
int string date_range_picker_day_in_range 0x7f08003f
int string date_range_picker_end_headline 0x7f080040
int string date_range_picker_scroll_to_next_month 0x7f080041
int string date_range_picker_scroll_to_previous_month 0x7f080042
int string date_range_picker_start_headline 0x7f080043
int string date_range_picker_title 0x7f080044
int string default_error_message 0x7f080045
int string default_popup_window_title 0x7f080046
int string degradation_basic 0x7f080047
int string degradation_cache_only 0x7f080048
int string degradation_lightweight 0x7f080049
int string degradation_offline_mode 0x7f08004a
int string degradation_simplified 0x7f08004b
int string dialog 0x7f08004c
int string dropdown_menu 0x7f08004d
int string error_microphone_permission 0x7f08004e
int string error_network 0x7f08004f
int string error_no_internet 0x7f080050
int string error_server 0x7f080051
int string error_speech_recognition 0x7f080052
int string error_timeout 0x7f080053
int string error_token_expired 0x7f080054
int string error_unauthorized 0x7f080055
int string error_unknown 0x7f080056
int string expanded 0x7f080057
int string huawei_app_description 0x7f080058
int string in_progress 0x7f080059
int string indeterminate 0x7f08005a
int string m3c_bottom_sheet_pane_title 0x7f08005b
int string memory_critical 0x7f08005c
int string memory_low 0x7f08005d
int string navigation_menu 0x7f08005e
int string network_connected 0x7f08005f
int string network_connecting 0x7f080060
int string network_offline 0x7f080061
int string network_poor 0x7f080062
int string not_selected 0x7f080063
int string off 0x7f080064
int string ok 0x7f080065
int string on 0x7f080066
int string oppo_app_description 0x7f080067
int string permission_microphone_description 0x7f080068
int string permission_network_description 0x7f080069
int string permission_storage_description 0x7f08006a
int string qihoo_app_description 0x7f08006b
int string range_end 0x7f08006c
int string range_start 0x7f08006d
int string rate_app_message 0x7f08006e
int string rate_app_title 0x7f08006f
int string rate_later 0x7f080070
int string rate_never 0x7f080071
int string rate_now 0x7f080072
int string retry 0x7f080073
int string screenshot_1_description 0x7f080074
int string screenshot_2_description 0x7f080075
int string screenshot_3_description 0x7f080076
int string screenshot_4_description 0x7f080077
int string screenshot_5_description 0x7f080078
int string search_bar_search 0x7f080079
int string selected 0x7f08007a
int string settings 0x7f08007b
int string share_app_message 0x7f08007c
int string share_app_title 0x7f08007d
int string snackbar_dismiss 0x7f08007e
int string startup_optimizing 0x7f08007f
int string status_bar_notification_info_overflow 0x7f080080
int string suggestions_available 0x7f080081
int string switch_role 0x7f080082
int string tab 0x7f080083
int string template_percent 0x7f080084
int string tencent_app_description 0x7f080085
int string time_picker_am 0x7f080086
int string time_picker_hour 0x7f080087
int string time_picker_hour_24h_suffix 0x7f080088
int string time_picker_hour_selection 0x7f080089
int string time_picker_hour_suffix 0x7f08008a
int string time_picker_hour_text_field 0x7f08008b
int string time_picker_minute 0x7f08008c
int string time_picker_minute_selection 0x7f08008d
int string time_picker_minute_suffix 0x7f08008e
int string time_picker_minute_text_field 0x7f08008f
int string time_picker_period_toggle_description 0x7f080090
int string time_picker_pm 0x7f080091
int string tooltip_long_press_label 0x7f080092
int string tooltip_pane_description 0x7f080093
int string update_available_message 0x7f080094
int string update_available_title 0x7f080095
int string update_ignore 0x7f080096
int string update_later 0x7f080097
int string update_now 0x7f080098
int string vivo_app_description 0x7f080099
int string xiaomi_app_description 0x7f08009a
int style DialogWindowTheme 0x7f090000
int style FloatingDialogTheme 0x7f090001
int style FloatingDialogWindowTheme 0x7f090002
int style TextAppearance_Compat_Notification 0x7f090003
int style TextAppearance_Compat_Notification_Info 0x7f090004
int style TextAppearance_Compat_Notification_Line2 0x7f090005
int style TextAppearance_Compat_Notification_Time 0x7f090006
int style TextAppearance_Compat_Notification_Title 0x7f090007
int style Theme_AIInterviewSimulator 0x7f090008
int style Widget_Compat_NotificationActionContainer 0x7f090009
int style Widget_Compat_NotificationActionText 0x7f09000a
int[] styleable ActivityNavigator { 0x01010003, 0x7f010000, 0x7f010003, 0x7f010004, 0x7f010024 }
int styleable ActivityNavigator_android_name 0
int styleable ActivityNavigator_action 1
int styleable ActivityNavigator_data 2
int styleable ActivityNavigator_dataPattern 3
int styleable ActivityNavigator_targetPackage 4
int[] styleable Capability { 0x7f01001f, 0x7f010022 }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x7f010001, 0x7f010014 }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable FontFamily { 0x7f010009, 0x7f01000a, 0x7f01000b, 0x7f01000c, 0x7f01000d, 0x7f01000e, 0x7f01000f }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int styleable FontFamily_fontProviderSystemFontFamily 6
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f010008, 0x7f010010, 0x7f010011, 0x7f010012, 0x7f010025 }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable NavAction { 0x010100d0, 0x7f010005, 0x7f010006, 0x7f010007, 0x7f010015, 0x7f01001a, 0x7f01001b, 0x7f01001c, 0x7f01001d, 0x7f01001e, 0x7f010020 }
int styleable NavAction_android_id 0
int styleable NavAction_destination 1
int styleable NavAction_enterAnim 2
int styleable NavAction_exitAnim 3
int styleable NavAction_launchSingleTop 4
int styleable NavAction_popEnterAnim 5
int styleable NavAction_popExitAnim 6
int styleable NavAction_popUpTo 7
int styleable NavAction_popUpToInclusive 8
int styleable NavAction_popUpToSaveState 9
int styleable NavAction_restoreState 10
int[] styleable NavArgument { 0x01010003, 0x010101ed, 0x7f010002, 0x7f010019 }
int styleable NavArgument_android_name 0
int styleable NavArgument_android_defaultValue 1
int styleable NavArgument_argType 2
int styleable NavArgument_nullable 3
int[] styleable NavDeepLink { 0x010104ee, 0x7f010000, 0x7f010016, 0x7f010026 }
int styleable NavDeepLink_android_autoVerify 0
int styleable NavDeepLink_action 1
int styleable NavDeepLink_mimeType 2
int styleable NavDeepLink_uri 3
int[] styleable NavGraphNavigator { 0x7f010023 }
int styleable NavGraphNavigator_startDestination 0
int[] styleable NavHost { 0x7f010017 }
int styleable NavHost_navGraph 0
int[] styleable NavInclude { 0x7f010013 }
int styleable NavInclude_graph 0
int[] styleable Navigator { 0x01010001, 0x010100d0, 0x7f010021 }
int styleable Navigator_android_label 0
int styleable Navigator_android_id 1
int styleable Navigator_route 2
int xml backup_rules 0x7f0b0000
int xml data_extraction_rules 0x7f0b0001
