package com.aiinterview.simulator.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

// Room entity version for database storage
@Entity(tableName = "interview_sessions")
data class InterviewSessionEntity(
    @PrimaryKey
    val id: String,
    val userId: String,
    val positionId: String,
    val status: String, // CREATED, STARTED, IN_PROGRESS, COMPLETED, CANCELLED
    val startTime: Long,
    val endTime: Long? = null,
    val currentQuestionIndex: Int = 0,
    val questionsJson: String, // JSON格式存储问题列表
    val answersJson: String? = null, // JSON格式存储答案列表
    val evaluationJson: String? = null // JSON格式存储评价结果
)

// Business logic version with proper data classes
data class InterviewSession(
    val id: String,
    val userId: String,
    val positionId: String,
    val status: InterviewStatus,
    val startTime: Long,
    val endTime: Long? = null,
    val currentQuestionIndex: Int = 0,
    val questions: List<Question> = emptyList(),
    val answers: List<Answer> = emptyList(),
    val evaluation: InterviewEvaluation? = null
)