package com.aiinterview.simulator.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.aiinterview.simulator.data.model.*
import com.aiinterview.simulator.data.service.InterviewFlowService
import com.aiinterview.simulator.data.service.TextToSpeechService
import com.aiinterview.simulator.domain.util.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.io.File
import javax.inject.Inject

@HiltViewModel
class InterviewFlowViewModel @Inject constructor(
    private val interviewFlowService: InterviewFlowService,
    private val ttsService: TextToSpeechService
) : ViewModel() {
    
    // 面试流程状态
    val currentSession: StateFlow<InterviewSession?> = interviewFlowService.currentSession
    val currentQuestion: StateFlow<Question?> = interviewFlowService.currentQuestion
    val flowState: StateFlow<InterviewFlowService.InterviewFlowState> = interviewFlowService.flowState
    val timeRemaining: StateFlow<Long> = interviewFlowService.timeRemaining
    val questionProgress: StateFlow<InterviewFlowService.QuestionProgress> = interviewFlowService.questionProgress
    
    // UI状态
    private val _uiState = MutableStateFlow(InterviewFlowUiState())
    val uiState: StateFlow<InterviewFlowUiState> = _uiState.asStateFlow()
    
    private val _isPlayingAudio = MutableStateFlow(false)
    val isPlayingAudio: StateFlow<Boolean> = _isPlayingAudio.asStateFlow()
    
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    data class InterviewFlowUiState(
        val isLoading: Boolean = false,
        val canStartRecording: Boolean = true,
        val canSkipQuestion: Boolean = true,
        val showTimeWarning: Boolean = false,
        val showTimeCritical: Boolean = false
    )
    
    init {
        // 监听时间变化，更新UI状态
        viewModelScope.launch {
            timeRemaining.collect { time ->
                _uiState.value = _uiState.value.copy(
                    showTimeWarning = time <= 5 * 60 * 1000L && time > 1 * 60 * 1000L,
                    showTimeCritical = time <= 1 * 60 * 1000L && time > 0
                )
            }
        }
        
        // 监听流程状态变化
        viewModelScope.launch {
            flowState.collect { state ->
                _uiState.value = _uiState.value.copy(
                    isLoading = state == InterviewFlowService.InterviewFlowState.PROCESSING,
                    canStartRecording = state == InterviewFlowService.InterviewFlowState.WAITING_ANSWER,
                    canSkipQuestion = state in listOf(
                        InterviewFlowService.InterviewFlowState.WAITING_ANSWER,
                        InterviewFlowService.InterviewFlowState.QUESTION_DISPLAY
                    )
                )
            }
        }
    }
    
    /**
     * 开始面试
     */
    fun startInterview(userId: String, position: Position, useAIGeneration: Boolean = true) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            val result = interviewFlowService.startInterview(userId, position, useAIGeneration)
            
            result.fold(
                onSuccess = { session ->
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    // 面试开始成功，自动开始第一个问题
                    startNextQuestion()
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    _errorMessage.value = "开始面试失败: ${error.message}"
                }
            )
        }
    }
    
    /**
     * 开始下一个问题
     */
    fun startNextQuestion() {
        viewModelScope.launch {
            val result = interviewFlowService.startNextQuestion()
            
            result.fold(
                onSuccess = { question ->
                    // 问题准备完成，可以开始朗读或等待用户操作
                },
                onFailure = { error ->
                    if (error.message?.contains("面试已完成") == true) {
                        // 面试完成，不需要显示错误
                    } else {
                        _errorMessage.value = "获取下一个问题失败: ${error.message}"
                    }
                }
            )
        }
    }
    
    /**
     * 朗读当前问题
     */
    fun readCurrentQuestion() {
        viewModelScope.launch {
            _isPlayingAudio.value = true
            
            val result = interviewFlowService.readCurrentQuestion()
            
            result.fold(
                onSuccess = {
                    // 朗读开始成功
                },
                onFailure = { error ->
                    _isPlayingAudio.value = false
                    _errorMessage.value = "朗读问题失败: ${error.message}"
                }
            )
            
            // 监听音频播放状态
            while (_isPlayingAudio.value && ttsService.isPlaying()) {
                kotlinx.coroutines.delay(100)
            }
            _isPlayingAudio.value = false
        }
    }
    
    /**
     * 停止音频播放
     */
    fun stopAudioPlayback() {
        ttsService.stopAudio()
        _isPlayingAudio.value = false
    }
    
    /**
     * 开始录音回答
     */
    fun startAnswerRecording() {
        viewModelScope.launch {
            val result = interviewFlowService.startAnswerRecording()
            
            result.fold(
                onSuccess = {
                    // 录音开始成功
                },
                onFailure = { error ->
                    _errorMessage.value = "开始录音失败: ${error.message}"
                }
            )
        }
    }
    
    /**
     * 提交问题回答
     */
    fun submitAnswer(audioFile: File, transcription: String, duration: Int) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            val result = interviewFlowService.submitAnswer(audioFile, transcription, duration)
            
            result.fold(
                onSuccess = {
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    // 回答提交成功，流程会自动进入下一个问题或完成面试
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    _errorMessage.value = "提交回答失败: ${error.message}"
                }
            )
        }
    }
    
    /**
     * 跳过当前问题
     */
    fun skipCurrentQuestion() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            val result = interviewFlowService.skipCurrentQuestion()
            
            result.fold(
                onSuccess = {
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    // 跳过成功，流程会自动进入下一个问题
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    _errorMessage.value = "跳过问题失败: ${error.message}"
                }
            )
        }
    }
    
    /**
     * 暂停面试
     */
    fun pauseInterview() {
        viewModelScope.launch {
            val result = interviewFlowService.pauseInterview()
            
            result.fold(
                onSuccess = {
                    stopAudioPlayback()
                },
                onFailure = { error ->
                    _errorMessage.value = "暂停面试失败: ${error.message}"
                }
            )
        }
    }
    
    /**
     * 恢复面试
     */
    fun resumeInterview() {
        viewModelScope.launch {
            val result = interviewFlowService.resumeInterview()
            
            result.fold(
                onSuccess = {
                    // 恢复成功
                },
                onFailure = { error ->
                    _errorMessage.value = "恢复面试失败: ${error.message}"
                }
            )
        }
    }
    
    /**
     * 取消面试
     */
    fun cancelInterview() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            val result = interviewFlowService.cancelInterview()
            
            result.fold(
                onSuccess = {
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    stopAudioPlayback()
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    _errorMessage.value = "取消面试失败: ${error.message}"
                }
            )
        }
    }
    
    /**
     * 检查是否可以进行下一步操作
     */
    fun canProceedToNext(): Boolean {
        return interviewFlowService.canProceedToNext()
    }
    
    /**
     * 获取面试进度
     */
    fun getInterviewProgress(): Float {
        return interviewFlowService.getInterviewProgress()
    }
    
    /**
     * 获取格式化的剩余时间
     */
    fun getFormattedTimeRemaining(): String {
        return interviewFlowService.getFormattedTimeRemaining()
    }
    
    /**
     * 获取当前问题的关键信息
     */
    fun getCurrentQuestionInfo(): QuestionInfo? {
        val question = currentQuestion.value ?: return null
        val progress = questionProgress.value
        
        return QuestionInfo(
            title = question.title,
            type = question.type.displayName,
            content = question.content,
            timeLimit = question.timeLimit,
            currentIndex = progress.current,
            totalCount = progress.total,
            keyPoints = question.keyPoints,
            backgroundInfo = question.backgroundInfo
        )
    }
    
    /**
     * 检查是否显示时间警告
     */
    fun shouldShowTimeWarning(): Boolean {
        return _uiState.value.showTimeWarning
    }
    
    /**
     * 检查是否显示时间紧急提醒
     */
    fun shouldShowTimeCritical(): Boolean {
        return _uiState.value.showTimeCritical
    }
    
    /**
     * 清除错误消息
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }
    
    /**
     * 获取面试状态描述
     */
    fun getFlowStateDescription(): String {
        return when (flowState.value) {
            InterviewFlowService.InterviewFlowState.IDLE -> "准备中"
            InterviewFlowService.InterviewFlowState.PREPARING -> "正在准备面试"
            InterviewFlowService.InterviewFlowState.QUESTION_DISPLAY -> "请查看题目"
            InterviewFlowService.InterviewFlowState.QUESTION_READING -> "正在朗读题目"
            InterviewFlowService.InterviewFlowState.WAITING_ANSWER -> "请开始回答"
            InterviewFlowService.InterviewFlowState.RECORDING -> "正在录音"
            InterviewFlowService.InterviewFlowState.PROCESSING -> "正在处理回答"
            InterviewFlowService.InterviewFlowState.COMPLETED -> "面试完成"
            InterviewFlowService.InterviewFlowState.ERROR -> "出现错误"
        }
    }
    
    /**
     * 检查面试是否完成
     */
    fun isInterviewCompleted(): Boolean {
        return flowState.value == InterviewFlowService.InterviewFlowState.COMPLETED
    }
    
    /**
     * 检查面试是否正在进行
     */
    fun isInterviewActive(): Boolean {
        return flowState.value in listOf(
            InterviewFlowService.InterviewFlowState.QUESTION_DISPLAY,
            InterviewFlowService.InterviewFlowState.QUESTION_READING,
            InterviewFlowService.InterviewFlowState.WAITING_ANSWER,
            InterviewFlowService.InterviewFlowState.RECORDING,
            InterviewFlowService.InterviewFlowState.PROCESSING
        )
    }
    
    override fun onCleared() {
        super.onCleared()
        // 清理资源
        stopAudioPlayback()
        ttsService.cleanupTempAudioFiles()
    }
    
    data class QuestionInfo(
        val title: String,
        val type: String,
        val content: String,
        val timeLimit: Int,
        val currentIndex: Int,
        val totalCount: Int,
        val keyPoints: List<String>,
        val backgroundInfo: String?
    )
}