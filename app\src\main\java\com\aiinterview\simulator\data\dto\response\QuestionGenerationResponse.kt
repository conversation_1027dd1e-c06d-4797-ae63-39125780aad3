package com.aiinterview.simulator.data.dto.response

/**
 * AI问题生成响应DTO
 */
data class QuestionGenerationResponse(
    val success: Boolean,
    val question: GeneratedQuestion?,
    val errorMessage: String? = null,
    val provider: String, // "wenxin", "tongyi"
    val requestId: String? = null
)

data class GeneratedQuestion(
    val id: String,
    val type: String,
    val category: String,
    val title: String,
    val content: String,
    val backgroundInfo: String? = null,
    val keyPoints: List<String> = emptyList(),
    val timeLimit: Int,
    val difficulty: Int,
    val followUpQuestions: List<String> = emptyList()
)

/**
 * 文心一言API响应格式
 */
data class WenxinQuestionResponse(
    val id: String,
    val object: String,
    val created: Long,
    val result: String,
    val is_truncated: Boolean,
    val need_clear_history: Boolean,
    val usage: WenxinUsage
)

data class WenxinUsage(
    val prompt_tokens: Int,
    val completion_tokens: Int,
    val total_tokens: Int
)

/**
 * 通义千问API响应格式
 */
data class TongyiQuestionResponse(
    val output: TongyiOutput,
    val usage: TongyiUsage,
    val request_id: String
)

data class TongyiOutput(
    val text: String?,
    val finish_reason: String,
    val choices: List<TongyiChoice>? = null
)

data class TongyiChoice(
    val finish_reason: String,
    val message: TongyiMessage
)

data class TongyiMessage(
    val role: String,
    val content: String
)

data class TongyiUsage(
    val input_tokens: Int,
    val output_tokens: Int,
    val total_tokens: Int
)