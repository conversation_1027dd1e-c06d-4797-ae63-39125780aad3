package com.aiinterview.simulator.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "positions")
data class Position(
    @PrimaryKey
    val id: String,
    val name: String,
    val category: String,
    val level: String,
    val description: String,
    val duration: Int, // 面试总时长（分钟）
    val questionCount: Int, // 题目数量
    val questionTypes: String, // JSON格式存储题目类型列表
    val timeWarnings: String // JSON格式存储时间提醒点
)