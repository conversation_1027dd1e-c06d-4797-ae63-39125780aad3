package com.aiinterview.simulator.util

import android.content.Context
import android.content.pm.PackageManager
import com.aiinterview.simulator.BuildConfig
import java.text.SimpleDateFormat
import java.util.*

/**
 * 版本管理工具类
 * 用于管理应用版本信息、版本检查和更新相关功能
 */
class VersionManager(private val context: Context) {
    
    companion object {
        private const val TAG = "VersionManager"
        
        // 版本比较结果常量
        const val VERSION_EQUAL = 0      // 版本相等
        const val VERSION_NEWER = 1      // 当前版本更新
        const val VERSION_OLDER = -1     // 当前版本较旧
    }
    
    /**
     * 获取当前应用版本名称
     * @return 版本名称字符串
     */
    fun getCurrentVersionName(): String {
        return BuildConfig.VERSION_NAME
    }
    
    /**
     * 获取当前应用版本号
     * @return 版本号整数
     */
    fun getCurrentVersionCode(): Int {
        return BuildConfig.VERSION_CODE
    }
    
    /**
     * 获取应用包名
     * @return 应用包名字符串
     */
    fun getPackageName(): String {
        return BuildConfig.APPLICATION_ID
    }
    
    /**
     * 获取应用构建时间
     * @return 构建时间字符串
     */
    fun getBuildTime(): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            val buildTime = packageInfo.lastUpdateTime
            val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
            formatter.format(Date(buildTime))
        } catch (e: PackageManager.NameNotFoundException) {
            "未知时间"
        }
    }
    
    /**
     * 获取详细的版本信息
     * @return 包含版本、渠道、构建时间等信息的字符串
     */
    fun getDetailedVersionInfo(): String {
        return buildString {
            appendLine("应用名称：AI面试模拟器")
            appendLine("版本名称：${getCurrentVersionName()}")
            appendLine("版本号：${getCurrentVersionCode()}")
            appendLine("包名：${getPackageName()}")
            appendLine("渠道：${ChannelUtil.getChannelDisplayName()}")
            appendLine("构建类型：${ChannelUtil.getBuildType()}")
            appendLine("构建时间：${getBuildTime()}")
            if (ChannelUtil.isDebugBuild()) {
                appendLine("调试模式：已启用")
            }
        }
    }
    
    /**
     * 比较两个版本号
     * @param version1 版本号1（如：1.2.3）
     * @param version2 版本号2（如：1.2.4）
     * @return 比较结果：1表示version1更新，-1表示version1较旧，0表示相等
     */
    fun compareVersions(version1: String, version2: String): Int {
        val v1Parts = version1.split(".").map { it.toIntOrNull() ?: 0 }
        val v2Parts = version2.split(".").map { it.toIntOrNull() ?: 0 }
        
        val maxLength = maxOf(v1Parts.size, v2Parts.size)
        
        for (i in 0 until maxLength) {
            val v1Part = v1Parts.getOrNull(i) ?: 0
            val v2Part = v2Parts.getOrNull(i) ?: 0
            
            when {
                v1Part > v2Part -> return VERSION_NEWER
                v1Part < v2Part -> return VERSION_OLDER
            }
        }
        
        return VERSION_EQUAL
    }
    
    /**
     * 检查是否有新版本可用
     * @param latestVersion 最新版本号字符串
     * @return true表示有新版本，false表示当前已是最新版本
     */
    fun hasNewVersion(latestVersion: String): Boolean {
        return compareVersions(getCurrentVersionName(), latestVersion) == VERSION_OLDER
    }
    
    /**
     * 获取版本更新信息
     * @param latestVersion 最新版本信息
     * @return 格式化的更新信息字符串
     */
    fun getUpdateInfo(latestVersion: String, updateDescription: String = ""): String {
        return buildString {
            appendLine("发现新版本：$latestVersion")
            appendLine("当前版本：${getCurrentVersionName()}")
            appendLine()
            if (updateDescription.isNotEmpty()) {
                appendLine("更新内容：")
                appendLine(updateDescription)
            }
        }
    }
    
    /**
     * 生成版本报告（用于调试和问题排查）
     * @return 详细的版本报告字符串
     */
    fun generateVersionReport(): String {
        return buildString {
            appendLine("=== AI面试模拟器版本报告 ===")
            appendLine()
            appendLine(getDetailedVersionInfo())
            appendLine()
            appendLine("系统信息：")
            appendLine("Android版本：${android.os.Build.VERSION.RELEASE}")
            appendLine("API级别：${android.os.Build.VERSION.SDK_INT}")
            appendLine("设备型号：${android.os.Build.MODEL}")
            appendLine("设备厂商：${android.os.Build.MANUFACTURER}")
            appendLine()
            appendLine("渠道信息：")
            appendLine("渠道标识：${ChannelUtil.getCurrentChannel()}")
            appendLine("渠道名称：${ChannelUtil.getChannelDisplayName()}")
            appendLine("客服信息：${ChannelUtil.getCustomerServiceInfo()}")
            appendLine()
            appendLine("报告生成时间：${SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())}")
            appendLine("=== 报告结束 ===")
        }
    }
    
    /**
     * 检查版本兼容性
     * @param minSupportedVersion 最低支持的版本号
     * @return true表示当前版本兼容，false表示需要强制更新
     */
    fun isVersionCompatible(minSupportedVersion: String): Boolean {
        return compareVersions(getCurrentVersionName(), minSupportedVersion) >= VERSION_EQUAL
    }
    
    /**
     * 获取版本升级建议
     * @param latestVersion 最新版本号
     * @param minSupportedVersion 最低支持版本号
     * @return 升级建议类型
     */
    fun getUpdateRecommendation(latestVersion: String, minSupportedVersion: String): UpdateRecommendation {
        val currentVersion = getCurrentVersionName()
        
        return when {
            !isVersionCompatible(minSupportedVersion) -> UpdateRecommendation.FORCE_UPDATE
            hasNewVersion(latestVersion) -> UpdateRecommendation.OPTIONAL_UPDATE
            else -> UpdateRecommendation.NO_UPDATE
        }
    }
    
    /**
     * 版本更新建议枚举
     */
    enum class UpdateRecommendation {
        NO_UPDATE,      // 无需更新
        OPTIONAL_UPDATE, // 可选更新
        FORCE_UPDATE    // 强制更新
    }
}