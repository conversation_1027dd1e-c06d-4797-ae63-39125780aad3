package com.aiinterview.simulator.data.service

import android.content.Context
import com.aiinterview.simulator.data.api.TextToSpeechApi
import com.aiinterview.simulator.data.dto.response.TTSResponse
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.junit.Assert.*

@ExperimentalCoroutinesApi
class TextToSpeechServiceTest {
    
    @Mock
    private lateinit var context: Context
    
    @Mock
    private lateinit var ttsApi: TextToSpeechApi
    
    private lateinit var ttsService: TextToSpeechService
    
    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        ttsService = TextToSpeechService(context, ttsApi)
    }
    
    @Test
    fun `synthesizeText should handle different voice types`() = runTest {
        // Test all voice types
        val voiceTypes = TextToSpeechService.VoiceType.values()
        val testText = "这是一个测试文本"
        
        for (voiceType in voiceTypes) {
            // When
            val result = ttsService.synthesizeText(
                text = testText,
                voiceType = voiceType,
                speed = 5
            )
            
            // Then
            assertNotNull(result)
            // 由于是模拟环境，可能会失败，但不应该抛出异常
        }
    }
    
    @Test
    fun `synthesizeText should handle different speeds`() = runTest {
        // Test different speech speeds
        val speeds = listOf(1, 3, 5, 7, 9)
        val testText = "测试语速"
        
        for (speed in speeds) {
            // When
            val result = ttsService.synthesizeText(
                text = testText,
                voiceType = TextToSpeechService.VoiceType.FEMALE_FRIENDLY,
                speed = speed
            )
            
            // Then
            assertNotNull(result)
        }
    }
    
    @Test
    fun `synthesizeText should handle different providers`() = runTest {
        val testText = "测试不同的TTS提供商"
        
        // Test AUTO provider
        val autoResult = ttsService.synthesizeText(
            text = testText,
            provider = TextToSpeechService.TTSProvider.AUTO
        )
        assertNotNull(autoResult)
        
        // Test specific providers
        val baiduResult = ttsService.synthesizeText(
            text = testText,
            provider = TextToSpeechService.TTSProvider.BAIDU
        )
        assertNotNull(baiduResult)
        
        val tencentResult = ttsService.synthesizeText(
            text = testText,
            provider = TextToSpeechService.TTSProvider.TENCENT
        )
        assertNotNull(tencentResult)
        
        val iflytekResult = ttsService.synthesizeText(
            text = testText,
            provider = TextToSpeechService.TTSProvider.IFLYTEK
        )
        assertNotNull(iflytekResult)
    }
    
    @Test
    fun `synthesizeText should handle empty text`() = runTest {
        // When
        val result = ttsService.synthesizeText(
            text = "",
            voiceType = TextToSpeechService.VoiceType.FEMALE_FRIENDLY,
            speed = 5
        )
        
        // Then
        assertNotNull(result)
        // 空文本应该返回失败或者很短的音频
    }
    
    @Test
    fun `synthesizeText should handle long text`() = runTest {
        // Given
        val longText = "这是一个很长的测试文本。".repeat(100)
        
        // When
        val result = ttsService.synthesizeText(
            text = longText,
            voiceType = TextToSpeechService.VoiceType.FEMALE_FRIENDLY,
            speed = 5
        )
        
        // Then
        assertNotNull(result)
        if (result.success && result.audioData != null) {
            assertTrue(result.duration > 0)
        }
    }
    
    @Test
    fun `playAudio should handle valid TTSResponse`() = runTest {
        // Given
        val mockAudioData = ByteArray(1024) { it.toByte() }
        val ttsResponse = TTSResponse(
            success = true,
            audioData = mockAudioData,
            duration = 5000L,
            format = "mp3",
            provider = "test"
        )
        
        // When
        val result = ttsService.playAudio(ttsResponse)
        
        // Then
        // 在测试环境中可能无法实际播放，但不应该抛出异常
        assertNotNull(result)
    }
    
    @Test
    fun `playAudio should handle TTSResponse with URL`() = runTest {
        // Given
        val ttsResponse = TTSResponse(
            success = true,
            audioUrl = "https://example.com/audio.mp3",
            duration = 5000L,
            format = "mp3",
            provider = "test"
        )
        
        // When
        val result = ttsService.playAudio(ttsResponse)
        
        // Then
        assertNotNull(result)
    }
    
    @Test
    fun `playAudio should handle invalid TTSResponse`() = runTest {
        // Given
        val invalidResponse = TTSResponse(
            success = false,
            audioData = null,
            audioUrl = null,
            provider = "test"
        )
        
        // When
        val result = ttsService.playAudio(invalidResponse)
        
        // Then
        assertFalse(result)
    }
    
    @Test
    fun `stopAudio should stop playback`() {
        // When
        ttsService.stopAudio()
        
        // Then
        assertFalse(ttsService.isPlaying())
    }
    
    @Test
    fun `isPlaying should return correct status`() {
        // Initially should not be playing
        assertFalse(ttsService.isPlaying())
        
        // After stopping, should not be playing
        ttsService.stopAudio()
        assertFalse(ttsService.isPlaying())
    }
    
    @Test
    fun `cleanupTempAudioFiles should not throw exception`() {
        // When & Then - should not throw any exception
        ttsService.cleanupTempAudioFiles()
    }
    
    @Test
    fun `synthesizeText should handle special characters`() = runTest {
        // Given
        val textWithSpecialChars = "测试特殊字符：！@#￥%……&*（）——+{}|："《》？[]\\;',./"
        
        // When
        val result = ttsService.synthesizeText(
            text = textWithSpecialChars,
            voiceType = TextToSpeechService.VoiceType.FEMALE_FRIENDLY,
            speed = 5
        )
        
        // Then
        assertNotNull(result)
    }
    
    @Test
    fun `synthesizeText should handle mixed Chinese and English`() = runTest {
        // Given
        val mixedText = "这是中英文混合的测试文本 This is a mixed Chinese and English test text"
        
        // When
        val result = ttsService.synthesizeText(
            text = mixedText,
            voiceType = TextToSpeechService.VoiceType.FEMALE_FRIENDLY,
            speed = 5
        )
        
        // Then
        assertNotNull(result)
    }
}