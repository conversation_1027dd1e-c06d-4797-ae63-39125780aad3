package com.aiinterview.simulator.util

import android.content.Context
import android.content.pm.PackageManager
import android.content.pm.Signature
import android.util.Log
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException

/**
 * 应用签名验证工具类
 * 用于验证应用的数字签名，防止应用被恶意篡改或重新打包
 */
class SignatureVerifier(private val context: Context) {
    
    companion object {
        private const val TAG = "SignatureVerifier"
        
        // 发布版本的签名SHA1值（需要在实际签名后更新）
        private const val RELEASE_SIGNATURE_SHA1 = "YOUR_RELEASE_SIGNATURE_SHA1_HERE"
        
        // 调试版本的签名SHA1值
        private const val DEBUG_SIGNATURE_SHA1 = "YOUR_DEBUG_SIGNATURE_SHA1_HERE"
    }
    
    /**
     * 验证应用签名是否合法
     * @return true表示签名合法，false表示签名异常
     */
    fun verifySignature(): Boolean {
        return try {
            val currentSignature = getCurrentSignatureSHA1()
            val expectedSignature = if (ChannelUtil.isDebugBuild()) {
                DEBUG_SIGNATURE_SHA1
            } else {
                RELEASE_SIGNATURE_SHA1
            }
            
            if (currentSignature == null) {
                Log.w(TAG, "无法获取当前应用签名")
                return false
            }
            
            val isValid = currentSignature.equals(expectedSignature, ignoreCase = true)
            
            if (!isValid) {
                Log.w(TAG, "应用签名验证失败")
                Log.w(TAG, "当前签名: $currentSignature")
                Log.w(TAG, "期望签名: $expectedSignature")
            } else {
                Log.d(TAG, "应用签名验证通过")
            }
            
            isValid
        } catch (e: Exception) {
            Log.e(TAG, "签名验证过程中发生异常", e)
            false
        }
    }
    
    /**
     * 获取当前应用的签名SHA1值
     * @return 签名的SHA1哈希值，获取失败返回null
     */
    fun getCurrentSignatureSHA1(): String? {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(
                context.packageName,
                PackageManager.GET_SIGNATURES
            )
            
            val signatures = packageInfo.signatures
            if (signatures.isEmpty()) {
                Log.w(TAG, "未找到应用签名信息")
                return null
            }
            
            // 获取第一个签名（通常应用只有一个签名）
            val signature = signatures[0]
            calculateSHA1(signature.toByteArray())
        } catch (e: PackageManager.NameNotFoundException) {
            Log.e(TAG, "获取包信息失败", e)
            null
        } catch (e: Exception) {
            Log.e(TAG, "获取签名信息时发生异常", e)
            null
        }
    }
    
    /**
     * 获取所有签名信息
     * @return 签名信息列表
     */
    fun getAllSignatures(): List<SignatureInfo> {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(
                context.packageName,
                PackageManager.GET_SIGNATURES
            )
            
            packageInfo.signatures.mapIndexed { index, signature ->
                SignatureInfo(
                    index = index,
                    sha1 = calculateSHA1(signature.toByteArray()) ?: "未知",
                    md5 = calculateMD5(signature.toByteArray()) ?: "未知",
                    signature = signature
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取所有签名信息时发生异常", e)
            emptyList()
        }
    }
    
    /**
     * 计算字节数组的SHA1哈希值
     * @param data 要计算哈希的字节数组
     * @return SHA1哈希值的十六进制字符串
     */
    private fun calculateSHA1(data: ByteArray): String? {
        return try {
            val digest = MessageDigest.getInstance("SHA1")
            val hashBytes = digest.digest(data)
            bytesToHex(hashBytes)
        } catch (e: NoSuchAlgorithmException) {
            Log.e(TAG, "SHA1算法不可用", e)
            null
        }
    }
    
    /**
     * 计算字节数组的MD5哈希值
     * @param data 要计算哈希的字节数组
     * @return MD5哈希值的十六进制字符串
     */
    private fun calculateMD5(data: ByteArray): String? {
        return try {
            val digest = MessageDigest.getInstance("MD5")
            val hashBytes = digest.digest(data)
            bytesToHex(hashBytes)
        } catch (e: NoSuchAlgorithmException) {
            Log.e(TAG, "MD5算法不可用", e)
            null
        }
    }
    
    /**
     * 将字节数组转换为十六进制字符串
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    private fun bytesToHex(bytes: ByteArray): String {
        val hexChars = "0123456789ABCDEF"
        val result = StringBuilder(bytes.size * 2)
        
        for (byte in bytes) {
            val i = byte.toInt()
            result.append(hexChars[i shr 4 and 0x0F])
            result.append(hexChars[i and 0x0F])
        }
        
        return result.toString()
    }
    
    /**
     * 检查应用是否被重新打包
     * @return true表示可能被重新打包，false表示签名正常
     */
    fun isRepackaged(): Boolean {
        // 在调试模式下跳过重新打包检查
        if (ChannelUtil.isDebugBuild()) {
            return false
        }
        
        return !verifySignature()
    }
    
    /**
     * 生成签名报告（用于调试和安全分析）
     * @return 详细的签名报告字符串
     */
    fun generateSignatureReport(): String {
        val signatures = getAllSignatures()
        
        return buildString {
            appendLine("=== 应用签名报告 ===")
            appendLine()
            appendLine("包名：${context.packageName}")
            appendLine("渠道：${ChannelUtil.getChannelDisplayName()}")
            appendLine("构建类型：${ChannelUtil.getBuildType()}")
            appendLine("签名数量：${signatures.size}")
            appendLine()
            
            signatures.forEach { signatureInfo ->
                appendLine("签名 ${signatureInfo.index + 1}:")
                appendLine("  SHA1: ${signatureInfo.sha1}")
                appendLine("  MD5:  ${signatureInfo.md5}")
                appendLine()
            }
            
            appendLine("签名验证结果：${if (verifySignature()) "通过" else "失败"}")
            appendLine("重新打包检查：${if (isRepackaged()) "疑似被重新打包" else "正常"}")
            appendLine()
            appendLine("报告生成时间：${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date())}")
            appendLine("=== 报告结束 ===")
        }
    }
    
    /**
     * 签名信息数据类
     */
    data class SignatureInfo(
        val index: Int,           // 签名索引
        val sha1: String,         // SHA1哈希值
        val md5: String,          // MD5哈希值
        val signature: Signature  // 原始签名对象
    )
}