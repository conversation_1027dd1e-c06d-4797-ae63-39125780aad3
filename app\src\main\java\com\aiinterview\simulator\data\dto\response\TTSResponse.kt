package com.aiinterview.simulator.data.dto.response

/**
 * TTS（文本转语音）响应DTO
 */
data class TTSResponse(
    val success: Boolean,
    val audioUrl: String? = null,
    val audioData: ByteArray? = null,
    val duration: Long = 0, // 音频时长（毫秒）
    val format: String = "mp3",
    val sampleRate: Int = 16000,
    val errorMessage: String? = null,
    val provider: String, // "baidu", "tencent", "iflytek"
    val requestId: String? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as TTSResponse

        if (success != other.success) return false
        if (audioUrl != other.audioUrl) return false
        if (audioData != null) {
            if (other.audioData == null) return false
            if (!audioData.contentEquals(other.audioData)) return false
        } else if (other.audioData != null) return false
        if (duration != other.duration) return false
        if (format != other.format) return false
        if (sampleRate != other.sampleRate) return false
        if (errorMessage != other.errorMessage) return false
        if (provider != other.provider) return false
        if (requestId != other.requestId) return false

        return true
    }

    override fun hashCode(): Int {
        var result = success.hashCode()
        result = 31 * result + (audioUrl?.hashCode() ?: 0)
        result = 31 * result + (audioData?.contentHashCode() ?: 0)
        result = 31 * result + duration.hashCode()
        result = 31 * result + format.hashCode()
        result = 31 * result + sampleRate.hashCode()
        result = 31 * result + (errorMessage?.hashCode() ?: 0)
        result = 31 * result + provider.hashCode()
        result = 31 * result + (requestId?.hashCode() ?: 0)
        return result
    }
}

/**
 * 百度TTS响应格式
 */
data class BaiduTTSResponse(
    val err_no: Int,
    val err_msg: String?,
    val sn: String?, // 语音唯一标识
    val idx: Int?, // 语音分片的索引
    val data: String? // base64编码的音频数据
)

/**
 * 腾讯云TTS响应格式
 */
data class TencentTTSResponse(
    val Response: TencentTTSResponseData
)

data class TencentTTSResponseData(
    val Audio: String?, // base64编码的音频数据
    val SessionId: String,
    val RequestId: String,
    val Error: TencentError?
)

data class TencentError(
    val Code: String,
    val Message: String
)

/**
 * 讯飞TTS响应格式
 */
data class iFlyTekTTSResponse(
    val code: Int,
    val message: String,
    val sid: String,
    val data: iFlyTekTTSData?
)

data class iFlyTekTTSData(
    val audio: String, // base64编码的音频数据
    val ced: String, // 合成进度
    val status: Int // 合成状态
)