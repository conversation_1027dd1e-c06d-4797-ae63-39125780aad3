package com.aiinterview.simulator.presentation.screen.history

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.aiinterview.simulator.presentation.viewmodel.InterviewHistoryStatistics

/**
 * 统计信息对话框组件
 * 显示用户面试记录的统计数据
 * @param statistics 统计数据
 * @param onDismiss 关闭对话框回调函数
 */
@Composable
fun StatisticsDialog(
    statistics: InterviewHistoryStatistics,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { 
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.Analytics,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("面试统计") 
            }
        },
        text = {
            LazyColumn(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 基础统计信息
                item {
                    StatisticsSection(
                        title = "基础统计",
                        items = listOf(
                            StatisticsItem(
                                icon = Icons.Default.Assignment,
                                label = "总面试次数",
                                value = "${statistics.totalInterviews}次"
                            ),
                            StatisticsItem(
                                icon = Icons.Default.CheckCircle,
                                label = "完成面试次数",
                                value = "${statistics.completedInterviews}次"
                            ),
                            StatisticsItem(
                                icon = Icons.Default.Schedule,
                                label = "总面试时长",
                                value = formatTotalDuration(statistics.totalDuration)
                            )
                        )
                    )
                }
                
                // 成绩统计信息（仅在有完成的面试时显示）
                if (statistics.completedInterviews > 0) {
                    item {
                        StatisticsSection(
                            title = "成绩统计",
                            items = listOf(
                                StatisticsItem(
                                    icon = Icons.Default.TrendingUp,
                                    label = "平均分数",
                                    value = "${String.format("%.1f", statistics.averageScore)}分"
                                ),
                                StatisticsItem(
                                    icon = Icons.Default.Star,
                                    label = "最高分数",
                                    value = "${statistics.bestScore.toInt()}分"
                                )
                            )
                        )
                    }
                }
                
                // 类别统计信息（仅在有数据时显示）
                if (statistics.categoryCounts.isNotEmpty()) {
                    item {
                        CategoryStatisticsSection(
                            title = "岗位类别统计",
                            categoryCounts = statistics.categoryCounts
                        )
                    }
                }
            }
        },
        confirmButton = {
            // 关闭按钮
            TextButton(onClick = onDismiss) {
                Text("关闭")
            }
        }
    )
}

/**
 * 统计信息分组组件
 * @param title 分组标题
 * @param items 统计项目列表
 */
@Composable
private fun StatisticsSection(
    title: String,
    items: List<StatisticsItem>
) {
    Column {
        // 分组标题
        Text(
            text = title,
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        // 统计项目列表
        items.forEach { item ->
            StatisticsItemRow(item = item)
            Spacer(modifier = Modifier.height(4.dp))
        }
    }
}

/**
 * 类别统计信息分组组件
 * @param title 分组标题
 * @param categoryCounts 各类别的面试次数统计
 */
@Composable
private fun CategoryStatisticsSection(
    title: String,
    categoryCounts: Map<String, Int>
) {
    Column {
        // 分组标题
        Text(
            text = title,
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        // 类别统计列表
        categoryCounts.entries.sortedByDescending { it.value }.forEach { (category, count) ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 2.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.Category,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp),
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = category,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
                
                Text(
                    text = "${count}次",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}

/**
 * 单个统计项目行组件
 * @param item 统计项目数据
 */
@Composable
private fun StatisticsItemRow(
    item: StatisticsItem
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 左侧：图标和标签
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                item.icon,
                contentDescription = null,
                modifier = Modifier.size(16.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = item.label,
                style = MaterialTheme.typography.bodyMedium
            )
        }
        
        // 右侧：数值
        Text(
            text = item.value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.primary
        )
    }
}

/**
 * 统计项目数据类
 * @param icon 图标
 * @param label 标签文本
 * @param value 数值文本
 */
private data class StatisticsItem(
    val icon: ImageVector,
    val label: String,
    val value: String
)

/**
 * 格式化总时长为可读字符串
 * @param totalSeconds 总秒数
 * @return 格式化后的时长字符串
 */
private fun formatTotalDuration(totalSeconds: Int): String {
    val hours = totalSeconds / 3600
    val minutes = (totalSeconds % 3600) / 60
    
    return when {
        hours > 0 -> "${hours}小时${minutes}分钟"
        minutes > 0 -> "${minutes}分钟"
        else -> "${totalSeconds}秒"
    }
}