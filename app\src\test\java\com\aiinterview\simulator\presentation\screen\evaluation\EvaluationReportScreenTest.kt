package com.aiinterview.simulator.presentation.screen.evaluation

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.aiinterview.simulator.data.dto.response.*
import com.aiinterview.simulator.presentation.viewmodel.EvaluationReportUiState
import com.aiinterview.simulator.presentation.viewmodel.EvaluationReportViewModel
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.flow.MutableStateFlow
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * 评价报告界面UI测试
 */
@RunWith(AndroidJUnit4::class)
class EvaluationReportScreenTest {
    
    @get:Rule
    val composeTestRule = createComposeRule()
    
    private val mockViewModel = mockk<EvaluationReportViewModel>(relaxed = true)
    
    @Test
    fun 加载状态应该显示进度指示器() {
        // 设置加载状态
        val loadingState = EvaluationReportUiState(isLoading = true)
        every { mockViewModel.uiState } returns MutableStateFlow(loadingState)
        
        composeTestRule.setContent {
            EvaluationReportScreen(
                sessionId = "test-session",
                onBackClick = {},
                viewModel = mockViewModel
            )
        }
        
        // 验证加载指示器存在
        composeTestRule.onNodeWithText("正在生成评价报告...").assertIsDisplayed()
        composeTestRule.onNode(hasProgressBarRangeInfo(ProgressBarRangeInfo.Indeterminate))
            .assertIsDisplayed()
    }
    
    @Test
    fun 错误状态应该显示错误信息和重试按钮() {
        val errorMessage = "网络连接失败"
        val errorState = EvaluationReportUiState(error = errorMessage)
        every { mockViewModel.uiState } returns MutableStateFlow(errorState)
        
        composeTestRule.setContent {
            EvaluationReportScreen(
                sessionId = "test-session",
                onBackClick = {},
                viewModel = mockViewModel
            )
        }
        
        // 验证错误信息和重试按钮
        composeTestRule.onNodeWithText(errorMessage).assertIsDisplayed()
        composeTestRule.onNodeWithText("重试").assertIsDisplayed()
    }
    
    @Test
    fun 成功状态应该显示评价内容() {
        val mockEvaluation = createMockEvaluation()
        val successState = EvaluationReportUiState(
            evaluation = mockEvaluation,
            expandedSections = setOf("overall")
        )
        every { mockViewModel.uiState } returns MutableStateFlow(successState)
        
        composeTestRule.setContent {
            EvaluationReportScreen(
                sessionId = "test-session",
                onBackClick = {},
                viewModel = mockViewModel
            )
        }
        
        // 验证主要内容存在
        composeTestRule.onNodeWithText("总体评价").assertIsDisplayed()
        composeTestRule.onNodeWithText("各维度评分").assertIsDisplayed()
        composeTestRule.onNodeWithText("时间分析").assertIsDisplayed()
        composeTestRule.onNodeWithText("改进建议").assertIsDisplayed()
        
        // 验证分数显示
        composeTestRule.onNodeWithText("82.5").assertIsDisplayed()
        composeTestRule.onNodeWithText("良好").assertIsDisplayed()
    }
    
    @Test
    fun 点击章节标题应该切换展开状态() {
        val mockEvaluation = createMockEvaluation()
        val successState = EvaluationReportUiState(
            evaluation = mockEvaluation,
            expandedSections = emptySet()
        )
        every { mockViewModel.uiState } returns MutableStateFlow(successState)
        
        composeTestRule.setContent {
            EvaluationReportScreen(
                sessionId = "test-session",
                onBackClick = {},
                viewModel = mockViewModel
            )
        }
        
        // 点击各维度评分标题
        composeTestRule.onNodeWithText("各维度评分").performClick()
        
        // 验证ViewModel方法被调用
        // 注意：在实际测试中，需要验证toggleSectionExpanded方法被调用
    }
    
    @Test
    fun 展开状态应该显示详细内容() {
        val mockEvaluation = createMockEvaluation()
        val expandedState = EvaluationReportUiState(
            evaluation = mockEvaluation,
            expandedSections = setOf("overall", "dimensions", "time", "suggestions")
        )
        every { mockViewModel.uiState } returns MutableStateFlow(expandedState)
        
        composeTestRule.setContent {
            EvaluationReportScreen(
                sessionId = "test-session",
                onBackClick = {},
                viewModel = mockViewModel
            )
        }
        
        // 验证详细内容显示
        composeTestRule.onNodeWithText("详细反馈").assertIsDisplayed()
        composeTestRule.onNodeWithText("各题用时分析").assertIsDisplayed()
        composeTestRule.onNodeWithText("时间管理建议").assertIsDisplayed()
        
        // 验证改进建议列表
        composeTestRule.onNodeWithText("5 条建议").assertIsDisplayed()
    }
    
    @Test
    fun 维度评分应该显示正确信息() {
        val mockEvaluation = createMockEvaluation()
        val expandedState = EvaluationReportUiState(
            evaluation = mockEvaluation,
            expandedSections = setOf("dimensions")
        )
        every { mockViewModel.uiState } returns MutableStateFlow(expandedState)
        
        composeTestRule.setContent {
            EvaluationReportScreen(
                sessionId = "test-session",
                onBackClick = {},
                viewModel = mockViewModel
            )
        }
        
        // 验证各维度名称和分数
        composeTestRule.onNodeWithText("答题要点").assertIsDisplayed()
        composeTestRule.onNodeWithText("85").assertIsDisplayed()
        composeTestRule.onNodeWithText("逻辑结构").assertIsDisplayed()
        composeTestRule.onNodeWithText("78").assertIsDisplayed()
        composeTestRule.onNodeWithText("语言流畅").assertIsDisplayed()
        composeTestRule.onNodeWithText("88").assertIsDisplayed()
        composeTestRule.onNodeWithText("表达清晰").assertIsDisplayed()
        composeTestRule.onNodeWithText("80").assertIsDisplayed()
        composeTestRule.onNodeWithText("时间掌控").assertIsDisplayed()
        composeTestRule.onNodeWithText("75").assertIsDisplayed()
    }
    
    @Test
    fun 时间分析应该显示正确格式() {
        val mockEvaluation = createMockEvaluation()
        val expandedState = EvaluationReportUiState(
            evaluation = mockEvaluation,
            expandedSections = setOf("time")
        )
        every { mockViewModel.uiState } returns MutableStateFlow(expandedState)
        
        composeTestRule.setContent {
            EvaluationReportScreen(
                sessionId = "test-session",
                onBackClick = {},
                viewModel = mockViewModel
            )
        }
        
        // 验证时间信息格式
        composeTestRule.onNodeWithText("18分0秒").assertIsDisplayed() // 1080秒
        composeTestRule.onNodeWithText("20分0秒").assertIsDisplayed() // 1200秒
        composeTestRule.onNodeWithText("90%").assertIsDisplayed() // 时间利用率
    }
    
    @Test
    fun 返回按钮应该可以点击() {
        val mockEvaluation = createMockEvaluation()
        val successState = EvaluationReportUiState(evaluation = mockEvaluation)
        every { mockViewModel.uiState } returns MutableStateFlow(successState)
        
        var backClicked = false
        
        composeTestRule.setContent {
            EvaluationReportScreen(
                sessionId = "test-session",
                onBackClick = { backClicked = true },
                viewModel = mockViewModel
            )
        }
        
        // 点击返回按钮
        composeTestRule.onNodeWithContentDescription("返回").performClick()
        
        // 验证回调被调用
        assert(backClicked)
    }
    
    private fun createMockEvaluation(): EvaluationResponse {
        return EvaluationResponse(
            sessionId = "test-session",
            overallScore = 82.5,
            overallGrade = "良好",
            dimensions = EvaluationDimensions(
                contentCompleteness = DimensionScore(
                    score = 85.0,
                    grade = "良好",
                    feedback = "答题要点覆盖较为全面",
                    keyStrengths = listOf("思路清晰", "要点完整"),
                    improvementAreas = listOf("可以增加更多具体案例")
                ),
                logicalStructure = DimensionScore(
                    score = 78.0,
                    grade = "良好",
                    feedback = "逻辑结构基本清晰",
                    keyStrengths = listOf("条理清楚"),
                    improvementAreas = listOf("论证过程可以更加严密")
                ),
                languageFluency = DimensionScore(
                    score = 88.0,
                    grade = "优秀",
                    feedback = "语言表达流畅自然",
                    keyStrengths = listOf("表达流畅", "用词准确"),
                    improvementAreas = listOf("可以适当增加语言的感染力")
                ),
                expressionClarity = DimensionScore(
                    score = 80.0,
                    grade = "良好",
                    feedback = "表达清晰明了",
                    keyStrengths = listOf("重点突出"),
                    improvementAreas = listOf("语速可以适当放慢")
                ),
                timeManagement = DimensionScore(
                    score = 75.0,
                    grade = "中等",
                    feedback = "时间掌控基本合理",
                    keyStrengths = listOf("整体时间控制在范围内"),
                    improvementAreas = listOf("各题时间分配需要更加均衡")
                )
            ),
            feedback = "本次面试表现整体良好，展现了扎实的专业基础和较好的表达能力。",
            suggestions = listOf(
                "加强时间管理，合理分配各题答题时间",
                "增加具体案例和实际经验的分享",
                "提升论证的逻辑性和严密性",
                "适当放慢语速，增强表达的感染力",
                "多练习结构化思维，提升答题的条理性"
            ),
            encouragement = "您在本次面试中展现了良好的专业素养和沟通能力，继续保持这种积极的学习态度！",
            timeAnalysis = TimeAnalysis(
                totalTime = 1080,
                timeLimit = 1200,
                timeUtilization = 0.9,
                questionTimeBreakdown = listOf(
                    QuestionTimeAnalysis(
                        questionId = "q1",
                        questionType = "综合分析",
                        timeSpent = 360,
                        recommendedTime = 300,
                        efficiency = "适中"
                    ),
                    QuestionTimeAnalysis(
                        questionId = "q2",
                        questionType = "计划组织",
                        timeSpent = 420,
                        recommendedTime = 360,
                        efficiency = "过长"
                    ),
                    QuestionTimeAnalysis(
                        questionId = "q3",
                        questionType = "人际关系",
                        timeSpent = 300,
                        recommendedTime = 360,
                        efficiency = "高效"
                    )
                ),
                timeManagementFeedback = "整体时间控制良好，但第二题用时偏长"
            ),
            createdAt = System.currentTimeMillis()
        )
    }
}