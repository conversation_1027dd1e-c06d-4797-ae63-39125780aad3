package com.aiinterview.simulator.data.api

import com.aiinterview.simulator.data.dto.request.LLMEvaluationRequest
import com.aiinterview.simulator.data.dto.response.ApiResponse
import com.aiinterview.simulator.data.dto.response.LLMEvaluationResponse
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.POST

/**
 * AI评价生成API接口
 * 支持文心一言、通义千问、腾讯混元等大语言模型API
 */
interface AIEvaluationApi {
    
    /**
     * 文心一言API - 生成面试评价
     */
    @POST("rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions")
    suspend fun generateEvaluationWithWenxin(
        @Header("Authorization") authorization: String,
        @Body request: LLMEvaluationRequest
    ): ApiResponse<LLMEvaluationResponse>
    
    /**
     * 通义千问API - 生成面试评价
     */
    @POST("api/v1/services/aigc/text-generation/generation")
    suspend fun generateEvaluationWithTongyi(
        @Header("Authorization") authorization: String,
        @Header("Content-Type") contentType: String = "application/json",
        @Body request: LLMEvaluationRequest
    ): ApiResponse<LLMEvaluationResponse>
    
    /**
     * 腾讯混元API - 生成面试评价
     */
    @POST("v1/chat/completions")
    suspend fun generateEvaluationWithHunyuan(
        @Header("Authorization") authorization: String,
        @Header("Content-Type") contentType: String = "application/json",
        @Body request: LLMEvaluationRequest
    ): ApiResponse<LLMEvaluationResponse>
}