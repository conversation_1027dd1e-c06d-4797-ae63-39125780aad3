package com.aiinterview.simulator.presentation.screen.analysis

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.aiinterview.simulator.data.model.*
import com.aiinterview.simulator.presentation.viewmodel.AnalysisDimension
import com.aiinterview.simulator.presentation.viewmodel.AnalysisTimeRange
import com.aiinterview.simulator.presentation.viewmodel.ProgressAnalysisViewModel
import java.text.SimpleDateFormat
import java.util.*

/**
 * 进步分析主界面
 * 展示用户的面试进步趋势、分类别分析和个性化建议
 * 
 * @param onNavigateBack 返回上一页的回调函数
 * @param onNavigateToRetry 导航到重新练习界面的回调函数
 * @param onNavigateToHistory 导航到历史记录界面的回调函数
 * @param viewModel 进步分析ViewModel
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProgressAnalysisScreen(
    onNavigateBack: () -> Unit,
    onNavigateToRetry: (String) -> Unit, // 传入岗位类别
    onNavigateToHistory: () -> Unit,
    viewModel: ProgressAnalysisViewModel = hiltViewModel()
) {
    // 收集ViewModel状态
    val uiState by viewModel.uiState.collectAsState()
    val selectedTimeRange by viewModel.selectedTimeRange.collectAsState()
    val selectedDimension by viewModel.selectedDimension.collectAsState()
    val isInSelectionMode by viewModel.isInSelectionMode.collectAsState()
    
    // 本地状态管理
    var showTimeRangeDialog by remember { mutableStateOf(false) }
    var showExportDialog by remember { mutableStateOf(false) }
    var showManagementDialog by remember { mutableStateOf(false) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
    ) {
        // 顶部应用栏
        TopAppBar(
            title = { Text("进步分析") },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            },
            actions = {
                // 时间范围选择按钮
                IconButton(onClick = { showTimeRangeDialog = true }) {
                    Icon(Icons.Default.DateRange, contentDescription = "时间范围")
                }
                
                // 导出报告按钮
                IconButton(onClick = { showExportDialog = true }) {
                    Icon(Icons.Default.FileDownload, contentDescription = "导出报告")
                }
                
                // 记录管理按钮
                IconButton(onClick = { showManagementDialog = true }) {
                    Icon(Icons.Default.ManageAccounts, contentDescription = "记录管理")
                }
                
                // 更多操作菜单
                var showMoreMenu by remember { mutableStateOf(false) }
                Box {
                    IconButton(onClick = { showMoreMenu = true }) {
                        Icon(Icons.Default.MoreVert, contentDescription = "更多")
                    }
                    
                    DropdownMenu(
                        expanded = showMoreMenu,
                        onDismissRequest = { showMoreMenu = false }
                    ) {
                        DropdownMenuItem(
                            text = { Text("刷新数据") },
                            onClick = {
                                viewModel.refresh()
                                showMoreMenu = false
                            },
                            leadingIcon = {
                                Icon(Icons.Default.Refresh, contentDescription = null)
                            }
                        )
                        
                        DropdownMenuItem(
                            text = { Text("查看历史") },
                            onClick = {
                                onNavigateToHistory()
                                showMoreMenu = false
                            },
                            leadingIcon = {
                                Icon(Icons.Default.History, contentDescription = null)
                            }
                        )
                    }
                }
            }
        )
        
        // 时间范围指示器
        TimeRangeIndicator(
            selectedTimeRange = selectedTimeRange,
            onClick = { showTimeRangeDialog = true },
            modifier = Modifier.padding(16.dp)
        )
        
        // 分析维度选择器
        AnalysisDimensionSelector(
            selectedDimension = selectedDimension,
            onDimensionSelected = viewModel::setAnalysisDimension,
            modifier = Modifier.padding(horizontal = 16.dp)
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 主内容区域
        Box(modifier = Modifier.fillMaxSize()) {
            when {
                uiState.isLoading -> {
                    // 加载状态
                    CircularProgressIndicator(
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                
                uiState.error != null -> {
                    // 错误状态
                    ErrorMessage(
                        message = uiState.error,
                        onRetry = viewModel::refresh,
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                
                uiState.analysisResult == null -> {
                    // 无数据状态
                    EmptyAnalysisState(
                        onNavigateToHistory = onNavigateToHistory,
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                
                else -> {
                    // 正常状态：显示分析内容
                    AnalysisContent(
                        analysisResult = uiState.analysisResult,
                        selectedDimension = selectedDimension,
                        onNavigateToRetry = onNavigateToRetry,
                        onCategorySelected = { category ->
                            viewModel.loadRetryRecords(category)
                        }
                    )
                }
            }
        }
    }
    
    // 时间范围选择对话框
    if (showTimeRangeDialog) {
        TimeRangeSelectionDialog(
            selectedTimeRange = selectedTimeRange,
            onTimeRangeSelected = { timeRange ->
                viewModel.setTimeRange(timeRange)
                showTimeRangeDialog = false
            },
            onDismiss = { showTimeRangeDialog = false }
        )
    }
    
    // 导出报告对话框
    if (showExportDialog) {
        ExportReportDialog(
            reportContent = viewModel.exportAnalysisReport(),
            onDismiss = { showExportDialog = false }
        )
    }
    
    // 记录管理对话框
    if (showManagementDialog) {
        RecordManagementDialog(
            onBatchDelete = { beforeTime ->
                viewModel.deleteRecordsBefore(beforeTime)
                showManagementDialog = false
            },
            onDismiss = { showManagementDialog = false }
        )
    }
    
    // 消息提示处理
    uiState.message?.let { message ->
        LaunchedEffect(message) {
            // 这里可以显示Snackbar或Toast
            viewModel.clearMessage()
        }
    }
}

/**
 * 时间范围指示器组件
 */
@Composable
private fun TimeRangeIndicator(
    selectedTimeRange: AnalysisTimeRange,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "分析时间范围",
                    style = MaterialTheme.typography.labelMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                Text(
                    text = selectedTimeRange.displayName,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }
            
            Icon(
                Icons.Default.KeyboardArrowDown,
                contentDescription = "选择时间范围",
                tint = MaterialTheme.colorScheme.onPrimaryContainer
            )
        }
    }
}

/**
 * 分析维度选择器组件
 */
@Composable
private fun AnalysisDimensionSelector(
    selectedDimension: AnalysisDimension,
    onDimensionSelected: (AnalysisDimension) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(AnalysisDimension.values()) { dimension ->
            FilterChip(
                onClick = { onDimensionSelected(dimension) },
                label = { Text(dimension.displayName) },
                selected = dimension == selectedDimension
            )
        }
    }
}

/**
 * 分析内容主体组件
 */
@Composable
private fun AnalysisContent(
    analysisResult: ProgressAnalysisResult,
    selectedDimension: AnalysisDimension,
    onNavigateToRetry: (String) -> Unit,
    onCategorySelected: (String) -> Unit
) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        when (selectedDimension) {
            AnalysisDimension.OVERALL -> {
                // 总体趋势分析
                item {
                    OverallTrendCard(
                        overallTrend = analysisResult.overallTrend,
                        scoreHistory = analysisResult.scoreHistory
                    )
                }
                
                // 改进建议
                item {
                    RecommendationsCard(
                        recommendations = analysisResult.recommendations
                    )
                }
            }
            
            AnalysisDimension.CATEGORY -> {
                // 分类别分析
                items(analysisResult.categoryAnalysis) { categoryProgress ->
                    CategoryProgressCard(
                        categoryProgress = categoryProgress,
                        onRetryClick = { 
                            onCategorySelected(categoryProgress.category)
                            onNavigateToRetry(categoryProgress.category)
                        }
                    )
                }
            }
            
            AnalysisDimension.TIME_PATTERN -> {
                // 时间模式分析
                item {
                    TimeAnalysisCard(
                        timeAnalysis = analysisResult.timeAnalysis
                    )
                }
            }
            
            AnalysisDimension.SCORE_DISTRIBUTION -> {
                // 分数分布分析
                item {
                    ScoreDistributionCard(
                        scoreHistory = analysisResult.scoreHistory
                    )
                }
            }
        }
    }
}

/**
 * 总体趋势卡片组件
 */
@Composable
private fun OverallTrendCard(
    overallTrend: OverallTrend,
    scoreHistory: List<ScorePoint>
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "总体趋势",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                // 趋势图标
                TrendIcon(trendDirection = overallTrend.trendDirection)
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 当前水平指示器
            PerformanceLevelIndicator(
                level = overallTrend.currentLevel,
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 改进率显示
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "改进率",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Text(
                    text = "${String.format("%.1f", overallTrend.improvementRate)}%",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Bold,
                    color = if (overallTrend.improvementRate >= 0) {
                        Color(0xFF4CAF50)
                    } else {
                        Color(0xFFF44336)
                    }
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 趋势描述
            Text(
                text = overallTrend.description,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            // 简化的分数趋势图（如果有足够数据）
            if (scoreHistory.size >= 2) {
                Spacer(modifier = Modifier.height(16.dp))
                SimpleScoreTrendChart(
                    scoreHistory = scoreHistory,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp)
                )
            }
        }
    }
}

/**
 * 分类别进步卡片组件
 */
@Composable
private fun CategoryProgressCard(
    categoryProgress: CategoryProgress,
    onRetryClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = categoryProgress.category,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                // 重新练习按钮
                TextButton(onClick = onRetryClick) {
                    Icon(
                        Icons.Default.Replay,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("重新练习")
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 统计信息网格
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatisticItem(
                    label = "平均分",
                    value = "${String.format("%.1f", categoryProgress.averageScore)}分",
                    modifier = Modifier.weight(1f)
                )
                
                StatisticItem(
                    label = "最高分",
                    value = "${String.format("%.1f", categoryProgress.bestScore)}分",
                    modifier = Modifier.weight(1f)
                )
                
                StatisticItem(
                    label = "练习次数",
                    value = "${categoryProgress.interviewCount}次",
                    modifier = Modifier.weight(1f)
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 改进率和趋势
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "改进率: ${String.format("%.1f", categoryProgress.improvementRate)}%",
                    style = MaterialTheme.typography.bodyMedium,
                    color = if (categoryProgress.improvementRate >= 0) {
                        Color(0xFF4CAF50)
                    } else {
                        Color(0xFFF44336)
                    }
                )
                
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    TrendIcon(
                        trendDirection = categoryProgress.trend,
                        size = 16.dp
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = categoryProgress.trend.displayName,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

/**
 * 改进建议卡片组件
 */
@Composable
private fun RecommendationsCard(
    recommendations: List<String>
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "个性化建议",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            recommendations.forEachIndexed { index, recommendation ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    crossAxisAlignment = Alignment.Top
                ) {
                    Text(
                        text = "${index + 1}.",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.padding(end = 8.dp)
                    )
                    
                    Text(
                        text = recommendation,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
    }
}

/**
 * 时间分析卡片组件
 */
@Composable
private fun TimeAnalysisCard(
    timeAnalysis: TimeAnalysis
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "练习时间分析",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 总体统计
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatisticItem(
                    label = "总练习时间",
                    value = "${timeAnalysis.totalPracticeTime}分钟",
                    modifier = Modifier.weight(1f)
                )
                
                StatisticItem(
                    label = "平均时长",
                    value = "${timeAnalysis.averageSessionDuration}分钟",
                    modifier = Modifier.weight(1f)
                )
            }
            
            // 如果有周统计数据，显示最近的趋势
            if (timeAnalysis.weeklyStats.isNotEmpty()) {
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "最近周练习情况",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                timeAnalysis.weeklyStats.takeLast(4).forEach { weekStats ->
                    WeeklyStatsItem(weekStats = weekStats)
                }
            }
        }
    }
}

/**
 * 分数分布卡片组件
 */
@Composable
private fun ScoreDistributionCard(
    scoreHistory: List<ScorePoint>
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "分数分布分析",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            if (scoreHistory.isNotEmpty()) {
                // 分数区间统计
                val scoreRanges = mapOf(
                    "优秀 (90-100)" to scoreHistory.count { it.score >= 90 },
                    "良好 (80-89)" to scoreHistory.count { it.score >= 80 && it.score < 90 },
                    "一般 (70-79)" to scoreHistory.count { it.score >= 70 && it.score < 80 },
                    "较差 (0-69)" to scoreHistory.count { it.score < 70 }
                )
                
                scoreRanges.forEach { (range, count) ->
                    ScoreRangeItem(
                        range = range,
                        count = count,
                        total = scoreHistory.size
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 统计信息
                val avgScore = scoreHistory.map { it.score }.average()
                val maxScore = scoreHistory.maxOfOrNull { it.score } ?: 0.0
                val minScore = scoreHistory.minOfOrNull { it.score } ?: 0.0
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    StatisticItem(
                        label = "平均分",
                        value = "${String.format("%.1f", avgScore)}分",
                        modifier = Modifier.weight(1f)
                    )
                    
                    StatisticItem(
                        label = "最高分",
                        value = "${String.format("%.1f", maxScore)}分",
                        modifier = Modifier.weight(1f)
                    )
                    
                    StatisticItem(
                        label = "最低分",
                        value = "${String.format("%.1f", minScore)}分",
                        modifier = Modifier.weight(1f)
                    )
                }
            } else {
                Text(
                    text = "暂无分数数据",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

// 辅助组件和工具函数将在下一个文件中继续...