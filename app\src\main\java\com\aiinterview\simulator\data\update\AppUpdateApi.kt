package com.aiinterview.simulator.data.update

import retrofit2.http.GET
import retrofit2.http.Query

/**
 * 应用更新API接口
 * 用于检查应用更新信息
 */
interface AppUpdateApi {
    
    /**
     * 检查应用更新
     * @param packageName 应用包名
     * @param currentVersion 当前版本号
     * @param channel 渠道标识
     * @return 更新信息
     */
    @GET("api/app/check-update")
    suspend fun checkUpdate(
        @Query("package_name") packageName: String,
        @Query("current_version") currentVersion: String,
        @Query("channel") channel: String
    ): UpdateInfo
    
    /**
     * 获取版本历史
     * @param packageName 应用包名
     * @param channel 渠道标识
     * @return 版本历史列表
     */
    @GET("api/app/version-history")
    suspend fun getVersionHistory(
        @Query("package_name") packageName: String,
        @Query("channel") channel: String
    ): List<VersionHistoryItem>
    
    /**
     * 上报更新统计
     * @param packageName 应用包名
     * @param fromVersion 原版本号
     * @param toVersion 目标版本号
     * @param channel 渠道标识
     * @param action 操作类型（check/download/install/ignore）
     */
    @GET("api/app/update-stats")
    suspend fun reportUpdateStats(
        @Query("package_name") packageName: String,
        @Query("from_version") fromVersion: String,
        @Query("to_version") toVersion: String,
        @Query("channel") channel: String,
        @Query("action") action: String
    )
}

/**
 * 版本历史项数据类
 */
data class VersionHistoryItem(
    val version: String,
    val versionCode: Int,
    val releaseDate: String,
    val description: String,
    val fileSize: Long,
    val downloadCount: Int,
    val isStable: Boolean
)