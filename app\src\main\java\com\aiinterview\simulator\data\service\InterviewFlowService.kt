package com.aiinterview.simulator.data.service

import com.aiinterview.simulator.data.model.*
import com.aiinterview.simulator.data.repository.InterviewRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class InterviewFlowService @Inject constructor(
    private val interviewRepository: InterviewRepository,
    private val aiQuestionService: AIQuestionGenerationService,
    private val ttsService: TextToSpeechService,
    private val recordSaveService: InterviewRecordSaveService
) {
    
    private val _currentSession = MutableStateFlow<InterviewSession?>(null)
    val currentSession: StateFlow<InterviewSession?> = _currentSession.asStateFlow()
    
    private val _currentQuestion = MutableStateFlow<Question?>(null)
    val currentQuestion: StateFlow<Question?> = _currentQuestion.asStateFlow()
    
    private val _flowState = MutableStateFlow(InterviewFlowState.IDLE)
    val flowState: StateFlow<InterviewFlowState> = _flowState.asStateFlow()
    
    private val _timeRemaining = MutableStateFlow(0L)
    val timeRemaining: StateFlow<Long> = _timeRemaining.asStateFlow()
    
    private val _questionProgress = MutableStateFlow(QuestionProgress(0, 0))
    val questionProgress: StateFlow<QuestionProgress> = _questionProgress.asStateFlow()
    
    private var sessionTimer: Timer? = null
    private var questionTimer: Timer? = null
    
    enum class InterviewFlowState {
        IDLE,           // 空闲状态
        PREPARING,      // 准备中
        QUESTION_DISPLAY, // 显示问题
        QUESTION_READING, // 朗读问题
        WAITING_ANSWER,   // 等待回答
        RECORDING,        // 录音中
        PROCESSING,       // 处理回答
        COMPLETED,        // 面试完成
        ERROR            // 错误状态
    }
    
    data class QuestionProgress(
        val current: Int,
        val total: Int
    )
    
    /**
     * 开始面试流程
     */
    suspend fun startInterview(
        userId: String,
        position: Position,
        useAIGeneration: Boolean = true
    ): Result<InterviewSession> {
        return try {
            _flowState.value = InterviewFlowState.PREPARING
            
            // 生成或获取面试问题
            val questions = if (useAIGeneration) {
                generateAIQuestions(position)
            } else {
                getPresetQuestions(position)
            }
            
            // 创建面试会话
            val session = InterviewSession(
                id = UUID.randomUUID().toString(),
                userId = userId,
                positionId = position.id,
                status = InterviewStatus.STARTED,
                startTime = System.currentTimeMillis(),
                endTime = null,
                currentQuestionIndex = 0,
                questions = questions,
                answers = emptyList(),
                evaluation = null
            )
            
            // 保存会话
            interviewRepository.saveInterviewSession(session)
            
            _currentSession.value = session
            _questionProgress.value = QuestionProgress(0, questions.size)
            
            // 开始第一个问题
            startNextQuestion()
            
            Result.success(session)
        } catch (e: Exception) {
            _flowState.value = InterviewFlowState.ERROR
            Result.failure(e)
        }
    }
    
    /**
     * 开始下一个问题
     */
    suspend fun startNextQuestion(): Result<Question> {
        val session = _currentSession.value ?: return Result.failure(Exception("没有活跃的面试会话"))
        
        return try {
            if (session.currentQuestionIndex >= session.questions.size) {
                // 面试完成
                completeInterview()
                return Result.failure(Exception("面试已完成"))
            }
            
            val question = session.questions[session.currentQuestionIndex]
            _currentQuestion.value = question
            _flowState.value = InterviewFlowState.QUESTION_DISPLAY
            
            // 更新进度
            _questionProgress.value = QuestionProgress(
                session.currentQuestionIndex + 1,
                session.questions.size
            )
            
            // 开始问题计时
            startQuestionTimer(question.timeLimit.toLong() * 1000)
            
            Result.success(question)
        } catch (e: Exception) {
            _flowState.value = InterviewFlowState.ERROR
            Result.failure(e)
        }
    }
    
    /**
     * 朗读当前问题
     */
    suspend fun readCurrentQuestion(): Result<Unit> {
        val question = _currentQuestion.value ?: return Result.failure(Exception("没有当前问题"))
        
        return try {
            _flowState.value = InterviewFlowState.QUESTION_READING
            
            // 构建朗读文本
            val readingText = buildQuestionReadingText(question)
            
            // 使用TTS朗读
            val ttsResponse = ttsService.synthesizeText(
                text = readingText,
                voiceType = TextToSpeechService.VoiceType.FEMALE_FRIENDLY,
                speed = 5
            )
            
            if (ttsResponse.success) {
                val playSuccess = ttsService.playAudio(ttsResponse)
                if (playSuccess) {
                    // 朗读完成后进入等待回答状态
                    _flowState.value = InterviewFlowState.WAITING_ANSWER
                    Result.success(Unit)
                } else {
                    // 播放失败，直接进入等待回答状态
                    _flowState.value = InterviewFlowState.WAITING_ANSWER
                    Result.success(Unit)
                }
            } else {
                // TTS失败，直接进入等待回答状态
                _flowState.value = InterviewFlowState.WAITING_ANSWER
                Result.success(Unit)
            }
        } catch (e: Exception) {
            _flowState.value = InterviewFlowState.ERROR
            Result.failure(e)
        }
    }
    
    /**
     * 开始录音回答
     */
    fun startAnswerRecording(): Result<Unit> {
        return try {
            _flowState.value = InterviewFlowState.RECORDING
            Result.success(Unit)
        } catch (e: Exception) {
            _flowState.value = InterviewFlowState.ERROR
            Result.failure(e)
        }
    }
    
    /**
     * 提交问题回答
     */
    suspend fun submitAnswer(
        audioFile: java.io.File,
        transcription: String,
        duration: Int
    ): Result<Unit> {
        val session = _currentSession.value ?: return Result.failure(Exception("没有活跃的面试会话"))
        val question = _currentQuestion.value ?: return Result.failure(Exception("没有当前问题"))
        
        return try {
            _flowState.value = InterviewFlowState.PROCESSING
            
            // 创建回答记录
            val answer = Answer(
                questionId = question.id,
                audioUrl = audioFile.absolutePath, // 实际应用中应该上传到服务器
                transcription = transcription,
                duration = duration,
                submittedAt = System.currentTimeMillis()
            )
            
            // 更新会话
            val updatedAnswers = session.answers + answer
            val updatedSession = session.copy(
                answers = updatedAnswers,
                currentQuestionIndex = session.currentQuestionIndex + 1
            )
            
            // 保存更新的会话
            interviewRepository.saveInterviewSession(updatedSession)
            _currentSession.value = updatedSession
            
            // 停止问题计时
            stopQuestionTimer()
            
            // 检查是否还有更多问题
            if (updatedSession.currentQuestionIndex < updatedSession.questions.size) {
                // 继续下一个问题
                startNextQuestion()
            } else {
                // 面试完成
                completeInterview()
            }
            
            Result.success(Unit)
        } catch (e: Exception) {
            _flowState.value = InterviewFlowState.ERROR
            Result.failure(e)
        }
    }
    
    /**
     * 跳过当前问题
     */
    suspend fun skipCurrentQuestion(): Result<Unit> {
        val session = _currentSession.value ?: return Result.failure(Exception("没有活跃的面试会话"))
        
        return try {
            // 创建空回答记录
            val answer = Answer(
                questionId = _currentQuestion.value?.id ?: "",
                audioUrl = "",
                transcription = "[已跳过]",
                duration = 0,
                submittedAt = System.currentTimeMillis()
            )
            
            // 提交空回答
            submitAnswer(
                audioFile = java.io.File(""), // 空文件
                transcription = "[已跳过]",
                duration = 0
            )
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 暂停面试
     */
    fun pauseInterview(): Result<Unit> {
        return try {
            stopQuestionTimer()
            stopSessionTimer()
            ttsService.stopAudio()
            
            val session = _currentSession.value
            if (session != null) {
                val pausedSession = session.copy(status = InterviewStatus.IN_PROGRESS)
                _currentSession.value = pausedSession
            }
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 恢复面试
     */
    fun resumeInterview(): Result<Unit> {
        return try {
            val session = _currentSession.value ?: return Result.failure(Exception("没有活跃的面试会话"))
            val question = _currentQuestion.value ?: return Result.failure(Exception("没有当前问题"))
            
            // 恢复问题计时
            startQuestionTimer(question.timeLimit.toLong() * 1000)
            
            _flowState.value = InterviewFlowState.WAITING_ANSWER
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 完成面试
     */
    private suspend fun completeInterview() {
        try {
            stopQuestionTimer()
            stopSessionTimer()
            ttsService.stopAudio()
            
            val session = _currentSession.value
            if (session != null) {
                val completedSession = session.copy(
                    status = InterviewStatus.COMPLETED,
                    endTime = System.currentTimeMillis()
                )
                
                // 保存面试会话
                interviewRepository.saveInterviewSession(completedSession)
                _currentSession.value = completedSession
                
                // 自动保存面试记录
                try {
                    val position = interviewRepository.getPositionById(completedSession.positionId)
                    if (position != null) {
                        val audioFiles = collectAudioFiles(completedSession)
                        recordSaveService.saveRecordFromSession(
                            session = completedSession,
                            position = position,
                            audioFiles = audioFiles
                        )
                    }
                } catch (e: Exception) {
                    // 记录保存失败不影响面试完成状态
                    // 可以在后台重试或通知用户
                }
            }
            
            _flowState.value = InterviewFlowState.COMPLETED
        } catch (e: Exception) {
            _flowState.value = InterviewFlowState.ERROR
        }
    }
    
    /**
     * 取消面试
     */
    suspend fun cancelInterview(): Result<Unit> {
        return try {
            stopQuestionTimer()
            stopSessionTimer()
            ttsService.stopAudio()
            
            val session = _currentSession.value
            if (session != null) {
                val cancelledSession = session.copy(
                    status = InterviewStatus.CANCELLED,
                    endTime = System.currentTimeMillis()
                )
                
                interviewRepository.saveInterviewSession(cancelledSession)
                _currentSession.value = cancelledSession
            }
            
            _flowState.value = InterviewFlowState.IDLE
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 生成AI问题
     */
    private suspend fun generateAIQuestions(position: Position): List<Question> {
        val questions = mutableListOf<Question>()
        val questionTypes = listOf(
            QuestionType.COMPREHENSIVE_ANALYSIS,
            QuestionType.PLANNING_ORGANIZATION,
            QuestionType.INTERPERSONAL_RELATIONS,
            QuestionType.EMERGENCY_RESPONSE,
            QuestionType.VERBAL_EXPRESSION
        )
        
        // 根据岗位配置生成相应数量的问题
        val questionCount = minOf(position.questionCount, questionTypes.size)
        val selectedTypes = questionTypes.shuffled().take(questionCount)
        
        for ((index, questionType) in selectedTypes.withIndex()) {
            try {
                val response = aiQuestionService.generateQuestion(
                    position = position,
                    questionType = questionType,
                    difficulty = 3, // 中等难度
                    previousQuestions = questions.map { it.content }
                )
                
                if (response.success && response.question != null) {
                    val generatedQuestion = response.question
                    val question = Question(
                        id = generatedQuestion.id,
                        type = questionType,
                        category = generatedQuestion.category,
                        title = generatedQuestion.title,
                        content = generatedQuestion.content,
                        backgroundInfo = generatedQuestion.backgroundInfo,
                        keyPoints = generatedQuestion.keyPoints,
                        timeLimit = generatedQuestion.timeLimit,
                        difficulty = generatedQuestion.difficulty
                    )
                    questions.add(question)
                }
            } catch (e: Exception) {
                // 如果AI生成失败，使用预设问题
                val fallbackQuestion = createFallbackQuestion(questionType, index + 1)
                questions.add(fallbackQuestion)
            }
        }
        
        return questions
    }
    
    /**
     * 获取预设问题
     */
    private fun getPresetQuestions(position: Position): List<Question> {
        // 这里应该从数据库或配置文件中获取预设问题
        // 为了演示，这里创建一些示例问题
        return listOf(
            createFallbackQuestion(QuestionType.COMPREHENSIVE_ANALYSIS, 1),
            createFallbackQuestion(QuestionType.PLANNING_ORGANIZATION, 2),
            createFallbackQuestion(QuestionType.INTERPERSONAL_RELATIONS, 3)
        )
    }
    
    /**
     * 创建降级问题
     */
    private fun createFallbackQuestion(questionType: QuestionType, index: Int): Question {
        return when (questionType) {
            QuestionType.COMPREHENSIVE_ANALYSIS -> Question(
                id = "fallback_comp_$index",
                type = questionType,
                category = questionType.displayName,
                title = "社会现象分析",
                content = "近年来，网络直播带货成为一种新兴的销售模式，既带来了商业机遇，也出现了一些问题。请你谈谈对网络直播带货现象的看法。",
                timeLimit = 300,
                difficulty = 3
            )
            QuestionType.PLANNING_ORGANIZATION -> Question(
                id = "fallback_plan_$index",
                type = questionType,
                category = questionType.displayName,
                title = "活动组织",
                content = "你所在的单位准备组织一次关爱空巢老人的志愿服务活动，领导让你负责此次活动的策划和组织工作。你会如何开展？",
                timeLimit = 300,
                difficulty = 3
            )
            QuestionType.INTERPERSONAL_RELATIONS -> Question(
                id = "fallback_inter_$index",
                type = questionType,
                category = questionType.displayName,
                title = "人际关系处理",
                content = "在工作中，你和同事小李对某个方案有不同意见，小李坚持自己的观点，不愿意采纳你的建议，这影响了项目进度。你会怎么处理？",
                timeLimit = 300,
                difficulty = 3
            )
            QuestionType.EMERGENCY_RESPONSE -> Question(
                id = "fallback_emerg_$index",
                type = questionType,
                category = questionType.displayName,
                title = "应急处理",
                content = "你正在主持一场重要会议，突然发生停电，会议室一片漆黑，参会人员有些慌乱。作为会议主持人，你会如何应对？",
                timeLimit = 300,
                difficulty = 3
            )
            QuestionType.VERBAL_EXPRESSION -> Question(
                id = "fallback_verbal_$index",
                type = questionType,
                category = questionType.displayName,
                title = "政策解释",
                content = "某项惠民政策发生了调整，一些群众对新政策不理解，甚至有抵触情绪。现在需要你向群众解释这项政策变化，你会怎么说？",
                timeLimit = 300,
                difficulty = 3
            )
        }
    }
    
    /**
     * 构建问题朗读文本
     */
    private fun buildQuestionReadingText(question: Question): String {
        return buildString {
            append("第${_questionProgress.value.current}题，")
            append("${question.type.displayName}题。")
            append("题目内容：")
            append(question.content)
            append("。请考生开始回答，答题时间${question.timeLimit / 60}分钟。")
        }
    }
    
    /**
     * 开始问题计时
     */
    private fun startQuestionTimer(timeLimit: Long) {
        stopQuestionTimer()
        
        _timeRemaining.value = timeLimit
        
        questionTimer = Timer().apply {
            scheduleAtFixedRate(object : TimerTask() {
                override fun run() {
                    val remaining = _timeRemaining.value - 1000
                    _timeRemaining.value = maxOf(0, remaining)
                    
                    // 时间提醒
                    when (remaining) {
                        5 * 60 * 1000L -> { // 剩余5分钟提醒
                            // 这里可以触发语音提醒
                        }
                        1 * 60 * 1000L -> { // 剩余1分钟提醒
                            // 这里可以触发语音提醒
                        }
                        0L -> { // 时间到
                            // 自动提交当前回答或跳过
                            cancel()
                        }
                    }
                }
            }, 1000, 1000)
        }
    }
    
    /**
     * 停止问题计时
     */
    private fun stopQuestionTimer() {
        questionTimer?.cancel()
        questionTimer = null
    }
    
    /**
     * 开始会话计时
     */
    private fun startSessionTimer() {
        // 实现会话总时长计时
    }
    
    /**
     * 停止会话计时
     */
    private fun stopSessionTimer() {
        sessionTimer?.cancel()
        sessionTimer = null
    }
    
    /**
     * 获取当前面试进度
     */
    fun getInterviewProgress(): Float {
        val progress = _questionProgress.value
        return if (progress.total > 0) {
            progress.current.toFloat() / progress.total.toFloat()
        } else {
            0f
        }
    }
    
    /**
     * 检查是否可以进行下一步操作
     */
    fun canProceedToNext(): Boolean {
        return _flowState.value in listOf(
            InterviewFlowState.WAITING_ANSWER,
            InterviewFlowState.RECORDING
        )
    }
    
    /**
     * 获取剩余时间格式化字符串
     */
    fun getFormattedTimeRemaining(): String {
        val timeMs = _timeRemaining.value
        val minutes = timeMs / (60 * 1000)
        val seconds = (timeMs % (60 * 1000)) / 1000
        return String.format("%02d:%02d", minutes, seconds)
    }
    
    /**
     * 收集面试过程中的音频文件
     */
    private fun collectAudioFiles(session: InterviewSession): List<java.io.File> {
        val audioFiles = mutableListOf<java.io.File>()
        
        for (answer in session.answers) {
            if (answer.audioUrl.isNotEmpty()) {
                val file = java.io.File(answer.audioUrl)
                if (file.exists()) {
                    audioFiles.add(file)
                }
            }
        }
        
        return audioFiles
    }
}