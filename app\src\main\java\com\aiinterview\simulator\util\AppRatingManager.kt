package com.aiinterview.simulator.util

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.Log
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import com.aiinterview.simulator.R
import com.aiinterview.simulator.data.local.dataStore
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 应用评分管理器
 * 负责管理应用评分提示、跳转应用商店评分等功能
 */
@Singleton
class AppRatingManager @Inject constructor(
    private val context: Context
) {
    
    companion object {
        private const val TAG = "AppRatingManager"
        
        // DataStore键名
        private val LAUNCH_COUNT_KEY = intPreferencesKey("app_launch_count")
        private val FIRST_LAUNCH_TIME_KEY = longPreferencesKey("first_launch_time")
        private val LAST_RATING_PROMPT_TIME_KEY = longPreferencesKey("last_rating_prompt_time")
        private val RATING_STATUS_KEY = stringPreferencesKey("rating_status")
        private val INTERVIEW_COUNT_KEY = intPreferencesKey("interview_count")
        
        // 评分提示条件
        private const val MIN_LAUNCH_COUNT = 5        // 最少启动次数
        private const val MIN_DAYS_SINCE_INSTALL = 3  // 安装后最少天数
        private const val MIN_INTERVIEW_COUNT = 2     // 最少面试次数
        private const val RATING_PROMPT_INTERVAL = 7 * 24 * 60 * 60 * 1000L // 评分提示间隔（7天）
        
        // 评分状态
        private const val RATING_STATUS_NEVER_ASKED = "never_asked"
        private const val RATING_STATUS_ASKED = "asked"
        private const val RATING_STATUS_RATED = "rated"
        private const val RATING_STATUS_DECLINED = "declined"
    }
    
    /**
     * 记录应用启动
     */
    suspend fun recordAppLaunch() {
        context.dataStore.edit { preferences ->
            val currentCount = preferences[LAUNCH_COUNT_KEY] ?: 0
            preferences[LAUNCH_COUNT_KEY] = currentCount + 1
            
            // 记录首次启动时间
            if (preferences[FIRST_LAUNCH_TIME_KEY] == null) {
                preferences[FIRST_LAUNCH_TIME_KEY] = System.currentTimeMillis()
            }
        }
        
        Log.d(TAG, "记录应用启动，当前启动次数：${getLaunchCount()}")
    }
    
    /**
     * 记录面试完成
     */
    suspend fun recordInterviewCompleted() {
        context.dataStore.edit { preferences ->
            val currentCount = preferences[INTERVIEW_COUNT_KEY] ?: 0
            preferences[INTERVIEW_COUNT_KEY] = currentCount + 1
        }
        
        Log.d(TAG, "记录面试完成，当前面试次数：${getInterviewCount()}")
    }
    
    /**
     * 检查是否应该显示评分提示
     * @return true表示应该显示评分提示
     */
    suspend fun shouldShowRatingPrompt(): Boolean {
        val launchCount = getLaunchCount()
        val daysSinceInstall = getDaysSinceInstall()
        val interviewCount = getInterviewCount()
        val ratingStatus = getRatingStatus()
        val daysSinceLastPrompt = getDaysSinceLastPrompt()
        
        Log.d(TAG, "评分检查 - 启动次数:$launchCount, 安装天数:$daysSinceInstall, 面试次数:$interviewCount, 状态:$ratingStatus, 距上次提示:$daysSinceLastPrompt天")
        
        return when (ratingStatus) {
            RATING_STATUS_RATED, RATING_STATUS_DECLINED -> false
            RATING_STATUS_NEVER_ASKED -> {
                launchCount >= MIN_LAUNCH_COUNT &&
                daysSinceInstall >= MIN_DAYS_SINCE_INSTALL &&
                interviewCount >= MIN_INTERVIEW_COUNT
            }
            RATING_STATUS_ASKED -> {
                daysSinceLastPrompt >= 7 && // 7天后再次提示
                launchCount >= MIN_LAUNCH_COUNT * 2 // 启动次数翻倍
            }
            else -> false
        }
    }
    
    /**
     * 记录评分提示已显示
     */
    suspend fun recordRatingPromptShown() {
        context.dataStore.edit { preferences ->
            preferences[LAST_RATING_PROMPT_TIME_KEY] = System.currentTimeMillis()
            preferences[RATING_STATUS_KEY] = RATING_STATUS_ASKED
        }
        
        Log.d(TAG, "记录评分提示已显示")
    }
    
    /**
     * 记录用户已评分
     */
    suspend fun recordUserRated() {
        context.dataStore.edit { preferences ->
            preferences[RATING_STATUS_KEY] = RATING_STATUS_RATED
        }
        
        Log.d(TAG, "记录用户已评分")
    }
    
    /**
     * 记录用户拒绝评分
     */
    suspend fun recordUserDeclined() {
        context.dataStore.edit { preferences ->
            preferences[RATING_STATUS_KEY] = RATING_STATUS_DECLINED
        }
        
        Log.d(TAG, "记录用户拒绝评分")
    }
    
    /**
     * 打开应用商店评分页面
     * @return 是否成功打开
     */
    fun openRatingPage(): Boolean {
        return try {
            val currentChannel = ChannelUtil.getCurrentChannel()
            val packageName = context.packageName
            
            // 获取评分页面URL
            val ratingUrl = AppStoreAdapter.getRatingPageUrl(context, packageName)
            
            if (ratingUrl != null) {
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(ratingUrl)).apply {
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                
                if (intent.resolveActivity(context.packageManager) != null) {
                    context.startActivity(intent)
                    Log.d(TAG, "成功打开应用商店评分页面：$ratingUrl")
                    return true
                }
            }
            
            // 如果特定商店评分页面打开失败，尝试智能打开应用商店
            val success = AppStoreAdapter.smartOpenAppStore(context, packageName)
            if (success) {
                Log.d(TAG, "通过智能方式打开应用商店")
            } else {
                Log.w(TAG, "无法打开应用商店评分页面")
            }
            
            success
        } catch (e: Exception) {
            Log.e(TAG, "打开评分页面失败", e)
            false
        }
    }
    
    /**
     * 分享应用
     */
    fun shareApp() {
        try {
            val appName = context.getString(R.string.app_store_name)
            val shareTitle = context.getString(R.string.share_app_title)
            val downloadUrl = getAppDownloadUrl()
            val shareMessage = context.getString(R.string.share_app_message, downloadUrl)
            
            val shareIntent = Intent(Intent.ACTION_SEND).apply {
                type = "text/plain"
                putExtra(Intent.EXTRA_SUBJECT, shareTitle)
                putExtra(Intent.EXTRA_TEXT, shareMessage)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            
            val chooserIntent = Intent.createChooser(shareIntent, "分享$appName").apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            
            context.startActivity(chooserIntent)
            Log.d(TAG, "启动应用分享")
            
        } catch (e: Exception) {
            Log.e(TAG, "分享应用失败", e)
        }
    }
    
    /**
     * 获取应用下载链接
     */
    private fun getAppDownloadUrl(): String {
        val storeUrl = ChannelUtil.getStoreDownloadUrl()
        return storeUrl ?: "https://www.aiinterview.com/download"
    }
    
    /**
     * 获取启动次数
     */
    private suspend fun getLaunchCount(): Int {
        return context.dataStore.data.map { preferences ->
            preferences[LAUNCH_COUNT_KEY] ?: 0
        }.first()
    }
    
    /**
     * 获取面试次数
     */
    private suspend fun getInterviewCount(): Int {
        return context.dataStore.data.map { preferences ->
            preferences[INTERVIEW_COUNT_KEY] ?: 0
        }.first()
    }
    
    /**
     * 获取安装后的天数
     */
    private suspend fun getDaysSinceInstall(): Int {
        val firstLaunchTime = context.dataStore.data.map { preferences ->
            preferences[FIRST_LAUNCH_TIME_KEY] ?: System.currentTimeMillis()
        }.first()
        
        val daysDiff = (System.currentTimeMillis() - firstLaunchTime) / (24 * 60 * 60 * 1000)
        return daysDiff.toInt()
    }
    
    /**
     * 获取距离上次评分提示的天数
     */
    private suspend fun getDaysSinceLastPrompt(): Int {
        val lastPromptTime = context.dataStore.data.map { preferences ->
            preferences[LAST_RATING_PROMPT_TIME_KEY] ?: 0L
        }.first()
        
        if (lastPromptTime == 0L) return Int.MAX_VALUE
        
        val daysDiff = (System.currentTimeMillis() - lastPromptTime) / (24 * 60 * 60 * 1000)
        return daysDiff.toInt()
    }
    
    /**
     * 获取评分状态
     */
    private suspend fun getRatingStatus(): String {
        return context.dataStore.data.map { preferences ->
            preferences[RATING_STATUS_KEY] ?: RATING_STATUS_NEVER_ASKED
        }.first()
    }
    
    /**
     * 获取评分统计信息
     */
    suspend fun getRatingStats(): RatingStats {
        return RatingStats(
            launchCount = getLaunchCount(),
            interviewCount = getInterviewCount(),
            daysSinceInstall = getDaysSinceInstall(),
            ratingStatus = getRatingStatus(),
            daysSinceLastPrompt = getDaysSinceLastPrompt()
        )
    }
    
    /**
     * 重置评分数据（用于测试）
     */
    suspend fun resetRatingData() {
        context.dataStore.edit { preferences ->
            preferences.remove(LAUNCH_COUNT_KEY)
            preferences.remove(FIRST_LAUNCH_TIME_KEY)
            preferences.remove(LAST_RATING_PROMPT_TIME_KEY)
            preferences.remove(RATING_STATUS_KEY)
            preferences.remove(INTERVIEW_COUNT_KEY)
        }
        
        Log.d(TAG, "重置评分数据")
    }
}

/**
 * 评分统计数据类
 */
data class RatingStats(
    val launchCount: Int,
    val interviewCount: Int,
    val daysSinceInstall: Int,
    val ratingStatus: String,
    val daysSinceLastPrompt: Int
)