package com.aiinterview.simulator.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.aiinterview.simulator.data.repository.EvaluationRecordRepository
import com.aiinterview.simulator.data.repository.AuthRepository
import com.aiinterview.simulator.data.model.EvaluationRecordModel
import com.aiinterview.simulator.data.model.EvaluationRecordSummary
import com.aiinterview.simulator.data.dao.EvaluationStatistics
import com.aiinterview.simulator.domain.util.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

data class EvaluationHistoryUiState(
    val isLoading: Boolean = false,
    val evaluationRecords: List<EvaluationRecordSummary> = emptyList(),
    val filteredRecords: List<EvaluationRecordSummary> = emptyList(),
    val statistics: EvaluationStatistics? = null,
    val selectedRecord: EvaluationRecordModel? = null,
    val filterPosition: String = "",
    val filterScoreRange: Pair<Double, Double> = Pair(0.0, 100.0),
    val sortOrder: SortOrder = SortOrder.DATE_DESC,
    val unsyncedCount: Int = 0,
    val isSyncing: Boolean = false,
    val errorMessage: String? = null,
    val successMessage: String? = null
)

enum class SortOrder {
    DATE_DESC,    // 按日期降序
    DATE_ASC,     // 按日期升序
    SCORE_DESC,   // 按分数降序
    SCORE_ASC     // 按分数升序
}

@HiltViewModel
class EvaluationHistoryViewModel @Inject constructor(
    private val evaluationRecordRepository: EvaluationRecordRepository,
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(EvaluationHistoryUiState())
    val uiState: StateFlow<EvaluationHistoryUiState> = _uiState.asStateFlow()

    private var currentUserId: String? = null

    init {
        viewModelScope.launch {
            authRepository.getCurrentUserId().collect { userId ->
                currentUserId = userId
                if (userId != null) {
                    loadEvaluationHistory()
                    loadStatistics()
                    loadUnsyncedCount()
                }
            }
        }
    }

    /**
     * 加载评价历史记录
     */
    private fun loadEvaluationHistory() {
        if (currentUserId == null) return

        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true) }
            
            evaluationRecordRepository.getEvaluationRecordSummariesByUser(currentUserId)
                .catch { exception ->
                    _uiState.update { 
                        it.copy(
                            isLoading = false,
                            errorMessage = exception.localizedMessage ?: "加载评价历史失败"
                        )
                    }
                }
                .collect { records ->
                    _uiState.update { currentState ->
                        val filteredRecords = applyFiltersAndSort(records, currentState)
                        currentState.copy(
                            isLoading = false,
                            evaluationRecords = records,
                            filteredRecords = filteredRecords,
                            errorMessage = null
                        )
                    }
                }
        }
    }

    /**
     * 加载统计信息
     */
    private fun loadStatistics() {
        if (currentUserId == null) return

        viewModelScope.launch {
            try {
                val statistics = evaluationRecordRepository.getEvaluationStatistics(currentUserId)
                _uiState.update { it.copy(statistics = statistics) }
            } catch (e: Exception) {
                // 统计信息加载失败不影响主要功能
            }
        }
    }

    /**
     * 加载未同步记录数量
     */
    private fun loadUnsyncedCount() {
        if (currentUserId == null) return

        viewModelScope.launch {
            try {
                val count = evaluationRecordRepository.getUnsyncedRecordsCount(currentUserId)
                _uiState.update { it.copy(unsyncedCount = count) }
            } catch (e: Exception) {
                // 未同步数量加载失败不影响主要功能
            }
        }
    }

    /**
     * 获取评价记录详情
     */
    fun getEvaluationRecordDetail(recordId: String) {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isLoading = true) }
                val record = evaluationRecordRepository.getEvaluationRecordById(recordId)
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        selectedRecord = record,
                        errorMessage = null
                    )
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        errorMessage = e.localizedMessage ?: "获取评价详情失败"
                    )
                }
            }
        }
    }

    /**
     * 设置岗位筛选
     */
    fun setPositionFilter(positionName: String) {
        _uiState.update { currentState ->
            val newState = currentState.copy(filterPosition = positionName)
            val filteredRecords = applyFiltersAndSort(currentState.evaluationRecords, newState)
            newState.copy(filteredRecords = filteredRecords)
        }
    }

    /**
     * 设置分数范围筛选
     */
    fun setScoreRangeFilter(minScore: Double, maxScore: Double) {
        _uiState.update { currentState ->
            val newState = currentState.copy(filterScoreRange = Pair(minScore, maxScore))
            val filteredRecords = applyFiltersAndSort(currentState.evaluationRecords, newState)
            newState.copy(filteredRecords = filteredRecords)
        }
    }

    /**
     * 设置排序方式
     */
    fun setSortOrder(sortOrder: SortOrder) {
        _uiState.update { currentState ->
            val newState = currentState.copy(sortOrder = sortOrder)
            val filteredRecords = applyFiltersAndSort(currentState.evaluationRecords, newState)
            newState.copy(filteredRecords = filteredRecords)
        }
    }

    /**
     * 清除所有筛选条件
     */
    fun clearFilters() {
        _uiState.update { currentState ->
            val newState = currentState.copy(
                filterPosition = "",
                filterScoreRange = Pair(0.0, 100.0),
                sortOrder = SortOrder.DATE_DESC
            )
            val filteredRecords = applyFiltersAndSort(currentState.evaluationRecords, newState)
            newState.copy(filteredRecords = filteredRecords)
        }
    }

    /**
     * 删除评价记录
     */
    fun deleteEvaluationRecord(recordId: String) {
        viewModelScope.launch {
            evaluationRecordRepository.deleteEvaluationRecord(recordId)
                .collect { resource ->
                    when (resource) {
                        is Resource.Loading -> {
                            _uiState.update { it.copy(isLoading = true) }
                        }
                        is Resource.Success -> {
                            _uiState.update { 
                                it.copy(
                                    isLoading = false,
                                    successMessage = "评价记录已删除",
                                    errorMessage = null
                                )
                            }
                            // 重新加载数据
                            loadEvaluationHistory()
                            loadStatistics()
                        }
                        is Resource.Error -> {
                            _uiState.update { 
                                it.copy(
                                    isLoading = false,
                                    errorMessage = resource.message ?: "删除评价记录失败"
                                )
                            }
                        }
                    }
                }
        }
    }

    /**
     * 删除所有评价记录
     */
    fun deleteAllEvaluationRecords() {
        if (currentUserId == null) return

        viewModelScope.launch {
            evaluationRecordRepository.deleteAllEvaluationRecordsByUser(currentUserId)
                .collect { resource ->
                    when (resource) {
                        is Resource.Loading -> {
                            _uiState.update { it.copy(isLoading = true) }
                        }
                        is Resource.Success -> {
                            _uiState.update { 
                                it.copy(
                                    isLoading = false,
                                    successMessage = "所有评价记录已删除",
                                    errorMessage = null
                                )
                            }
                            // 重新加载数据
                            loadEvaluationHistory()
                            loadStatistics()
                        }
                        is Resource.Error -> {
                            _uiState.update { 
                                it.copy(
                                    isLoading = false,
                                    errorMessage = resource.message ?: "删除所有评价记录失败"
                                )
                            }
                        }
                    }
                }
        }
    }

    /**
     * 同步评价记录到云端
     */
    fun syncEvaluationRecords() {
        if (currentUserId == null) return

        viewModelScope.launch {
            evaluationRecordRepository.syncEvaluationRecords(currentUserId)
                .collect { resource ->
                    when (resource) {
                        is Resource.Loading -> {
                            _uiState.update { it.copy(isSyncing = true) }
                        }
                        is Resource.Success -> {
                            _uiState.update { 
                                it.copy(
                                    isSyncing = false,
                                    successMessage = "评价记录同步成功",
                                    errorMessage = null
                                )
                            }
                            // 重新加载未同步数量
                            loadUnsyncedCount()
                        }
                        is Resource.Error -> {
                            _uiState.update { 
                                it.copy(
                                    isSyncing = false,
                                    errorMessage = resource.message ?: "同步评价记录失败"
                                )
                            }
                        }
                    }
                }
        }
    }

    /**
     * 清除错误消息
     */
    fun clearErrorMessage() {
        _uiState.update { it.copy(errorMessage = null) }
    }

    /**
     * 清除成功消息
     */
    fun clearSuccessMessage() {
        _uiState.update { it.copy(successMessage = null) }
    }

    /**
     * 应用筛选和排序
     */
    private fun applyFiltersAndSort(
        records: List<EvaluationRecordSummary>,
        state: EvaluationHistoryUiState
    ): List<EvaluationRecordSummary> {
        var filteredRecords = records

        // 应用岗位筛选
        if (state.filterPosition.isNotEmpty()) {
            filteredRecords = filteredRecords.filter { 
                it.positionName.contains(state.filterPosition, ignoreCase = true) 
            }
        }

        // 应用分数范围筛选
        filteredRecords = filteredRecords.filter { 
            it.overallScore >= state.filterScoreRange.first && 
            it.overallScore <= state.filterScoreRange.second 
        }

        // 应用排序
        filteredRecords = when (state.sortOrder) {
            SortOrder.DATE_DESC -> filteredRecords.sortedByDescending { it.createdAt }
            SortOrder.DATE_ASC -> filteredRecords.sortedBy { it.createdAt }
            SortOrder.SCORE_DESC -> filteredRecords.sortedByDescending { it.overallScore }
            SortOrder.SCORE_ASC -> filteredRecords.sortedBy { it.overallScore }
        }

        return filteredRecords
    }
}