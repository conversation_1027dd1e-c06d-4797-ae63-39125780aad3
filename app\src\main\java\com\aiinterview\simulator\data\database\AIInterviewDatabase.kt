package com.aiinterview.simulator.data.database

import androidx.room.Database
import androidx.room.RoomDatabase
import com.aiinterview.simulator.data.dao.UserDao
import com.aiinterview.simulator.data.dao.PositionDao
import com.aiinterview.simulator.data.dao.InterviewSessionDao
import com.aiinterview.simulator.data.dao.EvaluationRecordDao
import com.aiinterview.simulator.data.dao.InterviewRecordDao
import com.aiinterview.simulator.data.model.User
import com.aiinterview.simulator.data.model.Position
import com.aiinterview.simulator.data.model.InterviewSession
import com.aiinterview.simulator.data.model.EvaluationRecord
import com.aiinterview.simulator.data.model.InterviewRecord

@Database(
    entities = [User::class, Position::class, InterviewSession::class, EvaluationRecord::class, InterviewRecord::class],
    version = 3,
    exportSchema = false
)
abstract class AIInterviewDatabase : RoomDatabase() {
    abstract fun userDao(): UserDao
    abstract fun positionDao(): PositionDao
    abstract fun interviewSessionDao(): InterviewSessionDao
    abstract fun evaluationRecordDao(): EvaluationRecordDao
    abstract fun interviewRecordDao(): InterviewRecordDao
    
    companion object {
        const val DATABASE_NAME = "ai_interview_database"
    }
}