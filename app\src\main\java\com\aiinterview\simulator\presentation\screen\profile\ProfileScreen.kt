package com.aiinterview.simulator.presentation.screen.profile

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.aiinterview.simulator.presentation.viewmodel.AuthViewModel

/**
 * 个人中心屏幕
 * 显示用户信息和应用设置选项
 * @param onNavigateToLogin 导航到登录页面的回调
 * @param onNavigateToSettings 导航到设置页面的回调
 * @param onNavigateToThemeSettings 导航到主题设置页面的回调
 * @param onNavigateToOfflineRecordings 导航到离线录音页面的回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProfileScreen(
    onNavigateToLogin: () -> Unit,
    onNavigateToSettings: () -> Unit,
    onNavigateToThemeSettings: () -> Unit = {},
    onNavigateToOfflineRecordings: () -> Unit,
    authViewModel: AuthViewModel = hiltViewModel()
) {
    // 收集用户登录状态
    val isLoggedIn by authViewModel.isLoggedIn.collectAsState()
    // 收集用户信息状态
    val userState by authViewModel.userState.collectAsState()
    
    // 页面主体布局
    Column(
        modifier = Modifier
            .fillMaxSize()                              // 填充整个屏幕
            .padding(16.dp)                             // 设置内边距
    ) {
        // 页面标题
        Text(
            text = "个人中心",                           // 标题文字
            style = MaterialTheme.typography.headlineMedium, // 使用中等标题样式
            fontWeight = FontWeight.Bold,               // 设置字体粗细
            modifier = Modifier.padding(bottom = 24.dp) // 底部边距
        )
        
        // 用户信息卡片
        Card(
            modifier = Modifier
                .fillMaxWidth()                         // 填充宽度
                .padding(bottom = 24.dp),               // 底部边距
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp) // 设置卡片阴影
        ) {
            Column(
                modifier = Modifier.padding(16.dp)      // 卡片内边距
            ) {
                // 用户头像和基本信息
                Row(
                    modifier = Modifier.fillMaxWidth(),  // 填充宽度
                    verticalAlignment = Alignment.CenterVertically // 垂直居中对齐
                ) {
                    // 用户头像图标
                    Icon(
                        imageVector = Icons.Default.AccountCircle, // 账户圆形图标
                        contentDescription = "用户头像",        // 无障碍描述
                        modifier = Modifier.size(64.dp),        // 设置图标大小
                        tint = MaterialTheme.colorScheme.primary // 设置图标颜色
                    )
                    
                    Spacer(modifier = Modifier.width(16.dp))    // 水平间距
                    
                    // 用户信息文字
                    Column {
                        Text(
                            text = if (isLoggedIn) {            // 根据登录状态显示不同文字
                                userState.data?.phoneNumber ?: "用户" // 显示手机号或默认用户名
                            } else {
                                "未登录"                        // 未登录状态文字
                            },
                            style = MaterialTheme.typography.titleLarge, // 使用大标题样式
                            fontWeight = FontWeight.Bold        // 设置字体粗细
                        )
                        
                        Text(
                            text = if (isLoggedIn) {            // 根据登录状态显示不同描述
                                "欢迎使用AI面试模拟器"           // 登录状态描述
                            } else {
                                "请登录以使用完整功能"           // 未登录状态描述
                            },
                            style = MaterialTheme.typography.bodyMedium, // 使用中等正文样式
                            color = MaterialTheme.colorScheme.onSurfaceVariant // 设置文字颜色
                        )
                    }
                }
            }
        }
        
        // 功能选项列表
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(8.dp) // 设置列表项间距
        ) {
            // 主题设置选项
            item {
                ProfileMenuItem(
                    icon = Icons.Default.Palette,          // 调色板图标
                    title = "主题设置",                     // 选项标题
                    subtitle = "个性化外观和显示效果",       // 选项副标题
                    onClick = onNavigateToThemeSettings     // 点击回调
                )
            }
            
            // 设置选项
            item {
                ProfileMenuItem(
                    icon = Icons.Default.Settings,         // 设置图标
                    title = "应用设置",                     // 选项标题
                    subtitle = "个性化设置和偏好",           // 选项副标题
                    onClick = onNavigateToSettings          // 点击回调
                )
            }
            
            // 离线录音选项
            item {
                ProfileMenuItem(
                    icon = Icons.Default.MicOff,           // 麦克风关闭图标
                    title = "离线录音",                     // 选项标题
                    subtitle = "管理本地录音文件",           // 选项副标题
                    onClick = onNavigateToOfflineRecordings // 点击回调
                )
            }
            
            // 帮助与反馈选项
            item {
                ProfileMenuItem(
                    icon = Icons.Default.Help,             // 帮助图标
                    title = "帮助与反馈",                   // 选项标题
                    subtitle = "使用指南和问题反馈",         // 选项副标题
                    onClick = { /* TODO: 实现帮助页面 */ }   // 点击回调（待实现）
                )
            }
            
            // 关于应用选项
            item {
                ProfileMenuItem(
                    icon = Icons.Default.Info,             // 信息图标
                    title = "关于应用",                     // 选项标题
                    subtitle = "版本信息和开发者信息",       // 选项副标题
                    onClick = { /* TODO: 实现关于页面 */ }   // 点击回调（待实现）
                )
            }
            
            // 登出选项（仅在已登录时显示）
            if (isLoggedIn) {
                item {
                    ProfileMenuItem(
                        icon = Icons.Default.ExitToApp,     // 退出图标
                        title = "退出登录",                 // 选项标题
                        subtitle = "安全退出当前账户",       // 选项副标题
                        onClick = {                         // 点击回调
                            authViewModel.logout()          // 执行登出操作
                            onNavigateToLogin()             // 导航到登录页面
                        },
                        isDestructive = true                // 标记为危险操作
                    )
                }
            }
        }
    }
}

/**
 * 个人中心菜单项组件
 * 用于显示个人中心的各个功能选项
 * @param icon 菜单项图标
 * @param title 菜单项标题
 * @param subtitle 菜单项副标题
 * @param onClick 点击回调
 * @param isDestructive 是否为危险操作（影响颜色显示）
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ProfileMenuItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    title: String,
    subtitle: String,
    onClick: () -> Unit,
    isDestructive: Boolean = false
) {
    // 根据是否为危险操作设置颜色
    val iconColor = if (isDestructive) {
        MaterialTheme.colorScheme.error                     // 危险操作使用错误颜色
    } else {
        MaterialTheme.colorScheme.primary                   // 普通操作使用主色调
    }
    
    val titleColor = if (isDestructive) {
        MaterialTheme.colorScheme.error                     // 危险操作标题使用错误颜色
    } else {
        MaterialTheme.colorScheme.onSurface                 // 普通操作使用表面文字颜色
    }
    
    // 菜单项卡片
    Card(
        onClick = onClick,                                  // 设置点击事件
        modifier = Modifier.fillMaxWidth(),                 // 填充宽度
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface // 设置卡片背景色
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()                             // 填充宽度
                .padding(16.dp),                            // 设置内边距
            verticalAlignment = Alignment.CenterVertically  // 垂直居中对齐
        ) {
            // 菜单项图标
            Icon(
                imageVector = icon,                         // 设置图标
                contentDescription = title,                 // 无障碍描述
                tint = iconColor,                          // 设置图标颜色
                modifier = Modifier.size(24.dp)            // 设置图标大小
            )
            
            Spacer(modifier = Modifier.width(16.dp))        // 水平间距
            
            // 菜单项文字信息
            Column(
                modifier = Modifier.weight(1f)              // 占据剩余空间
            ) {
                Text(
                    text = title,                           // 显示标题
                    style = MaterialTheme.typography.titleMedium, // 使用中等标题样式
                    color = titleColor                      // 设置标题颜色
                )
                
                Text(
                    text = subtitle,                        // 显示副标题
                    style = MaterialTheme.typography.bodySmall, // 使用小正文样式
                    color = MaterialTheme.colorScheme.onSurfaceVariant // 设置副标题颜色
                )
            }
            
            // 右箭头图标
            Icon(
                imageVector = Icons.Default.ChevronRight,   // 右箭头图标
                contentDescription = "进入",                // 无障碍描述
                tint = MaterialTheme.colorScheme.onSurfaceVariant, // 设置图标颜色
                modifier = Modifier.size(20.dp)            // 设置图标大小
            )
        }
    }
}