package com.aiinterview.simulator.data.repository

import com.aiinterview.simulator.data.dao.EvaluationRecordDao
import com.aiinterview.simulator.data.model.EvaluationRecord
import com.aiinterview.simulator.data.model.EvaluationRecordModel
import com.aiinterview.simulator.data.model.EvaluationRecordSummary
import com.aiinterview.simulator.data.model.DimensionScore
import com.aiinterview.simulator.data.dao.EvaluationStatistics
import com.aiinterview.simulator.domain.util.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import javax.inject.Inject
import javax.inject.Singleton

interface EvaluationRecordRepository {
    fun getEvaluationRecordsByUser(userId: String): Flow<List<EvaluationRecordModel>>
    fun getEvaluationRecordSummariesByUser(userId: String): Flow<List<EvaluationRecordSummary>>
    suspend fun getEvaluationRecordById(recordId: String): EvaluationRecordModel?
    suspend fun getEvaluationRecordBySessionId(sessionId: String): EvaluationRecordModel?
    fun getEvaluationRecordsByUserAndPosition(userId: String, positionId: String): Flow<List<EvaluationRecordModel>>
    fun getEvaluationRecordsByScoreRange(userId: String, minScore: Double, maxScore: Double): Flow<List<EvaluationRecordModel>>
    suspend fun getEvaluationStatistics(userId: String): EvaluationStatistics?
    suspend fun saveEvaluationRecord(record: EvaluationRecordModel): Flow<Resource<Unit>>
    suspend fun updateEvaluationRecord(record: EvaluationRecordModel): Flow<Resource<Unit>>
    suspend fun deleteEvaluationRecord(recordId: String): Flow<Resource<Unit>>
    suspend fun deleteAllEvaluationRecordsByUser(userId: String): Flow<Resource<Unit>>
    suspend fun syncEvaluationRecords(userId: String): Flow<Resource<Unit>>
    suspend fun getUnsyncedRecordsCount(userId: String): Int
}

@Singleton
class EvaluationRecordRepositoryImpl @Inject constructor(
    private val evaluationRecordDao: EvaluationRecordDao,
    private val evaluationSyncService: com.aiinterview.simulator.data.service.EvaluationSyncService,
    private val gson: Gson
) : EvaluationRecordRepository {

    override fun getEvaluationRecordsByUser(userId: String): Flow<List<EvaluationRecordModel>> {
        return evaluationRecordDao.getEvaluationRecordsByUser(userId).map { records ->
            records.map { it.toModel() }
        }
    }

    override fun getEvaluationRecordSummariesByUser(userId: String): Flow<List<EvaluationRecordSummary>> {
        return evaluationRecordDao.getEvaluationRecordSummariesByUser(userId)
    }

    override suspend fun getEvaluationRecordById(recordId: String): EvaluationRecordModel? {
        return evaluationRecordDao.getEvaluationRecordById(recordId)?.toModel()
    }

    override suspend fun getEvaluationRecordBySessionId(sessionId: String): EvaluationRecordModel? {
        return evaluationRecordDao.getEvaluationRecordBySessionId(sessionId)?.toModel()
    }

    override fun getEvaluationRecordsByUserAndPosition(
        userId: String,
        positionId: String
    ): Flow<List<EvaluationRecordModel>> {
        return evaluationRecordDao.getEvaluationRecordsByUserAndPosition(userId, positionId).map { records ->
            records.map { it.toModel() }
        }
    }

    override fun getEvaluationRecordsByScoreRange(
        userId: String,
        minScore: Double,
        maxScore: Double
    ): Flow<List<EvaluationRecordModel>> {
        return evaluationRecordDao.getEvaluationRecordsByScoreRange(userId, minScore, maxScore).map { records ->
            records.map { it.toModel() }
        }
    }

    override suspend fun getEvaluationStatistics(userId: String): EvaluationStatistics? {
        return evaluationRecordDao.getEvaluationStatistics(userId)
    }

    override suspend fun saveEvaluationRecord(record: EvaluationRecordModel): Flow<Resource<Unit>> = flow {
        try {
            emit(Resource.Loading())
            val entity = record.toEntity()
            evaluationRecordDao.insertEvaluationRecord(entity)
            emit(Resource.Success(Unit))
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "保存评价记录失败"))
        }
    }

    override suspend fun updateEvaluationRecord(record: EvaluationRecordModel): Flow<Resource<Unit>> = flow {
        try {
            emit(Resource.Loading())
            val entity = record.toEntity()
            evaluationRecordDao.updateEvaluationRecord(entity)
            emit(Resource.Success(Unit))
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "更新评价记录失败"))
        }
    }

    override suspend fun deleteEvaluationRecord(recordId: String): Flow<Resource<Unit>> = flow {
        try {
            emit(Resource.Loading())
            evaluationRecordDao.deleteEvaluationRecordById(recordId)
            emit(Resource.Success(Unit))
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "删除评价记录失败"))
        }
    }

    override suspend fun deleteAllEvaluationRecordsByUser(userId: String): Flow<Resource<Unit>> = flow {
        try {
            emit(Resource.Loading())
            evaluationRecordDao.deleteAllEvaluationRecordsByUser(userId)
            emit(Resource.Success(Unit))
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "删除所有评价记录失败"))
        }
    }

    override suspend fun syncEvaluationRecords(userId: String): Flow<Resource<Unit>> {
        return evaluationSyncService.syncEvaluationRecords(userId)
    }

    override suspend fun getUnsyncedRecordsCount(userId: String): Int {
        return evaluationRecordDao.getUnsyncedEvaluationRecords()
            .count { it.userId == userId }
    }

    // 扩展函数：将Entity转换为Model
    private fun EvaluationRecord.toModel(): EvaluationRecordModel {
        val dimensionScoresType = object : TypeToken<Map<String, DimensionScore>>() {}.type
        val suggestionsType = object : TypeToken<List<String>>() {}.type
        
        return EvaluationRecordModel(
            id = id,
            sessionId = sessionId,
            userId = userId,
            positionId = positionId,
            positionName = positionName,
            overallScore = overallScore,
            dimensionScores = gson.fromJson(dimensionScoresJson, dimensionScoresType),
            feedback = feedback,
            suggestions = gson.fromJson(suggestionsJson, suggestionsType),
            createdAt = createdAt,
            isSynced = isSynced,
            syncedAt = syncedAt
        )
    }

    // 扩展函数：将Model转换为Entity
    private fun EvaluationRecordModel.toEntity(): EvaluationRecord {
        return EvaluationRecord(
            id = id,
            sessionId = sessionId,
            userId = userId,
            positionId = positionId,
            positionName = positionName,
            overallScore = overallScore,
            dimensionScoresJson = gson.toJson(dimensionScores),
            feedback = feedback,
            suggestionsJson = gson.toJson(suggestions),
            createdAt = createdAt,
            isSynced = isSynced,
            syncedAt = syncedAt
        )
    }
}