package com.aiinterview.simulator.data.service

import android.content.Context
import com.aiinterview.simulator.data.api.AIQuestionGenerationApi
import com.aiinterview.simulator.data.dto.request.*
import com.aiinterview.simulator.data.dto.response.GeneratedQuestion
import com.aiinterview.simulator.data.dto.response.QuestionGenerationResponse
import com.aiinterview.simulator.data.model.Position
import com.aiinterview.simulator.data.model.QuestionType
import com.aiinterview.simulator.domain.util.ErrorHandler
import com.google.gson.Gson
import kotlinx.coroutines.delay
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AIQuestionGenerationService @Inject constructor(
    private val context: Context,
    private val aiApi: AIQuestionGenerationApi,
    private val gson: Gson
) {
    
    companion object {
        private const val MAX_RETRY_COUNT = 3
        private const val RETRY_DELAY_MS = 1000L
        
        // API配置
        private const val WENXIN_API_KEY = "your_wenxin_api_key"
        private const val WENXIN_SECRET_KEY = "your_wenxin_secret_key"
        private const val TONGYI_API_KEY = "your_tongyi_api_key"
    }
    
    private var wenxinAccessToken: String? = null
    private var tokenExpireTime: Long = 0
    
    enum class AIProvider {
        WENXIN, TONGYI, AUTO
    }
    
    /**
     * 生成面试问题
     */
    suspend fun generateQuestion(
        position: Position,
        questionType: QuestionType,
        difficulty: Int = 3,
        previousQuestions: List<String> = emptyList(),
        provider: AIProvider = AIProvider.AUTO
    ): QuestionGenerationResponse {
        var lastError: Exception? = null
        
        val providers = when (provider) {
            AIProvider.WENXIN -> listOf(AIProvider.WENXIN)
            AIProvider.TONGYI -> listOf(AIProvider.TONGYI)
            AIProvider.AUTO -> listOf(AIProvider.WENXIN, AIProvider.TONGYI)
        }
        
        for (currentProvider in providers) {
            for (attempt in 1..MAX_RETRY_COUNT) {
                try {
                    val result = when (currentProvider) {
                        AIProvider.WENXIN -> generateWithWenxin(position, questionType, difficulty, previousQuestions)
                        AIProvider.TONGYI -> generateWithTongyi(position, questionType, difficulty, previousQuestions)
                        AIProvider.AUTO -> throw IllegalStateException("AUTO should not reach here")
                    }
                    
                    if (result.success) {
                        return result
                    }
                } catch (e: Exception) {
                    lastError = e
                    if (attempt < MAX_RETRY_COUNT) {
                        delay(RETRY_DELAY_MS * attempt)
                    }
                }
            }
        }
        
        // 所有尝试都失败了，返回默认问题
        return createFallbackQuestion(position, questionType, difficulty)
    }
    
    /**
     * 使用文心一言生成问题
     */
    private suspend fun generateWithWenxin(
        position: Position,
        questionType: QuestionType,
        difficulty: Int,
        previousQuestions: List<String>
    ): QuestionGenerationResponse {
        try {
            val accessToken = getWenxinAccessToken()
            val prompt = buildQuestionPrompt(position, questionType, difficulty, previousQuestions)
            
            val request = WenxinQuestionRequest(
                messages = listOf(
                    WenxinMessage(
                        role = "user",
                        content = prompt
                    )
                ),
                temperature = 0.7,
                max_output_tokens = 1000,
                system = buildSystemPrompt()
            )
            
            val response = aiApi.generateQuestionWithWenxin(
                authorization = "Bearer $accessToken",
                request = QuestionGenerationRequest(
                    positionType = position.category,
                    questionType = questionType.name,
                    difficulty = difficulty,
                    previousQuestions = previousQuestions
                )
            )
            
            return parseWenxinResponse(response.data, questionType, difficulty)
            
        } catch (e: Exception) {
            return QuestionGenerationResponse(
                success = false,
                question = null,
                errorMessage = "文心一言API调用失败: ${e.message}",
                provider = "wenxin"
            )
        }
    }
    
    /**
     * 使用通义千问生成问题
     */
    private suspend fun generateWithTongyi(
        position: Position,
        questionType: QuestionType,
        difficulty: Int,
        previousQuestions: List<String>
    ): QuestionGenerationResponse {
        try {
            val prompt = buildQuestionPrompt(position, questionType, difficulty, previousQuestions)
            
            val request = TongyiQuestionRequest(
                input = TongyiInput(
                    messages = listOf(
                        TongyiMessage(role = "system", content = buildSystemPrompt()),
                        TongyiMessage(role = "user", content = prompt)
                    )
                ),
                parameters = TongyiParameters(
                    temperature = 0.7,
                    max_tokens = 1000
                )
            )
            
            val response = aiApi.generateQuestionWithTongyi(
                authorization = "Bearer $TONGYI_API_KEY",
                request = QuestionGenerationRequest(
                    positionType = position.category,
                    questionType = questionType.name,
                    difficulty = difficulty,
                    previousQuestions = previousQuestions
                )
            )
            
            return parseTongyiResponse(response.data, questionType, difficulty)
            
        } catch (e: Exception) {
            return QuestionGenerationResponse(
                success = false,
                question = null,
                errorMessage = "通义千问API调用失败: ${e.message}",
                provider = "tongyi"
            )
        }
    }
    
    /**
     * 构建问题生成提示词
     */
    private fun buildQuestionPrompt(
        position: Position,
        questionType: QuestionType,
        difficulty: Int,
        previousQuestions: List<String>
    ): String {
        val difficultyText = when (difficulty) {
            1, 2 -> "简单"
            3, 4 -> "中等"
            else -> "困难"
        }
        
        val questionTypeText = when (questionType) {
            QuestionType.COMPREHENSIVE_ANALYSIS -> "综合分析题，要求考生对社会现象、政策措施等进行分析"
            QuestionType.PLANNING_ORGANIZATION -> "计划组织题，要求考生展示组织协调和计划执行能力"
            QuestionType.INTERPERSONAL_RELATIONS -> "人际关系题，要求考生处理工作中的人际关系问题"
            QuestionType.EMERGENCY_RESPONSE -> "应急应变题，要求考生处理突发情况和紧急事件"
            QuestionType.VERBAL_EXPRESSION -> "言语表达题，要求考生进行演讲、劝说或解释"
        }
        
        val previousQuestionsText = if (previousQuestions.isNotEmpty()) {
            "请避免与以下已出过的题目重复：\n${previousQuestions.joinToString("\n")}\n\n"
        } else ""
        
        return """
            请为${position.name}岗位生成一道${difficultyText}难度的${questionTypeText}。
            
            ${previousQuestionsText}要求：
            1. 题目要符合公务员面试的标准和规范
            2. 内容要贴近实际工作场景
            3. 难度适中，符合${difficultyText}级别
            4. 题目要有一定的区分度和思考深度
            5. 请提供题目的背景信息和关键考查点
            
            请按照以下JSON格式返回：
            {
                "title": "题目标题",
                "content": "详细题目内容",
                "backgroundInfo": "背景信息",
                "keyPoints": ["考查点1", "考查点2", "考查点3"],
                "timeLimit": 300,
                "followUpQuestions": ["追问1", "追问2"]
            }
        """.trimIndent()
    }
    
    /**
     * 构建系统提示词
     */
    private fun buildSystemPrompt(): String {
        return """
            你是一位资深的公务员面试考官，具有丰富的面试出题和评价经验。
            你需要根据不同岗位特点和题目类型，生成高质量的结构化面试题目。
            
            出题原则：
            1. 题目要符合公务员面试的标准和要求
            2. 内容要贴近实际工作，具有实用性
            3. 难度要适中，有一定的区分度
            4. 语言要规范、准确、简洁
            5. 要考虑到不同考生的知识背景
            
            请严格按照要求的JSON格式返回结果。
        """.trimIndent()
    }
    
    /**
     * 解析文心一言响应
     */
    private fun parseWenxinResponse(
        response: Any?,
        questionType: QuestionType,
        difficulty: Int
    ): QuestionGenerationResponse {
        try {
            // 这里需要根据实际的文心一言API响应格式进行解析
            // 由于API格式可能变化，这里提供一个通用的解析逻辑
            val responseText = response.toString()
            val question = parseQuestionFromText(responseText, questionType, difficulty)
            
            return QuestionGenerationResponse(
                success = true,
                question = question,
                provider = "wenxin",
                requestId = UUID.randomUUID().toString()
            )
        } catch (e: Exception) {
            return QuestionGenerationResponse(
                success = false,
                question = null,
                errorMessage = "解析文心一言响应失败: ${e.message}",
                provider = "wenxin"
            )
        }
    }
    
    /**
     * 解析通义千问响应
     */
    private fun parseTongyiResponse(
        response: Any?,
        questionType: QuestionType,
        difficulty: Int
    ): QuestionGenerationResponse {
        try {
            // 这里需要根据实际的通义千问API响应格式进行解析
            val responseText = response.toString()
            val question = parseQuestionFromText(responseText, questionType, difficulty)
            
            return QuestionGenerationResponse(
                success = true,
                question = question,
                provider = "tongyi",
                requestId = UUID.randomUUID().toString()
            )
        } catch (e: Exception) {
            return QuestionGenerationResponse(
                success = false,
                question = null,
                errorMessage = "解析通义千问响应失败: ${e.message}",
                provider = "tongyi"
            )
        }
    }
    
    /**
     * 从文本中解析问题
     */
    private fun parseQuestionFromText(
        text: String,
        questionType: QuestionType,
        difficulty: Int
    ): GeneratedQuestion {
        try {
            // 尝试解析JSON格式的响应
            val jsonStart = text.indexOf("{")
            val jsonEnd = text.lastIndexOf("}") + 1
            
            if (jsonStart != -1 && jsonEnd > jsonStart) {
                val jsonText = text.substring(jsonStart, jsonEnd)
                val questionData = gson.fromJson(jsonText, Map::class.java)
                
                return GeneratedQuestion(
                    id = UUID.randomUUID().toString(),
                    type = questionType.name,
                    category = questionType.displayName,
                    title = questionData["title"]?.toString() ?: "面试题目",
                    content = questionData["content"]?.toString() ?: text,
                    backgroundInfo = questionData["backgroundInfo"]?.toString(),
                    keyPoints = (questionData["keyPoints"] as? List<*>)?.map { it.toString() } ?: emptyList(),
                    timeLimit = (questionData["timeLimit"] as? Number)?.toInt() ?: 300,
                    difficulty = difficulty,
                    followUpQuestions = (questionData["followUpQuestions"] as? List<*>)?.map { it.toString() } ?: emptyList()
                )
            }
        } catch (e: Exception) {
            // JSON解析失败，使用原始文本
        }
        
        // 如果JSON解析失败，创建基本的问题对象
        return GeneratedQuestion(
            id = UUID.randomUUID().toString(),
            type = questionType.name,
            category = questionType.displayName,
            title = "面试题目",
            content = text,
            timeLimit = 300,
            difficulty = difficulty
        )
    }
    
    /**
     * 创建降级问题
     */
    private fun createFallbackQuestion(
        position: Position,
        questionType: QuestionType,
        difficulty: Int
    ): QuestionGenerationResponse {
        val fallbackQuestions = getFallbackQuestions(questionType)
        val selectedQuestion = fallbackQuestions.random()
        
        val question = GeneratedQuestion(
            id = UUID.randomUUID().toString(),
            type = questionType.name,
            category = questionType.displayName,
            title = selectedQuestion.title,
            content = selectedQuestion.content,
            backgroundInfo = selectedQuestion.backgroundInfo,
            keyPoints = selectedQuestion.keyPoints,
            timeLimit = 300,
            difficulty = difficulty
        )
        
        return QuestionGenerationResponse(
            success = true,
            question = question,
            provider = "fallback"
        )
    }
    
    /**
     * 获取降级问题库
     */
    private fun getFallbackQuestions(questionType: QuestionType): List<GeneratedQuestion> {
        return when (questionType) {
            QuestionType.COMPREHENSIVE_ANALYSIS -> listOf(
                GeneratedQuestion(
                    id = "fallback_1",
                    type = questionType.name,
                    category = questionType.displayName,
                    title = "网络直播带货现象分析",
                    content = "近年来，网络直播带货成为一种新兴的销售模式，既带来了商业机遇，也出现了一些问题。请你谈谈对网络直播带货现象的看法。",
                    backgroundInfo = "网络直播带货是指通过网络直播平台，主播向观众推荐商品并引导购买的销售模式。",
                    keyPoints = listOf("现象分析", "利弊权衡", "监管建议", "发展趋势"),
                    timeLimit = 300,
                    difficulty = 3
                )
            )
            QuestionType.PLANNING_ORGANIZATION -> listOf(
                GeneratedQuestion(
                    id = "fallback_2",
                    type = questionType.name,
                    category = questionType.displayName,
                    title = "组织社区志愿服务活动",
                    content = "你所在的社区准备组织一次关爱空巢老人的志愿服务活动，领导让你负责此次活动的策划和组织工作。你会如何开展？",
                    backgroundInfo = "社区有较多空巢老人，需要社会关爱和帮助。",
                    keyPoints = listOf("活动策划", "资源协调", "人员组织", "效果评估"),
                    timeLimit = 300,
                    difficulty = 3
                )
            )
            QuestionType.INTERPERSONAL_RELATIONS -> listOf(
                GeneratedQuestion(
                    id = "fallback_3",
                    type = questionType.name,
                    category = questionType.displayName,
                    title = "处理同事间的工作分歧",
                    content = "在一个项目中，你和同事小李对某个方案有不同意见，小李坚持自己的观点，不愿意采纳你的建议，这影响了项目进度。你会怎么处理？",
                    backgroundInfo = "团队合作中出现意见分歧是常见现象，需要妥善处理。",
                    keyPoints = listOf("沟通技巧", "求同存异", "团队协作", "问题解决"),
                    timeLimit = 300,
                    difficulty = 3
                )
            )
            QuestionType.EMERGENCY_RESPONSE -> listOf(
                GeneratedQuestion(
                    id = "fallback_4",
                    type = questionType.name,
                    category = questionType.displayName,
                    title = "处理突发停电事件",
                    content = "你正在主持一场重要会议，突然发生停电，会议室一片漆黑，参会人员有些慌乱。作为会议主持人，你会如何应对？",
                    backgroundInfo = "突发停电可能影响会议正常进行，需要及时妥善处理。",
                    keyPoints = listOf("应急反应", "现场控制", "替代方案", "后续安排"),
                    timeLimit = 300,
                    difficulty = 3
                )
            )
            QuestionType.VERBAL_EXPRESSION -> listOf(
                GeneratedQuestion(
                    id = "fallback_5",
                    type = questionType.name,
                    category = questionType.displayName,
                    title = "向群众解释政策变化",
                    content = "某项惠民政策发生了调整，一些群众对新政策不理解，甚至有抵触情绪。现在需要你向群众解释这项政策变化，你会怎么说？",
                    backgroundInfo = "政策调整需要做好宣传解释工作，获得群众理解和支持。",
                    keyPoints = listOf("政策解读", "沟通技巧", "情绪疏导", "说服能力"),
                    timeLimit = 300,
                    difficulty = 3
                )
            )
        }
    }
    
    /**
     * 获取文心一言访问令牌
     */
    private suspend fun getWenxinAccessToken(): String {
        if (wenxinAccessToken != null && System.currentTimeMillis() < tokenExpireTime) {
            return wenxinAccessToken!!
        }
        
        try {
            // 这里应该调用文心一言的token获取API
            // 由于具体实现依赖于实际的API，这里提供一个模拟实现
            wenxinAccessToken = "mock_access_token"
            tokenExpireTime = System.currentTimeMillis() + 30 * 24 * 60 * 60 * 1000L // 30天
            return wenxinAccessToken!!
        } catch (e: Exception) {
            throw Exception("获取文心一言访问令牌失败: ${e.message}")
        }
    }
    
    /**
     * 验证生成的问题质量
     */
    fun validateGeneratedQuestion(question: GeneratedQuestion): Boolean {
        return question.content.isNotBlank() &&
                question.content.length >= 20 &&
                question.timeLimit > 0 &&
                question.difficulty in 1..5
    }
}