package com.aiinterview.simulator.presentation.screen.interview

import androidx.compose.animation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.aiinterview.simulator.data.model.Question
import com.aiinterview.simulator.data.service.InterviewFlowService
import com.aiinterview.simulator.presentation.viewmodel.InterviewFlowViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun InterviewQuestionScreen(
    onStartRecording: () -> Unit,
    onPauseInterview: () -> Unit,
    onResumeInterview: () -> Unit,
    onSkipQuestion: () -> Unit,
    onExitInterview: () -> Unit,
    viewModel: InterviewFlowViewModel = hiltViewModel()
) {
    val scrollState = rememberScrollState()
    
    // 观察ViewModel状态
    val currentQuestion by viewModel.currentQuestion.collectAsState()
    val flowState by viewModel.flowState.collectAsState()
    val timeRemaining by viewModel.timeRemaining.collectAsState()
    val questionProgress by viewModel.questionProgress.collectAsState()
    val isPlaying by viewModel.isPlayingAudio.collectAsState()
    
    var showExitDialog by remember { mutableStateOf(false) }
    var showSkipDialog by remember { mutableStateOf(false) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(scrollState)
    ) {
        // 顶部应用栏
        TopAppBar(
            title = { 
                Text(
                    text = "面试进行中",
                    style = MaterialTheme.typography.titleMedium
                )
            },
            actions = {
                // 暂停/恢复按钮
                IconButton(
                    onClick = {
                        when (flowState) {
                            InterviewFlowService.InterviewFlowState.WAITING_ANSWER,
                            InterviewFlowService.InterviewFlowState.RECORDING -> onPauseInterview()
                            else -> onResumeInterview()
                        }
                    }
                ) {
                    Icon(
                        imageVector = if (flowState == InterviewFlowService.InterviewFlowState.WAITING_ANSWER || 
                                         flowState == InterviewFlowService.InterviewFlowState.RECORDING) {
                            Icons.Default.Pause
                        } else {
                            Icons.Default.PlayArrow
                        },
                        contentDescription = "暂停/恢复"
                    )
                }
                
                // 退出按钮
                IconButton(onClick = { showExitDialog = true }) {
                    Icon(Icons.Default.ExitToApp, contentDescription = "退出面试")
                }
            }
        )
        
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 进度指示器
            InterviewProgressIndicator(
                progress = questionProgress,
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 时间显示
            TimeRemainingCard(
                timeRemaining = timeRemaining,
                isWarning = timeRemaining <= 5 * 60 * 1000L, // 5分钟警告
                isCritical = timeRemaining <= 1 * 60 * 1000L // 1分钟紧急
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 问题内容
            currentQuestion?.let { question ->
                QuestionContentCard(
                    question = question,
                    flowState = flowState,
                    isPlaying = isPlaying,
                    onReadQuestion = { viewModel.readCurrentQuestion() },
                    onStopReading = { viewModel.stopAudioPlayback() }
                )
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 操作按钮区域
            InterviewActionButtons(
                flowState = flowState,
                onStartRecording = onStartRecording,
                onReadQuestion = { viewModel.readCurrentQuestion() },
                onSkipQuestion = { showSkipDialog = true },
                canProceed = viewModel.canProceedToNext()
            )
        }
    }
    
    // 退出确认对话框
    if (showExitDialog) {
        ExitInterviewDialog(
            onConfirm = {
                showExitDialog = false
                onExitInterview()
            },
            onDismiss = { showExitDialog = false }
        )
    }
    
    // 跳过问题确认对话框
    if (showSkipDialog) {
        SkipQuestionDialog(
            onConfirm = {
                showSkipDialog = false
                onSkipQuestion()
            },
            onDismiss = { showSkipDialog = false }
        )
    }
}

@Composable
fun InterviewProgressIndicator(
    progress: InterviewFlowService.QuestionProgress,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "面试进度",
                    style = MaterialTheme.typography.labelMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Text(
                    text = "${progress.current}/${progress.total}",
                    style = MaterialTheme.typography.labelMedium,
                    color = MaterialTheme.colorScheme.primary,
                    fontWeight = FontWeight.Bold
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            LinearProgressIndicator(
                progress = if (progress.total > 0) progress.current.toFloat() / progress.total.toFloat() else 0f,
                modifier = Modifier.fillMaxWidth(),
                color = MaterialTheme.colorScheme.primary,
                trackColor = MaterialTheme.colorScheme.surfaceVariant
            )
        }
    }
}

@Composable
fun TimeRemainingCard(
    timeRemaining: Long,
    isWarning: Boolean,
    isCritical: Boolean,
    modifier: Modifier = Modifier
) {
    val minutes = timeRemaining / (60 * 1000)
    val seconds = (timeRemaining % (60 * 1000)) / 1000
    val timeText = String.format("%02d:%02d", minutes, seconds)
    
    val cardColor = when {
        isCritical -> MaterialTheme.colorScheme.errorContainer
        isWarning -> MaterialTheme.colorScheme.tertiaryContainer
        else -> MaterialTheme.colorScheme.primaryContainer
    }
    
    val textColor = when {
        isCritical -> MaterialTheme.colorScheme.onErrorContainer
        isWarning -> MaterialTheme.colorScheme.onTertiaryContainer
        else -> MaterialTheme.colorScheme.onPrimaryContainer
    }
    
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(containerColor = cardColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = when {
                    isCritical -> Icons.Default.Warning
                    isWarning -> Icons.Default.Schedule
                    else -> Icons.Default.Timer
                },
                contentDescription = null,
                tint = textColor,
                modifier = Modifier.size(24.dp)
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Text(
                text = "剩余时间：$timeText",
                style = MaterialTheme.typography.titleLarge,
                color = textColor,
                fontWeight = FontWeight.Bold
            )
        }
    }
}

@Composable
fun QuestionContentCard(
    question: Question,
    flowState: InterviewFlowService.InterviewFlowState,
    isPlaying: Boolean,
    onReadQuestion: () -> Unit,
    onStopReading: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 问题类型和标题
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = question.type.displayName,
                    style = MaterialTheme.typography.labelLarge,
                    color = MaterialTheme.colorScheme.primary,
                    fontWeight = FontWeight.Bold
                )
                
                // 语音播放控制
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    if (isPlaying) {
                        IconButton(onClick = onStopReading) {
                            Icon(
                                Icons.Default.Stop,
                                contentDescription = "停止朗读",
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }
                        
                        Text(
                            text = "朗读中...",
                            style = MaterialTheme.typography.labelSmall,
                            color = MaterialTheme.colorScheme.primary
                        )
                    } else {
                        IconButton(onClick = onReadQuestion) {
                            Icon(
                                Icons.Default.VolumeUp,
                                contentDescription = "朗读问题",
                                tint = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 问题标题
            if (question.title != question.content) {
                Text(
                    text = question.title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(8.dp))
            }
            
            // 问题内容
            Card(
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
            ) {
                Text(
                    text = question.content,
                    style = MaterialTheme.typography.bodyLarge,
                    modifier = Modifier.padding(16.dp),
                    lineHeight = MaterialTheme.typography.bodyLarge.lineHeight * 1.2
                )
            }
            
            // 背景信息（如果有）
            question.backgroundInfo?.let { backgroundInfo ->
                Spacer(modifier = Modifier.height(12.dp))
                
                Text(
                    text = "背景信息",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = backgroundInfo,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            // 关键考查点（如果有）
            if (question.keyPoints.isNotEmpty()) {
                Spacer(modifier = Modifier.height(12.dp))
                
                Text(
                    text = "考查要点",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                question.keyPoints.forEach { point ->
                    Row(
                        modifier = Modifier.padding(vertical = 2.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            Icons.Default.FiberManualRecord,
                            contentDescription = null,
                            modifier = Modifier.size(6.dp),
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = point,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun InterviewActionButtons(
    flowState: InterviewFlowService.InterviewFlowState,
    onStartRecording: () -> Unit,
    onReadQuestion: () -> Unit,
    onSkipQuestion: () -> Unit,
    canProceed: Boolean,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        when (flowState) {
            InterviewFlowService.InterviewFlowState.QUESTION_DISPLAY -> {
                // 显示问题阶段
                Button(
                    onClick = onReadQuestion,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(
                        Icons.Default.VolumeUp,
                        contentDescription = null,
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("朗读问题")
                }
                
                OutlinedButton(
                    onClick = onStartRecording,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(
                        Icons.Default.Mic,
                        contentDescription = null,
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("开始回答")
                }
            }
            
            InterviewFlowService.InterviewFlowState.QUESTION_READING -> {
                // 朗读问题阶段
                Text(
                    text = "正在朗读问题，请仔细听题...",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.primary,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
            }
            
            InterviewFlowService.InterviewFlowState.WAITING_ANSWER -> {
                // 等待回答阶段
                Button(
                    onClick = onStartRecording,
                    modifier = Modifier.fillMaxWidth(),
                    enabled = canProceed
                ) {
                    Icon(
                        Icons.Default.Mic,
                        contentDescription = null,
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("开始录音回答")
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedButton(
                        onClick = onReadQuestion,
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(
                            Icons.Default.Replay,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("重听题目")
                    }
                    
                    OutlinedButton(
                        onClick = onSkipQuestion,
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(
                            Icons.Default.SkipNext,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("跳过此题")
                    }
                }
            }
            
            InterviewFlowService.InterviewFlowState.RECORDING -> {
                // 录音中
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            Icons.Default.FiberManualRecord,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.error,
                            modifier = Modifier.size(24.dp)
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = "正在录音中，请开始回答...",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.onErrorContainer,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
            
            InterviewFlowService.InterviewFlowState.PROCESSING -> {
                // 处理中
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.primaryContainer
                    )
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            strokeWidth = 2.dp
                        )
                        
                        Spacer(modifier = Modifier.width(12.dp))
                        
                        Text(
                            text = "正在处理您的回答...",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }
                }
            }
            
            else -> {
                // 其他状态
                Text(
                    text = "请等待...",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

@Composable
fun ExitInterviewDialog(
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { 
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.Warning,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.error
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("退出面试")
            }
        },
        text = {
            Text("确定要退出当前面试吗？退出后本次面试记录将被保存，但无法获得完整的评价结果。")
        },
        confirmButton = {
            Button(
                onClick = onConfirm,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.error
                )
            ) {
                Text("确定退出")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("继续面试")
            }
        }
    )
}

@Composable
fun SkipQuestionDialog(
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("跳过问题") },
        text = {
            Text("确定要跳过当前问题吗？跳过的问题将不会计入最终评分。")
        },
        confirmButton = {
            Button(onClick = onConfirm) {
                Text("确定跳过")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("继续回答")
            }
        }
    )
}