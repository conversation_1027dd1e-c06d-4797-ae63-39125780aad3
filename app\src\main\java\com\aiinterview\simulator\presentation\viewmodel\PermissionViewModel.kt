package com.aiinterview.simulator.presentation.viewmodel

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.aiinterview.simulator.data.permission.*
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 权限管理ViewModel
 * 负责管理权限状态和处理权限相关的业务逻辑
 */
@HiltViewModel
class PermissionViewModel @Inject constructor(
    private val permissionManager: PermissionManager,   // 注入权限管理器
    private val permissionHandler: PermissionHandler    // 注入权限处理器
) : ViewModel() {
    
    // 权限状态的私有可变状态流
    private val _permissionState = MutableStateFlow(PermissionUiState())
    // 对外暴露的权限状态只读状态流
    val permissionState: StateFlow<PermissionUiState> = _permissionState.asStateFlow()
    
    /**
     * 检查所有必需权限的状态
     * @param activity 当前活动实例
     */
    fun checkAllPermissions(activity: Activity) {
        viewModelScope.launch {
            // 更新加载状态
            _permissionState.value = _permissionState.value.copy(isLoading = true)
            
            try {
                // 获取所有必需权限
                val requiredPermissions = permissionManager.getRequiredPermissions()
                
                // 检查每个权限的状态
                val permissionResults = permissionHandler.checkMultiplePermissionsStatus(
                    activity, 
                    requiredPermissions
                )
                
                // 判断是否所有权限都已授权
                val allGranted = permissionResults.values.all { 
                    it.status == PermissionStatus.GRANTED 
                }
                
                // 获取被拒绝的权限
                val deniedPermissions = permissionResults.filter { 
                    it.value.status != PermissionStatus.GRANTED 
                }.keys.toList()
                
                // 更新状态
                _permissionState.value = _permissionState.value.copy(
                    isLoading = false,
                    allPermissionsGranted = allGranted,
                    permissionResults = permissionResults,
                    deniedPermissions = deniedPermissions
                )
                
            } catch (e: Exception) {
                // 处理异常
                _permissionState.value = _permissionState.value.copy(
                    isLoading = false,
                    error = "检查权限状态时发生错误：${e.message}"
                )
            }
        }
    }
    
    /**
     * 请求录音权限
     * @param activity 当前活动实例
     */
    fun requestMicrophonePermission(activity: Activity) {
        viewModelScope.launch {
            // 显示录音权限对话框
            _permissionState.value = _permissionState.value.copy(
                showMicrophonePermissionDialog = true,
                currentRequestingPermission = PermissionType.MICROPHONE
            )
        }
    }
    
    /**
     * 请求存储权限
     * @param activity 当前活动实例
     */
    fun requestStoragePermission(activity: Activity) {
        viewModelScope.launch {
            // 显示存储权限对话框
            _permissionState.value = _permissionState.value.copy(
                showStoragePermissionDialog = true,
                currentRequestingPermission = PermissionType.STORAGE
            )
        }
    }
    
    /**
     * 请求所有必需权限
     * @param activity 当前活动实例
     */
    fun requestAllRequiredPermissions(activity: Activity) {
        viewModelScope.launch {
            // 获取需要请求的权限
            val permissionsToRequest = mutableListOf<PermissionType>()
            
            // 检查录音权限
            if (!permissionManager.hasMicrophonePermission()) {
                permissionsToRequest.add(PermissionType.MICROPHONE)
            }
            
            // 检查存储权限
            if (!permissionManager.hasStoragePermission()) {
                permissionsToRequest.add(PermissionType.STORAGE)
            }
            
            if (permissionsToRequest.isNotEmpty()) {
                // 显示多权限请求对话框
                _permissionState.value = _permissionState.value.copy(
                    showMultiplePermissionsDialog = true,
                    pendingPermissions = permissionsToRequest
                )
            } else {
                // 所有权限都已授权
                _permissionState.value = _permissionState.value.copy(
                    allPermissionsGranted = true
                )
            }
        }
    }
    
    /**
     * 处理权限请求结果
     * @param activity 当前活动实例
     * @param requestCode 请求码
     * @param permissions 权限数组
     * @param grantResults 授权结果数组
     */
    fun handlePermissionResult(
        activity: Activity,
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        viewModelScope.launch {
            // 处理权限请求结果
            val result = permissionHandler.handlePermissionResult(
                activity, 
                requestCode, 
                permissions, 
                grantResults
            )
            
            // 更新权限结果
            _permissionState.value = _permissionState.value.copy(
                permissionResults = _permissionState.value.permissionResults + result.results,
                allPermissionsGranted = result.allGranted
            )
            
            // 处理被拒绝的权限
            val deniedPermissions = result.getDeniedPermissions()
            val permanentlyDeniedPermissions = result.getPermanentlyDeniedPermissions()
            
            if (permanentlyDeniedPermissions.isNotEmpty()) {
                // 有权限被永久拒绝，显示设置引导对话框
                val permissionType = PermissionType.fromPermission(permanentlyDeniedPermissions.first())
                if (permissionType != null) {
                    _permissionState.value = _permissionState.value.copy(
                        showPermanentlyDeniedDialog = true,
                        permanentlyDeniedPermission = permissionType
                    )
                }
            } else if (deniedPermissions.isNotEmpty()) {
                // 有权限被拒绝但未永久拒绝
                _permissionState.value = _permissionState.value.copy(
                    deniedPermissions = deniedPermissions,
                    error = "部分权限被拒绝，某些功能可能无法正常使用"
                )
            }
        }
    }
    
    /**
     * 确认授权权限
     * @param activity 当前活动实例
     */
    fun confirmGrantPermission(activity: Activity) {
        val currentPermission = _permissionState.value.currentRequestingPermission
        if (currentPermission != null) {
            when (currentPermission) {
                PermissionType.MICROPHONE -> {
                    permissionManager.requestMicrophonePermission(activity)
                }
                PermissionType.STORAGE -> {
                    permissionManager.requestStoragePermission(activity)
                }
                else -> {
                    // 其他权限类型的处理
                }
            }
        }
        
        // 隐藏对话框
        hideAllDialogs()
    }
    
    /**
     * 确认授权所有权限
     * @param activity 当前活动实例
     */
    fun confirmGrantAllPermissions(activity: Activity) {
        permissionManager.requestAllRequiredPermissions(activity)
        hideAllDialogs()
    }
    
    /**
     * 拒绝权限
     */
    fun denyPermission() {
        val currentPermission = _permissionState.value.currentRequestingPermission
        if (currentPermission != null) {
            // 记录权限被拒绝
            val deniedList = _permissionState.value.deniedPermissions.toMutableList()
            deniedList.add(currentPermission.permission)
            
            _permissionState.value = _permissionState.value.copy(
                deniedPermissions = deniedList,
                error = "${currentPermission.title}被拒绝，相关功能可能无法正常使用"
            )
        }
        
        hideAllDialogs()
    }
    
    /**
     * 前往应用设置页面
     * @param activity 当前活动实例
     */
    fun goToAppSettings(activity: Activity) {
        try {
            // 创建前往应用设置的Intent
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.fromParts("package", activity.packageName, null)
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            activity.startActivity(intent)
        } catch (e: Exception) {
            // 如果无法打开应用设置，尝试打开通用设置
            try {
                val intent = Intent(Settings.ACTION_SETTINGS)
                activity.startActivity(intent)
            } catch (e2: Exception) {
                _permissionState.value = _permissionState.value.copy(
                    error = "无法打开设置页面，请手动前往设置中开启权限"
                )
            }
        }
        
        hideAllDialogs()
    }
    
    /**
     * 隐藏所有对话框
     */
    fun hideAllDialogs() {
        _permissionState.value = _permissionState.value.copy(
            showMicrophonePermissionDialog = false,
            showStoragePermissionDialog = false,
            showMultiplePermissionsDialog = false,
            showPermanentlyDeniedDialog = false,
            currentRequestingPermission = null,
            permanentlyDeniedPermission = null
        )
    }
    
    /**
     * 清除错误信息
     */
    fun clearError() {
        _permissionState.value = _permissionState.value.copy(error = null)
    }
    
    /**
     * 检查特定权限是否已授权
     * @param permissionType 权限类型
     * @return true表示已授权，false表示未授权
     */
    fun isPermissionGranted(permissionType: PermissionType): Boolean {
        return when (permissionType) {
            PermissionType.MICROPHONE -> permissionManager.hasMicrophonePermission()
            PermissionType.STORAGE -> permissionManager.hasStoragePermission()
            PermissionType.NETWORK_STATE -> permissionManager.hasNetworkStatePermission()
            PermissionType.INTERNET -> true // 网络权限在manifest中声明，不需要运行时请求
        }
    }
}

/**
 * 权限UI状态数据类
 * 封装权限相关的UI状态信息
 */
data class PermissionUiState(
    val isLoading: Boolean = false,                                         // 是否正在加载
    val allPermissionsGranted: Boolean = false,                            // 是否所有权限都已授权
    val permissionResults: Map<String, PermissionResult> = emptyMap(),     // 权限结果映射
    val deniedPermissions: List<String> = emptyList(),                     // 被拒绝的权限列表
    val showMicrophonePermissionDialog: Boolean = false,                   // 是否显示录音权限对话框
    val showStoragePermissionDialog: Boolean = false,                      // 是否显示存储权限对话框
    val showMultiplePermissionsDialog: Boolean = false,                    // 是否显示多权限对话框
    val showPermanentlyDeniedDialog: Boolean = false,                      // 是否显示永久拒绝对话框
    val currentRequestingPermission: PermissionType? = null,               // 当前请求的权限类型
    val permanentlyDeniedPermission: PermissionType? = null,               // 被永久拒绝的权限类型
    val pendingPermissions: List<PermissionType> = emptyList(),            // 待请求的权限列表
    val error: String? = null                                              // 错误信息
)