package com.aiinterview.simulator.data.dao

import androidx.room.*
import com.aiinterview.simulator.data.model.InterviewRecord
import com.aiinterview.simulator.data.model.InterviewRecordSummary
import kotlinx.coroutines.flow.Flow

@Dao
interface InterviewRecordDao {
    
    /**
     * 获取用户的所有面试记录，按时间倒序排列
     */
    @Query("SELECT * FROM interview_records WHERE userId = :userId ORDER BY startTime DESC")
    fun getRecordsByUser(userId: String): Flow<List<InterviewRecord>>
    
    /**
     * 获取用户的面试记录摘要，用于列表显示
     */
    @Query("""
        SELECT id, sessionId, positionName, positionCategory, startTime, duration, 
               overallScore, status, isSynced 
        FROM interview_records 
        WHERE userId = :userId 
        ORDER BY startTime DESC
    """)
    fun getRecordSummariesByUser(userId: String): Flow<List<InterviewRecordSummary>>
    
    /**
     * 根据ID获取面试记录
     */
    @Query("SELECT * FROM interview_records WHERE id = :recordId")
    suspend fun getRecordById(recordId: String): InterviewRecord?
    
    /**
     * 根据会话ID获取面试记录
     */
    @Query("SELECT * FROM interview_records WHERE sessionId = :sessionId")
    suspend fun getRecordBySessionId(sessionId: String): InterviewRecord?
    
    /**
     * 获取指定时间范围内的面试记录
     */
    @Query("""
        SELECT * FROM interview_records 
        WHERE userId = :userId 
        AND startTime >= :startTime 
        AND startTime <= :endTime 
        ORDER BY startTime DESC
    """)
    suspend fun getRecordsByTimeRange(
        userId: String, 
        startTime: Long, 
        endTime: Long
    ): List<InterviewRecord>
    
    /**
     * 根据岗位类别获取面试记录
     */
    @Query("""
        SELECT * FROM interview_records 
        WHERE userId = :userId 
        AND positionCategory = :category 
        ORDER BY startTime DESC
    """)
    suspend fun getRecordsByCategory(userId: String, category: String): List<InterviewRecord>
    
    /**
     * 搜索面试记录（根据岗位名称和类别）
     * 支持在岗位名称和岗位类别中搜索关键词
     */
    @Query("""
        SELECT * FROM interview_records 
        WHERE userId = :userId 
        AND (positionName LIKE '%' || :keyword || '%' 
             OR positionCategory LIKE '%' || :keyword || '%')
        ORDER BY startTime DESC
    """)
    suspend fun searchRecords(userId: String, keyword: String): List<InterviewRecord>
    
    /**
     * 根据多个条件筛选面试记录
     * @param userId 用户ID
     * @param category 岗位类别（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param status 面试状态（可选）
     */
    @Query("""
        SELECT * FROM interview_records 
        WHERE userId = :userId 
        AND (:category IS NULL OR positionCategory = :category)
        AND (:startTime IS NULL OR startTime >= :startTime)
        AND (:endTime IS NULL OR startTime <= :endTime)
        AND (:status IS NULL OR status = :status)
        ORDER BY startTime DESC
    """)
    suspend fun filterRecords(
        userId: String,
        category: String? = null,
        startTime: Long? = null,
        endTime: Long? = null,
        status: String? = null
    ): List<InterviewRecord>
    
    /**
     * 组合搜索和筛选功能
     * 支持关键词搜索和条件筛选的组合
     */
    @Query("""
        SELECT * FROM interview_records 
        WHERE userId = :userId 
        AND (:keyword IS NULL OR :keyword = '' 
             OR positionName LIKE '%' || :keyword || '%' 
             OR positionCategory LIKE '%' || :keyword || '%')
        AND (:category IS NULL OR positionCategory = :category)
        AND (:startTime IS NULL OR startTime >= :startTime)
        AND (:endTime IS NULL OR startTime <= :endTime)
        AND (:status IS NULL OR status = :status)
        ORDER BY startTime DESC
    """)
    suspend fun searchAndFilterRecords(
        userId: String,
        keyword: String? = null,
        category: String? = null,
        startTime: Long? = null,
        endTime: Long? = null,
        status: String? = null
    ): List<InterviewRecord>
    
    /**
     * 获取未同步的面试记录
     */
    @Query("SELECT * FROM interview_records WHERE isSynced = 0")
    suspend fun getUnsyncedRecords(): List<InterviewRecord>
    
    /**
     * 获取用户的面试记录统计信息
     */
    @Query("""
        SELECT COUNT(*) as totalCount,
               AVG(overallScore) as avgScore,
               MAX(overallScore) as maxScore,
               MIN(startTime) as firstInterviewTime,
               MAX(startTime) as lastInterviewTime
        FROM interview_records 
        WHERE userId = :userId 
        AND status = 'COMPLETED'
        AND overallScore IS NOT NULL
    """)
    suspend fun getRecordStatistics(userId: String): InterviewRecordStatistics?
    
    /**
     * 插入面试记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRecord(record: InterviewRecord)
    
    /**
     * 批量插入面试记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRecords(records: List<InterviewRecord>)
    
    /**
     * 更新面试记录
     */
    @Update
    suspend fun updateRecord(record: InterviewRecord)
    
    /**
     * 更新同步状态
     */
    @Query("""
        UPDATE interview_records 
        SET isSynced = :isSynced, syncedAt = :syncedAt, updatedAt = :updatedAt
        WHERE id = :recordId
    """)
    suspend fun updateSyncStatus(recordId: String, isSynced: Boolean, syncedAt: Long?, updatedAt: Long)
    
    /**
     * 删除面试记录
     */
    @Delete
    suspend fun deleteRecord(record: InterviewRecord)
    
    /**
     * 根据ID删除面试记录
     */
    @Query("DELETE FROM interview_records WHERE id = :recordId")
    suspend fun deleteRecordById(recordId: String)
    
    /**
     * 删除指定时间之前的记录
     */
    @Query("DELETE FROM interview_records WHERE userId = :userId AND startTime < :beforeTime")
    suspend fun deleteRecordsBefore(userId: String, beforeTime: Long)
    
    /**
     * 获取记录总数
     */
    @Query("SELECT COUNT(*) FROM interview_records WHERE userId = :userId")
    suspend fun getRecordCount(userId: String): Int
}

/**
 * 面试记录统计信息
 */
data class InterviewRecordStatistics(
    val totalCount: Int,
    val avgScore: Double?,
    val maxScore: Double?,
    val firstInterviewTime: Long?,
    val lastInterviewTime: Long?
)