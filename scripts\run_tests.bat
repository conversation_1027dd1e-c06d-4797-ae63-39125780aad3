@echo off
REM AI面试模拟器测试执行脚本
REM 用于运行单元测试和UI测试

echo ================================
echo AI面试模拟器 - 测试执行脚本
echo ================================

echo.
echo 正在检查环境...

REM 检查是否在项目根目录
if not exist "app\build.gradle.kts" (
    echo 错误: 请在项目根目录运行此脚本
    pause
    exit /b 1
)

echo 环境检查完成
echo.

REM 设置选项
set /p choice="请选择测试类型 (1=单元测试, 2=UI测试, 3=全部测试, 4=生成覆盖率报告): "

if "%choice%"=="1" goto unit_tests
if "%choice%"=="2" goto ui_tests
if "%choice%"=="3" goto all_tests
if "%choice%"=="4" goto coverage_report
goto invalid_choice

:unit_tests
echo.
echo ================================
echo 运行单元测试
echo ================================
echo.
call gradlew test
if %errorlevel% neq 0 (
    echo 单元测试执行失败
    pause
    exit /b 1
)
echo 单元测试执行完成
goto end

:ui_tests
echo.
echo ================================
echo 运行UI测试
echo ================================
echo.
echo 请确保已连接Android设备或启动模拟器
pause
call gradlew connectedAndroidTest
if %errorlevel% neq 0 (
    echo UI测试执行失败
    pause
    exit /b 1
)
echo UI测试执行完成
goto end

:all_tests
echo.
echo ================================
echo 运行所有测试
echo ================================
echo.
echo 1. 运行单元测试...
call gradlew test
if %errorlevel% neq 0 (
    echo 单元测试执行失败
    pause
    exit /b 1
)
echo 单元测试完成

echo.
echo 2. 运行UI测试...
echo 请确保已连接Android设备或启动模拟器
pause
call gradlew connectedAndroidTest
if %errorlevel% neq 0 (
    echo UI测试执行失败
    pause
    exit /b 1
)
echo 所有测试执行完成
goto end

:coverage_report
echo.
echo ================================
echo 生成测试覆盖率报告
echo ================================
echo.
call gradlew testDebugUnitTestCoverage
if %errorlevel% neq 0 (
    echo 覆盖率报告生成失败
    pause
    exit /b 1
)
echo 覆盖率报告生成完成
echo 报告位置: app\build\reports\coverage\test\debug\
start app\build\reports\coverage\test\debug\index.html
goto end

:invalid_choice
echo 无效选择，请重新运行脚本
pause
exit /b 1

:end
echo.
echo ================================
echo 测试执行完成
echo ================================
echo.
echo 测试报告位置:
echo - 单元测试报告: app\build\reports\tests\testDebugUnitTest\
echo - UI测试报告: app\build\reports\androidTests\connected\
echo - 覆盖率报告: app\build\reports\coverage\test\debug\
echo.
pause