package com.aiinterview.simulator.di

import android.content.Context
import com.aiinterview.simulator.data.permission.PermissionHandler
import com.aiinterview.simulator.data.permission.PermissionManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 权限模块依赖注入配置
 * 提供权限管理相关的依赖注入
 */
@Module
@InstallIn(SingletonComponent::class)
object PermissionModule {
    
    /**
     * 提供权限管理器实例
     * @param context 应用上下文
     * @return 权限管理器单例
     */
    @Provides
    @Singleton
    fun providePermissionManager(
        @ApplicationContext context: Context
    ): PermissionManager {
        return PermissionManager(context)
    }
    
    /**
     * 提供权限处理器实例
     * @param permissionManager 权限管理器
     * @return 权限处理器单例
     */
    @Provides
    @Singleton
    fun providePermissionHandler(
        permissionManager: PermissionManager
    ): PermissionHandler {
        return PermissionHandler(permissionManager)
    }
}