package com.aiinterview.simulator.data.dto.request

data class SpeechRecognitionRequest(
    val audioUrl: String? = null,
    val audioBase64: String? = null,
    val format: String = "m4a", // 音频格式
    val rate: Int = 16000, // 采样率
    val channel: Int = 1, // 声道数
    val token: String? = null, // 访问令牌
    val cuid: String? = null, // 用户唯一标识
    val len: Int? = null, // 音频长度
    val speech: String? = null, // 音频数据base64编码
    val dev_pid: Int = 1537 // 普通话(支持简单的英文识别)
)

data class BaiduASRRequest(
    val format: String = "m4a",
    val rate: Int = 16000,
    val channel: Int = 1,
    val cuid: String,
    val token: String,
    val len: Int,
    val speech: String,
    val dev_pid: Int = 1537
)

data class TencentASRRequest(
    val ProjectId: Int = 0,
    val SubServiceType: Int = 2,
    val EngSerViceType: String = "16k_zh",
    val SourceType: Int = 1,
    val VoiceFormat: String = "m4a",
    val UsrAudioKey: String,
    val Url: String? = null,
    val Data: String? = null,
    val DataLen: Int? = null
)