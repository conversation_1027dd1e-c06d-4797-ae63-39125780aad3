package com.aiinterview.simulator.presentation.viewmodel

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import app.cash.turbine.test
import com.aiinterview.simulator.data.repository.InterviewRepository
import com.aiinterview.simulator.data.dto.response.InterviewResultResponse
import com.aiinterview.simulator.data.dto.response.InterviewSessionResponse
import com.aiinterview.simulator.data.dto.response.QuestionResponse
import com.aiinterview.simulator.data.model.InterviewSession
import com.aiinterview.simulator.data.model.Position
import com.aiinterview.simulator.domain.util.Resource
import com.aiinterview.simulator.util.MainCoroutineRule
import com.aiinterview.simulator.util.MockDataFactory
import com.aiinterview.simulator.util.TestUtils
import com.google.common.truth.Truth.assertThat
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.*

/**
 * InterviewViewModel单元测试类
 * 测试面试相关的ViewModel功能
 */
@ExperimentalCoroutinesApi
class InterviewViewModelTest {

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    @get:Rule
    val mainCoroutineRule = MainCoroutineRule()

    @Mock
    private lateinit var interviewRepository: InterviewRepository

    private lateinit var interviewViewModel: InterviewViewModel

    // 测试数据
    private val testPosition = MockDataFactory.createMockPosition()
    private val testPositions = listOf(testPosition)
    private val testInterviewSession = MockDataFactory.createMockInterviewSession()
    private val testInterviewSessions = listOf(testInterviewSession)

    @Before
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        interviewViewModel = InterviewViewModel(interviewRepository)
    }

    /**
     * 测试加载岗位列表成功场景
     */
    @Test
    fun `loadPositions should update positionsState when successful`() = runTest {
        // Given - 准备成功的岗位列表响应
        val successResource = Resource.Success(testPositions)
        whenever(interviewRepository.getPositions())
            .thenReturn(flowOf(Resource.Loading(), successResource))

        // When - 加载岗位列表
        interviewViewModel.loadPositions()

        // Then - 验证状态变化
        interviewViewModel.positionsState.test {
            // 应该先收到Loading状态
            val loadingState = awaitItem()
            assertThat(loadingState).isInstanceOf(Resource.Loading::class.java)

            // 然后收到Success状态
            val successState = awaitItem()
            assertThat(successState).isInstanceOf(Resource.Success::class.java)
            assertThat(successState.data).isEqualTo(testPositions)

            cancelAndIgnoreRemainingEvents()
        }

        verify(interviewRepository).getPositions()
    }

    /**
     * 测试加载岗位列表失败场景
     */
    @Test
    fun `loadPositions should update positionsState with error when failed`() = runTest {
        // Given - 准备失败的岗位列表响应
        val errorMessage = "网络连接失败"
        val errorResource = Resource.Error<List<Position>>(errorMessage)
        whenever(interviewRepository.getPositions())
            .thenReturn(flowOf(Resource.Loading(), errorResource))

        // When - 加载岗位列表
        interviewViewModel.loadPositions()

        // Then - 验证状态变化
        interviewViewModel.positionsState.test {
            val loadingState = awaitItem()
            assertThat(loadingState).isInstanceOf(Resource.Loading::class.java)

            val errorState = awaitItem()
            assertThat(errorState).isInstanceOf(Resource.Error::class.java)
            assertThat(errorState.message).isEqualTo(errorMessage)

            cancelAndIgnoreRemainingEvents()
        }
    }

    /**
     * 测试选择岗位功能
     */
    @Test
    fun `selectPosition should update selectedPosition`() = runTest {
        // When - 选择岗位
        interviewViewModel.selectPosition(testPosition)

        // Then - 验证选中的岗位
        interviewViewModel.selectedPosition.test {
            val selectedPosition = awaitItem()
            assertThat(selectedPosition).isEqualTo(testPosition)
            cancelAndIgnoreRemainingEvents()
        }
    }

    /**
     * 测试根据ID加载岗位成功场景
     */
    @Test
    fun `loadPositionById should update selectedPosition when successful`() = runTest {
        // Given - 准备成功的岗位响应
        val positionId = "position_001"
        val successResource = Resource.Success(testPosition)
        whenever(interviewRepository.getPositionById(positionId))
            .thenReturn(flowOf(Resource.Loading(), successResource))

        // When - 根据ID加载岗位
        interviewViewModel.loadPositionById(positionId)

        // Then - 验证选中的岗位更新
        interviewViewModel.selectedPosition.test {
            val selectedPosition = awaitItem()
            assertThat(selectedPosition).isEqualTo(testPosition)
            cancelAndIgnoreRemainingEvents()
        }

        verify(interviewRepository).getPositionById(positionId)
    }

    /**
     * 测试根据ID加载岗位失败场景
     */
    @Test
    fun `loadPositionById should not update selectedPosition when failed`() = runTest {
        // Given - 准备失败的岗位响应
        val positionId = "position_001"
        val errorResource = Resource.Error<Position>("岗位不存在")
        whenever(interviewRepository.getPositionById(positionId))
            .thenReturn(flowOf(Resource.Loading(), errorResource))

        // When - 根据ID加载岗位
        interviewViewModel.loadPositionById(positionId)

        // Then - 验证选中的岗位没有更新（保持初始值null）
        interviewViewModel.selectedPosition.test {
            val selectedPosition = awaitItem()
            assertThat(selectedPosition).isNull()
            cancelAndIgnoreRemainingEvents()
        }
    }

    /**
     * 测试开始面试成功场景
     */
    @Test
    fun `startInterview should update interviewSessionState when successful`() = runTest {
        // Given - 准备成功的面试会话响应
        val userId = TestUtils.createTestUserId()
        val positionId = "position_001"
        val sessionResponse = InterviewSessionResponse(
            sessionId = TestUtils.createTestSessionId(),
            questions = listOf(MockDataFactory.createMockQuestion()),
            currentQuestionIndex = 0
        )
        val successResource = Resource.Success(sessionResponse)
        whenever(interviewRepository.startInterview(userId, positionId))
            .thenReturn(flowOf(Resource.Loading(), successResource))

        // When - 开始面试
        interviewViewModel.startInterview(userId, positionId)

        // Then - 验证状态变化
        interviewViewModel.interviewSessionState.test {
            val loadingState = awaitItem()
            assertThat(loadingState).isInstanceOf(Resource.Loading::class.java)

            val successState = awaitItem()
            assertThat(successState).isInstanceOf(Resource.Success::class.java)
            assertThat(successState.data).isEqualTo(sessionResponse)

            cancelAndIgnoreRemainingEvents()
        }

        verify(interviewRepository).startInterview(userId, positionId)
    }

    /**
     * 测试提交答案成功场景
     */
    @Test
    fun `submitAnswer should update questionResponseState when successful`() = runTest {
        // Given - 准备成功的问题响应
        val sessionId = TestUtils.createTestSessionId()
        val questionId = TestUtils.createTestQuestionId()
        val audioUrl = "https://example.com/audio.mp3"
        val transcription = "这是我的回答"
        val duration = 120
        val questionResponse = QuestionResponse(
            nextQuestion = MockDataFactory.createMockQuestion(),
            isLastQuestion = false,
            currentQuestionIndex = 1
        )
        val successResource = Resource.Success(questionResponse)
        whenever(interviewRepository.submitAnswer(sessionId, questionId, audioUrl, transcription, duration))
            .thenReturn(flowOf(Resource.Loading(), successResource))

        // When - 提交答案
        interviewViewModel.submitAnswer(sessionId, questionId, audioUrl, transcription, duration)

        // Then - 验证状态变化
        interviewViewModel.questionResponseState.test {
            val loadingState = awaitItem()
            assertThat(loadingState).isInstanceOf(Resource.Loading::class.java)

            val successState = awaitItem()
            assertThat(successState).isInstanceOf(Resource.Success::class.java)
            assertThat(successState.data).isEqualTo(questionResponse)

            cancelAndIgnoreRemainingEvents()
        }

        verify(interviewRepository).submitAnswer(sessionId, questionId, audioUrl, transcription, duration)
    }

    /**
     * 测试获取面试结果成功场景
     */
    @Test
    fun `getInterviewResult should update interviewResultState when successful`() = runTest {
        // Given - 准备成功的面试结果响应
        val sessionId = TestUtils.createTestSessionId()
        val interviewResult = InterviewResultResponse(
            evaluation = MockDataFactory.createMockInterviewEvaluation(sessionId = sessionId),
            session = testInterviewSession
        )
        val successResource = Resource.Success(interviewResult)
        whenever(interviewRepository.getInterviewResult(sessionId))
            .thenReturn(flowOf(Resource.Loading(), successResource))

        // When - 获取面试结果
        interviewViewModel.getInterviewResult(sessionId)

        // Then - 验证状态变化
        interviewViewModel.interviewResultState.test {
            val loadingState = awaitItem()
            assertThat(loadingState).isInstanceOf(Resource.Loading::class.java)

            val successState = awaitItem()
            assertThat(successState).isInstanceOf(Resource.Success::class.java)
            assertThat(successState.data).isEqualTo(interviewResult)

            cancelAndIgnoreRemainingEvents()
        }

        verify(interviewRepository).getInterviewResult(sessionId)
    }

    /**
     * 测试加载用户面试会话列表
     */
    @Test
    fun `loadUserInterviewSessions should update userInterviewSessions`() = runTest {
        // Given - 准备用户面试会话列表
        val userId = TestUtils.createTestUserId()
        whenever(interviewRepository.getUserInterviewSessions(userId))
            .thenReturn(flowOf(testInterviewSessions))

        // When - 加载用户面试会话
        interviewViewModel.loadUserInterviewSessions(userId)

        // Then - 验证状态更新
        interviewViewModel.userInterviewSessions.test {
            val sessions = awaitItem()
            assertThat(sessions).isEqualTo(testInterviewSessions)
            cancelAndIgnoreRemainingEvents()
        }

        verify(interviewRepository).getUserInterviewSessions(userId)
    }

    /**
     * 测试清除面试会话状态
     */
    @Test
    fun `clearInterviewSession should set interviewSessionState to null`() = runTest {
        // Given - 设置初始面试会话状态
        val sessionResponse = InterviewSessionResponse(
            sessionId = TestUtils.createTestSessionId(),
            questions = listOf(MockDataFactory.createMockQuestion()),
            currentQuestionIndex = 0
        )
        whenever(interviewRepository.startInterview(any(), any()))
            .thenReturn(flowOf(Resource.Success(sessionResponse)))
        
        interviewViewModel.startInterview("userId", "positionId")

        // When - 清除面试会话状态
        interviewViewModel.clearInterviewSession()

        // Then - 验证状态被清除
        interviewViewModel.interviewSessionState.test {
            val state = awaitItem()
            assertThat(state).isNull()
            cancelAndIgnoreRemainingEvents()
        }
    }

    /**
     * 测试清除问题响应状态
     */
    @Test
    fun `clearQuestionResponse should set questionResponseState to null`() = runTest {
        // Given - 设置初始问题响应状态
        val questionResponse = QuestionResponse(
            nextQuestion = MockDataFactory.createMockQuestion(),
            isLastQuestion = false,
            currentQuestionIndex = 1
        )
        whenever(interviewRepository.submitAnswer(any(), any(), any(), any(), any()))
            .thenReturn(flowOf(Resource.Success(questionResponse)))
        
        interviewViewModel.submitAnswer("sessionId", "questionId", "audioUrl", "transcription", 120)

        // When - 清除问题响应状态
        interviewViewModel.clearQuestionResponse()

        // Then - 验证状态被清除
        interviewViewModel.questionResponseState.test {
            val state = awaitItem()
            assertThat(state).isNull()
            cancelAndIgnoreRemainingEvents()
        }
    }

    /**
     * 测试清除面试结果状态
     */
    @Test
    fun `clearInterviewResult should set interviewResultState to null`() = runTest {
        // Given - 设置初始面试结果状态
        val interviewResult = InterviewResultResponse(
            evaluation = MockDataFactory.createMockInterviewEvaluation(),
            session = testInterviewSession
        )
        whenever(interviewRepository.getInterviewResult(any()))
            .thenReturn(flowOf(Resource.Success(interviewResult)))
        
        interviewViewModel.getInterviewResult("sessionId")

        // When - 清除面试结果状态
        interviewViewModel.clearInterviewResult()

        // Then - 验证状态被清除
        interviewViewModel.interviewResultState.test {
            val state = awaitItem()
            assertThat(state).isNull()
            cancelAndIgnoreRemainingEvents()
        }
    }

    /**
     * 测试ViewModel初始状态
     */
    @Test
    fun `initial state should be correct`() = runTest {
        // Then - 验证初始状态
        interviewViewModel.positionsState.test {
            val initialState = awaitItem()
            assertThat(initialState).isNull()
            cancelAndIgnoreRemainingEvents()
        }

        interviewViewModel.selectedPosition.test {
            val initialPosition = awaitItem()
            assertThat(initialPosition).isNull()
            cancelAndIgnoreRemainingEvents()
        }

        interviewViewModel.interviewSessionState.test {
            val initialState = awaitItem()
            assertThat(initialState).isNull()
            cancelAndIgnoreRemainingEvents()
        }

        interviewViewModel.questionResponseState.test {
            val initialState = awaitItem()
            assertThat(initialState).isNull()
            cancelAndIgnoreRemainingEvents()
        }

        interviewViewModel.interviewResultState.test {
            val initialState = awaitItem()
            assertThat(initialState).isNull()
            cancelAndIgnoreRemainingEvents()
        }

        interviewViewModel.userInterviewSessions.test {
            val initialSessions = awaitItem()
            assertThat(initialSessions).isEmpty()
            cancelAndIgnoreRemainingEvents()
        }
    }

    /**
     * 测试多个操作的状态管理
     */
    @Test
    fun `multiple operations should maintain independent states`() = runTest {
        // Given - 准备多个操作的响应
        val userId = TestUtils.createTestUserId()
        val positionId = "position_001"
        val sessionId = TestUtils.createTestSessionId()
        
        val positionsResource = Resource.Success(testPositions)
        val sessionResource = Resource.Success(InterviewSessionResponse(
            sessionId = sessionId,
            questions = listOf(MockDataFactory.createMockQuestion()),
            currentQuestionIndex = 0
        ))
        
        whenever(interviewRepository.getPositions()).thenReturn(flowOf(positionsResource))
        whenever(interviewRepository.startInterview(userId, positionId)).thenReturn(flowOf(sessionResource))

        // When - 执行多个操作
        interviewViewModel.loadPositions()
        interviewViewModel.startInterview(userId, positionId)

        // Then - 验证各个状态都正确更新
        interviewViewModel.positionsState.test {
            val positionsState = awaitItem()
            assertThat(positionsState).isInstanceOf(Resource.Success::class.java)
            assertThat(positionsState.data).isEqualTo(testPositions)
            cancelAndIgnoreRemainingEvents()
        }

        interviewViewModel.interviewSessionState.test {
            val sessionState = awaitItem()
            assertThat(sessionState).isInstanceOf(Resource.Success::class.java)
            assertThat(sessionState.data?.sessionId).isEqualTo(sessionId)
            cancelAndIgnoreRemainingEvents()
        }
    }
}