package com.aiinterview.simulator.data.security

import android.content.Context
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import java.io.File
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 数据清理管理器
 * 负责清理敏感数据和临时文件
 */
@Singleton
class DataCleanupManager @Inject constructor(
    @ApplicationContext private val context: Context,           // 注入应用上下文
    private val encryptedStorageManager: EncryptedStorageManager, // 注入加密存储管理器
    private val privacyManager: PrivacyManager                  // 注入隐私管理器
) {
    
    companion object {
        // 临时文件最大保留时间（24小时）
        private const val TEMP_FILE_MAX_AGE = 24 * 60 * 60 * 1000L
        // 录音文件最大保留时间（7天）
        private const val AUDIO_FILE_MAX_AGE = 7 * 24 * 60 * 60 * 1000L
        // 缓存文件最大保留时间（3天）
        private const val CACHE_FILE_MAX_AGE = 3 * 24 * 60 * 60 * 1000L
        // 日志文件最大保留时间（30天）
        private const val LOG_FILE_MAX_AGE = 30 * 24 * 60 * 60 * 1000L
    }
    
    // 协程作用域
    private val cleanupScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    /**
     * 执行完整的数据清理
     * @param cleanupType 清理类型
     */
    suspend fun performFullCleanup(cleanupType: CleanupType = CleanupType.ROUTINE) {
        withContext(Dispatchers.IO) {
            try {
                // 记录清理开始
                privacyManager.logDataAccess(
                    "system_data", 
                    DataAccessType.DELETE, 
                    "执行${cleanupType.displayName}数据清理"
                )
                
                // 并行执行各种清理任务
                val cleanupJobs = listOf(
                    async { cleanupTemporaryFiles() },
                    async { cleanupExpiredAudioFiles() },
                    async { cleanupCacheFiles() },
                    async { cleanupExpiredLogs() },
                    async { cleanupOrphanedData() },
                    async { cleanupExpiredSessions() }
                )
                
                // 等待所有清理任务完成
                cleanupJobs.awaitAll()
                
                // 记录清理完成
                privacyManager.logDataAccess(
                    "system_data", 
                    DataAccessType.DELETE, 
                    "${cleanupType.displayName}数据清理完成"
                )
                
            } catch (e: Exception) {
                // 记录清理失败
                privacyManager.logDataAccess(
                    "system_data", 
                    DataAccessType.DELETE, 
                    "数据清理失败：${e.message}"
                )
            }
        }
    }
    
    /**
     * 清理临时文件
     */
    suspend fun cleanupTemporaryFiles() {
        withContext(Dispatchers.IO) {
            try {
                val tempDir = File(context.cacheDir, "temp")
                if (tempDir.exists()) {
                    cleanupFilesOlderThan(tempDir, TEMP_FILE_MAX_AGE)
                }
                
                // 清理应用临时目录
                val appTempDir = File(context.filesDir, "temp")
                if (appTempDir.exists()) {
                    cleanupFilesOlderThan(appTempDir, TEMP_FILE_MAX_AGE)
                }
                
            } catch (e: Exception) {
                // 记录清理失败
                privacyManager.logDataAccess(
                    "temp_files", 
                    DataAccessType.DELETE, 
                    "临时文件清理失败：${e.message}"
                )
            }
        }
    }
    
    /**
     * 清理过期的录音文件
     */
    suspend fun cleanupExpiredAudioFiles() {
        withContext(Dispatchers.IO) {
            try {
                val audioDir = File(context.filesDir, "audio")
                if (audioDir.exists()) {
                    cleanupFilesOlderThan(audioDir, AUDIO_FILE_MAX_AGE)
                }
                
                // 清理外部存储中的录音文件（如果有）
                val externalAudioDir = File(context.getExternalFilesDir(null), "audio")
                if (externalAudioDir?.exists() == true) {
                    cleanupFilesOlderThan(externalAudioDir, AUDIO_FILE_MAX_AGE)
                }
                
            } catch (e: Exception) {
                privacyManager.logDataAccess(
                    "audio_files", 
                    DataAccessType.DELETE, 
                    "录音文件清理失败：${e.message}"
                )
            }
        }
    }
    
    /**
     * 清理缓存文件
     */
    suspend fun cleanupCacheFiles() {
        withContext(Dispatchers.IO) {
            try {
                // 清理应用缓存目录
                cleanupFilesOlderThan(context.cacheDir, CACHE_FILE_MAX_AGE)
                
                // 清理外部缓存目录
                context.externalCacheDir?.let { externalCacheDir ->
                    cleanupFilesOlderThan(externalCacheDir, CACHE_FILE_MAX_AGE)
                }
                
            } catch (e: Exception) {
                privacyManager.logDataAccess(
                    "cache_files", 
                    DataAccessType.DELETE, 
                    "缓存文件清理失败：${e.message}"
                )
            }
        }
    }
    
    /**
     * 清理过期的日志文件
     */
    suspend fun cleanupExpiredLogs() {
        withContext(Dispatchers.IO) {
            try {
                val logDir = File(context.filesDir, "logs")
                if (logDir.exists()) {
                    cleanupFilesOlderThan(logDir, LOG_FILE_MAX_AGE)
                }
                
            } catch (e: Exception) {
                privacyManager.logDataAccess(
                    "log_files", 
                    DataAccessType.DELETE, 
                    "日志文件清理失败：${e.message}"
                )
            }
        }
    }
    
    /**
     * 清理孤立的数据
     * 清理没有关联用户或会话的数据
     */
    suspend fun cleanupOrphanedData() {
        withContext(Dispatchers.IO) {
            try {
                // 清理没有用户ID的临时数据
                val currentUserId = encryptedStorageManager.getUserId()
                if (currentUserId == null) {
                    // 如果没有当前用户，清理所有临时数据
                    encryptedStorageManager.clearAllInterviewTempData()
                }
                
                // 清理过期的会话数据
                cleanupExpiredSessionData()
                
            } catch (e: Exception) {
                privacyManager.logDataAccess(
                    "orphaned_data", 
                    DataAccessType.DELETE, 
                    "孤立数据清理失败：${e.message}"
                )
            }
        }
    }
    
    /**
     * 清理过期的会话数据
     */
    suspend fun cleanupExpiredSessions() {
        withContext(Dispatchers.IO) {
            try {
                // 清理超过24小时的面试会话临时数据
                val currentSessionId = encryptedStorageManager.getCurrentInterviewSessionId()
                val sessionStartTime = encryptedStorageManager.getLong("session_start_time", 0L)
                
                if (currentSessionId != null && sessionStartTime > 0) {
                    val sessionAge = System.currentTimeMillis() - sessionStartTime
                    if (sessionAge > 24 * 60 * 60 * 1000L) { // 24小时
                        // 清理过期的会话数据
                        encryptedStorageManager.clearAllInterviewTempData()
                        encryptedStorageManager.remove("session_start_time")
                    }
                }
                
            } catch (e: Exception) {
                privacyManager.logDataAccess(
                    "session_data", 
                    DataAccessType.DELETE, 
                    "会话数据清理失败：${e.message}"
                )
            }
        }
    }
    
    /**
     * 清理过期的会话数据（私有方法）
     */
    private suspend fun cleanupExpiredSessionData() {
        // 这里可以添加更复杂的会话数据清理逻辑
        // 例如清理数据库中的过期会话记录
    }
    
    /**
     * 清理指定目录中超过指定时间的文件
     * @param directory 目录
     * @param maxAge 最大保留时间（毫秒）
     */
    private suspend fun cleanupFilesOlderThan(directory: File, maxAge: Long) {
        withContext(Dispatchers.IO) {
            if (!directory.exists() || !directory.isDirectory) {
                return@withContext
            }
            
            val currentTime = System.currentTimeMillis()
            val files = directory.listFiles() ?: return@withContext
            
            for (file in files) {
                try {
                    if (file.isFile) {
                        val fileAge = currentTime - file.lastModified()
                        if (fileAge > maxAge) {
                            val deleted = file.delete()
                            if (deleted) {
                                privacyManager.logDataAccess(
                                    "file_system", 
                                    DataAccessType.DELETE, 
                                    "删除过期文件：${file.name}"
                                )
                            }
                        }
                    } else if (file.isDirectory) {
                        // 递归清理子目录
                        cleanupFilesOlderThan(file, maxAge)
                        
                        // 如果目录为空，删除目录
                        val remainingFiles = file.listFiles()
                        if (remainingFiles?.isEmpty() == true) {
                            file.delete()
                        }
                    }
                } catch (e: Exception) {
                    // 忽略单个文件的删除失败
                }
            }
        }
    }
    
    /**
     * 清理用户特定的敏感数据
     * @param userId 用户ID
     */
    suspend fun cleanupUserSensitiveData(userId: String) {
        withContext(Dispatchers.IO) {
            try {
                // 记录数据清理
                privacyManager.logDataAccess(
                    "user_sensitive_data", 
                    DataAccessType.DELETE, 
                    "清理用户敏感数据：$userId"
                )
                
                // 清理用户相关的临时文件
                cleanupUserTempFiles(userId)
                
                // 清理用户相关的录音文件
                cleanupUserAudioFiles(userId)
                
                // 清理用户相关的缓存数据
                cleanupUserCacheData(userId)
                
            } catch (e: Exception) {
                privacyManager.logDataAccess(
                    "user_sensitive_data", 
                    DataAccessType.DELETE, 
                    "用户敏感数据清理失败：${e.message}"
                )
            }
        }
    }
    
    /**
     * 清理用户临时文件
     */
    private suspend fun cleanupUserTempFiles(userId: String) {
        val userTempDir = File(context.filesDir, "temp/$userId")
        if (userTempDir.exists()) {
            userTempDir.deleteRecursively()
        }
    }
    
    /**
     * 清理用户录音文件
     */
    private suspend fun cleanupUserAudioFiles(userId: String) {
        val userAudioDir = File(context.filesDir, "audio/$userId")
        if (userAudioDir.exists()) {
            userAudioDir.deleteRecursively()
        }
    }
    
    /**
     * 清理用户缓存数据
     */
    private suspend fun cleanupUserCacheData(userId: String) {
        val userCacheDir = File(context.cacheDir, userId)
        if (userCacheDir.exists()) {
            userCacheDir.deleteRecursively()
        }
    }
    
    /**
     * 获取清理统计信息
     * @return 清理统计信息
     */
    suspend fun getCleanupStats(): CleanupStats {
        return withContext(Dispatchers.IO) {
            val tempFileCount = countFilesInDirectory(File(context.cacheDir, "temp"))
            val audioFileCount = countFilesInDirectory(File(context.filesDir, "audio"))
            val cacheFileCount = countFilesInDirectory(context.cacheDir)
            val logFileCount = countFilesInDirectory(File(context.filesDir, "logs"))
            
            val tempFileSize = calculateDirectorySize(File(context.cacheDir, "temp"))
            val audioFileSize = calculateDirectorySize(File(context.filesDir, "audio"))
            val cacheFileSize = calculateDirectorySize(context.cacheDir)
            val logFileSize = calculateDirectorySize(File(context.filesDir, "logs"))
            
            CleanupStats(
                tempFileCount = tempFileCount,
                audioFileCount = audioFileCount,
                cacheFileCount = cacheFileCount,
                logFileCount = logFileCount,
                tempFileSize = tempFileSize,
                audioFileSize = audioFileSize,
                cacheFileSize = cacheFileSize,
                logFileSize = logFileSize,
                totalFileCount = tempFileCount + audioFileCount + cacheFileCount + logFileCount,
                totalFileSize = tempFileSize + audioFileSize + cacheFileSize + logFileSize
            )
        }
    }
    
    /**
     * 计算目录中的文件数量
     */
    private fun countFilesInDirectory(directory: File): Int {
        if (!directory.exists() || !directory.isDirectory) {
            return 0
        }
        
        var count = 0
        directory.walkTopDown().forEach { file ->
            if (file.isFile) {
                count++
            }
        }
        return count
    }
    
    /**
     * 计算目录大小
     */
    private fun calculateDirectorySize(directory: File): Long {
        if (!directory.exists()) {
            return 0L
        }
        
        var size = 0L
        directory.walkTopDown().forEach { file ->
            if (file.isFile) {
                size += file.length()
            }
        }
        return size
    }
    
    /**
     * 启动定期清理任务
     */
    fun startPeriodicCleanup() {
        cleanupScope.launch {
            while (true) {
                try {
                    // 每24小时执行一次例行清理
                    delay(24 * 60 * 60 * 1000L)
                    performFullCleanup(CleanupType.ROUTINE)
                } catch (e: Exception) {
                    // 忽略清理异常，继续下次清理
                }
            }
        }
    }
    
    /**
     * 停止定期清理任务
     */
    fun stopPeriodicCleanup() {
        cleanupScope.cancel()
    }
}

/**
 * 清理类型枚举
 */
enum class CleanupType(val displayName: String) {
    ROUTINE("例行清理"),           // 例行清理
    MANUAL("手动清理"),            // 手动清理
    USER_LOGOUT("用户注销清理"),    // 用户注销时的清理
    APP_UNINSTALL("应用卸载清理")   // 应用卸载时的清理
}

/**
 * 清理统计信息数据类
 */
data class CleanupStats(
    val tempFileCount: Int,         // 临时文件数量
    val audioFileCount: Int,        // 录音文件数量
    val cacheFileCount: Int,        // 缓存文件数量
    val logFileCount: Int,          // 日志文件数量
    val tempFileSize: Long,         // 临时文件大小
    val audioFileSize: Long,        // 录音文件大小
    val cacheFileSize: Long,        // 缓存文件大小
    val logFileSize: Long,          // 日志文件大小
    val totalFileCount: Int,        // 总文件数量
    val totalFileSize: Long         // 总文件大小
) {
    /**
     * 格式化文件大小
     */
    fun formatFileSize(size: Long): String {
        return when {
            size < 1024 -> "${size}B"
            size < 1024 * 1024 -> "${size / 1024}KB"
            size < 1024 * 1024 * 1024 -> "${size / (1024 * 1024)}MB"
            else -> "${size / (1024 * 1024 * 1024)}GB"
        }
    }
    
    /**
     * 获取总文件大小的格式化字符串
     */
    fun getFormattedTotalSize(): String = formatFileSize(totalFileSize)
}