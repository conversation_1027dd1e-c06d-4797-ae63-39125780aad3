{"logs": [{"outputFile": "com.aiinterview.simulator.app-mergeOfficialDebugResources-55:/values-mr/values-mr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d78fa9dcb2040782f62e575b1696aadd\\transformed\\ui-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,281,378,476,563,649,734,823,906,975,1045,1125,1210,1281,1357,1423", "endColumns": "93,81,96,97,86,85,84,88,82,68,69,79,84,70,75,65,117", "endOffsets": "194,276,373,471,558,644,729,818,901,970,1040,1120,1205,1276,1352,1418,1536"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1305,1399,5091,5188,5358,5527,5613,5698,5787,5870,5939,6009,6089,6255,6609,6685,6751", "endColumns": "93,81,96,97,86,85,84,88,82,68,69,79,84,70,75,65,117", "endOffsets": "1394,1476,5183,5281,5440,5608,5693,5782,5865,5934,6004,6084,6169,6321,6680,6746,6864"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\73f51c4b00c3d8b5bd0709c291d17c64\\transformed\\material3-1.1.2\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,295,402,523,604,700,812,944,1063,1205,1286,1388,1475,1569,1676,1802,1909,2042,2171,2295,2470,2591,2703,2819,2939,3027,3118,3234,3359,3455,3554,3659,3791,3929,4040,4133,4205,4287,4368,4454,4550,4626,4705,4800,4895,4988,5083,5166,5266,5362,5461,5575,5651,5747", "endColumns": "120,118,106,120,80,95,111,131,118,141,80,101,86,93,106,125,106,132,128,123,174,120,111,115,119,87,90,115,124,95,98,104,131,137,110,92,71,81,80,85,95,75,78,94,94,92,94,82,99,95,98,113,75,95,89", "endOffsets": "171,290,397,518,599,695,807,939,1058,1200,1281,1383,1470,1564,1671,1797,1904,2037,2166,2290,2465,2586,2698,2814,2934,3022,3113,3229,3354,3450,3549,3654,3786,3924,4035,4128,4200,4282,4363,4449,4545,4621,4700,4795,4890,4983,5078,5161,5261,5357,5456,5570,5646,5742,5832"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,345,452,1481,1562,1658,1770,1902,2021,2163,2244,2346,2433,2527,2634,2760,2867,3000,3129,3253,3428,3549,3661,3777,3897,3985,4076,4192,4317,4413,4512,4617,4749,4887,4998,5286,5445,6174,6326,6513,6869,6945,7024,7119,7214,7307,7402,7485,7585,7681,7780,7894,7970,8066", "endColumns": "120,118,106,120,80,95,111,131,118,141,80,101,86,93,106,125,106,132,128,123,174,120,111,115,119,87,90,115,124,95,98,104,131,137,110,92,71,81,80,85,95,75,78,94,94,92,94,82,99,95,98,113,75,95,89", "endOffsets": "221,340,447,568,1557,1653,1765,1897,2016,2158,2239,2341,2428,2522,2629,2755,2862,2995,3124,3248,3423,3544,3656,3772,3892,3980,4071,4187,4312,4408,4507,4612,4744,4882,4993,5086,5353,5522,6250,6407,6604,6940,7019,7114,7209,7302,7397,7480,7580,7676,7775,7889,7965,8061,8151"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fd044ab7a523edb1345beec00f65097\\transformed\\core-1.12.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "573,673,777,878,981,1083,1188,6412", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "668,772,873,976,1078,1183,1300,6508"}}]}]}