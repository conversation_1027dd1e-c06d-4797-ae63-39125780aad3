package com.aiinterview.simulator.presentation.screen.analysis

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.aiinterview.simulator.data.model.*
import com.aiinterview.simulator.presentation.viewmodel.ProgressAnalysisViewModel
import java.text.SimpleDateFormat
import java.util.*

/**
 * 重新练习界面
 * 基于历史记录提供重新练习功能，帮助用户针对薄弱环节进行专项训练
 * 
 * @param category 要练习的岗位类别
 * @param onNavigateBack 返回上一页的回调函数
 * @param onStartInterview 开始面试的回调函数，传入岗位ID
 * @param viewModel 进步分析ViewModel
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RetryPracticeScreen(
    category: String,
    onNavigateBack: () -> Unit,
    onStartInterview: (String) -> Unit, // 传入岗位ID开始面试
    viewModel: ProgressAnalysisViewModel = hiltViewModel()
) {
    // 收集ViewModel状态
    val retryRecords by viewModel.retryRecords.collectAsState()
    val uiState by viewModel.uiState.collectAsState()
    val isInSelectionMode by viewModel.isInSelectionMode.collectAsState()
    val selectedRecords by viewModel.selectedRecords.collectAsState()
    
    // 本地状态
    var showPracticeOptions by remember { mutableStateOf(false) }
    var selectedRecord by remember { mutableStateOf<InterviewRecordModel?>(null) }
    
    // 加载该类别的重新练习记录
    LaunchedEffect(category) {
        viewModel.loadRetryRecords(category)
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
    ) {
        // 顶部应用栏
        TopAppBar(
            title = { 
                Column {
                    Text("重新练习")
                    Text(
                        text = category,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            },
            actions = {
                if (retryRecords.isNotEmpty()) {
                    // 批量操作按钮
                    if (isInSelectionMode) {
                        // 选择模式下的操作按钮
                        IconButton(
                            onClick = { viewModel.toggleSelectAll() }
                        ) {
                            Icon(
                                if (selectedRecords.size == retryRecords.size) {
                                    Icons.Default.Deselect
                                } else {
                                    Icons.Default.SelectAll
                                },
                                contentDescription = if (selectedRecords.size == retryRecords.size) {
                                    "取消全选"
                                } else {
                                    "全选"
                                }
                            )
                        }
                        
                        IconButton(
                            onClick = { viewModel.deleteSelectedRecords() },
                            enabled = selectedRecords.isNotEmpty()
                        ) {
                            Icon(
                                Icons.Default.Delete,
                                contentDescription = "删除选中",
                                tint = if (selectedRecords.isNotEmpty()) {
                                    MaterialTheme.colorScheme.error
                                } else {
                                    MaterialTheme.colorScheme.onSurfaceVariant
                                }
                            )
                        }
                        
                        IconButton(onClick = { viewModel.exitSelectionMode() }) {
                            Icon(Icons.Default.Close, contentDescription = "退出选择")
                        }
                    } else {
                        // 正常模式下的操作按钮
                        IconButton(onClick = { viewModel.enterSelectionMode() }) {
                            Icon(Icons.Default.Checklist, contentDescription = "批量操作")
                        }
                    }
                }
                
                // 更多操作菜单
                var showMoreMenu by remember { mutableStateOf(false) }
                Box {
                    IconButton(onClick = { showMoreMenu = true }) {
                        Icon(Icons.Default.MoreVert, contentDescription = "更多")
                    }
                    
                    DropdownMenu(
                        expanded = showMoreMenu,
                        onDismissRequest = { showMoreMenu = false }
                    ) {
                        DropdownMenuItem(
                            text = { Text("刷新记录") },
                            onClick = {
                                viewModel.loadRetryRecords(category)
                                showMoreMenu = false
                            },
                            leadingIcon = {
                                Icon(Icons.Default.Refresh, contentDescription = null)
                            }
                        )
                        
                        DropdownMenuItem(
                            text = { Text("练习建议") },
                            onClick = {
                                showPracticeOptions = true
                                showMoreMenu = false
                            },
                            leadingIcon = {
                                Icon(Icons.Default.Lightbulb, contentDescription = null)
                            }
                        )
                    }
                }
            }
        )
        
        // 类别信息卡片
        CategoryInfoCard(
            category = category,
            recordCount = retryRecords.size,
            modifier = Modifier.padding(16.dp)
        )
        
        // 主内容区域
        Box(modifier = Modifier.fillMaxSize()) {
            when {
                uiState.isLoading -> {
                    // 加载状态
                    CircularProgressIndicator(
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                
                uiState.error != null -> {
                    // 错误状态
                    ErrorMessage(
                        message = uiState.error,
                        onRetry = { viewModel.loadRetryRecords(category) },
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                
                retryRecords.isEmpty() -> {
                    // 空状态
                    EmptyRetryState(
                        category = category,
                        onStartNewPractice = { 
                            // 这里可以导航到岗位选择界面，预选该类别
                            showPracticeOptions = true
                        },
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                
                else -> {
                    // 正常状态：显示可重新练习的记录列表
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(16.dp),
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        // 快速开始新练习按钮
                        item {
                            QuickStartCard(
                                category = category,
                                onStartNewPractice = { showPracticeOptions = true }
                            )
                        }
                        
                        // 历史记录列表
                        item {
                            Text(
                                text = "基于历史记录重新练习",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold,
                                modifier = Modifier.padding(vertical = 8.dp)
                            )
                        }
                        
                        items(retryRecords) { record ->
                            RetryRecordCard(
                                record = record,
                                isSelected = record.id in selectedRecords,
                                isInSelectionMode = isInSelectionMode,
                                onRecordClick = { 
                                    if (isInSelectionMode) {
                                        viewModel.toggleRecordSelection(record.id)
                                    } else {
                                        selectedRecord = record
                                        showPracticeOptions = true
                                    }
                                },
                                onRetryClick = { 
                                    viewModel.startRetryPractice(record.id)
                                    // 这里应该导航到面试界面，使用相同的岗位配置
                                    onStartInterview(record.positionId)
                                }
                            )
                        }
                    }
                }
            }
        }
    }
    
    // 练习选项对话框
    if (showPracticeOptions) {
        PracticeOptionsDialog(
            category = category,
            selectedRecord = selectedRecord,
            onStartNewPractice = { 
                // 开始新的练习（随机题目）
                showPracticeOptions = false
                // 这里应该导航到岗位选择界面或直接开始面试
            },
            onRetryWithSameQuestions = { record ->
                // 使用相同题目重新练习
                showPracticeOptions = false
                viewModel.startRetryPractice(record.id)
                onStartInterview(record.positionId)
            },
            onDismiss = { 
                showPracticeOptions = false
                selectedRecord = null
            }
        )
    }
    
    // 消息提示处理
    uiState.message?.let { message ->
        LaunchedEffect(message) {
            // 显示成功消息
            viewModel.clearMessage()
        }
    }
}

/**
 * 类别信息卡片
 * 显示当前类别的基本信息和统计数据
 */
@Composable
private fun CategoryInfoCard(
    category: String,
    recordCount: Int,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "练习类别",
                    style = MaterialTheme.typography.labelMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                Text(
                    text = category,
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }
            
            Column(
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    text = "历史记录",
                    style = MaterialTheme.typography.labelMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                Text(
                    text = "${recordCount}条",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }
        }
    }
}

/**
 * 快速开始卡片
 * 提供快速开始新练习的入口
 */
@Composable
private fun QuickStartCard(
    category: String,
    onStartNewPractice: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onStartNewPractice() },
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.secondaryContainer
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                Icons.Default.PlayArrow,
                contentDescription = null,
                modifier = Modifier.size(32.dp),
                tint = MaterialTheme.colorScheme.onSecondaryContainer
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "开始新的${category}练习",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSecondaryContainer
                )
                Text(
                    text = "随机生成新题目，挑战自己",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSecondaryContainer
                )
            }
            
            Icon(
                Icons.Default.ChevronRight,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSecondaryContainer
            )
        }
    }
}

/**
 * 重新练习记录卡片
 * 显示单个历史记录的信息和重新练习选项
 */
@Composable
private fun RetryRecordCard(
    record: InterviewRecordModel,
    isSelected: Boolean,
    isInSelectionMode: Boolean,
    onRecordClick: () -> Unit,
    onRetryClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onRecordClick() },
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (isSelected) 8.dp else 4.dp
        ),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surface
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.weight(1f)
                ) {
                    // 选择模式下显示复选框
                    if (isInSelectionMode) {
                        Checkbox(
                            checked = isSelected,
                            onCheckedChange = { onRecordClick() }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                    }
                    
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = record.positionName,
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                        Text(
                            text = formatDateTime(record.startTime),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
                
                // 非选择模式下显示重新练习按钮
                if (!isInSelectionMode) {
                    TextButton(onClick = onRetryClick) {
                        Icon(
                            Icons.Default.Replay,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("重练")
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 统计信息行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 面试时长
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.Schedule,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp),
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = formatDuration(record.duration),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                // 题目数量
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.Quiz,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp),
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "${record.questions.size}题",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                // 总体评分
                if (record.overallScore != null) {
                    ScoreChip(score = record.overallScore)
                }
            }
        }
    }
}

/**
 * 评分标签组件
 */
@Composable
private fun ScoreChip(
    score: Double,
    modifier: Modifier = Modifier
) {
    val color = when {
        score >= 90 -> Color(0xFF4CAF50)
        score >= 80 -> Color(0xFF2196F3)
        score >= 70 -> Color(0xFFFF9800)
        else -> Color(0xFFF44336)
    }
    
    Surface(
        modifier = modifier,
        shape = RoundedCornerShape(12.dp),
        color = color.copy(alpha = 0.1f)
    ) {
        Text(
            text = "${score.toInt()}分",
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
            style = MaterialTheme.typography.labelSmall,
            color = color,
            fontWeight = FontWeight.Bold
        )
    }
}

/**
 * 空重新练习状态组件
 */
@Composable
private fun EmptyRetryState(
    category: String,
    onStartNewPractice: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            Icons.Default.School,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "暂无${category}练习记录",
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "开始第一次练习，建立学习记录",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Button(onClick = onStartNewPractice) {
            Icon(Icons.Default.PlayArrow, contentDescription = null)
            Spacer(modifier = Modifier.width(8.dp))
            Text("开始练习")
        }
    }
}

/**
 * 练习选项对话框
 */
@Composable
private fun PracticeOptionsDialog(
    category: String,
    selectedRecord: InterviewRecordModel?,
    onStartNewPractice: () -> Unit,
    onRetryWithSameQuestions: (InterviewRecordModel) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("选择练习方式") },
        text = {
            Column {
                Text(
                    text = "请选择您希望的练习方式：",
                    style = MaterialTheme.typography.bodyMedium
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 新练习选项
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { onStartNewPractice() },
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.primaryContainer
                    )
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            Icons.Default.AutoAwesome,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Column {
                            Text(
                                text = "随机新题目",
                                style = MaterialTheme.typography.titleSmall,
                                fontWeight = FontWeight.Bold,
                                color = MaterialTheme.colorScheme.onPrimaryContainer
                            )
                            Text(
                                text = "系统随机生成新的${category}题目",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onPrimaryContainer
                            )
                        }
                    }
                }
                
                // 如果有选中的记录，显示重复练习选项
                if (selectedRecord != null) {
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onRetryWithSameQuestions(selectedRecord) },
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.secondaryContainer
                        )
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(12.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                Icons.Default.Replay,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.onSecondaryContainer
                            )
                            Spacer(modifier = Modifier.width(12.dp))
                            Column {
                                Text(
                                    text = "重复相同题目",
                                    style = MaterialTheme.typography.titleSmall,
                                    fontWeight = FontWeight.Bold,
                                    color = MaterialTheme.colorScheme.onSecondaryContainer
                                )
                                Text(
                                    text = "使用${selectedRecord.positionName}的相同题目",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSecondaryContainer
                                )
                            }
                        }
                    }
                }
            }
        },
        confirmButton = {},
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

// 工具函数

/**
 * 格式化时间戳为可读的日期时间字符串
 */
private fun formatDateTime(timestamp: Long): String {
    val formatter = SimpleDateFormat("MM-dd HH:mm", Locale.getDefault())
    return formatter.format(Date(timestamp))
}

/**
 * 格式化时长为可读的时间字符串
 */
private fun formatDuration(seconds: Int): String {
    val minutes = seconds / 60
    val remainingSeconds = seconds % 60
    return if (minutes > 0) {
        "${minutes}分${remainingSeconds}秒"
    } else {
        "${remainingSeconds}秒"
    }
}