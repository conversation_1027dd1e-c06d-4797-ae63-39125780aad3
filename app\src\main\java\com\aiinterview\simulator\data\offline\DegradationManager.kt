package com.aiinterview.simulator.data.offline

import android.content.Context
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 降级处理管理器
 * 负责在网络不佳或服务不可用时提供降级服务
 */
@Singleton
class DegradationManager @Inject constructor(
    private val context: Context, // 注入应用上下文
    private val offlineModeManager: OfflineModeManager // 注入离线模式管理器
) {
    
    // 降级策略状态流
    private val _degradationStrategy = MutableStateFlow(DegradationStrategy.NONE)
    val degradationStrategy: StateFlow<DegradationStrategy> = _degradationStrategy.asStateFlow()
    
    // 服务可用性状态映射
    private val serviceAvailability = mutableMapOf<ServiceType, ServiceStatus>()
    
    /**
     * 初始化降级管理器
     */
    fun initialize() {
        // 初始化所有服务状态为可用
        ServiceType.values().forEach { serviceType ->
            serviceAvailability[serviceType] = ServiceStatus.AVAILABLE
        }
        
        // 监听网络状态变化
        observeNetworkChanges()
    }
    
    /**
     * 监听网络状态变化
     */
    private fun observeNetworkChanges() {
        // 这里可以使用协程来监听网络状态变化
        // 根据网络质量调整降级策略
    }
    
    /**
     * 评估并应用降级策略
     * @param serviceType 服务类型
     * @return 当前应该使用的降级策略
     */
    fun evaluateDegradationStrategy(serviceType: ServiceType): DegradationStrategy {
        val networkQuality = offlineModeManager.networkQuality.value // 获取当前网络质量
        val isOffline = offlineModeManager.isOfflineMode.value // 获取是否离线
        val serviceStatus = serviceAvailability[serviceType] ?: ServiceStatus.UNKNOWN // 获取服务状态
        
        val strategy = when {
            // 完全离线时使用离线策略
            isOffline -> DegradationStrategy.OFFLINE_MODE
            
            // 网络质量差时使用轻量级策略
            networkQuality == NetworkQuality.POOR -> DegradationStrategy.LIGHTWEIGHT
            
            // 服务不可用时使用缓存策略
            serviceStatus == ServiceStatus.UNAVAILABLE -> DegradationStrategy.CACHE_ONLY
            
            // 服务降级时使用简化策略
            serviceStatus == ServiceStatus.DEGRADED -> DegradationStrategy.SIMPLIFIED
            
            // 网络质量一般时使用基础策略
            networkQuality == NetworkQuality.FAIR -> DegradationStrategy.BASIC
            
            // 其他情况不降级
            else -> DegradationStrategy.NONE
        }
        
        _degradationStrategy.value = strategy // 更新降级策略
        return strategy
    }
    
    /**
     * 更新服务状态
     * @param serviceType 服务类型
     * @param status 服务状态
     */
    fun updateServiceStatus(serviceType: ServiceType, status: ServiceStatus) {
        serviceAvailability[serviceType] = status // 更新服务状态
        
        // 重新评估降级策略
        evaluateDegradationStrategy(serviceType)
    }
    
    /**
     * 检查服务是否可用
     * @param serviceType 服务类型
     * @return 服务是否可用
     */
    fun isServiceAvailable(serviceType: ServiceType): Boolean {
        val status = serviceAvailability[serviceType] ?: ServiceStatus.UNKNOWN
        return status == ServiceStatus.AVAILABLE || status == ServiceStatus.DEGRADED
    }
    
    /**
     * 获取语音识别的降级处理方案
     * @return 语音识别降级方案
     */
    fun getSpeechRecognitionDegradation(): SpeechRecognitionDegradation {
        val strategy = evaluateDegradationStrategy(ServiceType.SPEECH_RECOGNITION)
        
        return when (strategy) {
            DegradationStrategy.OFFLINE_MODE -> SpeechRecognitionDegradation(
                useOfflineRecognition = true, // 使用离线识别
                enableManualInput = true, // 启用手动输入
                showOfflineHint = true, // 显示离线提示
                maxRecordingDuration = 60, // 最大录音时长60秒
                compressionLevel = CompressionLevel.HIGH // 高压缩级别
            )
            
            DegradationStrategy.LIGHTWEIGHT -> SpeechRecognitionDegradation(
                useOfflineRecognition = false, // 不使用离线识别
                enableManualInput = true, // 启用手动输入
                showOfflineHint = false, // 不显示离线提示
                maxRecordingDuration = 30, // 最大录音时长30秒
                compressionLevel = CompressionLevel.HIGH // 高压缩级别
            )
            
            DegradationStrategy.CACHE_ONLY -> SpeechRecognitionDegradation(
                useOfflineRecognition = true, // 使用离线识别
                enableManualInput = true, // 启用手动输入
                showOfflineHint = true, // 显示离线提示
                maxRecordingDuration = 45, // 最大录音时长45秒
                compressionLevel = CompressionLevel.MEDIUM // 中等压缩级别
            )
            
            DegradationStrategy.SIMPLIFIED -> SpeechRecognitionDegradation(
                useOfflineRecognition = false, // 不使用离线识别
                enableManualInput = true, // 启用手动输入
                showOfflineHint = false, // 不显示离线提示
                maxRecordingDuration = 60, // 最大录音时长60秒
                compressionLevel = CompressionLevel.MEDIUM // 中等压缩级别
            )
            
            DegradationStrategy.BASIC -> SpeechRecognitionDegradation(
                useOfflineRecognition = false, // 不使用离线识别
                enableManualInput = false, // 不启用手动输入
                showOfflineHint = false, // 不显示离线提示
                maxRecordingDuration = 120, // 最大录音时长120秒
                compressionLevel = CompressionLevel.LOW // 低压缩级别
            )
            
            DegradationStrategy.NONE -> SpeechRecognitionDegradation(
                useOfflineRecognition = false, // 不使用离线识别
                enableManualInput = false, // 不启用手动输入
                showOfflineHint = false, // 不显示离线提示
                maxRecordingDuration = 180, // 最大录音时长180秒
                compressionLevel = CompressionLevel.NONE // 无压缩
            )
        }
    }
    
    /**
     * 获取AI评价的降级处理方案
     * @return AI评价降级方案
     */
    fun getAIEvaluationDegradation(): AIEvaluationDegradation {
        val strategy = evaluateDegradationStrategy(ServiceType.AI_EVALUATION)
        
        return when (strategy) {
            DegradationStrategy.OFFLINE_MODE -> AIEvaluationDegradation(
                useLocalEvaluation = true, // 使用本地评价
                enableBasicScoring = true, // 启用基础评分
                showDetailedFeedback = false, // 不显示详细反馈
                maxAnalysisDepth = AnalysisDepth.BASIC, // 基础分析深度
                enableCachedSuggestions = true // 启用缓存建议
            )
            
            DegradationStrategy.LIGHTWEIGHT -> AIEvaluationDegradation(
                useLocalEvaluation = false, // 不使用本地评价
                enableBasicScoring = true, // 启用基础评分
                showDetailedFeedback = false, // 不显示详细反馈
                maxAnalysisDepth = AnalysisDepth.BASIC, // 基础分析深度
                enableCachedSuggestions = true // 启用缓存建议
            )
            
            DegradationStrategy.CACHE_ONLY -> AIEvaluationDegradation(
                useLocalEvaluation = true, // 使用本地评价
                enableBasicScoring = true, // 启用基础评分
                showDetailedFeedback = true, // 显示详细反馈
                maxAnalysisDepth = AnalysisDepth.INTERMEDIATE, // 中等分析深度
                enableCachedSuggestions = true // 启用缓存建议
            )
            
            DegradationStrategy.SIMPLIFIED -> AIEvaluationDegradation(
                useLocalEvaluation = false, // 不使用本地评价
                enableBasicScoring = true, // 启用基础评分
                showDetailedFeedback = true, // 显示详细反馈
                maxAnalysisDepth = AnalysisDepth.INTERMEDIATE, // 中等分析深度
                enableCachedSuggestions = false // 不启用缓存建议
            )
            
            DegradationStrategy.BASIC -> AIEvaluationDegradation(
                useLocalEvaluation = false, // 不使用本地评价
                enableBasicScoring = false, // 不启用基础评分
                showDetailedFeedback = true, // 显示详细反馈
                maxAnalysisDepth = AnalysisDepth.ADVANCED, // 高级分析深度
                enableCachedSuggestions = false // 不启用缓存建议
            )
            
            DegradationStrategy.NONE -> AIEvaluationDegradation(
                useLocalEvaluation = false, // 不使用本地评价
                enableBasicScoring = false, // 不启用基础评分
                showDetailedFeedback = true, // 显示详细反馈
                maxAnalysisDepth = AnalysisDepth.COMPREHENSIVE, // 全面分析深度
                enableCachedSuggestions = false // 不启用缓存建议
            )
        }
    }
    
    /**
     * 获取问题生成的降级处理方案
     * @return 问题生成降级方案
     */
    fun getQuestionGenerationDegradation(): QuestionGenerationDegradation {
        val strategy = evaluateDegradationStrategy(ServiceType.QUESTION_GENERATION)
        
        return when (strategy) {
            DegradationStrategy.OFFLINE_MODE -> QuestionGenerationDegradation(
                usePresetQuestions = true, // 使用预设问题
                enableDynamicGeneration = false, // 不启用动态生成
                maxQuestionsPerSession = 3, // 每次会话最多3个问题
                questionComplexity = QuestionComplexity.BASIC, // 基础复杂度
                enablePersonalization = false // 不启用个性化
            )
            
            DegradationStrategy.LIGHTWEIGHT -> QuestionGenerationDegradation(
                usePresetQuestions = true, // 使用预设问题
                enableDynamicGeneration = true, // 启用动态生成
                maxQuestionsPerSession = 5, // 每次会话最多5个问题
                questionComplexity = QuestionComplexity.BASIC, // 基础复杂度
                enablePersonalization = false // 不启用个性化
            )
            
            DegradationStrategy.CACHE_ONLY -> QuestionGenerationDegradation(
                usePresetQuestions = false, // 不使用预设问题
                enableDynamicGeneration = true, // 启用动态生成
                maxQuestionsPerSession = 5, // 每次会话最多5个问题
                questionComplexity = QuestionComplexity.INTERMEDIATE, // 中等复杂度
                enablePersonalization = false // 不启用个性化
            )
            
            DegradationStrategy.SIMPLIFIED -> QuestionGenerationDegradation(
                usePresetQuestions = false, // 不使用预设问题
                enableDynamicGeneration = true, // 启用动态生成
                maxQuestionsPerSession = 8, // 每次会话最多8个问题
                questionComplexity = QuestionComplexity.INTERMEDIATE, // 中等复杂度
                enablePersonalization = true // 启用个性化
            )
            
            DegradationStrategy.BASIC -> QuestionGenerationDegradation(
                usePresetQuestions = false, // 不使用预设问题
                enableDynamicGeneration = true, // 启用动态生成
                maxQuestionsPerSession = 10, // 每次会话最多10个问题
                questionComplexity = QuestionComplexity.ADVANCED, // 高级复杂度
                enablePersonalization = true // 启用个性化
            )
            
            DegradationStrategy.NONE -> QuestionGenerationDegradation(
                usePresetQuestions = false, // 不使用预设问题
                enableDynamicGeneration = true, // 启用动态生成
                maxQuestionsPerSession = 15, // 每次会话最多15个问题
                questionComplexity = QuestionComplexity.COMPREHENSIVE, // 全面复杂度
                enablePersonalization = true // 启用个性化
            )
        }
    }
    
    /**
     * 获取当前降级策略的用户提示信息
     * @return 用户提示信息
     */
    fun getDegradationHint(): String? {
        return when (_degradationStrategy.value) {
            DegradationStrategy.OFFLINE_MODE -> "当前处于离线模式，部分功能可能受限"
            DegradationStrategy.LIGHTWEIGHT -> "网络信号较弱，已启用轻量级模式"
            DegradationStrategy.CACHE_ONLY -> "服务暂时不可用，正在使用缓存数据"
            DegradationStrategy.SIMPLIFIED -> "服务性能降级，已启用简化模式"
            DegradationStrategy.BASIC -> "网络质量一般，已启用基础模式"
            DegradationStrategy.NONE -> null // 无降级时不显示提示
        }
    }
}

/**
 * 降级策略枚举
 */
enum class DegradationStrategy {
    NONE, // 无降级
    BASIC, // 基础降级
    SIMPLIFIED, // 简化降级
    CACHE_ONLY, // 仅使用缓存
    LIGHTWEIGHT, // 轻量级降级
    OFFLINE_MODE // 离线模式
}

/**
 * 服务类型枚举
 */
enum class ServiceType {
    SPEECH_RECOGNITION, // 语音识别
    AI_EVALUATION, // AI评价
    QUESTION_GENERATION, // 问题生成
    TEXT_TO_SPEECH, // 文本转语音
    DATA_SYNC, // 数据同步
    USER_AUTHENTICATION // 用户认证
}

/**
 * 服务状态枚举
 */
enum class ServiceStatus {
    AVAILABLE, // 可用
    DEGRADED, // 降级
    UNAVAILABLE, // 不可用
    UNKNOWN // 未知
}

/**
 * 语音识别降级方案数据类
 */
data class SpeechRecognitionDegradation(
    val useOfflineRecognition: Boolean, // 是否使用离线识别
    val enableManualInput: Boolean, // 是否启用手动输入
    val showOfflineHint: Boolean, // 是否显示离线提示
    val maxRecordingDuration: Int, // 最大录音时长（秒）
    val compressionLevel: CompressionLevel // 压缩级别
)

/**
 * AI评价降级方案数据类
 */
data class AIEvaluationDegradation(
    val useLocalEvaluation: Boolean, // 是否使用本地评价
    val enableBasicScoring: Boolean, // 是否启用基础评分
    val showDetailedFeedback: Boolean, // 是否显示详细反馈
    val maxAnalysisDepth: AnalysisDepth, // 最大分析深度
    val enableCachedSuggestions: Boolean // 是否启用缓存建议
)

/**
 * 问题生成降级方案数据类
 */
data class QuestionGenerationDegradation(
    val usePresetQuestions: Boolean, // 是否使用预设问题
    val enableDynamicGeneration: Boolean, // 是否启用动态生成
    val maxQuestionsPerSession: Int, // 每次会话最大问题数
    val questionComplexity: QuestionComplexity, // 问题复杂度
    val enablePersonalization: Boolean // 是否启用个性化
)

/**
 * 压缩级别枚举
 */
enum class CompressionLevel {
    NONE, // 无压缩
    LOW, // 低压缩
    MEDIUM, // 中等压缩
    HIGH // 高压缩
}

/**
 * 分析深度枚举
 */
enum class AnalysisDepth {
    BASIC, // 基础
    INTERMEDIATE, // 中等
    ADVANCED, // 高级
    COMPREHENSIVE // 全面
}

/**
 * 问题复杂度枚举
 */
enum class QuestionComplexity {
    BASIC, // 基础
    INTERMEDIATE, // 中等
    ADVANCED, // 高级
    COMPREHENSIVE // 全面
}