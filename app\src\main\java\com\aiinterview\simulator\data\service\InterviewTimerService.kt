package com.aiinterview.simulator.data.service

import com.aiinterview.simulator.data.service.TextToSpeechService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class InterviewTimerService @Inject constructor(
    private val ttsService: TextToSpeechService
) {
    
    private val _totalTimeRemaining = MutableStateFlow(0L)
    val totalTimeRemaining: StateFlow<Long> = _totalTimeRemaining.asStateFlow()
    
    private val _questionTimeRemaining = MutableStateFlow(0L)
    val questionTimeRemaining: StateFlow<Long> = _questionTimeRemaining.asStateFlow()
    
    private val _timerState = MutableStateFlow(TimerState.STOPPED)
    val timerState: StateFlow<TimerState> = _timerState.asStateFlow()
    
    private val _timeWarnings = MutableStateFlow<List<TimeWarning>>(emptyList())
    val timeWarnings: StateFlow<List<TimeWarning>> = _timeWarnings.asStateFlow()
    
    private var totalTimer: Timer? = null
    private var questionTimer: Timer? = null
    private var totalTimeLimit: Long = 0L
    private var questionTimeLimit: Long = 0L
    private var questionStartTime: Long = 0L
    private var interviewStartTime: Long = 0L
    
    enum class TimerState {
        STOPPED, RUNNING, PAUSED
    }
    
    data class TimeWarning(
        val id: String,
        val type: TimeWarningType,
        val message: String,
        val triggeredAt: Long,
        val isVoiceWarning: Boolean = false,
        val acknowledged: Boolean = false
    )
    
    enum class TimeWarningType {
        INTERVIEW_5_MIN_WARNING,
        INTERVIEW_1_MIN_WARNING,
        INTERVIEW_30_SEC_WARNING,
        INTERVIEW_TIME_UP,
        QUESTION_1_MIN_WARNING,
        QUESTION_30_SEC_WARNING,
        QUESTION_TIME_UP
    }
    
    /**
     * 开始面试计时
     */
    fun startInterviewTimer(totalTimeLimitMinutes: Int) {
        stopAllTimers()
        
        totalTimeLimit = totalTimeLimitMinutes * 60 * 1000L
        interviewStartTime = System.currentTimeMillis()
        _totalTimeRemaining.value = totalTimeLimit
        _timerState.value = TimerState.RUNNING
        
        startTotalTimer()
    }
    
    /**
     * 开始问题计时
     */
    fun startQuestionTimer(questionTimeLimitSeconds: Int) {
        stopQuestionTimer()
        
        questionTimeLimit = questionTimeLimitSeconds * 1000L
        questionStartTime = System.currentTimeMillis()
        _questionTimeRemaining.value = questionTimeLimit
        
        startQuestionTimerInternal()
    }
    
    /**
     * 暂停计时
     */
    fun pauseTimers() {
        if (_timerState.value == TimerState.RUNNING) {
            _timerState.value = TimerState.PAUSED
            stopAllTimers()
        }
    }
    
    /**
     * 恢复计时
     */
    fun resumeTimers() {
        if (_timerState.value == TimerState.PAUSED) {
            _timerState.value = TimerState.RUNNING
            
            // 重新计算剩余时间
            val currentTime = System.currentTimeMillis()
            val elapsedInterviewTime = currentTime - interviewStartTime
            _totalTimeRemaining.value = maxOf(0L, totalTimeLimit - elapsedInterviewTime)
            
            val elapsedQuestionTime = currentTime - questionStartTime
            _questionTimeRemaining.value = maxOf(0L, questionTimeLimit - elapsedQuestionTime)
            
            startTotalTimer()
            if (questionTimeLimit > 0) {
                startQuestionTimerInternal()
            }
        }
    }
    
    /**
     * 停止所有计时器
     */
    fun stopAllTimers() {
        totalTimer?.cancel()
        totalTimer = null
        questionTimer?.cancel()
        questionTimer = null
        _timerState.value = TimerState.STOPPED
    }
    
    /**
     * 停止问题计时器
     */
    fun stopQuestionTimer() {
        questionTimer?.cancel()
        questionTimer = null
        _questionTimeRemaining.value = 0L
    }
    
    /**
     * 开始总计时器
     */
    private fun startTotalTimer() {
        totalTimer = Timer().apply {
            scheduleAtFixedRate(object : TimerTask() {
                override fun run() {
                    val currentTime = System.currentTimeMillis()
                    val elapsedTime = currentTime - interviewStartTime
                    val remaining = maxOf(0L, totalTimeLimit - elapsedTime)
                    
                    _totalTimeRemaining.value = remaining
                    
                    // 检查时间警告
                    checkInterviewTimeWarnings(remaining)
                    
                    if (remaining <= 0L) {
                        handleInterviewTimeUp()
                        cancel()
                    }
                }
            }, 1000, 1000)
        }
    }
    
    /**
     * 开始问题计时器
     */
    private fun startQuestionTimerInternal() {
        questionTimer = Timer().apply {
            scheduleAtFixedRate(object : TimerTask() {
                override fun run() {
                    val currentTime = System.currentTimeMillis()
                    val elapsedTime = currentTime - questionStartTime
                    val remaining = maxOf(0L, questionTimeLimit - elapsedTime)
                    
                    _questionTimeRemaining.value = remaining
                    
                    // 检查问题时间警告
                    checkQuestionTimeWarnings(remaining)
                    
                    if (remaining <= 0L) {
                        handleQuestionTimeUp()
                        cancel()
                    }
                }
            }, 1000, 1000)
        }
    }
    
    /**
     * 检查面试时间警告
     */
    private fun checkInterviewTimeWarnings(remainingTime: Long) {
        when {
            remainingTime <= 5 * 60 * 1000L && remainingTime > 4 * 60 * 1000L -> {
                if (!hasWarningBeenTriggered(TimeWarningType.INTERVIEW_5_MIN_WARNING)) {
                    triggerTimeWarning(
                        TimeWarningType.INTERVIEW_5_MIN_WARNING,
                        "面试时间还剩5分钟，请注意时间安排",
                        isVoiceWarning = true
                    )
                }
            }
            remainingTime <= 1 * 60 * 1000L && remainingTime > 30 * 1000L -> {
                if (!hasWarningBeenTriggered(TimeWarningType.INTERVIEW_1_MIN_WARNING)) {
                    triggerTimeWarning(
                        TimeWarningType.INTERVIEW_1_MIN_WARNING,
                        "面试时间还剩1分钟，请尽快完成当前问题",
                        isVoiceWarning = true
                    )
                }
            }
            remainingTime <= 30 * 1000L && remainingTime > 0L -> {
                if (!hasWarningBeenTriggered(TimeWarningType.INTERVIEW_30_SEC_WARNING)) {
                    triggerTimeWarning(
                        TimeWarningType.INTERVIEW_30_SEC_WARNING,
                        "面试时间还剩30秒，即将结束",
                        isVoiceWarning = true
                    )
                }
            }
        }
    }
    
    /**
     * 检查问题时间警告
     */
    private fun checkQuestionTimeWarnings(remainingTime: Long) {
        when {
            remainingTime <= 1 * 60 * 1000L && remainingTime > 30 * 1000L -> {
                if (!hasWarningBeenTriggered(TimeWarningType.QUESTION_1_MIN_WARNING)) {
                    triggerTimeWarning(
                        TimeWarningType.QUESTION_1_MIN_WARNING,
                        "当前问题时间还剩1分钟",
                        isVoiceWarning = false
                    )
                }
            }
            remainingTime <= 30 * 1000L && remainingTime > 0L -> {
                if (!hasWarningBeenTriggered(TimeWarningType.QUESTION_30_SEC_WARNING)) {
                    triggerTimeWarning(
                        TimeWarningType.QUESTION_30_SEC_WARNING,
                        "当前问题时间还剩30秒",
                        isVoiceWarning = false
                    )
                }
            }
        }
    }
    
    /**
     * 处理面试时间到
     */
    private fun handleInterviewTimeUp() {
        triggerTimeWarning(
            TimeWarningType.INTERVIEW_TIME_UP,
            "面试时间已到，系统将自动提交",
            isVoiceWarning = true
        )
        _timerState.value = TimerState.STOPPED
    }
    
    /**
     * 处理问题时间到
     */
    private fun handleQuestionTimeUp() {
        triggerTimeWarning(
            TimeWarningType.QUESTION_TIME_UP,
            "当前问题时间已到，建议进入下一题",
            isVoiceWarning = false
        )
    }
    
    /**
     * 触发时间警告
     */
    private fun triggerTimeWarning(
        type: TimeWarningType,
        message: String,
        isVoiceWarning: Boolean = false
    ) {
        val warning = TimeWarning(
            id = UUID.randomUUID().toString(),
            type = type,
            message = message,
            triggeredAt = System.currentTimeMillis(),
            isVoiceWarning = isVoiceWarning
        )
        
        val currentWarnings = _timeWarnings.value.toMutableList()
        currentWarnings.add(warning)
        _timeWarnings.value = currentWarnings
        
        // 如果需要语音提醒，播放TTS
        if (isVoiceWarning) {
            playVoiceWarning(message)
        }
    }
    
    /**
     * 播放语音警告
     */
    private fun playVoiceWarning(message: String) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val ttsResponse = ttsService.synthesizeText(
                    text = message,
                    voiceType = TextToSpeechService.VoiceType.FEMALE_FRIENDLY,
                    speed = 6 // 稍快的语速用于提醒
                )
                
                if (ttsResponse.success) {
                    ttsService.playAudio(ttsResponse)
                }
            } catch (e: Exception) {
                // 语音播放失败，不影响主流程
            }
        }
    }
    
    /**
     * 检查警告是否已经触发
     */
    private fun hasWarningBeenTriggered(type: TimeWarningType): Boolean {
        return _timeWarnings.value.any { it.type == type }
    }
    
    /**
     * 确认时间警告
     */
    fun acknowledgeWarning(warningId: String) {
        val currentWarnings = _timeWarnings.value.toMutableList()
        val index = currentWarnings.indexOfFirst { it.id == warningId }
        if (index != -1) {
            currentWarnings[index] = currentWarnings[index].copy(acknowledged = true)
            _timeWarnings.value = currentWarnings
        }
    }
    
    /**
     * 清除所有警告
     */
    fun clearWarnings() {
        _timeWarnings.value = emptyList()
    }
    
    /**
     * 获取格式化的总剩余时间
     */
    fun getFormattedTotalTime(): String {
        val timeMs = _totalTimeRemaining.value
        val minutes = timeMs / (60 * 1000)
        val seconds = (timeMs % (60 * 1000)) / 1000
        return String.format("%02d:%02d", minutes, seconds)
    }
    
    /**
     * 获取格式化的问题剩余时间
     */
    fun getFormattedQuestionTime(): String {
        val timeMs = _questionTimeRemaining.value
        val minutes = timeMs / (60 * 1000)
        val seconds = (timeMs % (60 * 1000)) / 1000
        return String.format("%02d:%02d", minutes, seconds)
    }
    
    /**
     * 获取面试进度百分比
     */
    fun getInterviewProgress(): Float {
        if (totalTimeLimit <= 0L) return 0f
        val elapsedTime = totalTimeLimit - _totalTimeRemaining.value
        return (elapsedTime.toFloat() / totalTimeLimit.toFloat()).coerceIn(0f, 1f)
    }
    
    /**
     * 获取问题进度百分比
     */
    fun getQuestionProgress(): Float {
        if (questionTimeLimit <= 0L) return 0f
        val elapsedTime = questionTimeLimit - _questionTimeRemaining.value
        return (elapsedTime.toFloat() / questionTimeLimit.toFloat()).coerceIn(0f, 1f)
    }
    
    /**
     * 检查是否需要显示时间警告
     */
    fun shouldShowTimeWarning(): Boolean {
        return _totalTimeRemaining.value <= 5 * 60 * 1000L // 5分钟内显示警告
    }
    
    /**
     * 检查是否时间紧急
     */
    fun isTimeCritical(): Boolean {
        return _totalTimeRemaining.value <= 1 * 60 * 1000L // 1分钟内为紧急状态
    }
    
    /**
     * 获取未确认的警告
     */
    fun getUnacknowledgedWarnings(): List<TimeWarning> {
        return _timeWarnings.value.filter { !it.acknowledged }
    }
    
    /**
     * 获取计时器统计信息
     */
    fun getTimerStatistics(): TimerStatistics {
        val currentTime = System.currentTimeMillis()
        val totalElapsed = if (interviewStartTime > 0) {
            currentTime - interviewStartTime
        } else 0L
        
        val questionElapsed = if (questionStartTime > 0) {
            currentTime - questionStartTime
        } else 0L
        
        return TimerStatistics(
            totalTimeLimit = totalTimeLimit,
            totalTimeElapsed = totalElapsed,
            totalTimeRemaining = _totalTimeRemaining.value,
            questionTimeLimit = questionTimeLimit,
            questionTimeElapsed = questionElapsed,
            questionTimeRemaining = _questionTimeRemaining.value,
            warningsTriggered = _timeWarnings.value.size,
            voiceWarningsTriggered = _timeWarnings.value.count { it.isVoiceWarning },
            timerState = _timerState.value
        )
    }
    
    data class TimerStatistics(
        val totalTimeLimit: Long,
        val totalTimeElapsed: Long,
        val totalTimeRemaining: Long,
        val questionTimeLimit: Long,
        val questionTimeElapsed: Long,
        val questionTimeRemaining: Long,
        val warningsTriggered: Int,
        val voiceWarningsTriggered: Int,
        val timerState: TimerState
    )
}