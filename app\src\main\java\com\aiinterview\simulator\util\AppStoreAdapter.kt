package com.aiinterview.simulator.util

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.util.Log

/**
 * 应用商店适配工具类
 * 用于适配不同的Android应用商店，提供统一的跳转和功能接口
 */
object AppStoreAdapter {
    
    private const val TAG = "AppStoreAdapter"
    
    /**
     * 应用商店信息数据类
     */
    data class AppStoreInfo(
        val name: String,           // 商店名称
        val packageName: String,    // 商店包名
        val scheme: String,         // URL协议
        val webUrl: String,         // 网页版链接
        val isInstalled: Boolean    // 是否已安装
    )
    
    /**
     * 获取所有支持的应用商店信息
     * @param context 上下文
     * @return 应用商店信息列表
     */
    fun getSupportedAppStores(context: Context): List<AppStoreInfo> {
        val packageManager = context.packageManager
        
        return listOf(
            // 华为应用市场
            AppStoreInfo(
                name = "华为应用市场",
                packageName = "com.huawei.appmarket",
                scheme = "appmarket://details?id=",
                webUrl = "https://appgallery.huawei.com/app/",
                isInstalled = isAppInstalled(packageManager, "com.huawei.appmarket")
            ),
            
            // 小米应用商店
            AppStoreInfo(
                name = "小米应用商店",
                packageName = "com.xiaomi.market",
                scheme = "mimarket://details?id=",
                webUrl = "https://app.mi.com/details?id=",
                isInstalled = isAppInstalled(packageManager, "com.xiaomi.market")
            ),
            
            // OPPO软件商店
            AppStoreInfo(
                name = "OPPO软件商店",
                packageName = "com.oppo.market",
                scheme = "oppomarket://details?packagename=",
                webUrl = "https://store.oppo.com/app/en?id=",
                isInstalled = isAppInstalled(packageManager, "com.oppo.market")
            ),
            
            // vivo应用商店
            AppStoreInfo(
                name = "vivo应用商店",
                packageName = "com.bbk.appstore",
                scheme = "vivomarket://details?id=",
                webUrl = "https://appstore.vivo.com.cn/details?id=",
                isInstalled = isAppInstalled(packageManager, "com.bbk.appstore")
            ),
            
            // 应用宝（腾讯）
            AppStoreInfo(
                name = "应用宝",
                packageName = "com.tencent.android.qqdownloader",
                scheme = "qqmarket://details/enter?name=",
                webUrl = "https://sj.qq.com/myapp/detail.htm?apkName=",
                isInstalled = isAppInstalled(packageManager, "com.tencent.android.qqdownloader")
            ),
            
            // 360手机助手
            AppStoreInfo(
                name = "360手机助手",
                packageName = "com.qihoo.appstore",
                scheme = "qihoopay://details?id=",
                webUrl = "https://zhushou.360.cn/detail/index/soft_id/",
                isInstalled = isAppInstalled(packageManager, "com.qihoo.appstore")
            ),
            
            // 百度手机助手
            AppStoreInfo(
                name = "百度手机助手",
                packageName = "com.baidu.appsearch",
                scheme = "bdappstore://details?id=",
                webUrl = "https://mobile.baidu.com/item?docid=",
                isInstalled = isAppInstalled(packageManager, "com.baidu.appsearch")
            ),
            
            // Google Play（国际版）
            AppStoreInfo(
                name = "Google Play",
                packageName = "com.android.vending",
                scheme = "market://details?id=",
                webUrl = "https://play.google.com/store/apps/details?id=",
                isInstalled = isAppInstalled(packageManager, "com.android.vending")
            )
        )
    }
    
    /**
     * 获取当前设备上已安装的应用商店
     * @param context 上下文
     * @return 已安装的应用商店列表
     */
    fun getInstalledAppStores(context: Context): List<AppStoreInfo> {
        return getSupportedAppStores(context).filter { it.isInstalled }
    }
    
    /**
     * 根据渠道获取推荐的应用商店
     * @param context 上下文
     * @param channel 渠道标识
     * @return 推荐的应用商店，如果没有则返回null
     */
    fun getRecommendedAppStore(context: Context, channel: String): AppStoreInfo? {
        val allStores = getSupportedAppStores(context)
        
        return when (channel) {
            "huawei" -> allStores.find { it.packageName == "com.huawei.appmarket" }
            "xiaomi" -> allStores.find { it.packageName == "com.xiaomi.market" }
            "oppo" -> allStores.find { it.packageName == "com.oppo.market" }
            "vivo" -> allStores.find { it.packageName == "com.bbk.appstore" }
            "tencent" -> allStores.find { it.packageName == "com.tencent.android.qqdownloader" }
            "qihoo360" -> allStores.find { it.packageName == "com.qihoo.appstore" }
            "baidu" -> allStores.find { it.packageName == "com.baidu.appsearch" }
            else -> null
        }
    }
    
    /**
     * 打开应用在指定商店的详情页
     * @param context 上下文
     * @param storeInfo 应用商店信息
     * @param packageName 应用包名
     * @return 是否成功打开
     */
    fun openAppInStore(context: Context, storeInfo: AppStoreInfo, packageName: String): Boolean {
        return try {
            if (storeInfo.isInstalled) {
                // 尝试打开应用商店客户端
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(storeInfo.scheme + packageName)).apply {
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    setPackage(storeInfo.packageName)
                }
                
                if (intent.resolveActivity(context.packageManager) != null) {
                    context.startActivity(intent)
                    Log.d(TAG, "成功打开${storeInfo.name}客户端")
                    return true
                }
            }
            
            // 如果客户端打开失败，尝试打开网页版
            val webIntent = Intent(Intent.ACTION_VIEW, Uri.parse(storeInfo.webUrl + packageName)).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            
            if (webIntent.resolveActivity(context.packageManager) != null) {
                context.startActivity(webIntent)
                Log.d(TAG, "成功打开${storeInfo.name}网页版")
                return true
            }
            
            false
        } catch (e: Exception) {
            Log.e(TAG, "打开${storeInfo.name}失败", e)
            false
        }
    }
    
    /**
     * 智能打开应用商店
     * 根据当前渠道和设备情况自动选择最合适的应用商店
     * @param context 上下文
     * @param packageName 应用包名
     * @return 是否成功打开
     */
    fun smartOpenAppStore(context: Context, packageName: String): Boolean {
        val currentChannel = ChannelUtil.getCurrentChannel()
        
        // 1. 优先尝试渠道对应的应用商店
        val recommendedStore = getRecommendedAppStore(context, currentChannel)
        if (recommendedStore != null && openAppInStore(context, recommendedStore, packageName)) {
            return true
        }
        
        // 2. 尝试已安装的应用商店（按优先级排序）
        val installedStores = getInstalledAppStores(context)
        val priorityOrder = listOf(
            "com.huawei.appmarket",
            "com.xiaomi.market",
            "com.oppo.market",
            "com.bbk.appstore",
            "com.tencent.android.qqdownloader",
            "com.qihoo.appstore",
            "com.baidu.appsearch",
            "com.android.vending"
        )
        
        for (packageName in priorityOrder) {
            val store = installedStores.find { it.packageName == packageName }
            if (store != null && openAppInStore(context, store, context.packageName)) {
                return true
            }
        }
        
        // 3. 最后尝试通用的market协议
        return try {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=$packageName")).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            
            if (intent.resolveActivity(context.packageManager) != null) {
                context.startActivity(intent)
                Log.d(TAG, "使用通用market协议打开应用商店")
                true
            } else {
                Log.w(TAG, "无法找到可用的应用商店")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "打开应用商店失败", e)
            false
        }
    }
    
    /**
     * 获取应用商店评分页面链接
     * @param context 上下文
     * @param packageName 应用包名
     * @return 评分页面链接，如果无法获取则返回null
     */
    fun getRatingPageUrl(context: Context, packageName: String): String? {
        val currentChannel = ChannelUtil.getCurrentChannel()
        val recommendedStore = getRecommendedAppStore(context, currentChannel)
        
        return when (recommendedStore?.packageName) {
            "com.huawei.appmarket" -> "appmarket://details?id=$packageName&tab=comment"
            "com.xiaomi.market" -> "mimarket://details?id=$packageName&ref=comment"
            "com.oppo.market" -> "oppomarket://details?packagename=$packageName&tab=comment"
            "com.bbk.appstore" -> "vivomarket://details?id=$packageName&tab=comment"
            "com.tencent.android.qqdownloader" -> "qqmarket://details/enter?name=$packageName&tab=comment"
            "com.android.vending" -> "market://details?id=$packageName&showAllReviews=true"
            else -> null
        }
    }
    
    /**
     * 检查应用是否已安装
     * @param packageManager 包管理器
     * @param packageName 包名
     * @return 是否已安装
     */
    private fun isAppInstalled(packageManager: PackageManager, packageName: String): Boolean {
        return try {
            packageManager.getPackageInfo(packageName, 0)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }
    
    /**
     * 获取设备品牌对应的默认应用商店
     * @return 应用商店包名，如果无法确定则返回null
     */
    fun getDefaultAppStoreByBrand(): String? {
        val brand = android.os.Build.BRAND.lowercase()
        
        return when {
            brand.contains("huawei") || brand.contains("honor") -> "com.huawei.appmarket"
            brand.contains("xiaomi") || brand.contains("redmi") -> "com.xiaomi.market"
            brand.contains("oppo") -> "com.oppo.market"
            brand.contains("vivo") -> "com.bbk.appstore"
            brand.contains("samsung") -> "com.sec.android.app.samsungapps"
            brand.contains("meizu") -> "com.meizu.mstore"
            else -> null
        }
    }
}