package com.aiinterview.simulator.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.aiinterview.simulator.data.model.*
import com.aiinterview.simulator.data.repository.AuthRepository
import com.aiinterview.simulator.data.repository.InterviewRecordRepository
import com.aiinterview.simulator.data.repository.ProgressAnalysisRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 进步分析ViewModel
 * 负责管理进步分析界面的数据状态和用户交互逻辑
 */
@HiltViewModel
class ProgressAnalysisViewModel @Inject constructor(
    private val progressAnalysisRepository: ProgressAnalysisRepository,
    private val interviewRecordRepository: InterviewRecordRepository,
    private val authRepository: AuthRepository
) : ViewModel() {
    
    // UI状态管理
    private val _uiState = MutableStateFlow(ProgressAnalysisUiState())
    val uiState: StateFlow<ProgressAnalysisUiState> = _uiState.asStateFlow()
    
    // 当前选中的时间范围
    private val _selectedTimeRange = MutableStateFlow(AnalysisTimeRange.ALL)
    val selectedTimeRange: StateFlow<AnalysisTimeRange> = _selectedTimeRange.asStateFlow()
    
    // 当前选中的分析维度
    private val _selectedDimension = MutableStateFlow(AnalysisDimension.OVERALL)
    val selectedDimension: StateFlow<AnalysisDimension> = _selectedDimension.asStateFlow()
    
    // 重新练习相关状态
    private val _retryRecords = MutableStateFlow<List<InterviewRecordModel>>(emptyList())
    val retryRecords: StateFlow<List<InterviewRecordModel>> = _retryRecords.asStateFlow()
    
    // 记录管理状态
    private val _selectedRecords = MutableStateFlow<Set<String>>(emptySet())
    val selectedRecords: StateFlow<Set<String>> = _selectedRecords.asStateFlow()
    
    // 批量操作状态
    private val _isInSelectionMode = MutableStateFlow(false)
    val isInSelectionMode: StateFlow<Boolean> = _isInSelectionMode.asStateFlow()
    
    init {
        // 初始化时加载进步分析数据
        loadProgressAnalysis()
    }
    
    /**
     * 加载进步分析数据
     */
    fun loadProgressAnalysis() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                val currentUser = authRepository.getCurrentUser()
                if (currentUser != null) {
                    // 生成进步分析报告
                    val analysisResult = progressAnalysisRepository.generateProgressAnalysis(currentUser.id)
                    
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        analysisResult = analysisResult,
                        error = null
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "用户未登录"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "加载分析数据失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 设置时间范围筛选
     */
    fun setTimeRange(timeRange: AnalysisTimeRange) {
        _selectedTimeRange.value = timeRange
        // 重新加载对应时间范围的数据
        loadProgressAnalysis()
    }
    
    /**
     * 设置分析维度
     */
    fun setAnalysisDimension(dimension: AnalysisDimension) {
        _selectedDimension.value = dimension
    }
    
    /**
     * 获取指定类别的重新练习记录
     */
    fun loadRetryRecords(category: String) {
        viewModelScope.launch {
            try {
                val currentUser = authRepository.getCurrentUser()
                if (currentUser != null) {
                    val records = progressAnalysisRepository.getRecordsForRetry(
                        userId = currentUser.id,
                        category = category,
                        limit = 10
                    )
                    _retryRecords.value = records
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "加载练习记录失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 获取推荐的练习类别
     */
    fun getRecommendedCategories(): List<String> {
        return _uiState.value.analysisResult?.categoryAnalysis
            ?.sortedBy { it.averageScore }
            ?.map { it.category }
            ?: emptyList()
    }
    
    /**
     * 开始基于历史记录的重新练习
     * @param recordId 要重新练习的记录ID
     */
    fun startRetryPractice(recordId: String) {
        viewModelScope.launch {
            try {
                val currentUser = authRepository.getCurrentUser()
                if (currentUser != null) {
                    val record = interviewRecordRepository.getRecordById(recordId)
                    if (record != null) {
                        // 这里应该触发导航到面试界面，使用相同的岗位和题目类型
                        _uiState.value = _uiState.value.copy(
                            retryPracticeRecord = record
                        )
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "启动重新练习失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 进入选择模式（用于批量操作）
     */
    fun enterSelectionMode() {
        _isInSelectionMode.value = true
        _selectedRecords.value = emptySet()
    }
    
    /**
     * 退出选择模式
     */
    fun exitSelectionMode() {
        _isInSelectionMode.value = false
        _selectedRecords.value = emptySet()
    }
    
    /**
     * 切换记录的选中状态
     */
    fun toggleRecordSelection(recordId: String) {
        val currentSelection = _selectedRecords.value
        _selectedRecords.value = if (recordId in currentSelection) {
            currentSelection - recordId
        } else {
            currentSelection + recordId
        }
    }
    
    /**
     * 全选/取消全选
     */
    fun toggleSelectAll() {
        val allRecordIds = _retryRecords.value.map { it.id }.toSet()
        val currentSelection = _selectedRecords.value
        
        _selectedRecords.value = if (currentSelection.size == allRecordIds.size) {
            emptySet() // 如果已全选，则取消全选
        } else {
            allRecordIds // 否则全选
        }
    }
    
    /**
     * 批量删除选中的记录
     */
    fun deleteSelectedRecords() {
        viewModelScope.launch {
            try {
                val selectedIds = _selectedRecords.value
                if (selectedIds.isNotEmpty()) {
                    // 逐个删除选中的记录
                    selectedIds.forEach { recordId ->
                        interviewRecordRepository.deleteRecord(recordId)
                    }
                    
                    // 退出选择模式并刷新数据
                    exitSelectionMode()
                    loadProgressAnalysis()
                    
                    _uiState.value = _uiState.value.copy(
                        message = "已删除 ${selectedIds.size} 条记录"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "批量删除失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 删除指定时间之前的记录
     */
    fun deleteRecordsBefore(beforeTime: Long) {
        viewModelScope.launch {
            try {
                val currentUser = authRepository.getCurrentUser()
                if (currentUser != null) {
                    interviewRecordRepository.deleteRecordsBefore(currentUser.id, beforeTime)
                    loadProgressAnalysis()
                    
                    _uiState.value = _uiState.value.copy(
                        message = "已清理历史记录"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "清理记录失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 导出分析报告（生成文本格式的报告）
     */
    fun exportAnalysisReport(): String {
        val analysis = _uiState.value.analysisResult ?: return "暂无分析数据"
        
        return buildString {
            appendLine("=== 面试进步分析报告 ===")
            appendLine()
            
            // 总体趋势
            appendLine("【总体趋势】")
            appendLine("趋势方向: ${analysis.overallTrend.trendDirection.displayName}")
            appendLine("改进率: ${String.format("%.1f", analysis.overallTrend.improvementRate)}%")
            appendLine("当前水平: ${analysis.overallTrend.currentLevel.displayName}")
            appendLine("描述: ${analysis.overallTrend.description}")
            appendLine()
            
            // 分类别分析
            if (analysis.categoryAnalysis.isNotEmpty()) {
                appendLine("【分类别表现】")
                analysis.categoryAnalysis.forEach { category ->
                    appendLine("${category.category}:")
                    appendLine("  平均分数: ${String.format("%.1f", category.averageScore)}分")
                    appendLine("  最高分数: ${String.format("%.1f", category.bestScore)}分")
                    appendLine("  面试次数: ${category.interviewCount}次")
                    appendLine("  改进率: ${String.format("%.1f", category.improvementRate)}%")
                    appendLine("  趋势: ${category.trend.displayName}")
                    appendLine()
                }
            }
            
            // 时间分析
            appendLine("【练习统计】")
            appendLine("总练习时间: ${analysis.timeAnalysis.totalPracticeTime}分钟")
            appendLine("平均单次时长: ${analysis.timeAnalysis.averageSessionDuration}分钟")
            appendLine()
            
            // 改进建议
            if (analysis.recommendations.isNotEmpty()) {
                appendLine("【改进建议】")
                analysis.recommendations.forEachIndexed { index, recommendation ->
                    appendLine("${index + 1}. $recommendation")
                }
            }
            
            appendLine()
            appendLine("报告生成时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date())}")
        }
    }
    
    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    /**
     * 清除消息状态
     */
    fun clearMessage() {
        _uiState.value = _uiState.value.copy(message = null)
    }
    
    /**
     * 刷新数据
     */
    fun refresh() {
        loadProgressAnalysis()
    }
}

/**
 * 进步分析UI状态
 */
data class ProgressAnalysisUiState(
    val isLoading: Boolean = false,
    val analysisResult: ProgressAnalysisResult? = null,
    val retryPracticeRecord: InterviewRecordModel? = null,
    val error: String? = null,
    val message: String? = null
)

/**
 * 分析时间范围枚举
 */
enum class AnalysisTimeRange(val displayName: String, val days: Int?) {
    ALL("全部时间", null),
    LAST_WEEK("最近一周", 7),
    LAST_MONTH("最近一月", 30),
    LAST_THREE_MONTHS("最近三月", 90)
}

/**
 * 分析维度枚举
 */
enum class AnalysisDimension(val displayName: String) {
    OVERALL("总体趋势"),
    CATEGORY("分类别分析"),
    TIME_PATTERN("时间模式"),
    SCORE_DISTRIBUTION("分数分布")
}