package com.aiinterview.simulator.presentation.screen.analysis

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.aiinterview.simulator.data.model.*
import com.aiinterview.simulator.presentation.viewmodel.AnalysisTimeRange
import java.text.SimpleDateFormat
import java.util.*

/**
 * 进步分析界面的辅助组件
 * 包含各种图表、指示器和对话框组件
 */

/**
 * 趋势图标组件
 * 根据趋势方向显示对应的图标和颜色
 */
@Composable
fun TrendIcon(
    trendDirection: TrendDirection,
    size: Dp = 24.dp,
    modifier: Modifier = Modifier
) {
    val (icon, color) = when (trendDirection) {
        TrendDirection.RISING -> Icons.Default.TrendingUp to Color(0xFF4CAF50)
        TrendDirection.FALLING -> Icons.Default.TrendingDown to Color(0xFFF44336)
        TrendDirection.STABLE -> Icons.Default.TrendingFlat to Color(0xFF2196F3)
        TrendDirection.FLUCTUATING -> Icons.Default.ShowChart to Color(0xFFFF9800)
    }
    
    Icon(
        imageVector = icon,
        contentDescription = trendDirection.displayName,
        tint = color,
        modifier = modifier.size(size)
    )
}

/**
 * 表现水平指示器组件
 * 显示当前表现水平的进度条和标签
 */
@Composable
fun PerformanceLevelIndicator(
    level: PerformanceLevel,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        // 标签行
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "当前水平",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Text(
                text = level.displayName,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Bold,
                color = Color(level.color)
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 进度条
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(8.dp)
                .clip(RoundedCornerShape(4.dp))
                .background(MaterialTheme.colorScheme.surfaceVariant)
        ) {
            // 根据水平显示不同长度的进度条
            val progress = when (level) {
                PerformanceLevel.POOR -> 0.25f
                PerformanceLevel.AVERAGE -> 0.5f
                PerformanceLevel.GOOD -> 0.75f
                PerformanceLevel.EXCELLENT -> 1.0f
            }
            
            Box(
                modifier = Modifier
                    .fillMaxHeight()
                    .fillMaxWidth(progress)
                    .background(
                        color = Color(level.color),
                        shape = RoundedCornerShape(4.dp)
                    )
            )
        }
    }
}

/**
 * 统计项组件
 * 显示标签和数值的统计信息
 */
@Composable
fun StatisticItem(
    label: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center
        )
    }
}

/**
 * 周统计项组件
 * 显示单周的练习统计信息
 */
@Composable
fun WeeklyStatsItem(
    weekStats: WeeklyStats,
    modifier: Modifier = Modifier
) {
    val dateFormatter = SimpleDateFormat("MM/dd", Locale.getDefault())
    val weekStartDate = dateFormatter.format(Date(weekStats.weekStart))
    
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "周 $weekStartDate",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "${weekStats.interviewCount}次",
                style = MaterialTheme.typography.bodyMedium
            )
            
            if (weekStats.averageScore > 0) {
                Text(
                    text = "${String.format("%.1f", weekStats.averageScore)}分",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.primary
                )
            }
            
            Text(
                text = "${weekStats.totalDuration}分钟",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 分数区间项组件
 * 显示分数区间的统计信息和占比
 */
@Composable
fun ScoreRangeItem(
    range: String,
    count: Int,
    total: Int,
    modifier: Modifier = Modifier
) {
    val percentage = if (total > 0) (count.toFloat() / total * 100).toInt() else 0
    
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = range,
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.weight(1f)
        )
        
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "${count}次",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.primary
            )
            
            Text(
                text = "(${percentage}%)",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 简化的分数趋势图组件
 * 显示分数变化的折线图
 */
@Composable
fun SimpleScoreTrendChart(
    scoreHistory: List<ScorePoint>,
    modifier: Modifier = Modifier
) {
    if (scoreHistory.isEmpty()) return
    
    Canvas(modifier = modifier) {
        drawScoreTrendLine(scoreHistory)
    }
}

/**
 * 绘制分数趋势线
 */
private fun DrawScope.drawScoreTrendLine(scoreHistory: List<ScorePoint>) {
    if (scoreHistory.size < 2) return
    
    val width = size.width
    val height = size.height
    val padding = 20.dp.toPx()
    
    // 计算数据范围
    val minScore = scoreHistory.minOfOrNull { it.score } ?: 0.0
    val maxScore = scoreHistory.maxOfOrNull { it.score } ?: 100.0
    val scoreRange = maxScore - minScore
    
    // 如果分数范围太小，扩展范围以便更好地显示
    val adjustedMinScore = if (scoreRange < 20) {
        (minScore - 10).coerceAtLeast(0.0)
    } else minScore
    val adjustedMaxScore = if (scoreRange < 20) {
        (maxScore + 10).coerceAtMost(100.0)
    } else maxScore
    val adjustedRange = adjustedMaxScore - adjustedMinScore
    
    // 创建路径点
    val path = Path()
    val points = mutableListOf<Offset>()
    
    scoreHistory.forEachIndexed { index, scorePoint ->
        val x = padding + (index.toFloat() / (scoreHistory.size - 1)) * (width - 2 * padding)
        val y = height - padding - ((scorePoint.score - adjustedMinScore) / adjustedRange * (height - 2 * padding)).toFloat()
        
        points.add(Offset(x, y))
        
        if (index == 0) {
            path.moveTo(x, y)
        } else {
            path.lineTo(x, y)
        }
    }
    
    // 绘制趋势线
    drawPath(
        path = path,
        color = Color(0xFF2196F3),
        style = Stroke(width = 3.dp.toPx())
    )
    
    // 绘制数据点
    points.forEach { point ->
        drawCircle(
            color = Color(0xFF2196F3),
            radius = 4.dp.toPx(),
            center = point
        )
        
        // 绘制白色内圈
        drawCircle(
            color = Color.White,
            radius = 2.dp.toPx(),
            center = point
        )
    }
}

/**
 * 时间范围选择对话框
 */
@Composable
fun TimeRangeSelectionDialog(
    selectedTimeRange: AnalysisTimeRange,
    onTimeRangeSelected: (AnalysisTimeRange) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("选择分析时间范围") },
        text = {
            Column {
                AnalysisTimeRange.values().forEach { timeRange ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = timeRange == selectedTimeRange,
                            onClick = { onTimeRangeSelected(timeRange) }
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = timeRange.displayName,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("确定")
            }
        }
    )
}

/**
 * 导出报告对话框
 */
@Composable
fun ExportReportDialog(
    reportContent: String,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("分析报告") },
        text = {
            Column {
                Text(
                    text = "报告已生成，您可以复制以下内容：",
                    style = MaterialTheme.typography.bodyMedium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 报告内容预览（截取前200字符）
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Text(
                        text = reportContent.take(200) + if (reportContent.length > 200) "..." else "",
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier.padding(8.dp)
                    )
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    // 这里可以实现复制到剪贴板的功能
                    onDismiss()
                }
            ) {
                Text("复制报告")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("关闭")
            }
        }
    )
}

/**
 * 记录管理对话框
 */
@Composable
fun RecordManagementDialog(
    onBatchDelete: (Long) -> Unit,
    onDismiss: () -> Unit
) {
    var selectedOption by remember { mutableStateOf(0) }
    
    val deleteOptions = listOf(
        "删除一个月前的记录" to (30 * 24 * 60 * 60 * 1000L),
        "删除三个月前的记录" to (90 * 24 * 60 * 60 * 1000L),
        "删除六个月前的记录" to (180 * 24 * 60 * 60 * 1000L),
        "删除一年前的记录" to (365 * 24 * 60 * 60 * 1000L)
    )
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("记录管理") },
        text = {
            Column {
                Text(
                    text = "选择要清理的记录范围：",
                    style = MaterialTheme.typography.bodyMedium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                deleteOptions.forEachIndexed { index, (label, _) ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = index == selectedOption,
                            onClick = { selectedOption = index }
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = label,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "⚠️ 此操作无法撤销，请谨慎操作",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.error
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val (_, timeOffset) = deleteOptions[selectedOption]
                    val beforeTime = System.currentTimeMillis() - timeOffset
                    onBatchDelete(beforeTime)
                }
            ) {
                Text("确认删除", color = MaterialTheme.colorScheme.error)
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 空分析状态组件
 * 当没有足够数据进行分析时显示
 */
@Composable
fun EmptyAnalysisState(
    onNavigateToHistory: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            Icons.Default.Analytics,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "暂无分析数据",
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "完成更多面试练习后，这里将显示您的进步分析",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Button(onClick = onNavigateToHistory) {
            Text("查看历史记录")
        }
    }
}

/**
 * 错误信息组件
 */
@Composable
fun ErrorMessage(
    message: String,
    onRetry: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            Icons.Default.Error,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.error
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = message,
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.error,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Button(onClick = onRetry) {
            Text("重试")
        }
    }
}