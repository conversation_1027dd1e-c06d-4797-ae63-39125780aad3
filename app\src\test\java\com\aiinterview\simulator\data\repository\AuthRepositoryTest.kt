package com.aiinterview.simulator.data.repository

import app.cash.turbine.test
import com.aiinterview.simulator.data.api.AuthApi
import com.aiinterview.simulator.data.api.TokenManager
import com.aiinterview.simulator.data.dao.UserDao
import com.aiinterview.simulator.data.dto.request.LoginRequest
import com.aiinterview.simulator.data.dto.request.RegisterRequest
import com.aiinterview.simulator.data.dto.request.RefreshTokenRequest
import com.aiinterview.simulator.data.dto.response.AuthResponse
import com.aiinterview.simulator.data.model.ApiResponse
import com.aiinterview.simulator.data.model.User
import com.aiinterview.simulator.domain.util.Resource
import com.aiinterview.simulator.util.MainCoroutineRule
import com.aiinterview.simulator.util.MockDataFactory
import com.aiinterview.simulator.util.TestUtils
import com.google.common.truth.Truth.assertThat
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.*
import java.io.IOException

/**
 * AuthRepository单元测试类
 * 测试用户认证相关的Repository功能
 */
@ExperimentalCoroutinesApi
class AuthRepositoryTest {

    @get:Rule
    val mainCoroutineRule = MainCoroutineRule()

    @Mock
    private lateinit var authApi: AuthApi

    @Mock
    private lateinit var userDao: UserDao

    @Mock
    private lateinit var tokenManager: TokenManager

    private lateinit var authRepository: AuthRepository

    // 测试数据
    private val testUser = MockDataFactory.createMockUser()
    private val testAuthResponse = AuthResponse(
        user = testUser,
        accessToken = "test_access_token",
        refreshToken = "test_refresh_token"
    )

    @Before
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        authRepository = AuthRepositoryImpl(authApi, userDao, tokenManager)
    }

    /**
     * 测试用户注册成功场景
     */
    @Test
    fun `register should return success when api call succeeds`() = runTest {
        // Given - 准备测试数据
        val phoneNumber = "13800138000"
        val verificationCode = "123456"
        val expectedRequest = RegisterRequest(phoneNumber, verificationCode)
        val apiResponse = MockDataFactory.createMockApiResponse(testAuthResponse)

        // Mock API调用
        whenever(authApi.register(expectedRequest)).thenReturn(apiResponse)

        // When - 执行注册操作
        authRepository.register(phoneNumber, verificationCode).test {
            // Then - 验证结果
            // 第一个发射应该是Loading状态
            val loadingResult = awaitItem()
            assertThat(loadingResult).isInstanceOf(Resource.Loading::class.java)

            // 第二个发射应该是Success状态
            val successResult = awaitItem()
            assertThat(successResult).isInstanceOf(Resource.Success::class.java)
            assertThat(successResult.data).isEqualTo(testAuthResponse)

            awaitComplete()
        }

        // 验证交互
        verify(authApi).register(expectedRequest)
        verify(userDao).insertUser(testUser)
        verify(tokenManager).saveTokens(
            accessToken = testAuthResponse.accessToken,
            refreshToken = testAuthResponse.refreshToken,
            userId = testUser.id,
            phoneNumber = testUser.phoneNumber
        )
    }

    /**
     * 测试用户注册失败场景
     */
    @Test
    fun `register should return error when api call fails`() = runTest {
        // Given - 准备失败的API响应
        val phoneNumber = "13800138000"
        val verificationCode = "123456"
        val expectedRequest = RegisterRequest(phoneNumber, verificationCode)
        val errorResponse = MockDataFactory.createMockErrorResponse("验证码错误")

        whenever(authApi.register(expectedRequest)).thenReturn(errorResponse)

        // When - 执行注册操作
        authRepository.register(phoneNumber, verificationCode).test {
            // Then - 验证结果
            val loadingResult = awaitItem()
            assertThat(loadingResult).isInstanceOf(Resource.Loading::class.java)

            val errorResult = awaitItem()
            assertThat(errorResult).isInstanceOf(Resource.Error::class.java)
            assertThat(errorResult.message).isEqualTo("验证码错误")

            awaitComplete()
        }

        // 验证没有保存用户数据
        verify(userDao, never()).insertUser(any())
        verify(tokenManager, never()).saveTokens(any(), any(), any(), any())
    }

    /**
     * 测试网络异常场景
     */
    @Test
    fun `register should return error when network exception occurs`() = runTest {
        // Given - 模拟网络异常
        val phoneNumber = "13800138000"
        val verificationCode = "123456"
        val expectedRequest = RegisterRequest(phoneNumber, verificationCode)

        whenever(authApi.register(expectedRequest)).thenThrow(IOException("网络连接失败"))

        // When - 执行注册操作
        authRepository.register(phoneNumber, verificationCode).test {
            // Then - 验证结果
            val loadingResult = awaitItem()
            assertThat(loadingResult).isInstanceOf(Resource.Loading::class.java)

            val errorResult = awaitItem()
            assertThat(errorResult).isInstanceOf(Resource.Error::class.java)
            assertThat(errorResult.message).contains("网络")

            awaitComplete()
        }
    }

    /**
     * 测试用户登录成功场景
     */
    @Test
    fun `login should return success when api call succeeds`() = runTest {
        // Given - 准备测试数据
        val phoneNumber = "13800138000"
        val verificationCode = "123456"
        val expectedRequest = LoginRequest(phoneNumber, null, verificationCode)
        val apiResponse = MockDataFactory.createMockApiResponse(testAuthResponse)

        whenever(authApi.login(expectedRequest)).thenReturn(apiResponse)

        // When - 执行登录操作
        authRepository.login(phoneNumber, verificationCode = verificationCode).test {
            // Then - 验证结果
            val loadingResult = awaitItem()
            assertThat(loadingResult).isInstanceOf(Resource.Loading::class.java)

            val successResult = awaitItem()
            assertThat(successResult).isInstanceOf(Resource.Success::class.java)
            assertThat(successResult.data).isEqualTo(testAuthResponse)

            awaitComplete()
        }

        // 验证交互
        verify(authApi).login(expectedRequest)
        verify(userDao).insertUser(testUser)
        verify(tokenManager).saveTokens(
            accessToken = testAuthResponse.accessToken,
            refreshToken = testAuthResponse.refreshToken,
            userId = testUser.id,
            phoneNumber = testUser.phoneNumber
        )
    }

    /**
     * 测试Token刷新成功场景
     */
    @Test
    fun `refreshToken should return success when api call succeeds`() = runTest {
        // Given - 准备测试数据
        val refreshToken = "test_refresh_token"
        val expectedRequest = RefreshTokenRequest(refreshToken)
        val apiResponse = MockDataFactory.createMockApiResponse(testAuthResponse)

        whenever(tokenManager.getRefreshToken()).thenReturn(flowOf(refreshToken))
        whenever(authApi.refreshToken(expectedRequest)).thenReturn(apiResponse)

        // When - 执行Token刷新操作
        authRepository.refreshToken().test {
            // Then - 验证结果
            val loadingResult = awaitItem()
            assertThat(loadingResult).isInstanceOf(Resource.Loading::class.java)

            val successResult = awaitItem()
            assertThat(successResult).isInstanceOf(Resource.Success::class.java)
            assertThat(successResult.data).isEqualTo(testAuthResponse)

            awaitComplete()
        }

        // 验证交互
        verify(tokenManager).getRefreshToken()
        verify(authApi).refreshToken(expectedRequest)
        verify(tokenManager).saveTokens(
            accessToken = testAuthResponse.accessToken,
            refreshToken = testAuthResponse.refreshToken,
            userId = testUser.id,
            phoneNumber = testUser.phoneNumber
        )
    }

    /**
     * 测试Token刷新失败场景 - 没有RefreshToken
     */
    @Test
    fun `refreshToken should return error when no refresh token available`() = runTest {
        // Given - 没有RefreshToken
        whenever(tokenManager.getRefreshToken()).thenReturn(flowOf(null))

        // When - 执行Token刷新操作
        authRepository.refreshToken().test {
            // Then - 验证结果
            val loadingResult = awaitItem()
            assertThat(loadingResult).isInstanceOf(Resource.Loading::class.java)

            val errorResult = awaitItem()
            assertThat(errorResult).isInstanceOf(Resource.Error::class.java)
            assertThat(errorResult.message).contains("No refresh token")

            awaitComplete()
        }

        // 验证没有调用API
        verify(authApi, never()).refreshToken(any())
    }

    /**
     * 测试用户登出功能
     */
    @Test
    fun `logout should clear tokens`() = runTest {
        // When - 执行登出操作
        authRepository.logout()

        // Then - 验证Token被清除
        verify(tokenManager).clearTokens()
    }

    /**
     * 测试根据ID获取用户
     */
    @Test
    fun `getUserById should return user from dao`() = runTest {
        // Given - 准备测试数据
        val userId = TestUtils.createTestUserId()
        whenever(userDao.getUserById(userId)).thenReturn(testUser)

        // When - 获取用户
        val result = authRepository.getUserById(userId)

        // Then - 验证结果
        assertThat(result).isEqualTo(testUser)
        verify(userDao).getUserById(userId)
    }

    /**
     * 测试根据手机号获取用户
     */
    @Test
    fun `getUserByPhone should return user from dao`() = runTest {
        // Given - 准备测试数据
        val phoneNumber = "13800138000"
        whenever(userDao.getUserByPhone(phoneNumber)).thenReturn(testUser)

        // When - 获取用户
        val result = authRepository.getUserByPhone(phoneNumber)

        // Then - 验证结果
        assertThat(result).isEqualTo(testUser)
        verify(userDao).getUserByPhone(phoneNumber)
    }

    /**
     * 测试检查登录状态
     */
    @Test
    fun `isLoggedIn should return login status from token manager`() = runTest {
        // Given - 准备登录状态
        val isLoggedIn = true
        whenever(tokenManager.isLoggedIn()).thenReturn(flowOf(isLoggedIn))

        // When - 检查登录状态
        authRepository.isLoggedIn().test {
            // Then - 验证结果
            val result = awaitItem()
            assertThat(result).isEqualTo(isLoggedIn)
            awaitComplete()
        }

        verify(tokenManager).isLoggedIn()
    }

    /**
     * 测试获取当前用户ID
     */
    @Test
    fun `getCurrentUserId should return user id from token manager`() = runTest {
        // Given - 准备用户ID
        val userId = TestUtils.createTestUserId()
        whenever(tokenManager.getUserId()).thenReturn(flowOf(userId))

        // When - 获取当前用户ID
        authRepository.getCurrentUserId().test {
            // Then - 验证结果
            val result = awaitItem()
            assertThat(result).isEqualTo(userId)
            awaitComplete()
        }

        verify(tokenManager).getUserId()
    }
}