package com.aiinterview.simulator.data.api

import com.aiinterview.simulator.data.dto.request.TTSRequest
import com.aiinterview.simulator.data.dto.response.ApiResponse
import com.aiinterview.simulator.data.dto.response.TTSResponse
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.POST

/**
 * 语音合成（TTS）API接口
 * 支持百度、腾讯、讯飞等多个TTS服务提供商
 */
interface TextToSpeechApi {
    
    /**
     * 百度TTS API
     */
    @POST("rest/2.0/tts/v1/synthesize")
    suspend fun synthesizeWithBaidu(
        @Header("Authorization") authorization: String,
        @Body request: TTSRequest
    ): ApiResponse<TTSResponse>
    
    /**
     * 腾讯云TTS API
     */
    @POST("tts/v1/")
    suspend fun synthesizeWithTencent(
        @Header("Authorization") authorization: String,
        @Header("Content-Type") contentType: String = "application/json",
        @Body request: TTSRequest
    ): ApiResponse<TTSResponse>
    
    /**
     * 讯飞TTS API
     */
    @POST("v2/tts")
    suspend fun synthesizeWithiFlytek(
        @Header("Authorization") authorization: String,
        @Header("Content-Type") contentType: String = "application/json",
        @Body request: TTSRequest
    ): ApiResponse<TTSResponse>
}