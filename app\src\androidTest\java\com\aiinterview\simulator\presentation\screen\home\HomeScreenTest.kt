package com.aiinterview.simulator.presentation.screen.home

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.aiinterview.simulator.presentation.theme.AIInterviewSimulatorTheme
import com.aiinterview.simulator.util.ComposeTestUtils
import com.aiinterview.simulator.util.ComposeTestUtils.assertButtonClickable
import com.aiinterview.simulator.util.ComposeTestUtils.assertListItemCount
import com.aiinterview.simulator.util.ComposeTestUtils.assertTextExists
import com.aiinterview.simulator.util.ComposeTestUtils.clickButton
import com.aiinterview.simulator.util.ComposeTestUtils.performPullToRefresh
import com.aiinterview.simulator.util.ComposeTestUtils.waitForLoadingToComplete
import com.aiinterview.simulator.util.MockTestData
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * 主界面UI测试类
 * 测试主界面的岗位列表显示和交互功能
 */
@RunWith(AndroidJUnit4::class)
class HomeScreenTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    /**
     * 测试主界面初始状态
     */
    @Test
    fun homeScreen_initialState_displaysCorrectly() {
        // Given - 设置主界面
        composeTestRule.setContent {
            AIInterviewSimulatorTheme {
                // HomeScreen(
                //     positions = MockTestData.createTestPositions(),
                //     isLoading = false,
                //     onPositionClick = {},
                //     onRefresh = {}
                // )
            }
        }

        // Then - 验证初始状态
        composeTestRule.assertTextExists("AI面试模拟器")
        composeTestRule.assertTextExists("选择面试岗位")
        composeTestRule.assertTextExists("为您推荐以下岗位")
        
        // 验证岗位列表显示
        composeTestRule.assertTextExists("公务员-行政管理")
        composeTestRule.assertTextExists("公务员-财务管理")
        composeTestRule.assertTextExists("公务员-人力资源")
        
        // 验证岗位信息显示
        composeTestRule.assertTextExists("中级")
        composeTestRule.assertTextExists("高级")
        composeTestRule.assertTextExists("初级")
    }

    /**
     * 测试岗位列表加载状态
     */
    @Test
    fun homeScreen_loadingState_displaysCorrectly() {
        composeTestRule.setContent {
            AIInterviewSimulatorTheme {
                // HomeScreen(
                //     positions = emptyList(),
                //     isLoading = true,
                //     onPositionClick = {},
                //     onRefresh = {}
                // )
            }
        }

        // Then - 验证加载状态
        composeTestRule.assertTextExists("加载中...")
        composeTestRule.onNode(hasProgressBarRangeInfo(ProgressBarRangeInfo.Indeterminate))
            .assertExists()
            .assertIsDisplayed()
    }

    /**
     * 测试岗位列表为空状态
     */
    @Test
    fun homeScreen_emptyState_displaysCorrectly() {
        composeTestRule.setContent {
            AIInterviewSimulatorTheme {
                // HomeScreen(
                //     positions = emptyList(),
                //     isLoading = false,
                //     onPositionClick = {},
                //     onRefresh = {}
                // )
            }
        }

        // Then - 验证空状态
        composeTestRule.assertTextExists("暂无可用岗位")
        composeTestRule.assertTextExists("请稍后再试或联系管理员")
        composeTestRule.assertButtonClickable("刷新")
    }

    /**
     * 测试岗位点击功能
     */
    @Test
    fun homeScreen_positionClick_triggersCallback() {
        var clickedPositionId: String? = null
        
        composeTestRule.setContent {
            AIInterviewSimulatorTheme {
                // HomeScreen(
                //     positions = MockTestData.createTestPositions(),
                //     isLoading = false,
                //     onPositionClick = { positionId -> clickedPositionId = positionId },
                //     onRefresh = {}
                // )
            }
        }

        // When - 点击第一个岗位
        composeTestRule.clickButton("公务员-行政管理")

        // Then - 验证回调被调用
        assert(clickedPositionId == "position_001") { 
            "岗位点击回调应该被调用，期望: position_001, 实际: $clickedPositionId" 
        }
    }

    /**
     * 测试岗位详细信息显示
     */
    @Test
    fun homeScreen_positionDetails_displayCorrectly() {
        composeTestRule.setContent {
            AIInterviewSimulatorTheme {
                // HomeScreen(
                //     positions = MockTestData.createTestPositions(),
                //     isLoading = false,
                //     onPositionClick = {},
                //     onRefresh = {}
                // )
            }
        }

        // Then - 验证岗位详细信息
        // 验证第一个岗位信息
        composeTestRule.assertTextExists("负责行政管理相关工作")
        composeTestRule.assertTextExists("本科及以上学历")
        composeTestRule.assertTextExists("2年以上工作经验")
        composeTestRule.assertTextExists("良好的沟通能力")
        
        // 验证面试配置信息
        composeTestRule.assertTextExists("面试时长：20分钟")
        composeTestRule.assertTextExists("题目数量：4题")
        
        // 验证第二个岗位信息
        composeTestRule.assertTextExists("负责财务管理和预算编制工作")
        composeTestRule.assertTextExists("财务相关专业")
        composeTestRule.assertTextExists("3年以上工作经验")
        composeTestRule.assertTextExists("持有会计师证书")
    }

    /**
     * 测试下拉刷新功能
     */
    @Test
    fun homeScreen_pullToRefresh_worksCorrectly() {
        var refreshCalled = false
        
        composeTestRule.setContent {
            AIInterviewSimulatorTheme {
                // HomeScreen(
                //     positions = MockTestData.createTestPositions(),
                //     isLoading = false,
                //     onPositionClick = {},
                //     onRefresh = { refreshCalled = true }
                // )
            }
        }

        // When - 执行下拉刷新
        composeTestRule.performPullToRefresh("position_list")

        // Then - 验证刷新回调被调用
        assert(refreshCalled) { "下拉刷新回调应该被调用" }
    }

    /**
     * 测试岗位列表滚动功能
     */
    @Test
    fun homeScreen_scrolling_worksCorrectly() {
        composeTestRule.setContent {
            AIInterviewSimulatorTheme {
                // HomeScreen(
                //     positions = MockTestData.createTestPositions(),
                //     isLoading = false,
                //     onPositionClick = {},
                //     onRefresh = {}
                // )
            }
        }

        // When - 滚动到列表底部
        composeTestRule.onNodeWithTag("position_list")
            .performScrollToIndex(2)

        // Then - 验证最后一个岗位可见
        composeTestRule.assertTextExists("公务员-人力资源")
        composeTestRule.assertTextExists("负责人力资源管理和招聘工作")
    }

    /**
     * 测试岗位筛选功能
     */
    @Test
    fun homeScreen_positionFilter_worksCorrectly() {
        composeTestRule.setContent {
            AIInterviewSimulatorTheme {
                // HomeScreen(
                //     positions = MockTestData.createTestPositions(),
                //     isLoading = false,
                //     onPositionClick = {},
                //     onRefresh = {},
                //     showFilter = true
                // )
            }
        }

        // 验证筛选按钮存在
        composeTestRule.assertButtonClickable("筛选")
        
        // When - 点击筛选按钮
        composeTestRule.clickButton("筛选")
        
        // Then - 验证筛选对话框显示
        composeTestRule.assertTextExists("岗位筛选")
        composeTestRule.assertTextExists("岗位类别")
        composeTestRule.assertTextExists("难度等级")
        
        // 选择筛选条件
        composeTestRule.clickButton("公务员")
        composeTestRule.clickButton("中级")
        composeTestRule.clickButton("确定")
        
        // 验证筛选结果
        composeTestRule.waitForLoadingToComplete()
        composeTestRule.assertTextExists("公务员-行政管理")
    }

    /**
     * 测试搜索功能
     */
    @Test
    fun homeScreen_search_worksCorrectly() {
        composeTestRule.setContent {
            AIInterviewSimulatorTheme {
                // HomeScreen(
                //     positions = MockTestData.createTestPositions(),
                //     isLoading = false,
                //     onPositionClick = {},
                //     onRefresh = {},
                //     showSearch = true
                // )
            }
        }

        // 验证搜索框存在
        composeTestRule.onNodeWithText("搜索岗位").assertExists()
        
        // When - 输入搜索关键词
        composeTestRule.onNodeWithText("搜索岗位")
            .performTextInput("行政")
        
        // Then - 验证搜索结果
        composeTestRule.waitForLoadingToComplete()
        composeTestRule.assertTextExists("公务员-行政管理")
        composeTestRule.onNodeWithText("公务员-财务管理").assertDoesNotExist()
        composeTestRule.onNodeWithText("公务员-人力资源").assertDoesNotExist()
    }

    /**
     * 测试岗位收藏功能
     */
    @Test
    fun homeScreen_positionFavorite_worksCorrectly() {
        composeTestRule.setContent {
            AIInterviewSimulatorTheme {
                // HomeScreen(
                //     positions = MockTestData.createTestPositions(),
                //     isLoading = false,
                //     onPositionClick = {},
                //     onRefresh = {},
                //     showFavorite = true
                // )
            }
        }

        // When - 点击收藏按钮
        composeTestRule.onNodeWithContentDescription("收藏岗位")
            .onFirst()
            .performClick()
        
        // Then - 验证收藏状态变化
        composeTestRule.onNodeWithContentDescription("已收藏")
            .assertExists()
        
        // 验证收藏成功提示
        composeTestRule.assertTextExists("已添加到收藏")
    }

    /**
     * 测试错误状态显示
     */
    @Test
    fun homeScreen_errorState_displaysCorrectly() {
        composeTestRule.setContent {
            AIInterviewSimulatorTheme {
                // HomeScreen(
                //     positions = emptyList(),
                //     isLoading = false,
                //     error = MockTestData.ErrorMessages.NETWORK_ERROR,
                //     onPositionClick = {},
                //     onRefresh = {}
                // )
            }
        }

        // Then - 验证错误状态
        composeTestRule.assertTextExists("加载失败")
        composeTestRule.assertTextExists(MockTestData.ErrorMessages.NETWORK_ERROR)
        composeTestRule.assertButtonClickable("重试")
        
        // 验证错误图标显示
        composeTestRule.onNodeWithContentDescription("错误图标")
            .assertExists()
            .assertIsDisplayed()
    }

    /**
     * 测试岗位卡片布局
     */
    @Test
    fun homeScreen_positionCard_layoutCorrectly() {
        composeTestRule.setContent {
            AIInterviewSimulatorTheme {
                // HomeScreen(
                //     positions = MockTestData.createTestPositions(),
                //     isLoading = false,
                //     onPositionClick = {},
                //     onRefresh = {}
                // )
            }
        }

        // 验证岗位卡片的各个元素
        val firstPositionCard = composeTestRule.onNodeWithTag("position_card_0")
        firstPositionCard.assertExists()
        
        // 验证卡片内容布局
        firstPositionCard.onChildren()
            .assertCountEquals(4) // 标题、描述、要求、按钮
        
        // 验证卡片可点击
        firstPositionCard.assertHasClickAction()
        
        // 验证卡片阴影效果
        firstPositionCard.assertIsDisplayed()
    }

    /**
     * 测试响应式布局
     */
    @Test
    fun homeScreen_responsiveLayout_adaptsCorrectly() {
        composeTestRule.setContent {
            AIInterviewSimulatorTheme {
                // HomeScreen(
                //     positions = MockTestData.createTestPositions(),
                //     isLoading = false,
                //     onPositionClick = {},
                //     onRefresh = {}
                // )
            }
        }

        // 验证在不同屏幕尺寸下的布局
        // 验证标题始终可见
        composeTestRule.assertTextExists("AI面试模拟器")
        
        // 验证岗位列表适应屏幕
        composeTestRule.onNodeWithTag("position_list")
            .assertExists()
            .assertIsDisplayed()
        
        // 验证所有岗位卡片都正确显示
        composeTestRule.assertListItemCount("position_list", 3)
    }

    /**
     * 测试无障碍功能
     */
    @Test
    fun homeScreen_accessibility_worksCorrectly() {
        composeTestRule.setContent {
            AIInterviewSimulatorTheme {
                // HomeScreen(
                //     positions = MockTestData.createTestPositions(),
                //     isLoading = false,
                //     onPositionClick = {},
                //     onRefresh = {}
                // )
            }
        }

        // 验证重要元素有正确的内容描述
        composeTestRule.onNodeWithContentDescription("岗位列表").assertExists()
        composeTestRule.onNodeWithContentDescription("刷新岗位列表").assertExists()
        
        // 验证岗位卡片的无障碍信息
        composeTestRule.onNodeWithContentDescription("公务员-行政管理岗位，中级难度")
            .assertExists()
        
        // 验证语义角色正确
        composeTestRule.onNode(hasRole(Role.Button) and hasText("公务员-行政管理"))
            .assertExists()
    }
}