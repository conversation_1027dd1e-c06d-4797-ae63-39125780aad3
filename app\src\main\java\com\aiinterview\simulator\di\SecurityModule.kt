package com.aiinterview.simulator.di

import android.content.Context
import com.aiinterview.simulator.data.security.*
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 安全模块依赖注入配置
 * 提供安全相关的依赖注入
 */
@Module
@InstallIn(SingletonComponent::class)
object SecurityModule {
    
    /**
     * 提供加密存储管理器实例
     * @param context 应用上下文
     * @return 加密存储管理器单例
     */
    @Provides
    @Singleton
    fun provideEncryptedStorageManager(
        @ApplicationContext context: Context
    ): EncryptedStorageManager {
        return EncryptedStorageManager(context)
    }
    
    /**
     * 提供API安全管理器实例
     * @param encryptedStorageManager 加密存储管理器
     * @return API安全管理器单例
     */
    @Provides
    @Singleton
    fun provideApiSecurityManager(
        encryptedStorageManager: EncryptedStorageManager
    ): ApiSecurityManager {
        return ApiSecurityManager(encryptedStorageManager)
    }
    
    /**
     * 提供隐私管理器实例
     * @param context 应用上下文
     * @param encryptedStorageManager 加密存储管理器
     * @return 隐私管理器单例
     */
    @Provides
    @Singleton
    fun providePrivacyManager(
        @ApplicationContext context: Context,
        encryptedStorageManager: EncryptedStorageManager
    ): PrivacyManager {
        return PrivacyManager(context, encryptedStorageManager)
    }
    
    /**
     * 提供数据清理管理器实例
     * @param context 应用上下文
     * @param encryptedStorageManager 加密存储管理器
     * @param privacyManager 隐私管理器
     * @return 数据清理管理器单例
     */
    @Provides
    @Singleton
    fun provideDataCleanupManager(
        @ApplicationContext context: Context,
        encryptedStorageManager: EncryptedStorageManager,
        privacyManager: PrivacyManager
    ): DataCleanupManager {
        return DataCleanupManager(context, encryptedStorageManager, privacyManager)
    }
}