package com.aiinterview.simulator.presentation.viewmodel

import com.aiinterview.simulator.data.repository.SpeechRecognitionRepository
import com.aiinterview.simulator.data.speech.SpeechRecognitionService
import com.aiinterview.simulator.data.dto.response.RecognitionResult
import com.aiinterview.simulator.domain.util.Resource
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.whenever
import org.mockito.kotlin.verify
import org.junit.Assert.*
import java.io.File

@ExperimentalCoroutinesApi
class SpeechRecognitionViewModelTest {
    
    @Mock
    private lateinit var speechRecognitionRepository: SpeechRecognitionRepository
    
    @Mock
    private lateinit var audioFile: File
    
    private lateinit var speechRecognitionViewModel: SpeechRecognitionViewModel
    private val testDispatcher = UnconfinedTestDispatcher()
    
    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        Dispatchers.setMain(testDispatcher)
        speechRecognitionViewModel = SpeechRecognitionViewModel(speechRecognitionRepository)
    }
    
    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }
    
    @Test
    fun `recognizeAudio should update recognition state`() = runTest {
        // Given
        val recognitionResult = RecognitionResult(
            text = "这是一个测试回答",
            confidence = 0.85,
            duration = 5000L,
            provider = "BAIDU",
            success = true
        )
        val successResource = Resource.Success(recognitionResult)
        
        whenever(speechRecognitionRepository.recognizeAudio(audioFile, SpeechRecognitionService.Provider.AUTO))
            .thenReturn(flowOf(successResource))
        
        // When
        speechRecognitionViewModel.recognizeAudio(audioFile)
        
        // Then
        assertEquals(successResource, speechRecognitionViewModel.recognitionState.value)
        assertEquals(recognitionResult, speechRecognitionViewModel.currentRecognitionResult.value)
        assertFalse(speechRecognitionViewModel.isRecognizing.value)
    }
    
    @Test
    fun `retryRecognition should use different provider`() = runTest {
        // Given
        val initialResult = RecognitionResult(
            text = "初始结果",
            confidence = 0.5,
            duration = 3000L,
            provider = "BAIDU",
            success = true
        )
        
        // 设置初始结果
        whenever(speechRecognitionRepository.recognizeAudio(audioFile, SpeechRecognitionService.Provider.AUTO))
            .thenReturn(flowOf(Resource.Success(initialResult)))
        
        speechRecognitionViewModel.recognizeAudio(audioFile)
        
        // 设置重试结果
        val retryResult = RecognitionResult(
            text = "重试结果",
            confidence = 0.9,
            duration = 3000L,
            provider = "TENCENT",
            success = true
        )
        
        whenever(speechRecognitionRepository.recognizeAudio(audioFile, SpeechRecognitionService.Provider.TENCENT))
            .thenReturn(flowOf(Resource.Success(retryResult)))
        
        // When
        speechRecognitionViewModel.retryRecognition(audioFile)
        
        // Then
        verify(speechRecognitionRepository).recognizeAudio(audioFile, SpeechRecognitionService.Provider.TENCENT)
    }
    
    @Test
    fun `updateRecognitionText should update current result`() = runTest {
        // Given
        val originalResult = RecognitionResult(
            text = "原始文本",
            confidence = 0.8,
            duration = 3000L,
            provider = "BAIDU",
            success = true
        )
        
        whenever(speechRecognitionRepository.recognizeAudio(audioFile, SpeechRecognitionService.Provider.AUTO))
            .thenReturn(flowOf(Resource.Success(originalResult)))
        
        speechRecognitionViewModel.recognizeAudio(audioFile)
        
        // When
        val newText = "编辑后的文本"
        speechRecognitionViewModel.updateRecognitionText(newText)
        
        // Then
        val updatedResult = speechRecognitionViewModel.currentRecognitionResult.value
        assertEquals(newText, updatedResult?.text)
        assertEquals(1.0, updatedResult?.confidence) // 手动编辑的文本置信度应该是1.0
    }
    
    @Test
    fun `getConfidenceDescription should call repository method`() {
        // Given
        val confidence = 0.85
        val expectedDescription = "高"
        
        whenever(speechRecognitionRepository.getConfidenceDescription(confidence))
            .thenReturn(expectedDescription)
        
        // When
        val result = speechRecognitionViewModel.getConfidenceDescription(confidence)
        
        // Then
        assertEquals(expectedDescription, result)
        verify(speechRecognitionRepository).getConfidenceDescription(confidence)
    }
    
    @Test
    fun `isRecognitionResultGood should return true for good result`() = runTest {
        // Given
        val goodResult = RecognitionResult(
            text = "这是一个很好的识别结果",
            confidence = 0.9,
            duration = 5000L,
            provider = "BAIDU",
            success = true
        )
        
        whenever(speechRecognitionRepository.recognizeAudio(audioFile, SpeechRecognitionService.Provider.AUTO))
            .thenReturn(flowOf(Resource.Success(goodResult)))
        
        speechRecognitionViewModel.recognizeAudio(audioFile)
        
        // When
        val isGood = speechRecognitionViewModel.isRecognitionResultGood()
        
        // Then
        assertTrue(isGood)
    }
    
    @Test
    fun `isRecognitionResultGood should return false for poor result`() = runTest {
        // Given
        val poorResult = RecognitionResult(
            text = "",
            confidence = 0.3,
            duration = 5000L,
            provider = "BAIDU",
            success = false
        )
        
        whenever(speechRecognitionRepository.recognizeAudio(audioFile, SpeechRecognitionService.Provider.AUTO))
            .thenReturn(flowOf(Resource.Success(poorResult)))
        
        speechRecognitionViewModel.recognizeAudio(audioFile)
        
        // When
        val isGood = speechRecognitionViewModel.isRecognitionResultGood()
        
        // Then
        assertFalse(isGood)
    }
    
    @Test
    fun `getRecognitionStatistics should calculate correctly`() = runTest {
        // Given - 模拟多次识别历史
        val results = listOf(
            RecognitionResult("结果1", 0.9, 3000L, "BAIDU", true),
            RecognitionResult("结果2", 0.8, 4000L, "BAIDU", true),
            RecognitionResult("", 0.3, 2000L, "TENCENT", false)
        )
        
        // 模拟添加到历史记录
        results.forEach { result ->
            whenever(speechRecognitionRepository.recognizeAudio(audioFile, SpeechRecognitionService.Provider.AUTO))
                .thenReturn(flowOf(Resource.Success(result)))
            speechRecognitionViewModel.recognizeAudio(audioFile)
        }
        
        // When
        val statistics = speechRecognitionViewModel.getRecognitionStatistics()
        
        // Then
        assertEquals(3, statistics.totalRecognitions)
        assertEquals(2, statistics.successfulRecognitions)
        assertEquals("BAIDU", statistics.mostUsedProvider)
        assertTrue(statistics.averageConfidence > 0.8) // (0.9 + 0.8) / 2 = 0.85
    }
}