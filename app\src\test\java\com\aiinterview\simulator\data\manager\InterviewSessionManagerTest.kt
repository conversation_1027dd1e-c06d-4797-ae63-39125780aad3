package com.aiinterview.simulator.data.manager

import com.aiinterview.simulator.data.model.*
import com.aiinterview.simulator.data.repository.InterviewRepository
import com.aiinterview.simulator.data.service.AIQuestionGenerationService
import com.aiinterview.simulator.data.service.TextToSpeechService
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.*
import org.junit.Assert.*
import java.io.File

@ExperimentalCoroutinesApi
class InterviewSessionManagerTest {
    
    @Mock
    private lateinit var interviewRepository: InterviewRepository
    
    @Mock
    private lateinit var aiQuestionService: AIQuestionGenerationService
    
    @Mock
    private lateinit var ttsService: TextToSpeechService
    
    @Mock
    private lateinit var mockFile: File
    
    private lateinit var sessionManager: InterviewSessionManager
    
    private val testPosition = Position(
        id = "test-position",
        name = "测试岗位",
        category = "测试分类",
        level = "测试级别",
        description = "测试描述",
        duration = 30,
        questionCount = 3,
        questionTypes = "[]",
        timeWarnings = "[]"
    )
    
    private val testQuestion = Question(
        id = "test-question-1",
        type = QuestionType.COMPREHENSIVE_ANALYSIS,
        category = "综合分析",
        title = "测试题目",
        content = "这是一个测试问题的内容",
        timeLimit = 300,
        difficulty = 3
    )
    
    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        sessionManager = InterviewSessionManager(interviewRepository, aiQuestionService, ttsService)
    }
    
    @Test
    fun `createSession should create new interview session successfully`() = runTest {
        // Given
        val userId = "test-user"
        val mockQuestionResponse = com.aiinterview.simulator.data.dto.response.QuestionGenerationResponse(
            success = true,
            question = com.aiinterview.simulator.data.dto.response.GeneratedQuestion(
                id = testQuestion.id,
                type = testQuestion.type.name,
                category = testQuestion.category,
                title = testQuestion.title,
                content = testQuestion.content,
                timeLimit = testQuestion.timeLimit,
                difficulty = testQuestion.difficulty
            ),
            provider = "test"
        )
        
        whenever(aiQuestionService.generateQuestion(any(), any(), any(), any()))
            .thenReturn(mockQuestionResponse)
        
        whenever(interviewRepository.saveInterviewSession(any()))
            .thenReturn(Unit)
        
        // When
        val result = sessionManager.createSession(userId, testPosition, true)
        
        // Then
        assertTrue(result.isSuccess)
        val session = result.getOrNull()
        assertNotNull(session)
        assertEquals(userId, session?.userId)
        assertEquals(testPosition.id, session?.positionId)
        assertEquals(InterviewStatus.CREATED, session?.status)
        
        verify(interviewRepository).saveInterviewSession(any())
    }
    
    @Test
    fun `startSession should start existing session successfully`() = runTest {
        // Given
        val sessionId = "test-session"
        val existingSession = InterviewSession(
            id = sessionId,
            userId = "test-user",
            positionId = testPosition.id,
            status = InterviewStatus.CREATED,
            startTime = System.currentTimeMillis(),
            endTime = null,
            currentQuestionIndex = 0,
            questions = listOf(testQuestion),
            answers = emptyList(),
            evaluation = null
        )
        
        whenever(interviewRepository.getInterviewSession(sessionId))
            .thenReturn(existingSession)
        whenever(interviewRepository.saveInterviewSession(any()))
            .thenReturn(Unit)
        
        // When
        val result = sessionManager.startSession(sessionId)
        
        // Then
        assertTrue(result.isSuccess)
        val session = result.getOrNull()
        assertEquals(InterviewStatus.STARTED, session?.status)
        
        verify(interviewRepository).saveInterviewSession(any())
    }
    
    @Test
    fun `proceedToNextQuestion should return next question successfully`() = runTest {
        // Given
        val session = InterviewSession(
            id = "test-session",
            userId = "test-user",
            positionId = testPosition.id,
            status = InterviewStatus.STARTED,
            startTime = System.currentTimeMillis(),
            endTime = null,
            currentQuestionIndex = 0,
            questions = listOf(testQuestion),
            answers = emptyList(),
            evaluation = null
        )
        
        // 设置活跃会话
        sessionManager.createSession("test-user", testPosition, false)
        
        whenever(interviewRepository.saveInterviewSession(any()))
            .thenReturn(Unit)
        
        // When
        val result = sessionManager.proceedToNextQuestion()
        
        // Then
        assertTrue(result.isSuccess)
        val question = result.getOrNull()
        assertNotNull(question)
    }
    
    @Test
    fun `submitAnswer should save answer and update session`() = runTest {
        // Given
        val questionId = "test-question-1"
        val transcription = "这是回答内容"
        val duration = 30
        
        // 先创建会话
        sessionManager.createSession("test-user", testPosition, false)
        
        whenever(interviewRepository.saveInterviewSession(any()))
            .thenReturn(Unit)
        
        // When
        val result = sessionManager.submitAnswer(questionId, mockFile, transcription, duration)
        
        // Then
        assertTrue(result.isSuccess)
        verify(interviewRepository, atLeastOnce()).saveInterviewSession(any())
    }
    
    @Test
    fun `handleRecordingFailure should create technical issue and return retry options`() = runTest {
        // Given
        val questionId = "test-question-1"
        val errorMessage = "录音设备不可用"
        
        // When
        val result = sessionManager.handleRecordingFailure(questionId, errorMessage)
        
        // Then
        assertTrue(result.isSuccess)
        val options = result.getOrNull()
        assertNotNull(options)
        assertTrue(options?.canRetryRecording == true)
        assertTrue(options?.canSkipQuestion == true)
        assertTrue(options?.canUseTextInput == true)
        assertEquals(3, options?.maxRetryAttempts)
    }
    
    @Test
    fun `handleSpeechRecognitionFailure should create issue and return retry options`() = runTest {
        // Given
        val questionId = "test-question-1"
        val provider = "BAIDU"
        val errorMessage = "识别服务不可用"
        
        // When
        val result = sessionManager.handleSpeechRecognitionFailure(questionId, mockFile, errorMessage)
        
        // Then
        assertTrue(result.isSuccess)
        val options = result.getOrNull()
        assertNotNull(options)
        assertTrue(options?.canRetryRecognition == true)
        assertTrue(options?.canRetryWithDifferentProvider == true)
        assertTrue(options?.canEditManually == true)
        assertTrue(options?.canReRecord == true)
        assertTrue(options?.availableProviders?.isNotEmpty() == true)
    }
    
    @Test
    fun `handleNetworkIssue should create issue and return recovery options`() = runTest {
        // Given
        val description = "网络连接超时"
        val questionId = "test-question-1"
        
        // When
        val result = sessionManager.handleNetworkIssue(description, questionId)
        
        // Then
        assertTrue(result.isSuccess)
        val options = result.getOrNull()
        assertNotNull(options)
        assertTrue(options?.canContinueOffline == true)
        assertTrue(options?.canRetryConnection == true)
        assertTrue(options?.canSaveProgress == true)
        assertTrue(options?.offlineCapabilities?.isNotEmpty() == true)
    }
    
    @Test
    fun `checkTimeWarnings should trigger appropriate warnings`() {
        // Given
        val fiveMinutesInMs = 5 * 60 * 1000L
        val oneMinuteInMs = 1 * 60 * 1000L
        val thirtySecondsInMs = 30 * 1000L
        val questionTimeLimit = 300 // 5 minutes
        
        // When & Then
        sessionManager.checkTimeWarnings(fiveMinutesInMs, questionTimeLimit)
        // Should trigger 5-minute warning
        
        sessionManager.checkTimeWarnings(oneMinuteInMs, questionTimeLimit)
        // Should trigger 1-minute warning
        
        sessionManager.checkTimeWarnings(thirtySecondsInMs, questionTimeLimit)
        // Should trigger 30-second warning
        
        sessionManager.checkTimeWarnings(0L, questionTimeLimit)
        // Should trigger time-up warning
        
        // Verify warnings were triggered (check internal state)
        val warnings = sessionManager.timeWarnings.value
        assertTrue(warnings.isNotEmpty())
    }
    
    @Test
    fun `pauseSession should pause active session`() = runTest {
        // Given
        sessionManager.createSession("test-user", testPosition, false)
        
        whenever(interviewRepository.saveInterviewSession(any()))
            .thenReturn(Unit)
        
        // When
        val result = sessionManager.pauseSession()
        
        // Then
        assertTrue(result.isSuccess)
        verify(ttsService).stopAudio()
    }
    
    @Test
    fun `resumeSession should resume paused session`() = runTest {
        // Given
        sessionManager.createSession("test-user", testPosition, false)
        sessionManager.pauseSession()
        
        whenever(interviewRepository.saveInterviewSession(any()))
            .thenReturn(Unit)
        
        // When
        val result = sessionManager.resumeSession()
        
        // Then
        assertTrue(result.isSuccess)
    }
    
    @Test
    fun `completeSession should mark session as completed`() = runTest {
        // Given
        sessionManager.createSession("test-user", testPosition, false)
        
        whenever(interviewRepository.saveInterviewSession(any()))
            .thenReturn(Unit)
        
        // When
        val result = sessionManager.completeSession()
        
        // Then
        assertTrue(result.isSuccess)
        val session = result.getOrNull()
        assertEquals(InterviewStatus.COMPLETED, session?.status)
        assertNotNull(session?.endTime)
    }
    
    @Test
    fun `cancelSession should cancel active session and cleanup resources`() = runTest {
        // Given
        sessionManager.createSession("test-user", testPosition, false)
        
        whenever(interviewRepository.saveInterviewSession(any()))
            .thenReturn(Unit)
        
        // When
        val result = sessionManager.cancelSession()
        
        // Then
        assertTrue(result.isSuccess)
        verify(ttsService).stopAudio()
        verify(ttsService).cleanupTempAudioFiles()
    }
    
    @Test
    fun `getSessionStatistics should return correct statistics`() {
        // Given
        // 假设有一个活跃的会话和一些进度数据
        
        // When
        val statistics = sessionManager.getSessionStatistics()
        
        // Then
        assertNotNull(statistics)
        assertTrue(statistics.totalQuestions >= 0)
        assertTrue(statistics.completedQuestions >= 0)
        assertTrue(statistics.completionRate >= 0f)
        assertTrue(statistics.totalTimeSpent >= 0L)
        assertTrue(statistics.technicalIssuesCount >= 0)
    }
    
    @Test
    fun `should handle AI question generation failure gracefully`() = runTest {
        // Given
        val userId = "test-user"
        
        whenever(aiQuestionService.generateQuestion(any(), any(), any(), any()))
            .thenThrow(RuntimeException("AI服务不可用"))
        
        whenever(interviewRepository.saveInterviewSession(any()))
            .thenReturn(Unit)
        
        // When
        val result = sessionManager.createSession(userId, testPosition, true)
        
        // Then
        assertTrue(result.isSuccess) // 应该使用降级问题
        val session = result.getOrNull()
        assertNotNull(session)
        assertTrue(session?.questions?.isNotEmpty() == true)
    }
}