package com.aiinterview.simulator.presentation.screen.history

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.aiinterview.simulator.presentation.viewmodel.SortOrder

/**
 * 排序对话框组件
 * 允许用户选择不同的排序方式
 * @param selectedSortOrder 当前选中的排序方式
 * @param onSortOrderSelected 排序方式选择回调函数
 * @param onDismiss 关闭对话框回调函数
 */
@Composable
fun SortDialog(
    selectedSortOrder: SortOrder,
    onSortOrderSelected: (SortOrder) -> Unit,
    onDismiss: () -> Unit
) {
    // 本地状态管理：临时存储用户的选择
    var tempSelectedSortOrder by remember { mutableStateOf(selectedSortOrder) }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { 
            Text("排序方式") 
        },
        text = {
            Column(
                modifier = Modifier.fillMaxWidth()
            ) {
                // 遍历所有排序选项
                SortOrder.values().forEach { sortOrder ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .selectable(
                                selected = tempSelectedSortOrder == sortOrder,
                                onClick = { tempSelectedSortOrder = sortOrder }
                            )
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 单选按钮
                        RadioButton(
                            selected = tempSelectedSortOrder == sortOrder,
                            onClick = { tempSelectedSortOrder = sortOrder }
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        
                        // 排序方式文本和描述
                        Column {
                            Text(
                                text = sortOrder.displayName,
                                style = MaterialTheme.typography.bodyLarge
                            )
                            // 为每种排序方式添加描述
                            Text(
                                text = getSortOrderDescription(sortOrder),
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            // 确认按钮
            TextButton(
                onClick = {
                    // 应用用户的选择
                    onSortOrderSelected(tempSelectedSortOrder)
                }
            ) {
                Text("确认")
            }
        },
        dismissButton = {
            // 取消按钮
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 获取排序方式的描述文本
 * @param sortOrder 排序方式
 * @return 描述文本
 */
private fun getSortOrderDescription(sortOrder: SortOrder): String {
    return when (sortOrder) {
        SortOrder.TIME_DESC -> "最新的面试记录在前"
        SortOrder.TIME_ASC -> "最早的面试记录在前"
        SortOrder.SCORE_DESC -> "分数高的面试记录在前"
        SortOrder.SCORE_ASC -> "分数低的面试记录在前"
        SortOrder.POSITION_NAME -> "按岗位名称字母顺序排列"
    }
}