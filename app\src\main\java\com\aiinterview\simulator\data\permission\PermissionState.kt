package com.aiinterview.simulator.data.permission

/**
 * 权限状态枚举
 * 表示权限的不同状态
 */
enum class PermissionStatus {
    GRANTED,        // 已授权
    DENIED,         // 被拒绝
    PERMANENTLY_DENIED, // 永久拒绝（用户选择了"不再询问"）
    NOT_REQUESTED   // 尚未请求
}

/**
 * 权限结果数据类
 * 封装权限请求的结果信息
 */
data class PermissionResult(
    val permission: String,     // 权限名称
    val status: PermissionStatus, // 权限状态
    val shouldShowRationale: Boolean = false // 是否应该显示权限说明
)

/**
 * 权限请求结果数据类
 * 封装多个权限请求的结果
 */
data class PermissionRequestResult(
    val requestCode: Int,                           // 请求码
    val results: Map<String, PermissionResult>,     // 权限结果映射
    val allGranted: Boolean                         // 是否所有权限都已授权
) {
    /**
     * 获取被拒绝的权限列表
     * @return 被拒绝的权限名称列表
     */
    fun getDeniedPermissions(): List<String> {
        return results.filter { 
            it.value.status == PermissionStatus.DENIED || 
            it.value.status == PermissionStatus.PERMANENTLY_DENIED 
        }.keys.toList()
    }
    
    /**
     * 获取永久拒绝的权限列表
     * @return 永久拒绝的权限名称列表
     */
    fun getPermanentlyDeniedPermissions(): List<String> {
        return results.filter { 
            it.value.status == PermissionStatus.PERMANENTLY_DENIED 
        }.keys.toList()
    }
    
    /**
     * 获取需要显示说明的权限列表
     * @return 需要显示说明的权限名称列表
     */
    fun getPermissionsNeedingRationale(): List<String> {
        return results.filter { 
            it.value.shouldShowRationale 
        }.keys.toList()
    }
}

/**
 * 权限类型枚举
 * 定义应用中使用的权限类型
 */
enum class PermissionType(
    val permission: String,     // 权限名称
    val title: String,          // 权限标题
    val description: String,    // 权限描述
    val importance: Int         // 重要性等级（1-5，5最重要）
) {
    MICROPHONE(
        permission = android.Manifest.permission.RECORD_AUDIO,
        title = "录音权限",
        description = "用于录制您的面试回答，这是面试功能的核心需求",
        importance = 5
    ),
    
    NETWORK_STATE(
        permission = android.Manifest.permission.ACCESS_NETWORK_STATE,
        title = "网络状态权限",
        description = "用于检查网络连接状态，确保面试数据能够正常上传",
        importance = 4
    ),
    
    INTERNET(
        permission = android.Manifest.permission.INTERNET,
        title = "网络访问权限",
        description = "用于连接服务器，获取面试题目和上传回答数据",
        importance = 5
    ),
    
    STORAGE(
        permission = android.Manifest.permission.WRITE_EXTERNAL_STORAGE,
        title = "存储权限",
        description = "用于保存录音文件和面试记录（仅Android 10以下需要）",
        importance = 3
    );
    
    companion object {
        /**
         * 根据权限名称获取权限类型
         * @param permission 权限名称
         * @return 对应的权限类型，如果未找到则返回null
         */
        fun fromPermission(permission: String): PermissionType? {
            return values().find { it.permission == permission }
        }
        
        /**
         * 获取所有运行时权限（需要用户授权的权限）
         * @return 运行时权限列表
         */
        fun getRuntimePermissions(): List<PermissionType> {
            return listOf(MICROPHONE, STORAGE)
        }
        
        /**
         * 获取所有必需权限
         * @return 必需权限列表
         */
        fun getRequiredPermissions(): List<PermissionType> {
            return listOf(MICROPHONE, NETWORK_STATE, INTERNET)
        }
    }
}