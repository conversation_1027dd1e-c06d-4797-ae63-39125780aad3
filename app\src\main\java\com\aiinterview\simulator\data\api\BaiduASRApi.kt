package com.aiinterview.simulator.data.api

import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.POST

/**
 * 百度语音识别API接口
 */
interface BaiduASRApi {
    
    @POST("server_api")
    suspend fun recognizeAudio(
        @Header("Content-Type") contentType: String = "audio/pcm;rate=16000",
        @Header("Content-Length") contentLength: String,
        @Body audioData: ByteArray
    ): BaiduASRResponse
}

data class BaiduASRResponse(
    val err_no: Int,
    val err_msg: String,
    val corpus_no: String?,
    val sn: String?,
    val result: List<String>?
)