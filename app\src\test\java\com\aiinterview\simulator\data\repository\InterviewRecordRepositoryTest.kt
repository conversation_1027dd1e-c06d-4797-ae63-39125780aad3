package com.aiinterview.simulator.data.repository

import com.aiinterview.simulator.data.dao.InterviewRecordDao
import com.aiinterview.simulator.data.model.*
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.Json
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.*
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class InterviewRecordRepositoryTest {
    
    @Mock
    private lateinit var mockDao: InterviewRecordDao
    
    private lateinit var repository: InterviewRecordRepository
    private val json = Json { ignoreUnknownKeys = true }
    
    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        repository = InterviewRecordRepository(mockDao, json)
    }
    
    @Test
    fun `saveRecord should insert record to database`() = runTest {
        // Given
        val record = createTestInterviewRecord()
        
        // When
        repository.saveRecord(record)
        
        // Then
        verify(mockDao).insertRecord(any())
    }
    
    @Test
    fun `getRecordById should return correct record`() = runTest {
        // Given
        val recordId = "test_record_id"
        val expectedRecord = createTestInterviewRecordEntity()
        whenever(mockDao.getRecordById(recordId)).thenReturn(expectedRecord)
        
        // When
        val result = repository.getRecordById(recordId)
        
        // Then
        assertNotNull(result)
        assertEquals(expectedRecord.id, result.id)
        assertEquals(expectedRecord.sessionId, result.sessionId)
    }
    
    @Test
    fun `getRecordsByUser should return user records`() = runTest {
        // Given
        val userId = "test_user_id"
        val records = listOf(createTestInterviewRecordEntity())
        whenever(mockDao.getRecordsByUser(userId)).thenReturn(flowOf(records))
        
        // When
        val result = repository.getRecordsByUser(userId)
        
        // Then
        result.collect { recordList ->
            assertEquals(1, recordList.size)
            assertEquals(records[0].id, recordList[0].id)
        }
    }
    
    @Test
    fun `createRecordFromSession should create valid record`() = runTest {
        // Given
        val session = createTestInterviewSession()
        val position = createTestPosition()
        val audioFiles = listOf(createTestAudioFileInfo())
        
        // When
        val result = repository.createRecordFromSession(session, position, audioFiles)
        
        // Then
        assertNotNull(result)
        assertEquals(session.sessionId, result.sessionId)
        assertEquals(session.userId, result.userId)
        assertEquals(position.name, result.positionName)
        assertEquals(1, result.audioFiles.size)
        verify(mockDao).insertRecord(any())
    }
    
    @Test
    fun `updateSyncStatus should update record sync status`() = runTest {
        // Given
        val recordId = "test_record_id"
        val isSynced = true
        
        // When
        repository.updateSyncStatus(recordId, isSynced)
        
        // Then
        verify(mockDao).updateSyncStatus(
            eq(recordId),
            eq(isSynced),
            any(),
            any()
        )
    }
    
    @Test
    fun `deleteRecord should remove record from database`() = runTest {
        // Given
        val recordId = "test_record_id"
        
        // When
        repository.deleteRecord(recordId)
        
        // Then
        verify(mockDao).deleteRecordById(recordId)
    }
    
    @Test
    fun `searchRecords should return matching records`() = runTest {
        // Given - 准备测试数据
        val userId = "test_user_id" // 测试用户ID
        val keyword = "测试岗位" // 搜索关键词
        val matchingRecords = listOf(createTestInterviewRecordEntity()) // 匹配的记录列表
        whenever(mockDao.searchRecords(userId, keyword)).thenReturn(matchingRecords) // 模拟DAO返回结果
        
        // When - 执行搜索操作
        val result = repository.searchRecords(userId, keyword)
        
        // Then - 验证结果
        assertEquals(1, result.size) // 验证返回记录数量
        assertTrue(result[0].positionName.contains("测试")) // 验证记录内容匹配
    }
    
    @Test
    fun `filterRecords should return filtered records by category`() = runTest {
        // Given - 准备按类别筛选的测试数据
        val userId = "test_user_id" // 测试用户ID
        val category = "技术类" // 筛选类别
        val filteredRecords = listOf(createTestInterviewRecordEntity()) // 筛选结果
        whenever(mockDao.filterRecords(userId, category, null, null, null)).thenReturn(filteredRecords) // 模拟DAO筛选结果
        
        // When - 执行筛选操作
        val result = repository.filterRecords(userId, category = category)
        
        // Then - 验证筛选结果
        assertEquals(1, result.size) // 验证返回记录数量
        assertEquals(category, result[0].positionCategory) // 验证类别匹配
    }
    
    @Test
    fun `filterRecords should return filtered records by time range`() = runTest {
        // Given - 准备按时间范围筛选的测试数据
        val userId = "test_user_id" // 测试用户ID
        val startTime = System.currentTimeMillis() - 86400000 // 24小时前
        val endTime = System.currentTimeMillis() // 当前时间
        val filteredRecords = listOf(createTestInterviewRecordEntity()) // 筛选结果
        whenever(mockDao.filterRecords(userId, null, startTime, endTime, null)).thenReturn(filteredRecords) // 模拟DAO筛选结果
        
        // When - 执行时间范围筛选
        val result = repository.filterRecords(userId, startTime = startTime, endTime = endTime)
        
        // Then - 验证筛选结果
        assertEquals(1, result.size) // 验证返回记录数量
        assertTrue(result[0].startTime >= startTime) // 验证时间范围
        assertTrue(result[0].startTime <= endTime) // 验证时间范围
    }
    
    @Test
    fun `filterRecords should return filtered records by status`() = runTest {
        // Given - 准备按状态筛选的测试数据
        val userId = "test_user_id" // 测试用户ID
        val status = InterviewRecordStatus.COMPLETED // 筛选状态
        val filteredRecords = listOf(createTestInterviewRecordEntity()) // 筛选结果
        whenever(mockDao.filterRecords(userId, null, null, null, status.name)).thenReturn(filteredRecords) // 模拟DAO筛选结果
        
        // When - 执行状态筛选
        val result = repository.filterRecords(userId, status = status)
        
        // Then - 验证筛选结果
        assertEquals(1, result.size) // 验证返回记录数量
        assertEquals(status, result[0].status) // 验证状态匹配
    }
    
    @Test
    fun `searchAndFilterRecords should return records matching all criteria`() = runTest {
        // Given - 准备组合搜索和筛选的测试数据
        val userId = "test_user_id" // 测试用户ID
        val keyword = "测试" // 搜索关键词
        val category = "技术类" // 筛选类别
        val status = InterviewRecordStatus.COMPLETED // 筛选状态
        val matchingRecords = listOf(createTestInterviewRecordEntity()) // 匹配结果
        whenever(mockDao.searchAndFilterRecords(userId, keyword, category, null, null, status.name)).thenReturn(matchingRecords) // 模拟DAO组合查询结果
        
        // When - 执行组合搜索和筛选
        val result = repository.searchAndFilterRecords(
            userId = userId,
            keyword = keyword,
            category = category,
            status = status
        )
        
        // Then - 验证组合查询结果
        assertEquals(1, result.size) // 验证返回记录数量
        assertTrue(result[0].positionName.contains("测试")) // 验证关键词匹配
        assertEquals(category, result[0].positionCategory) // 验证类别匹配
        assertEquals(status, result[0].status) // 验证状态匹配
    }
    
    @Test
    fun `getRecordsByTimeRange should return records within time range`() = runTest {
        // Given - 准备时间范围查询的测试数据
        val userId = "test_user_id" // 测试用户ID
        val startTime = System.currentTimeMillis() - 86400000 // 24小时前
        val endTime = System.currentTimeMillis() // 当前时间
        val recordsInRange = listOf(createTestInterviewRecordEntity()) // 时间范围内的记录
        whenever(mockDao.getRecordsByTimeRange(userId, startTime, endTime)).thenReturn(recordsInRange) // 模拟DAO时间范围查询
        
        // When - 执行时间范围查询
        val result = repository.getRecordsByTimeRange(userId, startTime, endTime)
        
        // Then - 验证时间范围查询结果
        assertEquals(1, result.size) // 验证返回记录数量
        assertTrue(result[0].startTime >= startTime) // 验证开始时间
        assertTrue(result[0].startTime <= endTime) // 验证结束时间
    }
    
    @Test
    fun `getRecordsByCategory should return records of specific category`() = runTest {
        // Given - 准备类别查询的测试数据
        val userId = "test_user_id" // 测试用户ID
        val category = "技术类" // 查询类别
        val categoryRecords = listOf(createTestInterviewRecordEntity()) // 该类别的记录
        whenever(mockDao.getRecordsByCategory(userId, category)).thenReturn(categoryRecords) // 模拟DAO类别查询
        
        // When - 执行类别查询
        val result = repository.getRecordsByCategory(userId, category)
        
        // Then - 验证类别查询结果
        assertEquals(1, result.size) // 验证返回记录数量
        assertEquals(category, result[0].positionCategory) // 验证类别匹配
    }
    
    // 测试辅助方法
    
    private fun createTestInterviewRecord(): InterviewRecordModel {
        return InterviewRecordModel(
            id = "test_record_id",
            sessionId = "test_session_id",
            userId = "test_user_id",
            positionId = "test_position_id",
            positionName = "测试岗位",
            positionCategory = "技术类",
            startTime = System.currentTimeMillis() - 3600000,
            endTime = System.currentTimeMillis(),
            duration = 3600,
            status = InterviewRecordStatus.COMPLETED,
            questions = listOf(createTestQuestion()),
            answers = listOf(createTestAnswerWithAudio()),
            audioFiles = listOf(createTestAudioFileInfo()),
            evaluationId = "test_evaluation_id",
            overallScore = 85.5,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis(),
            isSynced = false,
            syncedAt = null
        )
    }
    
    private fun createTestInterviewRecordEntity(): InterviewRecord {
        return InterviewRecord(
            id = "test_record_id",
            sessionId = "test_session_id",
            userId = "test_user_id",
            positionId = "test_position_id",
            positionName = "测试岗位",
            positionCategory = "技术类",
            startTime = System.currentTimeMillis() - 3600000,
            endTime = System.currentTimeMillis(),
            duration = 3600,
            status = "COMPLETED",
            questionsJson = "[]",
            answersJson = "[]",
            audioFilesJson = "[]",
            evaluationId = "test_evaluation_id",
            overallScore = 85.5,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis(),
            isSynced = false,
            syncedAt = null
        )
    }
    
    private fun createTestInterviewSession(): InterviewSession {
        return InterviewSession(
            id = "test_session_id",
            userId = "test_user_id",
            positionId = "test_position_id",
            status = InterviewStatus.COMPLETED,
            startTime = System.currentTimeMillis() - 3600000,
            endTime = System.currentTimeMillis(),
            currentQuestionIndex = 1,
            questions = listOf(createTestQuestion()),
            answers = listOf(createTestAnswer()),
            evaluation = createTestEvaluation()
        )
    }
    
    private fun createTestPosition(): Position {
        return Position(
            id = "test_position_id",
            name = "测试岗位",
            category = "技术类",
            level = "中级",
            description = "测试岗位描述",
            duration = 60,
            questionCount = 3,
            questionTypes = "[\"COMPREHENSIVE_ANALYSIS\"]",
            timeWarnings = "[5, 1]"
        )
    }
    
    private fun createTestQuestion(): Question {
        return Question(
            id = "test_question_id",
            type = QuestionType.COMPREHENSIVE_ANALYSIS,
            category = "综合分析",
            title = "测试问题",
            content = "这是一个测试问题的内容",
            timeLimit = 300,
            difficulty = 3
        )
    }
    
    private fun createTestAnswer(): Answer {
        return Answer(
            questionId = "test_question_id",
            audioUrl = "/path/to/audio.mp3",
            transcription = "这是测试回答的文本内容",
            duration = 120,
            submittedAt = System.currentTimeMillis()
        )
    }
    
    private fun createTestAnswerWithAudio(): AnswerWithAudio {
        return AnswerWithAudio(
            questionId = "test_question_id",
            questionContent = "这是一个测试问题的内容",
            audioFilePath = "/path/to/audio.mp3",
            audioUrl = "https://example.com/audio.mp3",
            transcription = "这是测试回答的文本内容",
            duration = 120,
            submittedAt = System.currentTimeMillis(),
            audioFileSize = 1024000
        )
    }
    
    private fun createTestAudioFileInfo(): AudioFileInfo {
        return AudioFileInfo(
            filePath = "/path/to/audio.mp3",
            fileName = "test_audio.mp3",
            fileSize = 1024000,
            duration = 120,
            questionId = "test_question_id",
            createdAt = System.currentTimeMillis(),
            isUploaded = false,
            uploadUrl = null
        )
    }
    
    private fun createTestEvaluation(): InterviewEvaluation {
        return InterviewEvaluation(
            sessionId = "test_session_id",
            overallScore = 85.5,
            dimensions = mapOf(
                "逻辑性" to DimensionScore(80.0, 100.0, "逻辑清晰"),
                "流畅度" to DimensionScore(90.0, 100.0, "表达流畅")
            ),
            feedback = "整体表现良好",
            suggestions = listOf("可以进一步提升逻辑性"),
            createdAt = System.currentTimeMillis()
        )
    }
}