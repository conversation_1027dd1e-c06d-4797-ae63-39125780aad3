package com.aiinterview.simulator.util

import com.aiinterview.simulator.BuildConfig

/**
 * 渠道管理工具类
 * 用于获取当前应用的渠道信息和相关配置
 */
object ChannelUtil {
    
    /**
     * 获取当前渠道名称
     * @return 渠道名称字符串
     */
    fun getCurrentChannel(): String {
        return BuildConfig.CHANNEL
    }
    
    /**
     * 获取当前构建类型
     * @return 构建类型字符串（debug/release/staging）
     */
    fun getBuildType(): String {
        return BuildConfig.BUILD_TYPE
    }
    
    /**
     * 是否启用日志输出
     * @return true表示启用日志，false表示禁用
     */
    fun isLoggingEnabled(): Boolean {
        return BuildConfig.ENABLE_LOGGING
    }
    
    /**
     * 获取版本信息字符串
     * @return 格式化的版本信息
     */
    fun getVersionInfo(): String {
        return "v${BuildConfig.VERSION_NAME} (${BuildConfig.VERSION_CODE})"
    }
    
    /**
     * 获取完整的应用信息
     * @return 包含渠道、版本、构建类型的完整信息
     */
    fun getAppInfo(): String {
        return "AI面试模拟器 ${getVersionInfo()} - ${getCurrentChannel()}渠道 (${getBuildType()})"
    }
    
    /**
     * 根据渠道获取对应的应用商店下载链接
     * @return 应用商店下载链接，如果渠道不支持则返回null
     */
    fun getStoreDownloadUrl(): String? {
        return when (getCurrentChannel()) {
            "huawei" -> "https://appgallery.huawei.com/app/C123456789" // 华为应用市场
            "xiaomi" -> "https://app.mi.com/details?id=${BuildConfig.APPLICATION_ID}" // 小米应用商店
            "oppo" -> "https://store.oppo.com/app/en?id=${BuildConfig.APPLICATION_ID}" // OPPO软件商店
            "vivo" -> "https://appstore.vivo.com.cn/details?id=${BuildConfig.APPLICATION_ID}" // vivo应用商店
            "tencent" -> "https://sj.qq.com/myapp/detail.htm?apkName=${BuildConfig.APPLICATION_ID}" // 应用宝
            "qihoo360" -> "https://zhushou.360.cn/detail/index/soft_id/123456" // 360手机助手
            "baidu" -> "https://mobile.baidu.com/item?docid=123456" // 百度手机助手
            else -> null // 官方渠道或其他渠道暂不提供商店链接
        }
    }
    
    /**
     * 获取渠道对应的客服联系方式
     * @return 客服联系信息
     */
    fun getCustomerServiceInfo(): String {
        return when (getCurrentChannel()) {
            "huawei" -> "华为应用市场客服：************"
            "xiaomi" -> "小米应用商店客服：************"
            "oppo" -> "OPPO软件商店客服：************"
            "vivo" -> "vivo应用商店客服：************"
            "tencent" -> "应用宝客服：0755-83765566"
            "qihoo360" -> "360手机助手客服：400-6822-360"
            "baidu" -> "百度手机助手客服：************"
            else -> "官方客服：<EMAIL>"
        }
    }
    
    /**
     * 检查是否为调试版本
     * @return true表示调试版本，false表示发布版本
     */
    fun isDebugBuild(): Boolean {
        return BuildConfig.DEBUG
    }
    
    /**
     * 检查是否为预发布版本
     * @return true表示预发布版本（staging），false表示正式版本
     */
    fun isStagingBuild(): Boolean {
        return getBuildType() == "staging"
    }
    
    /**
     * 获取渠道显示名称（中文）
     * @return 渠道的中文显示名称
     */
    fun getChannelDisplayName(): String {
        return when (getCurrentChannel()) {
            "official" -> "官方版本"
            "huawei" -> "华为应用市场"
            "xiaomi" -> "小米应用商店"
            "oppo" -> "OPPO软件商店"
            "vivo" -> "vivo应用商店"
            "tencent" -> "应用宝"
            "qihoo360" -> "360手机助手"
            "baidu" -> "百度手机助手"
            else -> "未知渠道"
        }
    }
    
    /**
     * 获取渠道特定的统计上报配置
     * @return 统计配置信息
     */
    fun getAnalyticsConfig(): Map<String, String> {
        val config = mutableMapOf<String, String>()
        
        // 基础配置
        config["channel"] = getCurrentChannel()
        config["version"] = BuildConfig.VERSION_NAME
        config["build_type"] = getBuildType()
        
        // 渠道特定配置
        when (getCurrentChannel()) {
            "huawei" -> {
                config["analytics_provider"] = "huawei_analytics"
                config["crash_reporter"] = "huawei_crash"
            }
            "xiaomi" -> {
                config["analytics_provider"] = "xiaomi_analytics"
                config["crash_reporter"] = "xiaomi_crash"
            }
            "tencent" -> {
                config["analytics_provider"] = "tencent_analytics"
                config["crash_reporter"] = "bugly"
            }
            else -> {
                config["analytics_provider"] = "custom_analytics"
                config["crash_reporter"] = "custom_crash"
            }
        }
        
        return config
    }
}