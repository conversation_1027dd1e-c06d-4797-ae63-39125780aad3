package com.aiinterview.simulator.data.offline

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 离线模式管理器
 * 负责监控网络状态并提供离线模式和降级处理功能
 */
@Singleton
class OfflineModeManager @Inject constructor(
    private val context: Context // 注入应用上下文
) {
    
    // 连接管理器
    private val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    
    // 网络状态流
    private val _networkState = MutableStateFlow(NetworkState.UNKNOWN)
    val networkState: StateFlow<NetworkState> = _networkState.asStateFlow()
    
    // 离线模式状态流
    private val _isOfflineMode = MutableStateFlow(false)
    val isOfflineMode: StateFlow<Boolean> = _isOfflineMode.asStateFlow()
    
    // 网络质量状态流
    private val _networkQuality = MutableStateFlow(NetworkQuality.UNKNOWN)
    val networkQuality: StateFlow<NetworkQuality> = _networkQuality.asStateFlow()
    
    // 网络回调
    private val networkCallback = object : ConnectivityManager.NetworkCallback() {
        
        /**
         * 网络可用时的回调
         * @param network 可用的网络
         */
        override fun onAvailable(network: Network) {
            super.onAvailable(network)
            updateNetworkState(NetworkState.CONNECTED) // 更新网络状态为已连接
            _isOfflineMode.value = false // 退出离线模式
            evaluateNetworkQuality(network) // 评估网络质量
        }
        
        /**
         * 网络丢失时的回调
         * @param network 丢失的网络
         */
        override fun onLost(network: Network) {
            super.onLost(network)
            updateNetworkState(NetworkState.DISCONNECTED) // 更新网络状态为已断开
            _isOfflineMode.value = true // 进入离线模式
            _networkQuality.value = NetworkQuality.OFFLINE // 设置网络质量为离线
        }
        
        /**
         * 网络能力改变时的回调
         * @param network 网络对象
         * @param networkCapabilities 网络能力
         */
        override fun onCapabilitiesChanged(network: Network, networkCapabilities: NetworkCapabilities) {
            super.onCapabilitiesChanged(network, networkCapabilities)
            evaluateNetworkQuality(network, networkCapabilities) // 重新评估网络质量
        }
        
        /**
         * 网络不可用时的回调
         */
        override fun onUnavailable() {
            super.onUnavailable()
            updateNetworkState(NetworkState.UNAVAILABLE) // 更新网络状态为不可用
            _isOfflineMode.value = true // 进入离线模式
            _networkQuality.value = NetworkQuality.OFFLINE // 设置网络质量为离线
        }
    }
    
    /**
     * 初始化离线模式管理器
     */
    fun initialize() {
        // 注册网络状态监听
        val networkRequest = NetworkRequest.Builder()
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) // 要求具备互联网能力
            .addTransportType(NetworkCapabilities.TRANSPORT_WIFI) // 支持WiFi传输
            .addTransportType(NetworkCapabilities.TRANSPORT_CELLULAR) // 支持蜂窝网络传输
            .build()
        
        connectivityManager.registerNetworkCallback(networkRequest, networkCallback) // 注册网络回调
        
        // 初始化网络状态
        checkInitialNetworkState()
    }
    
    /**
     * 检查初始网络状态
     */
    private fun checkInitialNetworkState() {
        val activeNetwork = connectivityManager.activeNetwork // 获取活动网络
        if (activeNetwork != null) {
            val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork) // 获取网络能力
            if (networkCapabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true) {
                updateNetworkState(NetworkState.CONNECTED) // 网络已连接
                _isOfflineMode.value = false // 不在离线模式
                evaluateNetworkQuality(activeNetwork, networkCapabilities) // 评估网络质量
            } else {
                updateNetworkState(NetworkState.DISCONNECTED) // 网络已断开
                _isOfflineMode.value = true // 进入离线模式
                _networkQuality.value = NetworkQuality.OFFLINE // 设置为离线
            }
        } else {
            updateNetworkState(NetworkState.DISCONNECTED) // 网络已断开
            _isOfflineMode.value = true // 进入离线模式
            _networkQuality.value = NetworkQuality.OFFLINE // 设置为离线
        }
    }
    
    /**
     * 更新网络状态
     * @param newState 新的网络状态
     */
    private fun updateNetworkState(newState: NetworkState) {
        val oldState = _networkState.value // 获取旧状态
        _networkState.value = newState // 设置新状态
        
        // 如果状态发生变化，可以在这里执行相应的操作
        if (oldState != newState) {
            onNetworkStateChanged(oldState, newState) // 处理网络状态变化
        }
    }
    
    /**
     * 评估网络质量
     * @param network 网络对象
     * @param networkCapabilities 网络能力（可选）
     */
    private fun evaluateNetworkQuality(network: Network, networkCapabilities: NetworkCapabilities? = null) {
        val capabilities = networkCapabilities ?: connectivityManager.getNetworkCapabilities(network)
        
        if (capabilities == null) {
            _networkQuality.value = NetworkQuality.POOR // 无法获取能力，设为差
            return
        }
        
        val quality = when {
            // WiFi连接通常质量较好
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> {
                if (capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)) {
                    NetworkQuality.EXCELLENT // WiFi已验证，质量优秀
                } else {
                    NetworkQuality.GOOD // WiFi未验证，质量良好
                }
            }
            
            // 蜂窝网络根据类型判断质量
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> {
                when {
                    capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED) -> NetworkQuality.GOOD // 蜂窝网络已验证
                    capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) -> NetworkQuality.FAIR // 有互联网能力
                    else -> NetworkQuality.POOR // 其他情况质量较差
                }
            }
            
            // 以太网连接质量优秀
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> NetworkQuality.EXCELLENT
            
            // 其他类型网络质量一般
            else -> NetworkQuality.FAIR
        }
        
        _networkQuality.value = quality // 更新网络质量
    }
    
    /**
     * 处理网络状态变化
     * @param oldState 旧状态
     * @param newState 新状态
     */
    private fun onNetworkStateChanged(oldState: NetworkState, newState: NetworkState) {
        when (newState) {
            NetworkState.CONNECTED -> {
                // 网络连接时的处理
                onNetworkConnected()
            }
            NetworkState.DISCONNECTED -> {
                // 网络断开时的处理
                onNetworkDisconnected()
            }
            NetworkState.UNAVAILABLE -> {
                // 网络不可用时的处理
                onNetworkUnavailable()
            }
            NetworkState.UNKNOWN -> {
                // 网络状态未知时的处理
                onNetworkUnknown()
            }
        }
    }
    
    /**
     * 网络连接时的处理
     */
    private fun onNetworkConnected() {
        // 可以在这里执行网络恢复后的操作
        // 例如：同步离线数据、重新发送失败的请求等
    }
    
    /**
     * 网络断开时的处理
     */
    private fun onNetworkDisconnected() {
        // 可以在这里执行网络断开时的操作
        // 例如：保存当前状态、启用离线模式等
    }
    
    /**
     * 网络不可用时的处理
     */
    private fun onNetworkUnavailable() {
        // 可以在这里执行网络不可用时的操作
        // 例如：显示离线提示、禁用网络相关功能等
    }
    
    /**
     * 网络状态未知时的处理
     */
    private fun onNetworkUnknown() {
        // 可以在这里执行网络状态未知时的操作
        // 例如：重新检测网络状态等
    }
    
    /**
     * 检查是否有网络连接
     * @return 是否有网络连接
     */
    fun isNetworkAvailable(): Boolean {
        return _networkState.value == NetworkState.CONNECTED
    }
    
    /**
     * 检查网络质量是否足够好
     * @param minimumQuality 最低质量要求
     * @return 网络质量是否满足要求
     */
    fun isNetworkQualityGood(minimumQuality: NetworkQuality = NetworkQuality.FAIR): Boolean {
        return _networkQuality.value.ordinal >= minimumQuality.ordinal
    }
    
    /**
     * 强制进入离线模式
     */
    fun forceOfflineMode() {
        _isOfflineMode.value = true // 强制进入离线模式
    }
    
    /**
     * 退出强制离线模式
     */
    fun exitForceOfflineMode() {
        if (isNetworkAvailable()) {
            _isOfflineMode.value = false // 如果网络可用，退出离线模式
        }
    }
    
    /**
     * 获取网络类型描述
     * @return 网络类型描述字符串
     */
    fun getNetworkTypeDescription(): String {
        val activeNetwork = connectivityManager.activeNetwork ?: return "无网络连接"
        val capabilities = connectivityManager.getNetworkCapabilities(activeNetwork) ?: return "未知网络"
        
        return when {
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> "WiFi"
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> "移动网络"
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> "以太网"
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_BLUETOOTH) -> "蓝牙"
            else -> "其他网络"
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            connectivityManager.unregisterNetworkCallback(networkCallback) // 注销网络回调
        } catch (e: Exception) {
            e.printStackTrace() // 打印注销回调时的异常
        }
    }
}

/**
 * 网络状态枚举
 */
enum class NetworkState {
    UNKNOWN, // 未知状态
    CONNECTED, // 已连接
    DISCONNECTED, // 已断开
    UNAVAILABLE // 不可用
}

/**
 * 网络质量枚举
 */
enum class NetworkQuality {
    OFFLINE, // 离线
    POOR, // 差
    FAIR, // 一般
    GOOD, // 良好
    EXCELLENT, // 优秀
    UNKNOWN // 未知
}