package com.aiinterview.simulator.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.aiinterview.simulator.data.repository.SpeechRecognitionRepository
import com.aiinterview.simulator.data.speech.SpeechRecognitionService
import com.aiinterview.simulator.data.dto.response.RecognitionResult
import com.aiinterview.simulator.domain.util.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.io.File
import javax.inject.Inject

@HiltViewModel
class SpeechRecognitionViewModel @Inject constructor(
    private val speechRecognitionRepository: SpeechRecognitionRepository
) : ViewModel() {
    
    private val _recognitionState = MutableStateFlow<Resource<RecognitionResult>?>(null)
    val recognitionState: StateFlow<Resource<RecognitionResult>?> = _recognitionState.asStateFlow()
    
    private val _currentRecognitionResult = MutableStateFlow<RecognitionResult?>(null)
    val currentRecognitionResult: StateFlow<RecognitionResult?> = _currentRecognitionResult.asStateFlow()
    
    private val _isRecognizing = MutableStateFlow(false)
    val isRecognizing: StateFlow<Boolean> = _isRecognizing.asStateFlow()
    
    private val _recognitionHistory = MutableStateFlow<List<RecognitionResult>>(emptyList())
    val recognitionHistory: StateFlow<List<RecognitionResult>> = _recognitionHistory.asStateFlow()
    
    fun recognizeAudio(
        audioFile: File,
        provider: SpeechRecognitionService.Provider = SpeechRecognitionService.Provider.AUTO
    ) {
        viewModelScope.launch {
            _isRecognizing.value = true
            
            speechRecognitionRepository.recognizeAudio(audioFile, provider).collect { result ->
                _recognitionState.value = result
                _isRecognizing.value = false
                
                if (result is Resource.Success) {
                    _currentRecognitionResult.value = result.data
                    
                    // 添加到历史记录
                    val currentHistory = _recognitionHistory.value.toMutableList()
                    currentHistory.add(result.data!!)
                    _recognitionHistory.value = currentHistory
                }
            }
        }
    }
    
    fun retryRecognition(audioFile: File) {
        // 重试时使用不同的提供商
        val currentResult = _currentRecognitionResult.value
        val nextProvider = when (currentResult?.provider) {
            "BAIDU" -> SpeechRecognitionService.Provider.TENCENT
            "TENCENT" -> SpeechRecognitionService.Provider.BAIDU
            else -> SpeechRecognitionService.Provider.AUTO
        }
        
        recognizeAudio(audioFile, nextProvider)
    }
    
    fun validateCurrentResult(): Boolean {
        val result = _currentRecognitionResult.value ?: return false
        return viewModelScope.run {
            speechRecognitionRepository.validateRecognitionQuality(result)
        }
    }
    
    fun getConfidenceDescription(confidence: Double): String {
        return speechRecognitionRepository.getConfidenceDescription(confidence)
    }
    
    fun updateRecognitionText(newText: String) {
        val currentResult = _currentRecognitionResult.value
        if (currentResult != null) {
            val updatedResult = currentResult.copy(
                text = newText,
                confidence = if (newText != currentResult.text) 1.0 else currentResult.confidence // 手动编辑的文本置信度设为1.0
            )
            _currentRecognitionResult.value = updatedResult
        }
    }
    
    fun clearRecognitionResult() {
        _currentRecognitionResult.value = null
        _recognitionState.value = null
    }
    
    fun clearRecognitionHistory() {
        _recognitionHistory.value = emptyList()
    }
    
    fun getRecognitionStatistics(): RecognitionStatistics {
        val history = _recognitionHistory.value
        if (history.isEmpty()) {
            return RecognitionStatistics(
                totalRecognitions = 0,
                successfulRecognitions = 0,
                averageConfidence = 0.0,
                mostUsedProvider = "无"
            )
        }
        
        val successful = history.count { it.success }
        val averageConfidence = history.filter { it.success }.map { it.confidence }.average()
        val providerCounts = history.groupBy { it.provider }.mapValues { it.value.size }
        val mostUsedProvider = providerCounts.maxByOrNull { it.value }?.key ?: "无"
        
        return RecognitionStatistics(
            totalRecognitions = history.size,
            successfulRecognitions = successful,
            averageConfidence = if (averageConfidence.isNaN()) 0.0 else averageConfidence,
            mostUsedProvider = mostUsedProvider
        )
    }
    
    fun isRecognitionResultGood(): Boolean {
        val result = _currentRecognitionResult.value ?: return false
        return result.success && result.confidence >= 0.7 && result.text.isNotBlank()
    }
    
    fun getRecognitionQualityMessage(): String {
        val result = _currentRecognitionResult.value ?: return "暂无识别结果"
        
        return when {
            !result.success -> "识别失败：${result.errorMessage ?: "未知错误"}"
            result.text.isBlank() -> "未识别到有效内容"
            result.confidence < 0.6 -> "识别质量较低，建议重新录制或手动编辑"
            result.confidence < 0.8 -> "识别质量一般，建议检查后使用"
            else -> "识别质量良好"
        }
    }
}

data class RecognitionStatistics(
    val totalRecognitions: Int,
    val successfulRecognitions: Int,
    val averageConfidence: Double,
    val mostUsedProvider: String
)