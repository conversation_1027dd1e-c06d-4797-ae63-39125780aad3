{"logs": [{"outputFile": "com.aiinterview.simulator.app-mergeBaiduDebugResources-55:/values-ur/values-ur.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\73f51c4b00c3d8b5bd0709c291d17c64\\transformed\\material3-1.1.2\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,287,399,513,591,684,796,927,1046,1181,1262,1362,1454,1550,1663,1789,1894,2034,2173,2301,2495,2619,2734,2854,2989,3082,3173,3293,3413,3510,3611,3713,3853,4000,4102,4201,4273,4352,4438,4525,4636,4722,4803,4902,5004,5097,5196,5277,5380,5475,5573,5709,5795,5893", "endColumns": "113,117,111,113,77,92,111,130,118,134,80,99,91,95,112,125,104,139,138,127,193,123,114,119,134,92,90,119,119,96,100,101,139,146,101,98,71,78,85,86,110,85,80,98,101,92,98,80,102,94,97,135,85,97,89", "endOffsets": "164,282,394,508,586,679,791,922,1041,1176,1257,1357,1449,1545,1658,1784,1889,2029,2168,2296,2490,2614,2729,2849,2984,3077,3168,3288,3408,3505,3606,3708,3848,3995,4097,4196,4268,4347,4433,4520,4631,4717,4798,4897,4999,5092,5191,5272,5375,5470,5568,5704,5790,5888,5978"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,337,449,1461,1539,1632,1744,1875,1994,2129,2210,2310,2402,2498,2611,2737,2842,2982,3121,3249,3443,3567,3682,3802,3937,4030,4121,4241,4361,4458,4559,4661,4801,4948,5050,5336,5496,6225,6386,6574,6943,7029,7110,7209,7311,7404,7503,7584,7687,7782,7880,8016,8102,8200", "endColumns": "113,117,111,113,77,92,111,130,118,134,80,99,91,95,112,125,104,139,138,127,193,123,114,119,134,92,90,119,119,96,100,101,139,146,101,98,71,78,85,86,110,85,80,98,101,92,98,80,102,94,97,135,85,97,89", "endOffsets": "214,332,444,558,1534,1627,1739,1870,1989,2124,2205,2305,2397,2493,2606,2732,2837,2977,3116,3244,3438,3562,3677,3797,3932,4025,4116,4236,4356,4453,4554,4656,4796,4943,5045,5144,5403,5570,6306,6468,6680,7024,7105,7204,7306,7399,7498,7579,7682,7777,7875,8011,8097,8195,8285"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d78fa9dcb2040782f62e575b1696aadd\\transformed\\ui-release\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,282,372,469,557,638,731,819,905,972,1039,1122,1207,1282,1357,1423", "endColumns": "93,82,89,96,87,80,92,87,85,66,66,82,84,74,74,65,116", "endOffsets": "194,277,367,464,552,633,726,814,900,967,1034,1117,1202,1277,1352,1418,1535"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1284,1378,5149,5239,5408,5575,5656,5749,5837,5923,5990,6057,6140,6311,6685,6760,6826", "endColumns": "93,82,89,96,87,80,92,87,85,66,66,82,84,74,74,65,116", "endOffsets": "1373,1456,5234,5331,5491,5651,5744,5832,5918,5985,6052,6135,6220,6381,6755,6821,6938"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fd044ab7a523edb1345beec00f65097\\transformed\\core-1.12.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,357,461,564,662,776", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "148,250,352,456,559,657,771,872"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "563,661,763,865,969,1072,1170,6473", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "656,758,860,964,1067,1165,1279,6569"}}]}]}