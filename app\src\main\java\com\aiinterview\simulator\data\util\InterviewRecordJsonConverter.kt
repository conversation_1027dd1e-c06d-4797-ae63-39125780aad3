package com.aiinterview.simulator.data.util

import com.aiinterview.simulator.data.model.*
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.Serializable

/**
 * 面试记录JSON转换工具类
 * 用于处理复杂数据结构的序列化和反序列化
 */
object InterviewRecordJsonConverter {
    
    private val json = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
    }
    
    /**
     * 将问题列表转换为JSON字符串
     */
    fun questionsToJson(questions: List<Question>): String {
        val serializableQuestions = questions.map { it.toSerializable() }
        return json.encodeToString(serializableQuestions)
    }
    
    /**
     * 从JSON字符串解析问题列表
     */
    fun questionsFromJson(jsonString: String): List<Question> {
        return try {
            val serializableQuestions = json.decodeFromString<List<SerializableQuestion>>(jsonString)
            serializableQuestions.map { it.toQuestion() }
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    /**
     * 将答案列表转换为JSON字符串
     */
    fun answersToJson(answers: List<AnswerWithAudio>): String {
        val serializableAnswers = answers.map { it.toSerializable() }
        return json.encodeToString(serializableAnswers)
    }
    
    /**
     * 从JSON字符串解析答案列表
     */
    fun answersFromJson(jsonString: String): List<AnswerWithAudio> {
        return try {
            val serializableAnswers = json.decodeFromString<List<SerializableAnswerWithAudio>>(jsonString)
            serializableAnswers.map { it.toAnswerWithAudio() }
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    /**
     * 将音频文件信息列表转换为JSON字符串
     */
    fun audioFilesToJson(audioFiles: List<AudioFileInfo>): String {
        val serializableAudioFiles = audioFiles.map { it.toSerializable() }
        return json.encodeToString(serializableAudioFiles)
    }
    
    /**
     * 从JSON字符串解析音频文件信息列表
     */
    fun audioFilesFromJson(jsonString: String): List<AudioFileInfo> {
        return try {
            val serializableAudioFiles = json.decodeFromString<List<SerializableAudioFileInfo>>(jsonString)
            serializableAudioFiles.map { it.toAudioFileInfo() }
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    // 可序列化的数据类定义
    
    @Serializable
    data class SerializableQuestion(
        val id: String,
        val type: String,
        val category: String,
        val title: String,
        val content: String,
        val backgroundInfo: String? = null,
        val keyPoints: List<String> = emptyList(),
        val timeLimit: Int,
        val difficulty: Int
    )
    
    @Serializable
    data class SerializableAnswerWithAudio(
        val questionId: String,
        val questionContent: String,
        val audioFilePath: String,
        val audioUrl: String? = null,
        val transcription: String,
        val duration: Int,
        val submittedAt: Long,
        val audioFileSize: Long = 0L
    )
    
    @Serializable
    data class SerializableAudioFileInfo(
        val filePath: String,
        val fileName: String,
        val fileSize: Long,
        val duration: Int,
        val questionId: String,
        val createdAt: Long,
        val isUploaded: Boolean = false,
        val uploadUrl: String? = null
    )
    
    // 扩展函数：转换为可序列化对象
    
    private fun Question.toSerializable(): SerializableQuestion {
        return SerializableQuestion(
            id = id,
            type = type.name,
            category = category,
            title = title,
            content = content,
            backgroundInfo = backgroundInfo,
            keyPoints = keyPoints,
            timeLimit = timeLimit,
            difficulty = difficulty
        )
    }
    
    private fun AnswerWithAudio.toSerializable(): SerializableAnswerWithAudio {
        return SerializableAnswerWithAudio(
            questionId = questionId,
            questionContent = questionContent,
            audioFilePath = audioFilePath,
            audioUrl = audioUrl,
            transcription = transcription,
            duration = duration,
            submittedAt = submittedAt,
            audioFileSize = audioFileSize
        )
    }
    
    private fun AudioFileInfo.toSerializable(): SerializableAudioFileInfo {
        return SerializableAudioFileInfo(
            filePath = filePath,
            fileName = fileName,
            fileSize = fileSize,
            duration = duration,
            questionId = questionId,
            createdAt = createdAt,
            isUploaded = isUploaded,
            uploadUrl = uploadUrl
        )
    }
    
    // 扩展函数：从可序列化对象转换
    
    private fun SerializableQuestion.toQuestion(): Question {
        return Question(
            id = id,
            type = QuestionType.valueOf(type),
            category = category,
            title = title,
            content = content,
            backgroundInfo = backgroundInfo,
            keyPoints = keyPoints,
            timeLimit = timeLimit,
            difficulty = difficulty
        )
    }
    
    private fun SerializableAnswerWithAudio.toAnswerWithAudio(): AnswerWithAudio {
        return AnswerWithAudio(
            questionId = questionId,
            questionContent = questionContent,
            audioFilePath = audioFilePath,
            audioUrl = audioUrl,
            transcription = transcription,
            duration = duration,
            submittedAt = submittedAt,
            audioFileSize = audioFileSize
        )
    }
    
    private fun SerializableAudioFileInfo.toAudioFileInfo(): AudioFileInfo {
        return AudioFileInfo(
            filePath = filePath,
            fileName = fileName,
            fileSize = fileSize,
            duration = duration,
            questionId = questionId,
            createdAt = createdAt,
            isUploaded = isUploaded,
            uploadUrl = uploadUrl
        )
    }
}