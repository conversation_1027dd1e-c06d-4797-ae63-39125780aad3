package com.aiinterview.simulator.data.dao

import androidx.room.*
import com.aiinterview.simulator.data.model.InterviewSession
import kotlinx.coroutines.flow.Flow

@Dao
interface InterviewSessionDao {
    @Query("SELECT * FROM interview_sessions WHERE userId = :userId ORDER BY startTime DESC")
    fun getSessionsByUser(userId: String): Flow<List<InterviewSession>>
    
    @Query("SELECT * FROM interview_sessions WHERE id = :sessionId")
    suspend fun getSessionById(sessionId: String): InterviewSession?
    
    @Query("SELECT * FROM interview_sessions WHERE userId = :userId AND status = :status")
    fun getSessionsByUserAndStatus(userId: String, status: String): Flow<List<InterviewSession>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSession(session: InterviewSession)
    
    @Update
    suspend fun updateSession(session: InterviewSession)
    
    @Delete
    suspend fun deleteSession(session: InterviewSession)
}