package com.aiinterview.simulator.data.update

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.Log
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import com.aiinterview.simulator.data.local.dataStore
import com.aiinterview.simulator.util.ChannelUtil
import com.aiinterview.simulator.util.VersionManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 应用更新管理器
 * 负责检查应用更新、下载更新包、管理更新流程
 */
@Singleton
class AppUpdateManager @Inject constructor(
    private val context: Context,
    private val updateApi: AppUpdateApi,
    private val versionManager: VersionManager
) {
    
    companion object {
        private const val TAG = "AppUpdateManager"
        
        // DataStore键名
        private val LAST_CHECK_TIME_KEY = stringPreferencesKey("last_update_check_time")
        private val IGNORED_VERSION_KEY = stringPreferencesKey("ignored_version")
        private val UPDATE_REMINDER_COUNT_KEY = stringPreferencesKey("update_reminder_count")
        
        // 更新检查间隔（24小时）
        private const val UPDATE_CHECK_INTERVAL = 24 * 60 * 60 * 1000L
        
        // 最大提醒次数
        private const val MAX_REMINDER_COUNT = 3
    }
    
    /**
     * 检查应用更新
     * @param forceCheck 是否强制检查（忽略时间间隔）
     * @return 更新检查结果
     */
    suspend fun checkForUpdate(forceCheck: Boolean = false): UpdateCheckResult {
        return try {
            // 检查是否需要进行更新检查
            if (!forceCheck && !shouldCheckForUpdate()) {
                return UpdateCheckResult.NoNeedToCheck
            }
            
            // 记录检查时间
            recordCheckTime()
            
            // 获取当前版本信息
            val currentVersion = versionManager.getCurrentVersionName()
            val currentChannel = ChannelUtil.getCurrentChannel()
            
            Log.d(TAG, "检查更新：当前版本=$currentVersion, 渠道=$currentChannel")
            
            // 调用API检查更新
            val updateInfo = updateApi.checkUpdate(
                packageName = context.packageName,
                currentVersion = currentVersion,
                channel = currentChannel
            )
            
            // 处理更新检查结果
            when {
                updateInfo.hasUpdate -> {
                    val ignoredVersion = getIgnoredVersion()
                    
                    // 检查是否为用户忽略的版本
                    if (updateInfo.latestVersion == ignoredVersion && !updateInfo.isForceUpdate) {
                        UpdateCheckResult.IgnoredVersion(updateInfo)
                    } else {
                        UpdateCheckResult.UpdateAvailable(updateInfo)
                    }
                }
                else -> UpdateCheckResult.NoUpdate
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "检查更新失败", e)
            UpdateCheckResult.CheckFailed(e.message ?: "未知错误")
        }
    }
    
    /**
     * 启动应用更新
     * @param updateInfo 更新信息
     */
    fun startUpdate(updateInfo: UpdateInfo) {
        try {
            val downloadUrl = updateInfo.downloadUrl
            val storeUrl = ChannelUtil.getStoreDownloadUrl()
            
            when {
                // 优先使用应用商店链接
                !storeUrl.isNullOrEmpty() -> {
                    openAppStore(storeUrl)
                }
                // 使用直接下载链接
                !downloadUrl.isNullOrEmpty() -> {
                    downloadUpdate(downloadUrl)
                }
                // 都没有则尝试打开默认应用商店
                else -> {
                    openDefaultAppStore()
                }
            }
            
            Log.d(TAG, "启动更新：${updateInfo.latestVersion}")
            
        } catch (e: Exception) {
            Log.e(TAG, "启动更新失败", e)
        }
    }
    
    /**
     * 忽略指定版本的更新
     * @param version 要忽略的版本号
     */
    suspend fun ignoreVersion(version: String) {
        context.dataStore.edit { preferences ->
            preferences[IGNORED_VERSION_KEY] = version
        }
        Log.d(TAG, "忽略版本更新：$version")
    }
    
    /**
     * 增加更新提醒次数
     */
    suspend fun incrementReminderCount() {
        val currentCount = getReminderCount()
        context.dataStore.edit { preferences ->
            preferences[UPDATE_REMINDER_COUNT_KEY] = (currentCount + 1).toString()
        }
    }
    
    /**
     * 重置更新提醒次数
     */
    suspend fun resetReminderCount() {
        context.dataStore.edit { preferences ->
            preferences.remove(UPDATE_REMINDER_COUNT_KEY)
        }
    }
    
    /**
     * 获取更新提醒次数
     */
    private suspend fun getReminderCount(): Int {
        return context.dataStore.data.map { preferences ->
            preferences[UPDATE_REMINDER_COUNT_KEY]?.toIntOrNull() ?: 0
        }.first()
    }
    
    /**
     * 检查是否应该显示更新提醒
     */
    suspend fun shouldShowUpdateReminder(): Boolean {
        return getReminderCount() < MAX_REMINDER_COUNT
    }
    
    /**
     * 打开应用商店
     */
    private fun openAppStore(storeUrl: String) {
        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(storeUrl)).apply {
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
        
        if (intent.resolveActivity(context.packageManager) != null) {
            context.startActivity(intent)
        } else {
            // 如果无法打开指定商店，尝试打开默认商店
            openDefaultAppStore()
        }
    }
    
    /**
     * 打开默认应用商店
     */
    private fun openDefaultAppStore() {
        val intent = Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=${context.packageName}")).apply {
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
        
        if (intent.resolveActivity(context.packageManager) != null) {
            context.startActivity(intent)
        } else {
            // 如果没有应用商店，打开浏览器
            val webIntent = Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id=${context.packageName}")).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(webIntent)
        }
    }
    
    /**
     * 下载更新包
     */
    private fun downloadUpdate(downloadUrl: String) {
        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(downloadUrl)).apply {
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
        context.startActivity(intent)
    }
    
    /**
     * 检查是否需要进行更新检查
     */
    private suspend fun shouldCheckForUpdate(): Boolean {
        val lastCheckTime = context.dataStore.data.map { preferences ->
            preferences[LAST_CHECK_TIME_KEY]?.toLongOrNull() ?: 0L
        }.first()
        
        val currentTime = System.currentTimeMillis()
        return (currentTime - lastCheckTime) > UPDATE_CHECK_INTERVAL
    }
    
    /**
     * 记录更新检查时间
     */
    private suspend fun recordCheckTime() {
        context.dataStore.edit { preferences ->
            preferences[LAST_CHECK_TIME_KEY] = System.currentTimeMillis().toString()
        }
    }
    
    /**
     * 获取被忽略的版本号
     */
    private suspend fun getIgnoredVersion(): String? {
        return context.dataStore.data.map { preferences ->
            preferences[IGNORED_VERSION_KEY]
        }.first()
    }
    
    /**
     * 获取更新检查状态流
     */
    fun getUpdateCheckStatus(): Flow<UpdateCheckStatus> {
        return context.dataStore.data.map { preferences ->
            val lastCheckTime = preferences[LAST_CHECK_TIME_KEY]?.toLongOrNull() ?: 0L
            val ignoredVersion = preferences[IGNORED_VERSION_KEY]
            val reminderCount = preferences[UPDATE_REMINDER_COUNT_KEY]?.toIntOrNull() ?: 0
            
            UpdateCheckStatus(
                lastCheckTime = lastCheckTime,
                ignoredVersion = ignoredVersion,
                reminderCount = reminderCount,
                canShowReminder = reminderCount < MAX_REMINDER_COUNT
            )
        }
    }
}

/**
 * 更新检查结果密封类
 */
sealed class UpdateCheckResult {
    object NoNeedToCheck : UpdateCheckResult()
    object NoUpdate : UpdateCheckResult()
    data class UpdateAvailable(val updateInfo: UpdateInfo) : UpdateCheckResult()
    data class IgnoredVersion(val updateInfo: UpdateInfo) : UpdateCheckResult()
    data class CheckFailed(val error: String) : UpdateCheckResult()
}

/**
 * 更新信息数据类
 */
data class UpdateInfo(
    val latestVersion: String,          // 最新版本号
    val versionCode: Int,               // 最新版本代码
    val updateDescription: String,       // 更新描述
    val downloadUrl: String?,           // 下载链接
    val fileSize: Long,                 // 文件大小（字节）
    val isForceUpdate: Boolean,         // 是否强制更新
    val minSupportedVersion: String,    // 最低支持版本
    val releaseDate: String,            // 发布日期
    val hasUpdate: Boolean              // 是否有更新
)

/**
 * 更新检查状态数据类
 */
data class UpdateCheckStatus(
    val lastCheckTime: Long,
    val ignoredVersion: String?,
    val reminderCount: Int,
    val canShowReminder: Boolean
)