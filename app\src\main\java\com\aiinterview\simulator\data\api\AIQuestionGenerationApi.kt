package com.aiinterview.simulator.data.api

import com.aiinterview.simulator.data.dto.request.QuestionGenerationRequest
import com.aiinterview.simulator.data.dto.response.ApiResponse
import com.aiinterview.simulator.data.dto.response.QuestionGenerationResponse
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.POST

/**
 * AI问题生成API接口
 * 支持文心一言和通义千问API
 */
interface AIQuestionGenerationApi {
    
    /**
     * 文心一言API - 生成面试问题
     */
    @POST("rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions")
    suspend fun generateQuestionWithWenxin(
        @Header("Authorization") authorization: String,
        @Body request: QuestionGenerationRequest
    ): ApiResponse<QuestionGenerationResponse>
    
    /**
     * 通义千问API - 生成面试问题
     */
    @POST("api/v1/services/aigc/text-generation/generation")
    suspend fun generateQuestionWithTongyi(
        @Header("Authorization") authorization: String,
        @Header("Content-Type") contentType: String = "application/json",
        @Body request: QuestionGenerationRequest
    ): ApiResponse<QuestionGenerationResponse>
}