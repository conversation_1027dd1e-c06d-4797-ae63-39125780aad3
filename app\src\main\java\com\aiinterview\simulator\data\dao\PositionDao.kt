package com.aiinterview.simulator.data.dao

import androidx.room.*
import com.aiinterview.simulator.data.model.Position
import kotlinx.coroutines.flow.Flow

@Dao
interface PositionDao {
    @Query("SELECT * FROM positions")
    fun getAllPositions(): Flow<List<Position>>
    
    @Query("SELECT * FROM positions WHERE id = :positionId")
    suspend fun getPositionById(positionId: String): Position?
    
    @Query("SELECT * FROM positions WHERE category = :category")
    fun getPositionsByCategory(category: String): Flow<List<Position>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPosition(position: Position)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPositions(positions: List<Position>)
    
    @Update
    suspend fun updatePosition(position: Position)
    
    @Delete
    suspend fun deletePosition(position: Position)
}