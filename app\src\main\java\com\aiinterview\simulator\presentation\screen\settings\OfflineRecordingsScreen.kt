package com.aiinterview.simulator.presentation.screen.settings

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.aiinterview.simulator.data.audio.PendingRecording
import com.aiinterview.simulator.data.audio.RecordingStatus
import com.aiinterview.simulator.presentation.viewmodel.OfflineRecordingViewModel
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OfflineRecordingsScreen(
    onNavigateBack: () -> Unit,
    viewModel: OfflineRecordingViewModel = hiltViewModel()
) {
    val pendingUploads by viewModel.pendingUploads.collectAsState()
    val uploadProgress by viewModel.uploadProgress.collectAsState()
    
    var showDeleteAllDialog by remember { mutableStateOf(false) }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top App Bar
        TopAppBar(
            title = { Text("离线录音管理") },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            },
            actions = {
                // Upload all button
                if (viewModel.getPendingRecordingsCount() > 0) {
                    IconButton(onClick = { viewModel.uploadAllPending() }) {
                        Icon(Icons.Default.CloudUpload, contentDescription = "全部上传")
                    }
                }
                
                // More options
                var showMenu by remember { mutableStateOf(false) }
                IconButton(onClick = { showMenu = true }) {
                    Icon(Icons.Default.MoreVert, contentDescription = "更多选项")
                }
                
                DropdownMenu(
                    expanded = showMenu,
                    onDismissRequest = { showMenu = false }
                ) {
                    DropdownMenuItem(
                        text = { Text("重试失败的上传") },
                        onClick = {
                            showMenu = false
                            viewModel.retryFailedUploads()
                        },
                        leadingIcon = {
                            Icon(Icons.Default.Refresh, contentDescription = null)
                        },
                        enabled = viewModel.getFailedRecordingsCount() > 0
                    )
                    
                    DropdownMenuItem(
                        text = { Text("清除已完成") },
                        onClick = {
                            showMenu = false
                            viewModel.clearCompletedRecordings()
                        },
                        leadingIcon = {
                            Icon(Icons.Default.Clear, contentDescription = null)
                        }
                    )
                    
                    DropdownMenuItem(
                        text = { Text("删除全部") },
                        onClick = {
                            showMenu = false
                            showDeleteAllDialog = true
                        },
                        leadingIcon = {
                            Icon(Icons.Default.Delete, contentDescription = null)
                        }
                    )
                }
            }
        )
        
        // Statistics Card
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "统计信息",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    StatisticItem(
                        label = "等待上传",
                        value = viewModel.getPendingRecordingsCount().toString(),
                        color = MaterialTheme.colorScheme.primary
                    )
                    
                    StatisticItem(
                        label = "上传失败",
                        value = viewModel.getFailedRecordingsCount().toString(),
                        color = MaterialTheme.colorScheme.error
                    )
                    
                    StatisticItem(
                        label = "总大小",
                        value = viewModel.formatFileSize(viewModel.getTotalPendingSize()),
                        color = MaterialTheme.colorScheme.tertiary
                    )
                }
            }
        }
        
        // Recordings List
        if (pendingUploads.isEmpty()) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        Icons.Default.CloudDone,
                        contentDescription = null,
                        modifier = Modifier.size(64.dp),
                        tint = MaterialTheme.colorScheme.outline
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Text(
                        text = "暂无离线录音",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    Text(
                        text = "所有录音都已成功上传",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.outline
                    )
                }
            }
        } else {
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(pendingUploads) { recording ->
                    RecordingItem(
                        recording = recording,
                        progress = uploadProgress[recording.id],
                        onUpload = { viewModel.uploadPendingRecording(recording.id) },
                        onDelete = { viewModel.deleteRecording(recording.id) },
                        getStatusDescription = { viewModel.getStatusDescription(it) },
                        getStatusColor = { viewModel.getStatusColor(it) },
                        formatFileSize = { viewModel.formatFileSize(it) }
                    )
                }
            }
        }
    }
    
    // Delete All Confirmation Dialog
    if (showDeleteAllDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteAllDialog = false },
            title = { Text("确认删除") },
            text = { Text("确定要删除所有离线录音吗？此操作无法撤销。") },
            confirmButton = {
                Button(
                    onClick = {
                        showDeleteAllDialog = false
                        pendingUploads.forEach { recording ->
                            viewModel.deleteRecording(recording.id)
                        }
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text("删除全部")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteAllDialog = false }) {
                    Text("取消")
                }
            }
        )
    }
}

@Composable
fun StatisticItem(
    label: String,
    value: String,
    color: androidx.compose.ui.graphics.Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold,
            color = color
        )
        
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
fun RecordingItem(
    recording: PendingRecording,
    progress: com.aiinterview.simulator.data.audio.UploadProgress?,
    onUpload: () -> Unit,
    onDelete: () -> Unit,
    getStatusDescription: (RecordingStatus) -> String,
    getStatusColor: (RecordingStatus) -> androidx.compose.ui.graphics.Color,
    formatFileSize: (Long) -> String
) {
    var showDeleteDialog by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Header
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = "题目 ${recording.questionId}",
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Text(
                        text = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
                            .format(Date(recording.createdAt)),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                // Status Badge
                Surface(
                    color = getStatusColor(recording.status).copy(alpha = 0.1f),
                    shape = MaterialTheme.shapes.small
                ) {
                    Text(
                        text = getStatusDescription(recording.status),
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                        style = MaterialTheme.typography.labelSmall,
                        color = getStatusColor(recording.status)
                    )
                }
            }
            
            // Progress Bar (if uploading)
            progress?.let { prog ->
                Spacer(modifier = Modifier.height(8.dp))
                
                LinearProgressIndicator(
                    progress = prog.percentage / 100f,
                    modifier = Modifier.fillMaxWidth()
                )
                
                Text(
                    text = prog.message,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }
            
            // Transcription Preview
            if (recording.transcription.isNotBlank()) {
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "回答内容：",
                    style = MaterialTheme.typography.labelSmall,
                    fontWeight = FontWeight.Bold
                )
                
                Text(
                    text = if (recording.transcription.length > 100) {
                        recording.transcription.take(100) + "..."
                    } else {
                        recording.transcription
                    },
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            // Error Message (if failed)
            recording.errorMessage?.let { error ->
                Spacer(modifier = Modifier.height(8.dp))
                
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Text(
                        text = "错误: $error",
                        modifier = Modifier.padding(8.dp),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
            }
            
            // Action Buttons
            Spacer(modifier = Modifier.height(12.dp))
            
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                when (recording.status) {
                    RecordingStatus.PENDING, RecordingStatus.FAILED -> {
                        Button(
                            onClick = onUpload,
                            modifier = Modifier.weight(1f)
                        ) {
                            Icon(
                                Icons.Default.CloudUpload,
                                contentDescription = null,
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(if (recording.status == RecordingStatus.FAILED) "重试上传" else "上传")
                        }
                    }
                    
                    RecordingStatus.UPLOADING -> {
                        Button(
                            onClick = { },
                            enabled = false,
                            modifier = Modifier.weight(1f)
                        ) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(16.dp),
                                strokeWidth = 2.dp
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("上传中...")
                        }
                    }
                    
                    RecordingStatus.COMPLETED -> {
                        Button(
                            onClick = { },
                            enabled = false,
                            modifier = Modifier.weight(1f)
                        ) {
                            Icon(
                                Icons.Default.CheckCircle,
                                contentDescription = null,
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text("已完成")
                        }
                    }
                }
                
                OutlinedButton(
                    onClick = { showDeleteDialog = true }
                ) {
                    Icon(
                        Icons.Default.Delete,
                        contentDescription = "删除",
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
    
    // Delete Confirmation Dialog
    if (showDeleteDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteDialog = false },
            title = { Text("确认删除") },
            text = { Text("确定要删除这个录音吗？此操作无法撤销。") },
            confirmButton = {
                Button(
                    onClick = {
                        showDeleteDialog = false
                        onDelete()
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text("删除")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteDialog = false }) {
                    Text("取消")
                }
            }
        )
    }
}