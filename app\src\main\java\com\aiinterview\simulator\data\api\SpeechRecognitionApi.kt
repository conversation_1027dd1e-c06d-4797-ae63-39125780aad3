package com.aiinterview.simulator.data.api

import com.aiinterview.simulator.data.dto.request.BaiduASRRequest
import com.aiinterview.simulator.data.dto.request.TencentASRRequest
import com.aiinterview.simulator.data.dto.response.BaiduASRResponse
import com.aiinterview.simulator.data.dto.response.TencentASRResponse
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.http.*

interface BaiduSpeechRecognitionApi {
    @POST("server_api")
    @Headers("Content-Type: application/json")
    suspend fun recognizeAudio(@Body request: BaiduASRRequest): BaiduASRResponse
    
    @GET("oauth/2.0/token")
    suspend fun getAccessToken(
        @Query("grant_type") grantType: String = "client_credentials",
        @Query("client_id") clientId: String,
        @Query("client_secret") clientSecret: String
    ): Map<String, Any>
}

interface TencentASRApi {
    @POST("/")
    @Headers("Content-Type: application/json")
    suspend fun recognizeAudio(@Body request: TencentASRRequest): TencentASRResponse
}

// 通用的语音识别API接口
interface SpeechRecognitionApi {
    @Multipart
    @POST("speech/recognize")
    suspend fun recognizeAudioFile(
        @Part audio: MultipartBody.Part,
        @Part("provider") provider: RequestBody,
        @Part("format") format: RequestBody,
        @Part("rate") rate: RequestBody
    ): com.aiinterview.simulator.data.dto.response.SpeechRecognitionResponse
    
    @POST("speech/recognize/base64")
    @Headers("Content-Type: application/json")
    suspend fun recognizeAudioBase64(
        @Body request: com.aiinterview.simulator.data.dto.request.SpeechRecognitionRequest
    ): com.aiinterview.simulator.data.dto.response.SpeechRecognitionResponse
}