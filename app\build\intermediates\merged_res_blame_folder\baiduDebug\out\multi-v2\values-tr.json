{"logs": [{"outputFile": "com.aiinterview.simulator.app-mergeBaiduDebugResources-55:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\73f51c4b00c3d8b5bd0709c291d17c64\\transformed\\material3-1.1.2\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,275,389,500,580,672,782,911,1029,1167,1248,1342,1427,1520,1631,1748,1847,1980,2112,2230,2397,2511,2623,2738,2850,2936,3030,3150,3275,3372,3470,3572,3703,3839,3947,4044,4125,4206,4288,4369,4482,4558,4638,4734,4830,4922,5013,5097,5199,5295,5389,5506,5582,5685", "endColumns": "110,108,113,110,79,91,109,128,117,137,80,93,84,92,110,116,98,132,131,117,166,113,111,114,111,85,93,119,124,96,97,101,130,135,107,96,80,80,81,80,112,75,79,95,95,91,90,83,101,95,93,116,75,102,88", "endOffsets": "161,270,384,495,575,667,777,906,1024,1162,1243,1337,1422,1515,1626,1743,1842,1975,2107,2225,2392,2506,2618,2733,2845,2931,3025,3145,3270,3367,3465,3567,3698,3834,3942,4039,4120,4201,4283,4364,4477,4553,4633,4729,4825,4917,5008,5092,5194,5290,5384,5501,5577,5680,5769"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,439,1440,1520,1612,1722,1851,1969,2107,2188,2282,2367,2460,2571,2688,2787,2920,3052,3170,3337,3451,3563,3678,3790,3876,3970,4090,4215,4312,4410,4512,4643,4779,4887,5179,5344,6082,6235,6417,6794,6870,6950,7046,7142,7234,7325,7409,7511,7607,7701,7818,7894,7997", "endColumns": "110,108,113,110,79,91,109,128,117,137,80,93,84,92,110,116,98,132,131,117,166,113,111,114,111,85,93,119,124,96,97,101,130,135,107,96,80,80,81,80,112,75,79,95,95,91,90,83,101,95,93,116,75,102,88", "endOffsets": "211,320,434,545,1515,1607,1717,1846,1964,2102,2183,2277,2362,2455,2566,2683,2782,2915,3047,3165,3332,3446,3558,3673,3785,3871,3965,4085,4210,4307,4405,4507,4638,4774,4882,4979,5255,5420,6159,6311,6525,6865,6945,7041,7137,7229,7320,7404,7506,7602,7696,7813,7889,7992,8081"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fd044ab7a523edb1345beec00f65097\\transformed\\core-1.12.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "550,647,749,847,944,1046,1152,6316", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "642,744,842,939,1041,1147,1258,6412"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d78fa9dcb2040782f62e575b1696aadd\\transformed\\ui-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,377,477,561,644,744,832,916,984,1050,1130,1218,1289,1367,1435", "endColumns": "92,83,94,99,83,82,99,87,83,67,65,79,87,70,77,67,117", "endOffsets": "193,277,372,472,556,639,739,827,911,979,1045,1125,1213,1284,1362,1430,1548"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1263,1356,4984,5079,5260,5425,5508,5608,5696,5780,5848,5914,5994,6164,6530,6608,6676", "endColumns": "92,83,94,99,83,82,99,87,83,67,65,79,87,70,77,67,117", "endOffsets": "1351,1435,5074,5174,5339,5503,5603,5691,5775,5843,5909,5989,6077,6230,6603,6671,6789"}}]}]}