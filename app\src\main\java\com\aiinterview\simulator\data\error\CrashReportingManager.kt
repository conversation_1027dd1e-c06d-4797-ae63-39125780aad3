package com.aiinterview.simulator.data.error

import android.content.Context
import android.os.Build
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.PrintWriter
import java.io.StringWriter
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 崩溃报告管理器
 * 负责收集、分析和报告应用崩溃信息
 */
@Singleton
class CrashReportingManager @Inject constructor(
    private val context: Context // 注入应用上下文
) : Thread.UncaughtExceptionHandler {
    
    companion object {
        private const val CRASH_LOG_DIR = "crash_logs" // 崩溃日志目录名
        private const val MAX_CRASH_FILES = 10 // 最大崩溃文件数量
        private const val CRASH_FILE_PREFIX = "crash_" // 崩溃文件前缀
        private const val CRASH_FILE_EXTENSION = ".log" // 崩溃文件扩展名
    }
    
    // 默认的异常处理器
    private val defaultExceptionHandler = Thread.getDefaultUncaughtExceptionHandler()
    
    // 日期格式化器
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd_HH-mm-ss", Locale.getDefault())
    private val timestampFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    
    // 崩溃日志目录
    private val crashLogDir: File by lazy {
        File(context.filesDir, CRASH_LOG_DIR).apply {
            if (!exists()) {
                mkdirs() // 创建目录
            }
        }
    }
    
    /**
     * 初始化崩溃报告管理器
     */
    fun initialize() {
        // 设置为默认的未捕获异常处理器
        Thread.setDefaultUncaughtExceptionHandler(this)
        
        // 清理旧的崩溃日志文件
        cleanupOldCrashLogs()
    }
    
    /**
     * 处理未捕获的异常
     * @param thread 发生异常的线程
     * @param exception 未捕获的异常
     */
    override fun uncaughtException(thread: Thread, exception: Throwable) {
        try {
            // 收集崩溃信息
            val crashInfo = collectCrashInfo(thread, exception)
            
            // 保存崩溃日志到本地文件
            saveCrashLogToFile(crashInfo)
            
            // 可以在这里发送崩溃报告到服务器
            // sendCrashReportToServer(crashInfo)
            
        } catch (e: Exception) {
            e.printStackTrace() // 打印保存崩溃日志时的异常
        } finally {
            // 调用默认的异常处理器
            defaultExceptionHandler?.uncaughtException(thread, exception)
        }
    }
    
    /**
     * 收集崩溃信息
     * @param thread 发生异常的线程
     * @param exception 异常对象
     * @return 崩溃信息对象
     */
    private fun collectCrashInfo(thread: Thread, exception: Throwable): CrashInfo {
        return CrashInfo(
            timestamp = System.currentTimeMillis(), // 崩溃时间戳
            threadName = thread.name, // 线程名称
            threadId = thread.id, // 线程ID
            exceptionType = exception.javaClass.name, // 异常类型
            exceptionMessage = exception.message ?: "No message", // 异常信息
            stackTrace = getStackTraceString(exception), // 堆栈跟踪
            deviceInfo = collectDeviceInfo(), // 设备信息
            appInfo = collectAppInfo(), // 应用信息
            memoryInfo = collectMemoryInfo(), // 内存信息
            systemInfo = collectSystemInfo() // 系统信息
        )
    }
    
    /**
     * 获取异常的堆栈跟踪字符串
     * @param exception 异常对象
     * @return 堆栈跟踪字符串
     */
    private fun getStackTraceString(exception: Throwable): String {
        val stringWriter = StringWriter() // 创建字符串写入器
        val printWriter = PrintWriter(stringWriter) // 创建打印写入器
        exception.printStackTrace(printWriter) // 打印堆栈跟踪
        printWriter.close() // 关闭写入器
        return stringWriter.toString() // 返回堆栈跟踪字符串
    }
    
    /**
     * 收集设备信息
     * @return 设备信息对象
     */
    private fun collectDeviceInfo(): DeviceInfo {
        return DeviceInfo(
            manufacturer = Build.MANUFACTURER, // 制造商
            model = Build.MODEL, // 型号
            brand = Build.BRAND, // 品牌
            device = Build.DEVICE, // 设备名
            board = Build.BOARD, // 主板
            hardware = Build.HARDWARE, // 硬件
            product = Build.PRODUCT, // 产品名
            fingerprint = Build.FINGERPRINT, // 指纹
            bootloader = Build.BOOTLOADER, // 引导程序
            display = Build.DISPLAY, // 显示
            host = Build.HOST, // 主机
            id = Build.ID, // ID
            tags = Build.TAGS, // 标签
            type = Build.TYPE, // 类型
            user = Build.USER // 用户
        )
    }
    
    /**
     * 收集应用信息
     * @return 应用信息对象
     */
    private fun collectAppInfo(): AppInfo {
        val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
        return AppInfo(
            packageName = context.packageName, // 包名
            versionName = packageInfo.versionName, // 版本名
            versionCode = packageInfo.longVersionCode, // 版本号
            targetSdkVersion = packageInfo.applicationInfo.targetSdkVersion, // 目标SDK版本
            minSdkVersion = packageInfo.applicationInfo.minSdkVersion, // 最小SDK版本
            installTime = packageInfo.firstInstallTime, // 安装时间
            updateTime = packageInfo.lastUpdateTime // 更新时间
        )
    }
    
    /**
     * 收集内存信息
     * @return 内存信息对象
     */
    private fun collectMemoryInfo(): MemoryInfo {
        val runtime = Runtime.getRuntime() // 获取运行时对象
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
        val memInfo = android.app.ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memInfo) // 获取系统内存信息
        
        return MemoryInfo(
            totalAppMemory = runtime.totalMemory(), // 应用总内存
            freeAppMemory = runtime.freeMemory(), // 应用空闲内存
            maxAppMemory = runtime.maxMemory(), // 应用最大内存
            usedAppMemory = runtime.totalMemory() - runtime.freeMemory(), // 应用已使用内存
            totalSystemMemory = memInfo.totalMem, // 系统总内存
            availableSystemMemory = memInfo.availMem, // 系统可用内存
            isLowMemory = memInfo.lowMemory, // 是否低内存状态
            threshold = memInfo.threshold // 低内存阈值
        )
    }
    
    /**
     * 收集系统信息
     * @return 系统信息对象
     */
    private fun collectSystemInfo(): SystemInfo {
        return SystemInfo(
            osVersion = Build.VERSION.RELEASE, // 操作系统版本
            apiLevel = Build.VERSION.SDK_INT, // API级别
            codename = Build.VERSION.CODENAME, // 代号
            incremental = Build.VERSION.INCREMENTAL, // 增量版本
            release = Build.VERSION.RELEASE, // 发布版本
            securityPatch = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                Build.VERSION.SECURITY_PATCH // 安全补丁版本
            } else {
                "Unknown"
            },
            baseOs = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                Build.VERSION.BASE_OS // 基础操作系统
            } else {
                "Unknown"
            }
        )
    }
    
    /**
     * 保存崩溃日志到文件
     * @param crashInfo 崩溃信息
     */
    private suspend fun saveCrashLogToFile(crashInfo: CrashInfo) = withContext(Dispatchers.IO) {
        try {
            val fileName = "${CRASH_FILE_PREFIX}${dateFormat.format(Date(crashInfo.timestamp))}$CRASH_FILE_EXTENSION"
            val crashFile = File(crashLogDir, fileName) // 创建崩溃日志文件
            
            val logContent = formatCrashLog(crashInfo) // 格式化崩溃日志内容
            crashFile.writeText(logContent) // 写入文件
            
        } catch (e: Exception) {
            e.printStackTrace() // 打印保存文件时的异常
        }
    }
    
    /**
     * 格式化崩溃日志内容
     * @param crashInfo 崩溃信息
     * @return 格式化后的日志内容
     */
    private fun formatCrashLog(crashInfo: CrashInfo): String {
        return buildString {
            appendLine("=== 崩溃报告 ===")
            appendLine("时间: ${timestampFormat.format(Date(crashInfo.timestamp))}")
            appendLine("线程: ${crashInfo.threadName} (ID: ${crashInfo.threadId})")
            appendLine()
            
            appendLine("=== 异常信息 ===")
            appendLine("异常类型: ${crashInfo.exceptionType}")
            appendLine("异常信息: ${crashInfo.exceptionMessage}")
            appendLine()
            
            appendLine("=== 堆栈跟踪 ===")
            appendLine(crashInfo.stackTrace)
            appendLine()
            
            appendLine("=== 应用信息 ===")
            appendLine("包名: ${crashInfo.appInfo.packageName}")
            appendLine("版本名: ${crashInfo.appInfo.versionName}")
            appendLine("版本号: ${crashInfo.appInfo.versionCode}")
            appendLine("目标SDK: ${crashInfo.appInfo.targetSdkVersion}")
            appendLine("最小SDK: ${crashInfo.appInfo.minSdkVersion}")
            appendLine()
            
            appendLine("=== 设备信息 ===")
            appendLine("制造商: ${crashInfo.deviceInfo.manufacturer}")
            appendLine("型号: ${crashInfo.deviceInfo.model}")
            appendLine("品牌: ${crashInfo.deviceInfo.brand}")
            appendLine("设备: ${crashInfo.deviceInfo.device}")
            appendLine("产品: ${crashInfo.deviceInfo.product}")
            appendLine()
            
            appendLine("=== 系统信息 ===")
            appendLine("Android版本: ${crashInfo.systemInfo.osVersion}")
            appendLine("API级别: ${crashInfo.systemInfo.apiLevel}")
            appendLine("安全补丁: ${crashInfo.systemInfo.securityPatch}")
            appendLine()
            
            appendLine("=== 内存信息 ===")
            appendLine("应用总内存: ${crashInfo.memoryInfo.totalAppMemory / 1024 / 1024} MB")
            appendLine("应用已用内存: ${crashInfo.memoryInfo.usedAppMemory / 1024 / 1024} MB")
            appendLine("应用最大内存: ${crashInfo.memoryInfo.maxAppMemory / 1024 / 1024} MB")
            appendLine("系统总内存: ${crashInfo.memoryInfo.totalSystemMemory / 1024 / 1024} MB")
            appendLine("系统可用内存: ${crashInfo.memoryInfo.availableSystemMemory / 1024 / 1024} MB")
            appendLine("低内存状态: ${crashInfo.memoryInfo.isLowMemory}")
            appendLine()
            
            appendLine("=== 报告结束 ===")
        }
    }
    
    /**
     * 清理旧的崩溃日志文件
     */
    private fun cleanupOldCrashLogs() {
        try {
            val crashFiles = crashLogDir.listFiles { file ->
                file.name.startsWith(CRASH_FILE_PREFIX) && file.name.endsWith(CRASH_FILE_EXTENSION)
            }?.sortedByDescending { it.lastModified() } // 按修改时间降序排列
            
            // 如果崩溃文件数量超过限制，删除最旧的文件
            if (crashFiles != null && crashFiles.size > MAX_CRASH_FILES) {
                crashFiles.drop(MAX_CRASH_FILES).forEach { file ->
                    try {
                        file.delete() // 删除文件
                    } catch (e: Exception) {
                        e.printStackTrace() // 打印删除文件时的异常
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace() // 打印清理文件时的异常
        }
    }
    
    /**
     * 获取所有崩溃日志文件
     * @return 崩溃日志文件列表
     */
    suspend fun getCrashLogFiles(): List<File> = withContext(Dispatchers.IO) {
        try {
            crashLogDir.listFiles { file ->
                file.name.startsWith(CRASH_FILE_PREFIX) && file.name.endsWith(CRASH_FILE_EXTENSION)
            }?.sortedByDescending { it.lastModified() }?.toList() ?: emptyList()
        } catch (e: Exception) {
            e.printStackTrace() // 打印获取文件列表时的异常
            emptyList()
        }
    }
    
    /**
     * 读取崩溃日志文件内容
     * @param file 崩溃日志文件
     * @return 文件内容
     */
    suspend fun readCrashLogFile(file: File): String = withContext(Dispatchers.IO) {
        try {
            file.readText() // 读取文件内容
        } catch (e: Exception) {
            e.printStackTrace() // 打印读取文件时的异常
            "读取崩溃日志失败: ${e.message}"
        }
    }
    
    /**
     * 删除指定的崩溃日志文件
     * @param file 要删除的文件
     * @return 是否删除成功
     */
    suspend fun deleteCrashLogFile(file: File): Boolean = withContext(Dispatchers.IO) {
        try {
            file.delete() // 删除文件
        } catch (e: Exception) {
            e.printStackTrace() // 打印删除文件时的异常
            false
        }
    }
    
    /**
     * 清空所有崩溃日志文件
     */
    suspend fun clearAllCrashLogs() = withContext(Dispatchers.IO) {
        try {
            crashLogDir.listFiles()?.forEach { file ->
                try {
                    file.delete() // 删除文件
                } catch (e: Exception) {
                    e.printStackTrace() // 打印删除文件时的异常
                }
            }
        } catch (e: Exception) {
            e.printStackTrace() // 打印清空文件时的异常
        }
    }
}

/**
 * 崩溃信息数据类
 */
data class CrashInfo(
    val timestamp: Long, // 崩溃时间戳
    val threadName: String, // 线程名称
    val threadId: Long, // 线程ID
    val exceptionType: String, // 异常类型
    val exceptionMessage: String, // 异常信息
    val stackTrace: String, // 堆栈跟踪
    val deviceInfo: DeviceInfo, // 设备信息
    val appInfo: AppInfo, // 应用信息
    val memoryInfo: MemoryInfo, // 内存信息
    val systemInfo: SystemInfo // 系统信息
)

/**
 * 设备信息数据类
 */
data class DeviceInfo(
    val manufacturer: String, // 制造商
    val model: String, // 型号
    val brand: String, // 品牌
    val device: String, // 设备名
    val board: String, // 主板
    val hardware: String, // 硬件
    val product: String, // 产品名
    val fingerprint: String, // 指纹
    val bootloader: String, // 引导程序
    val display: String, // 显示
    val host: String, // 主机
    val id: String, // ID
    val tags: String, // 标签
    val type: String, // 类型
    val user: String // 用户
)

/**
 * 应用信息数据类
 */
data class AppInfo(
    val packageName: String, // 包名
    val versionName: String, // 版本名
    val versionCode: Long, // 版本号
    val targetSdkVersion: Int, // 目标SDK版本
    val minSdkVersion: Int, // 最小SDK版本
    val installTime: Long, // 安装时间
    val updateTime: Long // 更新时间
)

/**
 * 内存信息数据类
 */
data class MemoryInfo(
    val totalAppMemory: Long, // 应用总内存
    val freeAppMemory: Long, // 应用空闲内存
    val maxAppMemory: Long, // 应用最大内存
    val usedAppMemory: Long, // 应用已使用内存
    val totalSystemMemory: Long, // 系统总内存
    val availableSystemMemory: Long, // 系统可用内存
    val isLowMemory: Boolean, // 是否低内存状态
    val threshold: Long // 低内存阈值
)

/**
 * 系统信息数据类
 */
data class SystemInfo(
    val osVersion: String, // 操作系统版本
    val apiLevel: Int, // API级别
    val codename: String, // 代号
    val incremental: String, // 增量版本
    val release: String, // 发布版本
    val securityPatch: String, // 安全补丁版本
    val baseOs: String // 基础操作系统
)