package com.aiinterview.simulator.di

import android.content.Context
import com.aiinterview.simulator.data.api.AuthApi
import com.aiinterview.simulator.data.api.AuthInterceptor
import com.aiinterview.simulator.data.api.InterviewApi
import com.aiinterview.simulator.data.api.SpeechRecognitionApi
import com.aiinterview.simulator.data.api.BaiduASRApi
import com.aiinterview.simulator.data.api.TokenManager
import com.aiinterview.simulator.data.network.NetworkCacheManager
import com.aiinterview.simulator.data.network.NetworkRetryInterceptor
import com.aiinterview.simulator.domain.util.Constants
import com.google.gson.Gson
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {
    
    @Provides
    @Singleton
    fun provideHttpLoggingInterceptor(): HttpLoggingInterceptor {
        return HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }
    }
    
    @Provides
    @Singleton
    fun provideNetworkCacheManager(@ApplicationContext context: Context): NetworkCacheManager {
        return NetworkCacheManager(context) // 创建网络缓存管理器
    }
    
    @Provides
    @Singleton
    fun provideNetworkRetryInterceptor(): NetworkRetryInterceptor {
        return NetworkRetryInterceptor() // 创建网络重试拦截器
    }
    
    @Provides
    @Singleton
    fun provideOkHttpClient(
        loggingInterceptor: HttpLoggingInterceptor,
        authInterceptor: AuthInterceptor,
        networkCacheManager: NetworkCacheManager,
        retryInterceptor: NetworkRetryInterceptor
    ): OkHttpClient {
        return OkHttpClient.Builder()
            .addInterceptor(authInterceptor) // 添加认证拦截器
            .addInterceptor(retryInterceptor) // 添加重试拦截器
            .addInterceptor(networkCacheManager.createCacheInterceptor()) // 添加缓存拦截器
            .addNetworkInterceptor(networkCacheManager.createNetworkCacheInterceptor()) // 添加网络缓存拦截器
            .addInterceptor(loggingInterceptor) // 添加日志拦截器（放在最后以记录完整请求）
            .cache(networkCacheManager.httpCache) // 设置HTTP缓存
            .connectTimeout(Constants.CONNECT_TIMEOUT, TimeUnit.SECONDS) // 设置连接超时
            .readTimeout(Constants.READ_TIMEOUT, TimeUnit.SECONDS) // 设置读取超时
            .writeTimeout(Constants.WRITE_TIMEOUT, TimeUnit.SECONDS) // 设置写入超时
            .build()
    }
    
    @Provides
    @Singleton
    fun provideRetrofit(okHttpClient: OkHttpClient): Retrofit {
        return Retrofit.Builder()
            .baseUrl(Constants.BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }
    
    @Provides
    @Singleton
    fun provideAuthApi(retrofit: Retrofit): AuthApi {
        return retrofit.create(AuthApi::class.java)
    }
    
    @Provides
    @Singleton
    fun provideInterviewApi(retrofit: Retrofit): InterviewApi {
        return retrofit.create(InterviewApi::class.java)
    }
    

    
    @Provides
    @Singleton
    fun provideSpeechRecognitionApi(retrofit: Retrofit): SpeechRecognitionApi {
        return retrofit.create(SpeechRecognitionApi::class.java)
    }
    
    @Provides
    @Singleton
    fun provideBaiduASRApi(): BaiduASRApi {
        return Retrofit.Builder()
            .baseUrl("https://vop.baidu.com/")
            .addConverterFactory(GsonConverterFactory.create())
            .build()
            .create(BaiduASRApi::class.java)
    }
    
    @Provides
    @Singleton
    fun provideEvaluationSyncApi(retrofit: Retrofit): com.aiinterview.simulator.data.api.EvaluationSyncApi {
        return retrofit.create(com.aiinterview.simulator.data.api.EvaluationSyncApi::class.java)
    }
    
    @Provides
    @Singleton
    fun provideTokenManager(@ApplicationContext context: Context): TokenManager {
        return TokenManager(context)
    }
    
    @Provides
    @Singleton
    fun provideGson(): Gson {
        return Gson()
    }
}