package com.aiinterview.simulator.presentation

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import com.aiinterview.simulator.presentation.navigation.AIInterviewNavigation
import com.aiinterview.simulator.presentation.theme.AIInterviewTheme
import com.aiinterview.simulator.presentation.viewmodel.PermissionViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * 主活动类
 * 应用的入口点，负责设置UI主题和处理权限请求
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    
    // 权限ViewModel，用于处理权限相关逻辑
    private val permissionViewModel: PermissionViewModel by viewModels()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 设置Compose UI内容
        setContent {
            // 应用主题
            AIInterviewTheme {
                // 主界面容器
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    // 导航组件
                    AIInterviewNavigation()
                }
            }
        }
    }
    
    /**
     * 处理权限请求结果
     * 当用户对权限请求做出响应时，系统会调用此方法
     * @param requestCode 权限请求码
     * @param permissions 请求的权限数组
     * @param grantResults 权限授权结果数组
     */
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        
        // 将权限请求结果传递给ViewModel处理
        permissionViewModel.handlePermissionResult(
            activity = this,
            requestCode = requestCode,
            permissions = permissions,
            grantResults = grantResults
        )
    }
    
    /**
     * 当活动重新获得焦点时调用
     * 用于检查用户是否在设置中修改了权限状态
     */
    override fun onResume() {
        super.onResume()
        
        // 重新检查权限状态，以防用户在设置中修改了权限
        permissionViewModel.checkAllPermissions(this)
    }
}