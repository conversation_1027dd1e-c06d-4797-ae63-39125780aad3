package com.aiinterview.simulator.util

import com.aiinterview.simulator.data.model.*

/**
 * Mock数据工厂类
 * 用于创建测试所需的各种模拟数据
 */
object MockDataFactory {
    
    /**
     * 创建模拟用户数据
     */
    fun createMockUser(
        id: String = TestUtils.createTestUserId(),
        phoneNumber: String = "13800138000",
        nickname: String = "测试用户",
        avatar: String? = null
    ): User {
        return User(
            id = id,
            phoneNumber = phoneNumber,
            nickname = nickname,
            avatar = avatar,
            createdAt = TestUtils.createTestTimestamp(),
            updatedAt = TestUtils.createTestTimestamp()
        )
    }
    
    /**
     * 创建模拟用户资料数据
     */
    fun createMockUserProfile(
        userId: String = TestUtils.createTestUserId(),
        realName: String = "张三",
        education: String = "本科",
        major: String = "计算机科学与技术",
        workExperience: Int = 2,
        targetPosition: String = "软件工程师"
    ): UserProfile {
        return UserProfile(
            userId = userId,
            realName = realName,
            education = education,
            major = major,
            workExperience = workExperience,
            targetPosition = targetPosition
        )
    }
    
    /**
     * 创建模拟岗位数据
     */
    fun createMockPosition(
        id: String = "position_001",
        name: String = "公务员-行政管理",
        category: String = "公务员",
        level: String = "中级",
        description: String = "负责行政管理相关工作"
    ): Position {
        return Position(
            id = id,
            name = name,
            category = category,
            level = level,
            description = description,
            requirements = listOf("本科及以上学历", "2年以上工作经验", "良好的沟通能力"),
            interviewConfig = createMockInterviewConfig()
        )
    }
    
    /**
     * 创建模拟面试配置数据
     */
    fun createMockInterviewConfig(
        duration: Int = 20,
        questionCount: Int = 4,
        questionTypes: List<QuestionType> = listOf(
            QuestionType.COMPREHENSIVE_ANALYSIS,
            QuestionType.PLANNING_ORGANIZATION,
            QuestionType.INTERPERSONAL_RELATIONS,
            QuestionType.EMERGENCY_RESPONSE
        ),
        timeWarnings: List<Int> = listOf(5, 2)
    ): InterviewConfig {
        return InterviewConfig(
            duration = duration,
            questionCount = questionCount,
            questionTypes = questionTypes,
            timeWarnings = timeWarnings
        )
    }
    
    /**
     * 创建模拟问题数据
     */
    fun createMockQuestion(
        id: String = TestUtils.createTestQuestionId(),
        type: QuestionType = QuestionType.COMPREHENSIVE_ANALYSIS,
        category: String = "综合分析",
        content: String = "请谈谈你对当前社会热点问题的看法",
        backgroundInfo: String? = "这是一道综合分析题",
        keyPoints: List<String> = listOf("观点明确", "逻辑清晰", "论证充分"),
        timeLimit: Int = 5,
        difficulty: Int = 3
    ): Question {
        return Question(
            id = id,
            type = type,
            category = category,
            content = content,
            backgroundInfo = backgroundInfo,
            keyPoints = keyPoints,
            timeLimit = timeLimit,
            difficulty = difficulty
        )
    }
    
    /**
     * 创建模拟面试会话数据
     */
    fun createMockInterviewSession(
        id: String = TestUtils.createTestSessionId(),
        userId: String = TestUtils.createTestUserId(),
        positionId: String = "position_001",
        status: InterviewStatus = InterviewStatus.CREATED,
        startTime: Long = TestUtils.createTestTimestamp(),
        endTime: Long? = null,
        currentQuestionIndex: Int = 0,
        questions: List<Question> = listOf(createMockQuestion()),
        answers: List<Answer> = emptyList(),
        evaluation: InterviewEvaluation? = null
    ): InterviewSession {
        return InterviewSession(
            id = id,
            userId = userId,
            positionId = positionId,
            status = status,
            startTime = startTime,
            endTime = endTime,
            currentQuestionIndex = currentQuestionIndex,
            questions = questions,
            answers = answers,
            evaluation = evaluation
        )
    }
    
    /**
     * 创建模拟回答数据
     */
    fun createMockAnswer(
        questionId: String = TestUtils.createTestQuestionId(),
        audioUrl: String = "https://example.com/audio/test.mp3",
        transcription: String = "这是我的回答内容",
        duration: Int = 120,
        submittedAt: Long = TestUtils.createTestTimestamp()
    ): Answer {
        return Answer(
            questionId = questionId,
            audioUrl = audioUrl,
            transcription = transcription,
            duration = duration,
            submittedAt = submittedAt
        )
    }
    
    /**
     * 创建模拟面试评价数据
     */
    fun createMockInterviewEvaluation(
        sessionId: String = TestUtils.createTestSessionId(),
        overallScore: Double = 85.5,
        dimensions: Map<String, DimensionScore> = mapOf(
            "内容完整性" to DimensionScore(80.0, 100.0, "回答内容较为完整"),
            "逻辑性" to DimensionScore(85.0, 100.0, "逻辑清晰"),
            "语言流畅度" to DimensionScore(90.0, 100.0, "表达流畅"),
            "时间掌控" to DimensionScore(87.0, 100.0, "时间控制良好")
        ),
        feedback: String = "整体表现良好，建议在内容深度上进一步提升",
        suggestions: List<String> = listOf(
            "加强对热点问题的深入分析",
            "提高论证的说服力",
            "注意回答的结构化"
        ),
        createdAt: Long = TestUtils.createTestTimestamp()
    ): InterviewEvaluation {
        return InterviewEvaluation(
            sessionId = sessionId,
            overallScore = overallScore,
            dimensions = dimensions,
            feedback = feedback,
            suggestions = suggestions,
            createdAt = createdAt
        )
    }
    
    /**
     * 创建模拟维度评分数据
     */
    fun createMockDimensionScore(
        score: Double = 85.0,
        maxScore: Double = 100.0,
        feedback: String = "表现良好"
    ): DimensionScore {
        return DimensionScore(
            score = score,
            maxScore = maxScore,
            feedback = feedback
        )
    }
    
    /**
     * 创建模拟面试记录数据
     */
    fun createMockInterviewRecord(
        id: String = TestUtils.createTestSessionId(),
        userId: String = TestUtils.createTestUserId(),
        positionName: String = "公务员-行政管理",
        interviewDate: Long = TestUtils.createTestTimestamp(),
        overallScore: Double = 85.5,
        status: String = "已完成",
        duration: Int = 1200
    ): InterviewRecord {
        return InterviewRecord(
            id = id,
            userId = userId,
            positionName = positionName,
            interviewDate = interviewDate,
            overallScore = overallScore,
            status = status,
            duration = duration
        )
    }
    
    /**
     * 创建模拟进步分析数据
     */
    fun createMockProgressAnalysis(
        userId: String = TestUtils.createTestUserId(),
        totalInterviews: Int = 10,
        averageScore: Double = 82.5,
        improvementRate: Double = 15.2,
        strongPoints: List<String> = listOf("逻辑清晰", "表达流畅"),
        weakPoints: List<String> = listOf("内容深度", "时间控制"),
        scoreHistory: List<Double> = listOf(70.0, 75.0, 80.0, 85.0, 82.5),
        lastUpdated: Long = TestUtils.createTestTimestamp()
    ): ProgressAnalysis {
        return ProgressAnalysis(
            userId = userId,
            totalInterviews = totalInterviews,
            averageScore = averageScore,
            improvementRate = improvementRate,
            strongPoints = strongPoints,
            weakPoints = weakPoints,
            scoreHistory = scoreHistory,
            lastUpdated = lastUpdated
        )
    }
    
    /**
     * 创建模拟API响应数据
     */
    fun createMockApiResponse<T>(
        data: T,
        success: Boolean = true,
        message: String = "操作成功",
        code: Int = 200
    ): ApiResponse<T> {
        return ApiResponse(
            data = data,
            success = success,
            message = message,
            code = code
        )
    }
    
    /**
     * 创建模拟错误响应数据
     */
    fun createMockErrorResponse(
        message: String = "操作失败",
        code: Int = 500
    ): ApiResponse<Nothing> {
        return ApiResponse(
            data = null,
            success = false,
            message = message,
            code = code
        )
    }
}