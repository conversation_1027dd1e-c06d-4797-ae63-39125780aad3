package com.aiinterview.simulator.data.dao

import androidx.room.*
import com.aiinterview.simulator.data.model.EvaluationRecord
import com.aiinterview.simulator.data.model.EvaluationRecordSummary
import kotlinx.coroutines.flow.Flow

@Dao
interface EvaluationRecordDao {
    
    /**
     * 获取用户的所有评价记录，按创建时间倒序排列
     */
    @Query("SELECT * FROM evaluation_records WHERE userId = :userId ORDER BY createdAt DESC")
    fun getEvaluationRecordsByUser(userId: String): Flow<List<EvaluationRecord>>
    
    /**
     * 获取用户的评价记录摘要，用于列表显示
     */
    @Query("""
        SELECT id, sessionId, positionName, overallScore, createdAt, isSynced 
        FROM evaluation_records 
        WHERE userId = :userId 
        ORDER BY createdAt DESC
    """)
    fun getEvaluationRecordSummariesByUser(userId: String): Flow<List<EvaluationRecordSummary>>
    
    /**
     * 根据ID获取特定的评价记录
     */
    @Query("SELECT * FROM evaluation_records WHERE id = :recordId")
    suspend fun getEvaluationRecordById(recordId: String): EvaluationRecord?
    
    /**
     * 根据会话ID获取评价记录
     */
    @Query("SELECT * FROM evaluation_records WHERE sessionId = :sessionId")
    suspend fun getEvaluationRecordBySessionId(sessionId: String): EvaluationRecord?
    
    /**
     * 获取未同步的评价记录
     */
    @Query("SELECT * FROM evaluation_records WHERE isSynced = 0 ORDER BY createdAt ASC")
    suspend fun getUnsyncedEvaluationRecords(): List<EvaluationRecord>
    
    /**
     * 根据岗位筛选评价记录
     */
    @Query("SELECT * FROM evaluation_records WHERE userId = :userId AND positionId = :positionId ORDER BY createdAt DESC")
    fun getEvaluationRecordsByUserAndPosition(userId: String, positionId: String): Flow<List<EvaluationRecord>>
    
    /**
     * 根据分数范围筛选评价记录
     */
    @Query("SELECT * FROM evaluation_records WHERE userId = :userId AND overallScore >= :minScore AND overallScore <= :maxScore ORDER BY createdAt DESC")
    fun getEvaluationRecordsByScoreRange(userId: String, minScore: Double, maxScore: Double): Flow<List<EvaluationRecord>>
    
    /**
     * 获取用户的评价记录统计信息
     */
    @Query("""
        SELECT COUNT(*) as totalCount, 
               AVG(overallScore) as averageScore, 
               MAX(overallScore) as bestScore,
               MIN(createdAt) as firstRecordTime,
               MAX(createdAt) as lastRecordTime
        FROM evaluation_records 
        WHERE userId = :userId
    """)
    suspend fun getEvaluationStatistics(userId: String): EvaluationStatistics?
    
    /**
     * 插入评价记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertEvaluationRecord(record: EvaluationRecord)
    
    /**
     * 批量插入评价记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertEvaluationRecords(records: List<EvaluationRecord>)
    
    /**
     * 更新评价记录
     */
    @Update
    suspend fun updateEvaluationRecord(record: EvaluationRecord)
    
    /**
     * 标记记录为已同步
     */
    @Query("UPDATE evaluation_records SET isSynced = 1, syncedAt = :syncedAt WHERE id = :recordId")
    suspend fun markAsSynced(recordId: String, syncedAt: Long)
    
    /**
     * 批量标记记录为已同步
     */
    @Query("UPDATE evaluation_records SET isSynced = 1, syncedAt = :syncedAt WHERE id IN (:recordIds)")
    suspend fun markMultipleAsSynced(recordIds: List<String>, syncedAt: Long)
    
    /**
     * 删除评价记录
     */
    @Delete
    suspend fun deleteEvaluationRecord(record: EvaluationRecord)
    
    /**
     * 根据ID删除评价记录
     */
    @Query("DELETE FROM evaluation_records WHERE id = :recordId")
    suspend fun deleteEvaluationRecordById(recordId: String)
    
    /**
     * 删除用户的所有评价记录
     */
    @Query("DELETE FROM evaluation_records WHERE userId = :userId")
    suspend fun deleteAllEvaluationRecordsByUser(userId: String)
    
    /**
     * 删除指定时间之前的评价记录
     */
    @Query("DELETE FROM evaluation_records WHERE createdAt < :timestamp")
    suspend fun deleteEvaluationRecordsBefore(timestamp: Long)
}

/**
 * 评价统计信息
 */
data class EvaluationStatistics(
    val totalCount: Int,
    val averageScore: Double,
    val bestScore: Double,
    val firstRecordTime: Long,
    val lastRecordTime: Long
)