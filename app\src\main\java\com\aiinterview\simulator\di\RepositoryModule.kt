package com.aiinterview.simulator.di

import com.aiinterview.simulator.data.repository.AuthRepository
import com.aiinterview.simulator.data.repository.AuthRepositoryImpl
import com.aiinterview.simulator.data.repository.InterviewRepository
import com.aiinterview.simulator.data.repository.InterviewRepositoryImpl
import com.aiinterview.simulator.data.repository.EvaluationRecordRepository
import com.aiinterview.simulator.data.repository.EvaluationRecordRepositoryImpl
import com.aiinterview.simulator.data.service.EvaluationSyncService
import com.aiinterview.simulator.data.service.EvaluationSyncServiceImpl
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryModule {
    
    @Binds
    @Singleton
    abstract fun bindAuthRepository(
        authRepositoryImpl: AuthRepositoryImpl
    ): AuthRepository
    
    @Binds
    @Singleton
    abstract fun bindInterviewRepository(
        interviewRepositoryImpl: InterviewRepositoryImpl
    ): InterviewRepository
    
    @Binds
    @Singleton
    abstract fun bindEvaluationRecordRepository(
        evaluationRecordRepositoryImpl: EvaluationRecordRepositoryImpl
    ): EvaluationRecordRepository
    
    @Binds
    @Singleton
    abstract fun bindEvaluationSyncService(
        evaluationSyncServiceImpl: EvaluationSyncServiceImpl
    ): EvaluationSyncService
}