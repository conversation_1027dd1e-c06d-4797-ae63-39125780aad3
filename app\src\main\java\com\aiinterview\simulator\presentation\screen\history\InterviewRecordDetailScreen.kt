package com.aiinterview.simulator.presentation.screen.history

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.aiinterview.simulator.data.model.*
import com.aiinterview.simulator.presentation.viewmodel.InterviewRecordDetailViewModel
import java.text.SimpleDateFormat
import java.util.*

/**
 * 面试记录详情界面
 * 显示单个面试记录的完整信息，包括问题、答案、评价等
 * @param recordId 面试记录ID
 * @param onNavigateBack 返回上一页的回调函数
 * @param viewModel 详情页ViewModel，使用Hilt注入
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun InterviewRecordDetailScreen(
    recordId: String,
    onNavigateBack: () -> Unit,
    viewModel: InterviewRecordDetailViewModel = hiltViewModel()
) {
    // 收集ViewModel中的状态数据
    val uiState by viewModel.uiState.collectAsState()
    
    // 初始化时加载记录详情
    LaunchedEffect(recordId) {
        viewModel.loadRecordDetail(recordId)
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
    ) {
        // 顶部应用栏
        TopAppBar(
            title = { Text("面试详情") },
            navigationIcon = {
                // 返回按钮
                IconButton(onClick = onNavigateBack) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            },
            actions = {
                // 更多操作按钮：显示下拉菜单
                var showMoreMenu by remember { mutableStateOf(false) }
                Box {
                    IconButton(onClick = { showMoreMenu = true }) {
                        Icon(Icons.Default.MoreVert, contentDescription = "更多")
                    }
                    
                    // 更多操作下拉菜单
                    DropdownMenu(
                        expanded = showMoreMenu,
                        onDismissRequest = { showMoreMenu = false }
                    ) {
                        // 重新练习选项：基于当前记录的岗位重新开始面试
                        DropdownMenuItem(
                            text = { Text("重新练习") },
                            onClick = {
                                // TODO: 实现重新练习功能，跳转到面试界面
                                showMoreMenu = false // 关闭菜单
                            },
                            leadingIcon = {
                                Icon(Icons.Default.Replay, contentDescription = null)
                            }
                        )
                        
                        // 分享记录选项：分享面试记录摘要
                        DropdownMenuItem(
                            text = { Text("分享记录") },
                            onClick = {
                                // TODO: 实现分享功能，生成分享内容
                                showMoreMenu = false // 关闭菜单
                            },
                            leadingIcon = {
                                Icon(Icons.Default.Share, contentDescription = null)
                            }
                        )
                        
                        // 导出记录选项：导出详细的面试记录
                        DropdownMenuItem(
                            text = { Text("导出记录") },
                            onClick = {
                                // TODO: 实现导出功能，生成PDF或文本文件
                                showMoreMenu = false // 关闭菜单
                            },
                            leadingIcon = {
                                Icon(Icons.Default.FileDownload, contentDescription = null)
                            }
                        )
                        
                        // 删除记录选项：删除当前面试记录
                        var showDeleteDialog by remember { mutableStateOf(false) }
                        DropdownMenuItem(
                            text = { Text("删除记录") },
                            onClick = {
                                showDeleteDialog = true // 显示删除确认对话框
                                showMoreMenu = false // 关闭菜单
                            },
                            leadingIcon = {
                                Icon(
                                    Icons.Default.Delete, 
                                    contentDescription = null,
                                    tint = MaterialTheme.colorScheme.error // 使用错误颜色
                                )
                            }
                        )
                        
                        // 删除确认对话框
                        if (showDeleteDialog) {
                            AlertDialog(
                                onDismissRequest = { showDeleteDialog = false }, // 点击外部关闭
                                title = { Text("删除面试记录") }, // 对话框标题
                                text = { Text("确定要删除这条面试记录吗？此操作无法撤销。") }, // 确认文本
                                confirmButton = {
                                    // 确认删除按钮
                                    TextButton(
                                        onClick = {
                                            viewModel.deleteRecord(recordId) { // 执行删除操作
                                                onNavigateBack() // 删除成功后返回上一页
                                            }
                                            showDeleteDialog = false // 关闭对话框
                                        }
                                    ) {
                                        Text("删除", color = MaterialTheme.colorScheme.error) // 使用错误颜色
                                    }
                                },
                                dismissButton = {
                                    // 取消按钮
                                    TextButton(onClick = { showDeleteDialog = false }) {
                                        Text("取消")
                                    }
                                }
                            )
                        }
                    }
                }
            }
        )
        
        // 主内容区域
        Box(modifier = Modifier.fillMaxSize()) {
            when {
                // 加载状态
                uiState.isLoading -> {
                    CircularProgressIndicator(
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                // 错误状态
                uiState.error != null -> {
                    ErrorMessage(
                        message = uiState.error,
                        onRetry = { viewModel.loadRecordDetail(recordId) },
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                // 正常状态：显示详情内容
                uiState.record != null -> {
                    InterviewRecordDetailContent(
                        record = uiState.record,
                        modifier = Modifier.fillMaxSize()
                    )
                }
            }
        }
    }
}

/**
 * 面试记录详情内容组件
 * @param record 面试记录详细数据
 * @param modifier 修饰符
 */
@Composable
private fun InterviewRecordDetailContent(
    record: InterviewRecordModel,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier,
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 基本信息卡片
        item {
            BasicInfoCard(record = record)
        }
        
        // 面试问题和答案列表
        item {
            Text(
                text = "面试问答",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(vertical = 8.dp)
            )
        }
        
        // 为每个问题创建问答卡片
        items(record.questions.zip(record.answers)) { (question, answer) ->
            QuestionAnswerCard(
                question = question,
                answer = answer
            )
        }
        
        // 评价信息（如果有的话）
        if (record.overallScore != null) {
            item {
                EvaluationCard(
                    score = record.overallScore,
                    evaluationId = record.evaluationId
                )
            }
        }
        
        // 音频文件信息
        if (record.audioFiles.isNotEmpty()) {
            item {
                AudioFilesCard(audioFiles = record.audioFiles)
            }
        }
    }
}

/**
 * 基本信息卡片组件
 * 显示面试的基本信息，如岗位、时间、状态等
 * @param record 面试记录数据
 */
@Composable
private fun BasicInfoCard(
    record: InterviewRecordModel
) {
    Card(
        modifier = Modifier.fillMaxWidth(), // 卡片占满宽度
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp), // 设置阴影
        shape = RoundedCornerShape(12.dp) // 设置圆角
    ) {
        Column(
            modifier = Modifier.padding(16.dp) // 内边距
        ) {
            // 岗位名称标题
            Text(
                text = record.positionName, // 显示岗位名称
                style = MaterialTheme.typography.headlineSmall, // 使用标题样式
                fontWeight = FontWeight.Bold, // 粗体显示
                color = MaterialTheme.colorScheme.primary // 使用主色调
            )
            
            Spacer(modifier = Modifier.height(8.dp)) // 垂直间距
            
            // 岗位类别
            Text(
                text = record.positionCategory, // 显示岗位类别
                style = MaterialTheme.typography.bodyLarge, // 使用正文样式
                color = MaterialTheme.colorScheme.onSurfaceVariant // 使用次要颜色
            )
            
            Spacer(modifier = Modifier.height(16.dp)) // 垂直间距
            
            // 面试时间信息行
            InfoRow(
                icon = Icons.Default.Schedule, // 时间图标
                label = "面试时间", // 标签
                value = formatDateTime(record.startTime) // 格式化的开始时间
            )
            
            Spacer(modifier = Modifier.height(8.dp)) // 垂直间距
            
            // 面试时长信息行
            InfoRow(
                icon = Icons.Default.Timer, // 计时器图标
                label = "面试时长", // 标签
                value = formatDuration(record.duration) // 格式化的时长
            )
            
            Spacer(modifier = Modifier.height(8.dp)) // 垂直间距
            
            // 面试状态信息行
            Row(
                modifier = Modifier.fillMaxWidth(), // 占满宽度
                horizontalArrangement = Arrangement.SpaceBetween, // 两端对齐
                verticalAlignment = Alignment.CenterVertically // 垂直居中
            ) {
                // 左侧：状态标签
                Row(
                    verticalAlignment = Alignment.CenterVertically // 垂直居中
                ) {
                    Icon(
                        Icons.Default.Info, // 信息图标
                        contentDescription = null, // 无需描述
                        modifier = Modifier.size(16.dp), // 图标大小
                        tint = MaterialTheme.colorScheme.onSurfaceVariant // 图标颜色
                    )
                    Spacer(modifier = Modifier.width(8.dp)) // 水平间距
                    Text(
                        text = "状态", // 标签文本
                        style = MaterialTheme.typography.bodyMedium, // 正文样式
                        color = MaterialTheme.colorScheme.onSurfaceVariant // 文本颜色
                    )
                }
                
                // 右侧：状态标签
                StatusChip(status = record.status) // 状态标签组件
            }
            
            // 如果有评分，显示评分信息
            if (record.overallScore != null) {
                Spacer(modifier = Modifier.height(8.dp)) // 垂直间距
                
                Row(
                    modifier = Modifier.fillMaxWidth(), // 占满宽度
                    horizontalArrangement = Arrangement.SpaceBetween, // 两端对齐
                    verticalAlignment = Alignment.CenterVertically // 垂直居中
                ) {
                    // 左侧：评分标签
                    Row(
                        verticalAlignment = Alignment.CenterVertically // 垂直居中
                    ) {
                        Icon(
                            Icons.Default.Star, // 星星图标
                            contentDescription = null, // 无需描述
                            modifier = Modifier.size(16.dp), // 图标大小
                            tint = MaterialTheme.colorScheme.onSurfaceVariant // 图标颜色
                        )
                        Spacer(modifier = Modifier.width(8.dp)) // 水平间距
                        Text(
                            text = "总体评分", // 标签文本
                            style = MaterialTheme.typography.bodyMedium, // 正文样式
                            color = MaterialTheme.colorScheme.onSurfaceVariant // 文本颜色
                        )
                    }
                    
                    // 右侧：评分标签
                    ScoreChip(score = record.overallScore) // 评分标签组件
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp)) // 垂直间距
            
            // 同步状态信息行
            Row(
                modifier = Modifier.fillMaxWidth(), // 占满宽度
                horizontalArrangement = Arrangement.SpaceBetween, // 两端对齐
                verticalAlignment = Alignment.CenterVertically // 垂直居中
            ) {
                // 左侧：同步状态标签
                Row(
                    verticalAlignment = Alignment.CenterVertically // 垂直居中
                ) {
                    Icon(
                        if (record.isSynced) Icons.Default.CloudDone else Icons.Default.CloudOff, // 根据同步状态选择图标
                        contentDescription = null, // 无需描述
                        modifier = Modifier.size(16.dp), // 图标大小
                        tint = if (record.isSynced) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurfaceVariant // 根据状态设置颜色
                    )
                    Spacer(modifier = Modifier.width(8.dp)) // 水平间距
                    Text(
                        text = "云端同步", // 标签文本
                        style = MaterialTheme.typography.bodyMedium, // 正文样式
                        color = MaterialTheme.colorScheme.onSurfaceVariant // 文本颜色
                    )
                }
                
                // 右侧：同步状态文本
                Text(
                    text = if (record.isSynced) "已同步" else "未同步", // 根据状态显示文本
                    style = MaterialTheme.typography.bodyMedium, // 正文样式
                    color = if (record.isSynced) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurfaceVariant, // 根据状态设置颜色
                    fontWeight = FontWeight.Medium // 中等粗细
                )
            }
        }
    }
}

/**
 * 信息行组件
 * 显示图标、标签和值的组合
 * @param icon 图标
 * @param label 标签文本
 * @param value 值文本
 */
@Composable
private fun InfoRow(
    icon: androidx.compose.ui.graphics.vector.ImageVector, // 图标
    label: String, // 标签
    value: String // 值
) {
    Row(
        modifier = Modifier.fillMaxWidth(), // 占满宽度
        horizontalArrangement = Arrangement.SpaceBetween, // 两端对齐
        verticalAlignment = Alignment.CenterVertically // 垂直居中
    ) {
        // 左侧：图标和标签
        Row(
            verticalAlignment = Alignment.CenterVertically // 垂直居中
        ) {
            Icon(
                icon, // 传入的图标
                contentDescription = null, // 无需描述
                modifier = Modifier.size(16.dp), // 图标大小
                tint = MaterialTheme.colorScheme.onSurfaceVariant // 图标颜色
            )
            Spacer(modifier = Modifier.width(8.dp)) // 水平间距
            Text(
                text = label, // 标签文本
                style = MaterialTheme.typography.bodyMedium, // 正文样式
                color = MaterialTheme.colorScheme.onSurfaceVariant // 文本颜色
            )
        }
        
        // 右侧：值
        Text(
            text = value, // 值文本
            style = MaterialTheme.typography.bodyMedium, // 正文样式
            fontWeight = FontWeight.Medium // 中等粗细
        )
    }
}

/**
 * 问答卡片组件
 * 显示单个问题和对应的答案
 * @param question 问题数据
 * @param answer 答案数据
 */
@Composable
private fun QuestionAnswerCard(
    question: Question, // 问题对象
    answer: AnswerWithAudio // 带音频的答案对象
) {
    Card(
        modifier = Modifier.fillMaxWidth(), // 卡片占满宽度
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp), // 设置较小的阴影
        shape = RoundedCornerShape(8.dp) // 设置圆角
    ) {
        Column(
            modifier = Modifier.padding(16.dp) // 内边距
        ) {
            // 问题标题
            Text(
                text = "问题", // 问题标签
                style = MaterialTheme.typography.labelLarge, // 标签样式
                color = MaterialTheme.colorScheme.primary, // 主色调
                fontWeight = FontWeight.Bold // 粗体
            )
            
            Spacer(modifier = Modifier.height(4.dp)) // 垂直间距
            
            // 问题内容
            Text(
                text = question.content, // 问题内容
                style = MaterialTheme.typography.bodyLarge, // 正文样式
                modifier = Modifier.padding(bottom = 12.dp) // 底部边距
            )
            
            // 答案标题
            Text(
                text = "回答", // 答案标签
                style = MaterialTheme.typography.labelLarge, // 标签样式
                color = MaterialTheme.colorScheme.secondary, // 次要色调
                fontWeight = FontWeight.Bold // 粗体
            )
            
            Spacer(modifier = Modifier.height(4.dp)) // 垂直间距
            
            // 答案内容
            Text(
                text = answer.transcription, // 答案转录文本
                style = MaterialTheme.typography.bodyMedium, // 正文样式
                modifier = Modifier.padding(bottom = 8.dp) // 底部边距
            )
            
            // 答案信息行（时长和提交时间）
            Row(
                modifier = Modifier.fillMaxWidth(), // 占满宽度
                horizontalArrangement = Arrangement.SpaceBetween, // 两端对齐
                verticalAlignment = Alignment.CenterVertically // 垂直居中
            ) {
                // 左侧：录音时长
                Row(
                    verticalAlignment = Alignment.CenterVertically // 垂直居中
                ) {
                    Icon(
                        Icons.Default.Mic, // 麦克风图标
                        contentDescription = null, // 无需描述
                        modifier = Modifier.size(14.dp), // 图标大小
                        tint = MaterialTheme.colorScheme.onSurfaceVariant // 图标颜色
                    )
                    Spacer(modifier = Modifier.width(4.dp)) // 水平间距
                    Text(
                        text = formatDuration(answer.duration), // 格式化的录音时长
                        style = MaterialTheme.typography.bodySmall, // 小号正文样式
                        color = MaterialTheme.colorScheme.onSurfaceVariant // 文本颜色
                    )
                }
                
                // 右侧：提交时间
                Text(
                    text = formatDateTime(answer.submittedAt), // 格式化的提交时间
                    style = MaterialTheme.typography.bodySmall, // 小号正文样式
                    color = MaterialTheme.colorScheme.onSurfaceVariant // 文本颜色
                )
            }
        }
    }
}

/**
 * 评价卡片组件
 * 显示面试评价信息
 * @param score 评分
 * @param evaluationId 评价ID
 */
@Composable
private fun EvaluationCard(
    score: Double, // 评分
    evaluationId: String? // 评价ID
) {
    Card(
        modifier = Modifier.fillMaxWidth(), // 卡片占满宽度
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp), // 设置阴影
        shape = RoundedCornerShape(12.dp) // 设置圆角
    ) {
        Column(
            modifier = Modifier.padding(16.dp) // 内边距
        ) {
            // 评价标题
            Row(
                verticalAlignment = Alignment.CenterVertically // 垂直居中
            ) {
                Icon(
                    Icons.Default.Assessment, // 评估图标
                    contentDescription = null, // 无需描述
                    tint = MaterialTheme.colorScheme.primary // 主色调
                )
                Spacer(modifier = Modifier.width(8.dp)) // 水平间距
                Text(
                    text = "AI评价", // 标题文本
                    style = MaterialTheme.typography.titleMedium, // 标题样式
                    fontWeight = FontWeight.Bold // 粗体
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp)) // 垂直间距
            
            // 评分显示
            Row(
                modifier = Modifier.fillMaxWidth(), // 占满宽度
                horizontalArrangement = Arrangement.SpaceBetween, // 两端对齐
                verticalAlignment = Alignment.CenterVertically // 垂直居中
            ) {
                Text(
                    text = "总体评分", // 标签
                    style = MaterialTheme.typography.bodyLarge // 正文样式
                )
                
                // 大号评分显示
                Text(
                    text = "${score.toInt()}分", // 评分文本
                    style = MaterialTheme.typography.headlineMedium, // 大标题样式
                    fontWeight = FontWeight.Bold, // 粗体
                    color = getScoreColor(score) // 根据分数获取颜色
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp)) // 垂直间距
            
            // 评价等级
            Text(
                text = "等级：${getScoreLevel(score)}", // 评价等级
                style = MaterialTheme.typography.bodyMedium, // 正文样式
                color = getScoreColor(score) // 根据分数获取颜色
            )
            
            // 如果有评价ID，显示查看详细评价的提示
            if (evaluationId != null) {
                Spacer(modifier = Modifier.height(8.dp)) // 垂直间距
                Text(
                    text = "点击查看详细评价报告", // 提示文本
                    style = MaterialTheme.typography.bodySmall, // 小号正文样式
                    color = MaterialTheme.colorScheme.primary // 主色调
                )
            }
        }
    }
}

/**
 * 音频文件卡片组件
 * 显示面试相关的音频文件信息
 * @param audioFiles 音频文件列表
 */
@Composable
private fun AudioFilesCard(
    audioFiles: List<AudioFileInfo> // 音频文件信息列表
) {
    Card(
        modifier = Modifier.fillMaxWidth(), // 卡片占满宽度
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp), // 设置较小的阴影
        shape = RoundedCornerShape(8.dp) // 设置圆角
    ) {
        Column(
            modifier = Modifier.padding(16.dp) // 内边距
        ) {
            // 音频文件标题
            Row(
                verticalAlignment = Alignment.CenterVertically // 垂直居中
            ) {
                Icon(
                    Icons.Default.AudioFile, // 音频文件图标
                    contentDescription = null, // 无需描述
                    tint = MaterialTheme.colorScheme.primary // 主色调
                )
                Spacer(modifier = Modifier.width(8.dp)) // 水平间距
                Text(
                    text = "音频文件 (${audioFiles.size}个)", // 标题和文件数量
                    style = MaterialTheme.typography.titleMedium, // 标题样式
                    fontWeight = FontWeight.Bold // 粗体
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp)) // 垂直间距
            
            // 音频文件列表
            audioFiles.forEach { audioFile ->
                AudioFileItem(audioFile = audioFile) // 音频文件项组件
                Spacer(modifier = Modifier.height(8.dp)) // 垂直间距
            }
        }
    }
}

/**
 * 音频文件项组件
 * 显示单个音频文件的信息
 * @param audioFile 音频文件信息
 */
@Composable
private fun AudioFileItem(
    audioFile: AudioFileInfo // 音频文件信息
) {
    Row(
        modifier = Modifier.fillMaxWidth(), // 占满宽度
        horizontalArrangement = Arrangement.SpaceBetween, // 两端对齐
        verticalAlignment = Alignment.CenterVertically // 垂直居中
    ) {
        // 左侧：文件信息
        Column(
            modifier = Modifier.weight(1f) // 占据剩余空间
        ) {
            // 文件名
            Text(
                text = audioFile.fileName, // 文件名
                style = MaterialTheme.typography.bodyMedium, // 正文样式
                maxLines = 1, // 限制单行
                overflow = TextOverflow.Ellipsis // 超出显示省略号
            )
            
            // 文件大小和时长
            Text(
                text = "${formatFileSize(audioFile.fileSize)} • ${formatDuration(audioFile.duration)}", // 文件大小和时长
                style = MaterialTheme.typography.bodySmall, // 小号正文样式
                color = MaterialTheme.colorScheme.onSurfaceVariant // 次要颜色
            )
        }
        
        // 右侧：上传状态图标
        Icon(
            if (audioFile.isUploaded) Icons.Default.CloudDone else Icons.Default.CloudUpload, // 根据上传状态选择图标
            contentDescription = if (audioFile.isUploaded) "已上传" else "未上传", // 图标描述
            modifier = Modifier.size(20.dp), // 图标大小
            tint = if (audioFile.isUploaded) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurfaceVariant // 根据状态设置颜色
        )
    }
}

/**
 * 状态标签组件
 * 显示面试记录的状态
 * @param status 面试记录状态
 */
@Composable
private fun StatusChip(
    status: InterviewRecordStatus // 面试记录状态
) {
    // 根据状态确定显示文本和颜色
    val (text, color) = when (status) {
        InterviewRecordStatus.COMPLETED -> "已完成" to MaterialTheme.colorScheme.primary // 已完成：主色调
        InterviewRecordStatus.CANCELLED -> "已取消" to MaterialTheme.colorScheme.error // 已取消：错误颜色
        InterviewRecordStatus.INTERRUPTED -> "中断" to MaterialTheme.colorScheme.tertiary // 中断：第三色调
    }
    
    // 带背景色的标签
    Surface(
        shape = RoundedCornerShape(12.dp), // 圆角
        color = color.copy(alpha = 0.1f) // 半透明背景
    ) {
        Text(
            text = text, // 状态文本
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp), // 内边距
            style = MaterialTheme.typography.labelSmall, // 小标签样式
            color = color // 使用对应的颜色
        )
    }
}

/**
 * 评分标签组件
 * 显示面试评分，根据分数使用不同颜色
 * @param score 面试评分
 */
@Composable
private fun ScoreChip(
    score: Double // 面试评分
) {
    val color = getScoreColor(score) // 获取分数对应的颜色
    
    // 带背景色的评分标签
    Surface(
        shape = RoundedCornerShape(12.dp), // 圆角
        color = color.copy(alpha = 0.1f) // 半透明背景
    ) {
        Text(
            text = "${score.toInt()}分", // 显示整数分数
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp), // 内边距
            style = MaterialTheme.typography.labelSmall, // 小标签样式
            color = color, // 使用对应的颜色
            fontWeight = FontWeight.Bold // 粗体显示
        )
    }
}

/**
 * 错误信息组件
 * 当加载失败时显示错误信息和重试按钮
 * @param message 错误信息文本
 * @param onRetry 重试操作的回调函数
 * @param modifier 修饰符
 */
@Composable
private fun ErrorMessage(
    message: String, // 错误信息
    onRetry: () -> Unit, // 重试回调
    modifier: Modifier = Modifier // 修饰符
) {
    Column(
        modifier = modifier, // 应用修饰符
        horizontalAlignment = Alignment.CenterHorizontally // 居中对齐
    ) {
        // 错误图标
        Icon(
            Icons.Default.Error, // 错误图标
            contentDescription = null, // 无需描述
            modifier = Modifier.size(64.dp), // 图标大小
            tint = MaterialTheme.colorScheme.error // 使用错误颜色
        )
        Spacer(modifier = Modifier.height(16.dp)) // 垂直间距
        
        // 错误信息文本
        Text(
            text = message, // 错误信息
            style = MaterialTheme.typography.titleMedium, // 标题样式
            color = MaterialTheme.colorScheme.error // 错误颜色
        )
        Spacer(modifier = Modifier.height(16.dp)) // 垂直间距
        
        // 重试按钮
        Button(onClick = onRetry) { // 点击重试
            Text("重试") // 按钮文本
        }
    }
}

// 工具函数

/**
 * 格式化时间戳为可读的日期时间字符串
 * @param timestamp 时间戳（毫秒）
 * @return 格式化后的时间字符串，格式为"yyyy-MM-dd HH:mm"
 */
private fun formatDateTime(timestamp: Long): String {
    val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()) // 创建日期格式化器
    return formatter.format(Date(timestamp)) // 格式化时间戳
}

/**
 * 格式化时长为可读的时间字符串
 * @param seconds 时长（秒）
 * @return 格式化后的时长字符串，如"5分30秒"或"30秒"
 */
private fun formatDuration(seconds: Int): String {
    val minutes = seconds / 60 // 计算分钟数
    val remainingSeconds = seconds % 60 // 计算剩余秒数
    return if (minutes > 0) {
        "${minutes}分${remainingSeconds}秒" // 有分钟时显示分钟和秒
    } else {
        "${remainingSeconds}秒" // 只有秒时只显示秒
    }
}

/**
 * 格式化文件大小为可读的字符串
 * @param bytes 文件大小（字节）
 * @return 格式化后的文件大小字符串，如"1.2MB"或"512KB"
 */
private fun formatFileSize(bytes: Long): String {
    return when {
        bytes >= 1024 * 1024 -> String.format("%.1fMB", bytes / (1024.0 * 1024.0)) // MB单位
        bytes >= 1024 -> String.format("%.1fKB", bytes / 1024.0) // KB单位
        else -> "${bytes}B" // 字节单位
    }
}

/**
 * 根据分数获取对应的颜色
 * @param score 分数
 * @return 对应的颜色
 */
@Composable
private fun getScoreColor(score: Double): Color {
    return when {
        score >= 90 -> Color(0xFF4CAF50) // 绿色：优秀
        score >= 80 -> Color(0xFF2196F3) // 蓝色：良好
        score >= 70 -> Color(0xFFFF9800) // 橙色：一般
        else -> Color(0xFFF44336) // 红色：较差
    }
}

/**
 * 根据分数获取评价等级
 * @param score 分数
 * @return 评价等级字符串
 */
private fun getScoreLevel(score: Double): String {
    return when {
        score >= 90 -> "优秀" // 90分以上为优秀
        score >= 80 -> "良好" // 80-89分为良好
        score >= 70 -> "一般" // 70-79分为一般
        score >= 60 -> "及格" // 60-69分为及格
        else -> "不及格" // 60分以下为不及格
    }
}