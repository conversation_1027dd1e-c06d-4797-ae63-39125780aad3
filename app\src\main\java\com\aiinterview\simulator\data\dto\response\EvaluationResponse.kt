package com.aiinterview.simulator.data.dto.response

/**
 * AI评价生成响应
 */
data class EvaluationResponse(
    val sessionId: String,
    val overallScore: Double,
    val overallGrade: String, // 优秀、良好、中等、需改进
    val dimensions: EvaluationDimensions,
    val feedback: String,
    val suggestions: List<String>,
    val encouragement: String,
    val timeAnalysis: TimeAnalysis,
    val createdAt: Long
)

/**
 * 评价维度详情
 */
data class EvaluationDimensions(
    val contentCompleteness: DimensionScore, // 答题要点全面性
    val logicalStructure: DimensionScore,    // 逻辑性
    val languageFluency: DimensionScore,     // 语言流畅度
    val expressionClarity: DimensionScore,   // 表达清晰度
    val timeManagement: DimensionScore       // 时间掌控能力
)

data class DimensionScore(
    val score: Double,
    val maxScore: Double = 100.0,
    val grade: String, // 优秀、良好、中等、需改进
    val feedback: String,
    val keyStrengths: List<String> = emptyList(),
    val improvementAreas: List<String> = emptyList()
)

/**
 * 时间分析
 */
data class TimeAnalysis(
    val totalTime: Int, // 实际用时（秒）
    val timeLimit: Int, // 时间限制（秒）
    val timeUtilization: Double, // 时间利用率 (0.0-1.0)
    val questionTimeBreakdown: List<QuestionTimeAnalysis>,
    val timeManagementFeedback: String
)

data class QuestionTimeAnalysis(
    val questionId: String,
    val questionType: String,
    val timeSpent: Int, // 该题用时（秒）
    val recommendedTime: Int, // 建议用时（秒）
    val efficiency: String // 高效、适中、过长、过短
)

/**
 * 大语言模型API响应格式
 */
data class LLMEvaluationResponse(
    val choices: List<LLMChoice>,
    val usage: LLMUsage? = null
)

data class LLMChoice(
    val message: LLMMessage,
    val finish_reason: String
)

data class LLMMessage(
    val role: String,
    val content: String
)

data class LLMUsage(
    val prompt_tokens: Int,
    val completion_tokens: Int,
    val total_tokens: Int
)