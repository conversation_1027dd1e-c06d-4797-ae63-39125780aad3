package com.aiinterview.simulator.presentation.theme

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * 统一的组件样式定义
 * 提供应用中所有组件的统一样式配置
 */
object ComponentStyles {
    
    /**
     * 按钮样式配置
     * 定义不同类型按钮的样式
     */
    object Button {
        
        /**
         * 主要按钮样式
         * 用于最重要的操作按钮
         */
        @Composable
        fun primaryButtonColors(): ButtonColors {
            return ButtonDefaults.buttonColors(
                containerColor = MaterialTheme.colorScheme.primary,        // 容器颜色使用主色调
                contentColor = MaterialTheme.colorScheme.onPrimary,        // 内容颜色使用主色调上的文字色
                disabledContainerColor = MaterialTheme.colorScheme.surfaceVariant, // 禁用状态容器颜色
                disabledContentColor = MaterialTheme.colorScheme.onSurfaceVariant   // 禁用状态内容颜色
            )
        }
        
        /**
         * 次要按钮样式
         * 用于次要操作按钮
         */
        @Composable
        fun secondaryButtonColors(): ButtonColors {
            return ButtonDefaults.buttonColors(
                containerColor = MaterialTheme.colorScheme.secondary,      // 容器颜色使用次要色调
                contentColor = MaterialTheme.colorScheme.onSecondary,      // 内容颜色使用次要色调上的文字色
                disabledContainerColor = MaterialTheme.colorScheme.surfaceVariant, // 禁用状态容器颜色
                disabledContentColor = MaterialTheme.colorScheme.onSurfaceVariant   // 禁用状态内容颜色
            )
        }
        
        /**
         * 轮廓按钮样式
         * 用于辅助操作按钮
         */
        @Composable
        fun outlinedButtonColors(): ButtonColors {
            return ButtonDefaults.outlinedButtonColors(
                contentColor = MaterialTheme.colorScheme.primary,          // 内容颜色使用主色调
                disabledContentColor = MaterialTheme.colorScheme.onSurfaceVariant // 禁用状态内容颜色
            )
        }
        
        /**
         * 文本按钮样式
         * 用于最低优先级的操作按钮
         */
        @Composable
        fun textButtonColors(): ButtonColors {
            return ButtonDefaults.textButtonColors(
                contentColor = MaterialTheme.colorScheme.primary,          // 内容颜色使用主色调
                disabledContentColor = MaterialTheme.colorScheme.onSurfaceVariant // 禁用状态内容颜色
            )
        }
        
        /**
         * 危险操作按钮样式
         * 用于删除、取消等危险操作
         */
        @Composable
        fun dangerButtonColors(): ButtonColors {
            return ButtonDefaults.buttonColors(
                containerColor = MaterialTheme.colorScheme.error,          // 容器颜色使用错误色
                contentColor = MaterialTheme.colorScheme.onError,          // 内容颜色使用错误色上的文字色
                disabledContainerColor = MaterialTheme.colorScheme.surfaceVariant, // 禁用状态容器颜色
                disabledContentColor = MaterialTheme.colorScheme.onSurfaceVariant   // 禁用状态内容颜色
            )
        }
        
        /**
         * 成功操作按钮样式
         * 用于确认、完成等成功操作
         */
        @Composable
        fun successButtonColors(): ButtonColors {
            return ButtonDefaults.buttonColors(
                containerColor = Success40,                               // 容器颜色使用成功色
                contentColor = MaterialTheme.colorScheme.onPrimary,       // 内容颜色使用白色
                disabledContainerColor = MaterialTheme.colorScheme.surfaceVariant, // 禁用状态容器颜色
                disabledContentColor = MaterialTheme.colorScheme.onSurfaceVariant   // 禁用状态内容颜色
            )
        }
        
        // 按钮形状定义
        val smallButtonShape: Shape = RoundedCornerShape(8.dp)          // 小按钮圆角
        val mediumButtonShape: Shape = RoundedCornerShape(12.dp)        // 中等按钮圆角
        val largeButtonShape: Shape = RoundedCornerShape(16.dp)         // 大按钮圆角
        
        // 按钮内边距定义
        val smallButtonPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp)   // 小按钮内边距
        val mediumButtonPadding = PaddingValues(horizontal = 24.dp, vertical = 12.dp) // 中等按钮内边距
        val largeButtonPadding = PaddingValues(horizontal = 32.dp, vertical = 16.dp)  // 大按钮内边距
    }
    
    /**
     * 卡片样式配置
     * 定义不同类型卡片的样式
     */
    object Card {
        
        /**
         * 标准卡片样式
         * 用于一般内容展示
         */
        @Composable
        fun standardCardColors(): CardColors {
            return CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface,        // 容器颜色使用表面色
                contentColor = MaterialTheme.colorScheme.onSurface,        // 内容颜色使用表面上的文字色
                disabledContainerColor = MaterialTheme.colorScheme.surfaceVariant, // 禁用状态容器颜色
                disabledContentColor = MaterialTheme.colorScheme.onSurfaceVariant   // 禁用状态内容颜色
            )
        }
        
        /**
         * 强调卡片样式
         * 用于重要内容展示
         */
        @Composable
        fun emphasizedCardColors(): CardColors {
            return CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer, // 容器颜色使用主色调容器
                contentColor = MaterialTheme.colorScheme.onPrimaryContainer, // 内容颜色使用主色调容器上的文字色
                disabledContainerColor = MaterialTheme.colorScheme.surfaceVariant, // 禁用状态容器颜色
                disabledContentColor = MaterialTheme.colorScheme.onSurfaceVariant   // 禁用状态内容颜色
            )
        }
        
        /**
         * 轮廓卡片样式
         * 用于需要边框的卡片
         */
        @Composable
        fun outlinedCardColors(): CardColors {
            return CardDefaults.outlinedCardColors(
                containerColor = MaterialTheme.colorScheme.surface,        // 容器颜色使用表面色
                contentColor = MaterialTheme.colorScheme.onSurface,        // 内容颜色使用表面上的文字色
                disabledContainerColor = MaterialTheme.colorScheme.surfaceVariant, // 禁用状态容器颜色
                disabledContentColor = MaterialTheme.colorScheme.onSurfaceVariant   // 禁用状态内容颜色
            )
        }
        
        // 卡片阴影高度定义
        val lowElevation = CardDefaults.cardElevation(defaultElevation = 2.dp)      // 低阴影
        val mediumElevation = CardDefaults.cardElevation(defaultElevation = 4.dp)   // 中等阴影
        val highElevation = CardDefaults.cardElevation(defaultElevation = 8.dp)     // 高阴影
        
        // 卡片边框定义
        val thinBorder = BorderStroke(1.dp, MaterialTheme.colorScheme.outline)     // 细边框
        val mediumBorder = BorderStroke(2.dp, MaterialTheme.colorScheme.outline)   // 中等边框
    }
    
    /**
     * 输入框样式配置
     * 定义不同类型输入框的样式
     */
    object TextField {
        
        /**
         * 标准输入框样式
         * 用于一般文本输入
         */
        @Composable
        fun standardTextFieldColors(): TextFieldColors {
            return TextFieldDefaults.colors(
                focusedContainerColor = MaterialTheme.colorScheme.surface,         // 聚焦状态容器颜色
                unfocusedContainerColor = MaterialTheme.colorScheme.surfaceVariant, // 未聚焦状态容器颜色
                focusedTextColor = MaterialTheme.colorScheme.onSurface,            // 聚焦状态文字颜色
                unfocusedTextColor = MaterialTheme.colorScheme.onSurfaceVariant,   // 未聚焦状态文字颜色
                focusedIndicatorColor = MaterialTheme.colorScheme.primary,         // 聚焦状态指示器颜色
                unfocusedIndicatorColor = MaterialTheme.colorScheme.outline,       // 未聚焦状态指示器颜色
                errorIndicatorColor = MaterialTheme.colorScheme.error,             // 错误状态指示器颜色
                focusedLabelColor = MaterialTheme.colorScheme.primary,             // 聚焦状态标签颜色
                unfocusedLabelColor = MaterialTheme.colorScheme.onSurfaceVariant,  // 未聚焦状态标签颜色
                errorLabelColor = MaterialTheme.colorScheme.error                  // 错误状态标签颜色
            )
        }
        
        /**
         * 轮廓输入框样式
         * 用于需要边框的输入框
         */
        @Composable
        fun outlinedTextFieldColors(): TextFieldColors {
            return OutlinedTextFieldDefaults.colors(
                focusedContainerColor = Color.Transparent,                         // 聚焦状态容器颜色（透明）
                unfocusedContainerColor = Color.Transparent,                       // 未聚焦状态容器颜色（透明）
                focusedTextColor = MaterialTheme.colorScheme.onSurface,            // 聚焦状态文字颜色
                unfocusedTextColor = MaterialTheme.colorScheme.onSurfaceVariant,   // 未聚焦状态文字颜色
                focusedBorderColor = MaterialTheme.colorScheme.primary,            // 聚焦状态边框颜色
                unfocusedBorderColor = MaterialTheme.colorScheme.outline,          // 未聚焦状态边框颜色
                errorBorderColor = MaterialTheme.colorScheme.error,                // 错误状态边框颜色
                focusedLabelColor = MaterialTheme.colorScheme.primary,             // 聚焦状态标签颜色
                unfocusedLabelColor = MaterialTheme.colorScheme.onSurfaceVariant,  // 未聚焦状态标签颜色
                errorLabelColor = MaterialTheme.colorScheme.error                  // 错误状态标签颜色
            )
        }
    }
    
    /**
     * 芯片样式配置
     * 定义不同类型芯片的样式
     */
    object Chip {
        
        /**
         * 标准芯片样式
         * 用于一般标签展示
         */
        @Composable
        fun standardChipColors(): ChipColors {
            return FilterChipDefaults.filterChipColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant,         // 容器颜色
                labelColor = MaterialTheme.colorScheme.onSurfaceVariant,           // 标签颜色
                selectedContainerColor = MaterialTheme.colorScheme.primaryContainer, // 选中状态容器颜色
                selectedLabelColor = MaterialTheme.colorScheme.onPrimaryContainer, // 选中状态标签颜色
                disabledContainerColor = MaterialTheme.colorScheme.surfaceVariant, // 禁用状态容器颜色
                disabledLabelColor = MaterialTheme.colorScheme.onSurfaceVariant    // 禁用状态标签颜色
            )
        }
        
        /**
         * 建议芯片样式
         * 用于建议和推荐标签
         */
        @Composable
        fun suggestionChipColors(): ChipColors {
            return SuggestionChipDefaults.suggestionChipColors(
                containerColor = MaterialTheme.colorScheme.surface,                // 容器颜色
                labelColor = MaterialTheme.colorScheme.onSurface,                  // 标签颜色
                disabledContainerColor = MaterialTheme.colorScheme.surfaceVariant, // 禁用状态容器颜色
                disabledLabelColor = MaterialTheme.colorScheme.onSurfaceVariant    // 禁用状态标签颜色
            )
        }
    }
    
    /**
     * 浮动操作按钮样式配置
     * 定义不同类型浮动操作按钮的样式
     */
    object FloatingActionButton {
        
        /**
         * 标准浮动操作按钮样式
         * 用于主要操作
         */
        @Composable
        fun standardFabColors(): FloatingActionButtonColors {
            return FloatingActionButtonDefaults.containerColor(
                containerColor = MaterialTheme.colorScheme.primaryContainer,       // 容器颜色使用主色调容器
                contentColor = MaterialTheme.colorScheme.onPrimaryContainer        // 内容颜色使用主色调容器上的文字色
            )
        }
        
        /**
         * 小型浮动操作按钮样式
         * 用于次要操作
         */
        @Composable
        fun smallFabColors(): FloatingActionButtonColors {
            return FloatingActionButtonDefaults.containerColor(
                containerColor = MaterialTheme.colorScheme.secondaryContainer,     // 容器颜色使用次要色调容器
                contentColor = MaterialTheme.colorScheme.onSecondaryContainer      // 内容颜色使用次要色调容器上的文字色
            )
        }
    }
    
    /**
     * 应用栏样式配置
     * 定义不同类型应用栏的样式
     */
    object AppBar {
        
        /**
         * 顶部应用栏样式
         * 用于页面标题栏
         */
        @Composable
        fun topAppBarColors(): TopAppBarColors {
            return TopAppBarDefaults.topAppBarColors(
                containerColor = MaterialTheme.colorScheme.surface,                // 容器颜色使用表面色
                titleContentColor = MaterialTheme.colorScheme.onSurface,           // 标题内容颜色
                navigationIconContentColor = MaterialTheme.colorScheme.onSurface,  // 导航图标颜色
                actionIconContentColor = MaterialTheme.colorScheme.onSurface       // 操作图标颜色
            )
        }
        
        /**
         * 中等顶部应用栏样式
         * 用于需要更多空间的标题栏
         */
        @Composable
        fun mediumTopAppBarColors(): TopAppBarColors {
            return TopAppBarDefaults.mediumTopAppBarColors(
                containerColor = MaterialTheme.colorScheme.surface,                // 容器颜色使用表面色
                titleContentColor = MaterialTheme.colorScheme.onSurface,           // 标题内容颜色
                navigationIconContentColor = MaterialTheme.colorScheme.onSurface,  // 导航图标颜色
                actionIconContentColor = MaterialTheme.colorScheme.onSurface       // 操作图标颜色
            )
        }
        
        /**
         * 大型顶部应用栏样式
         * 用于重要页面的大标题栏
         */
        @Composable
        fun largeTopAppBarColors(): TopAppBarColors {
            return TopAppBarDefaults.largeTopAppBarColors(
                containerColor = MaterialTheme.colorScheme.surface,                // 容器颜色使用表面色
                titleContentColor = MaterialTheme.colorScheme.onSurface,           // 标题内容颜色
                navigationIconContentColor = MaterialTheme.colorScheme.onSurface,  // 导航图标颜色
                actionIconContentColor = MaterialTheme.colorScheme.onSurface       // 操作图标颜色
            )
        }
    }
    
    /**
     * 底部导航栏样式配置
     * 定义底部导航栏的样式
     */
    object BottomNavigation {
        
        /**
         * 标准底部导航栏样式
         * 用于主要导航
         */
        @Composable
        fun standardNavigationBarItemColors(): NavigationBarItemColors {
            return NavigationBarItemDefaults.colors(
                selectedIconColor = MaterialTheme.colorScheme.primary,             // 选中状态图标颜色
                selectedTextColor = MaterialTheme.colorScheme.primary,             // 选中状态文字颜色
                unselectedIconColor = MaterialTheme.colorScheme.onSurfaceVariant,  // 未选中状态图标颜色
                unselectedTextColor = MaterialTheme.colorScheme.onSurfaceVariant,  // 未选中状态文字颜色
                indicatorColor = MaterialTheme.colorScheme.secondaryContainer      // 指示器颜色
            )
        }
    }
}

/**
 * 响应式样式扩展
 * 根据屏幕尺寸提供不同的样式配置
 */
object ResponsiveStyles {
    
    /**
     * 获取响应式内边距
     * 根据屏幕尺寸返回合适的内边距
     */
    @Composable
    fun getResponsivePadding(): PaddingValues {
        val windowSizeInfo = currentWindowSizeInfo()  // 获取当前窗口尺寸信息
        return when (windowSizeInfo.screenSize) {     // 根据屏幕尺寸返回不同内边距
            ScreenSize.COMPACT -> PaddingValues(16.dp)      // 紧凑屏幕使用16dp内边距
            ScreenSize.MEDIUM -> PaddingValues(24.dp)       // 中等屏幕使用24dp内边距
            ScreenSize.EXPANDED -> PaddingValues(32.dp)     // 扩展屏幕使用32dp内边距
        }
    }
    
    /**
     * 获取响应式间距
     * 根据屏幕尺寸返回合适的间距
     */
    @Composable
    fun getResponsiveSpacing(): Dp {
        val windowSizeInfo = currentWindowSizeInfo()  // 获取当前窗口尺寸信息
        return when (windowSizeInfo.screenSize) {     // 根据屏幕尺寸返回不同间距
            ScreenSize.COMPACT -> 8.dp                      // 紧凑屏幕使用8dp间距
            ScreenSize.MEDIUM -> 12.dp                      // 中等屏幕使用12dp间距
            ScreenSize.EXPANDED -> 16.dp                    // 扩展屏幕使用16dp间距
        }
    }
    
    /**
     * 获取响应式文字样式
     * 根据屏幕尺寸和用户设置返回合适的文字样式
     */
    @Composable
    fun getResponsiveTextStyle(baseStyle: TextStyle): TextStyle {
        val themeConfig = currentThemeConfig()        // 获取当前主题配置
        val windowSizeInfo = currentWindowSizeInfo()  // 获取当前窗口尺寸信息
        
        // 计算字体缩放比例
        val fontScale = themeConfig.fontSize.scale *  // 用户设置的字体缩放
            when (windowSizeInfo.screenSize) {        // 根据屏幕尺寸调整
                ScreenSize.COMPACT -> 1.0f                  // 紧凑屏幕不额外缩放
                ScreenSize.MEDIUM -> 1.1f                   // 中等屏幕放大10%
                ScreenSize.EXPANDED -> 1.2f                 // 扩展屏幕放大20%
            }
        
        return baseStyle.copy(                        // 返回调整后的文字样式
            fontSize = baseStyle.fontSize * fontScale  // 应用字体缩放
        )
    }
}