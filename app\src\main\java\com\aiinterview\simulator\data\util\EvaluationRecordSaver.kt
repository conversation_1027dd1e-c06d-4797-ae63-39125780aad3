package com.aiinterview.simulator.data.util

import com.aiinterview.simulator.data.repository.EvaluationRecordRepository
import com.aiinterview.simulator.data.model.EvaluationRecordModel
import com.aiinterview.simulator.data.model.InterviewEvaluation
import com.aiinterview.simulator.domain.util.Resource
import kotlinx.coroutines.flow.Flow
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 评价记录保存工具类
 * 用于将面试评价结果保存为评价记录
 */
@Singleton
class EvaluationRecordSaver @Inject constructor(
    private val evaluationRecordRepository: EvaluationRecordRepository
) {
    
    /**
     * 保存面试评价为评价记录
     */
    suspend fun saveEvaluationRecord(
        sessionId: String,
        userId: String,
        positionId: String,
        positionName: String,
        evaluation: InterviewEvaluation
    ): Flow<Resource<Unit>> {
        val record = EvaluationRecordModel(
            id = UUID.randomUUID().toString(),
            sessionId = sessionId,
            userId = userId,
            positionId = positionId,
            positionName = positionName,
            overallScore = evaluation.overallScore,
            dimensionScores = evaluation.dimensions,
            feedback = evaluation.feedback,
            suggestions = evaluation.suggestions,
            createdAt = evaluation.createdAt,
            isSynced = false,
            syncedAt = null
        )
        
        return evaluationRecordRepository.saveEvaluationRecord(record)
    }
    
    /**
     * 从面试会话保存评价记录
     */
    suspend fun saveEvaluationRecordFromSession(
        sessionId: String,
        userId: String,
        positionId: String,
        positionName: String,
        overallScore: Double,
        dimensionScores: Map<String, com.aiinterview.simulator.data.model.DimensionScore>,
        feedback: String,
        suggestions: List<String>
    ): Flow<Resource<Unit>> {
        val record = EvaluationRecordModel(
            id = UUID.randomUUID().toString(),
            sessionId = sessionId,
            userId = userId,
            positionId = positionId,
            positionName = positionName,
            overallScore = overallScore,
            dimensionScores = dimensionScores,
            feedback = feedback,
            suggestions = suggestions,
            createdAt = System.currentTimeMillis(),
            isSynced = false,
            syncedAt = null
        )
        
        return evaluationRecordRepository.saveEvaluationRecord(record)
    }
}