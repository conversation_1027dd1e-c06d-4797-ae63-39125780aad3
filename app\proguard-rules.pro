# AI面试模拟器 ProGuard 混淆规则配置
# 此文件包含项目特定的ProGuard规则，用于代码混淆和优化

# ================================
# 基础配置
# ================================

# 保留行号信息，便于调试崩溃日志
-keepattributes SourceFile,LineNumberTable

# 保留注解信息
-keepattributes *Annotation*

# 保留泛型信息
-keepattributes Signature

# 保留异常信息
-keepattributes Exceptions

# 保留内部类信息
-keepattributes InnerClasses,EnclosingMethod

# ================================
# Android 基础组件
# ================================

# 保留Activity、Service、BroadcastReceiver等组件
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider

# 保留View相关类
-keep public class * extends android.view.View {
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
    public void set*(...);
    *** get*();
}

# ================================
# Kotlin 相关
# ================================

# 保留Kotlin协程相关
-keep,allowobfuscation,allowshrinking interface kotlin.coroutines.Continuation
-keep class kotlin.coroutines.** { *; }
-keep class kotlinx.coroutines.** { *; }

# 保留Kotlin元数据
-keep class kotlin.Metadata { *; }

# ================================
# Jetpack Compose
# ================================

# 保留Compose相关类
-keep class androidx.compose.** { *; }
-keep class androidx.compose.runtime.** { *; }
-keep class androidx.compose.ui.** { *; }

# 保留Composable函数
-keep @androidx.compose.runtime.Composable class * { *; }

# ================================
# 网络请求相关 (Retrofit + OkHttp)
# ================================

# Retrofit接口保留
-keep,allowobfuscation,allowshrinking interface retrofit2.Call
-keep,allowobfuscation,allowshrinking class retrofit2.Response
-keep,allowobfuscation,allowshrinking class retrofit2.Callback

# 保留Retrofit服务接口
-keep interface com.aiinterview.simulator.data.api.** { *; }

# OkHttp相关
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-dontwarn okhttp3.**
-dontwarn okio.**

# Gson相关
-keep class com.google.gson.** { *; }
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# ================================
# 数据库相关 (Room)
# ================================

# 保留Room实体类
-keep class com.aiinterview.simulator.data.model.** { *; }
-keep class com.aiinterview.simulator.data.entity.** { *; }

# 保留Room DAO接口
-keep interface com.aiinterview.simulator.data.dao.** { *; }

# 保留Room数据库类
-keep class com.aiinterview.simulator.data.database.** { *; }

# Room注解处理
-keep class androidx.room.** { *; }
-dontwarn androidx.room.**

# ================================
# 依赖注入 (Hilt)
# ================================

# 保留Hilt相关类
-keep class dagger.hilt.** { *; }
-keep class javax.inject.** { *; }
-keep class dagger.** { *; }

# 保留Hilt模块
-keep class com.aiinterview.simulator.di.** { *; }

# ================================
# 数据传输对象 (DTO)
# ================================

# 保留所有DTO类，防止序列化/反序列化出错
-keep class com.aiinterview.simulator.data.dto.** { *; }
-keep class com.aiinterview.simulator.data.request.** { *; }
-keep class com.aiinterview.simulator.data.response.** { *; }

# ================================
# 语音识别和AI服务相关
# ================================

# 保留语音识别相关类
-keep class com.aiinterview.simulator.data.speech.** { *; }

# 保留AI服务相关类
-keep class com.aiinterview.simulator.data.ai.** { *; }

# ================================
# 第三方SDK
# ================================

# 百度语音识别SDK
-keep class com.baidu.speech.** { *; }
-dontwarn com.baidu.speech.**

# 腾讯云SDK
-keep class com.tencent.** { *; }
-dontwarn com.tencent.**

# 讯飞语音SDK
-keep class com.iflytek.** { *; }
-dontwarn com.iflytek.**

# ================================
# 序列化相关
# ================================

# 保留Parcelable实现
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# 保留Serializable实现
-keep class * implements java.io.Serializable { *; }

# ================================
# 反射相关
# ================================

# 保留使用反射的类
-keepclassmembers class * {
    @com.google.gson.annotations.SerializedName <fields>;
}

# ================================
# 自定义规则
# ================================

# 保留枚举类
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# 保留native方法
-keepclasseswithmembernames class * {
    native <methods>;
}

# 保留自定义View的构造方法
-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
}

# ================================
# 警告忽略
# ================================

# 忽略一些常见的警告
-dontwarn javax.annotation.**
-dontwarn javax.inject.**
-dontwarn sun.misc.Unsafe
-dontwarn java.lang.invoke.**

# ================================
# 优化配置
# ================================

# 启用优化
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification

# 不混淆类名（可选，便于调试）
# -keepnames class ** { *; }

# 混淆时不使用大小写混合类名
-dontusemixedcaseclassnames

# 不跳过非公共的库类
-dontskipnonpubliclibraryclasses

# 打印混淆的详细信息
-verbose