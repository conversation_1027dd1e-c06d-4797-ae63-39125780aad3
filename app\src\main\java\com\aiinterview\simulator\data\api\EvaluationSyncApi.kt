package com.aiinterview.simulator.data.api

import com.aiinterview.simulator.data.dto.request.SyncEvaluationRecordsRequest
import com.aiinterview.simulator.data.dto.response.ApiResponse
import com.aiinterview.simulator.data.dto.response.SyncEvaluationRecordsResponse
import com.aiinterview.simulator.data.model.EvaluationRecordModel
import retrofit2.http.*

/**
 * 评价记录云端同步API接口
 */
interface EvaluationSyncApi {
    
    /**
     * 上传评价记录到云端
     */
    @POST("/api/evaluation/sync/upload")
    suspend fun uploadEvaluationRecords(
        @Body request: SyncEvaluationRecordsRequest
    ): ApiResponse<SyncEvaluationRecordsResponse>
    
    /**
     * 从云端下载评价记录
     */
    @GET("/api/evaluation/sync/download")
    suspend fun downloadEvaluationRecords(
        @Query("userId") userId: String,
        @Query("lastSyncTime") lastSyncTime: Long? = null
    ): ApiResponse<List<EvaluationRecordModel>>
    
    /**
     * 获取云端评价记录的最后更新时间
     */
    @GET("/api/evaluation/sync/last-update")
    suspend fun getLastUpdateTime(
        @Query("userId") userId: String
    ): ApiResponse<Long>
    
    /**
     * 删除云端的评价记录
     */
    @DELETE("/api/evaluation/sync/{recordId}")
    suspend fun deleteCloudEvaluationRecord(
        @Path("recordId") recordId: String
    ): ApiResponse<Unit>
    
    /**
     * 批量删除云端的评价记录
     */
    @POST("/api/evaluation/sync/batch-delete")
    suspend fun batchDeleteCloudEvaluationRecords(
        @Body recordIds: List<String>
    ): ApiResponse<Unit>
}