package com.aiinterview.simulator.di

import android.content.Context
import com.aiinterview.simulator.data.network.CoroutineNetworkRetry
import com.aiinterview.simulator.data.performance.MemoryOptimizationManager
import com.aiinterview.simulator.data.performance.StartupOptimizationManager
import com.aiinterview.simulator.data.util.MediaCompressionUtil
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 性能优化依赖注入模块
 * 提供性能优化相关的单例对象
 */
@Module
@InstallIn(SingletonComponent::class)
object PerformanceModule {
    
    /**
     * 提供媒体压缩工具单例
     * @param context 应用上下文
     * @return MediaCompressionUtil实例
     */
    @Provides
    @Singleton
    fun provideMediaCompressionUtil(@ApplicationContext context: Context): MediaCompressionUtil {
        return MediaCompressionUtil(context) // 创建媒体压缩工具实例
    }
    
    /**
     * 提供内存优化管理器单例
     * @param context 应用上下文
     * @return MemoryOptimizationManager实例
     */
    @Provides
    @Singleton
    fun provideMemoryOptimizationManager(@ApplicationContext context: Context): MemoryOptimizationManager {
        return MemoryOptimizationManager(context) // 创建内存优化管理器实例
    }
    
    /**
     * 提供启动优化管理器单例
     * @param context 应用上下文
     * @return StartupOptimizationManager实例
     */
    @Provides
    @Singleton
    fun provideStartupOptimizationManager(@ApplicationContext context: Context): StartupOptimizationManager {
        return StartupOptimizationManager(context) // 创建启动优化管理器实例
    }
    
    /**
     * 提供协程网络重试工具单例
     * @return CoroutineNetworkRetry实例
     */
    @Provides
    @Singleton
    fun provideCoroutineNetworkRetry(): CoroutineNetworkRetry {
        return CoroutineNetworkRetry() // 创建协程网络重试工具实例
    }
}