package com.aiinterview.simulator.data.util

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.media.MediaMetadataRetriever
import android.net.Uri
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 媒体文件压缩工具类
 * 用于压缩图片和音频文件以优化存储空间和传输效率
 */
@Singleton
class MediaCompressionUtil @Inject constructor(
    private val context: Context // 注入应用上下文
) {
    
    companion object {
        private const val DEFAULT_IMAGE_QUALITY = 80 // 默认图片压缩质量（0-100）
        private const val MAX_IMAGE_WIDTH = 1080 // 最大图片宽度
        private const val MAX_IMAGE_HEIGHT = 1920 // 最大图片高度
        private const val AUDIO_COMPRESSION_RATIO = 0.7f // 音频压缩比例
    }
    
    /**
     * 压缩图片文件
     * @param inputFile 输入的图片文件
     * @param outputFile 输出的压缩图片文件
     * @param quality 压缩质量（0-100）
     * @return 压缩是否成功
     */
    suspend fun compressImage(
        inputFile: File,
        outputFile: File,
        quality: Int = DEFAULT_IMAGE_QUALITY
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            // 获取原始图片的尺寸信息
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true // 只解码边界，不加载实际图片数据
            }
            BitmapFactory.decodeFile(inputFile.absolutePath, options)
            
            // 计算合适的采样率以减少内存使用
            val sampleSize = calculateInSampleSize(options, MAX_IMAGE_WIDTH, MAX_IMAGE_HEIGHT)
            
            // 使用计算出的采样率解码图片
            val finalOptions = BitmapFactory.Options().apply {
                inSampleSize = sampleSize // 设置采样率
                inPreferredConfig = Bitmap.Config.RGB_565 // 使用RGB_565格式减少内存占用
            }
            
            // 解码压缩后的图片
            val bitmap = BitmapFactory.decodeFile(inputFile.absolutePath, finalOptions)
                ?: return@withContext false // 如果解码失败则返回false
            
            // 将压缩后的图片保存到输出文件
            FileOutputStream(outputFile).use { outputStream ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, quality, outputStream) // 以JPEG格式压缩保存
            }
            
            // 回收bitmap内存
            bitmap.recycle()
            
            true // 压缩成功
        } catch (e: Exception) {
            e.printStackTrace() // 打印异常信息
            false // 压缩失败
        }
    }
    
    /**
     * 压缩图片字节数组
     * @param imageBytes 原始图片字节数组
     * @param quality 压缩质量
     * @return 压缩后的字节数组
     */
    suspend fun compressImageBytes(
        imageBytes: ByteArray,
        quality: Int = DEFAULT_IMAGE_QUALITY
    ): ByteArray? = withContext(Dispatchers.IO) {
        try {
            // 从字节数组解码图片
            val bitmap = BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.size)
                ?: return@withContext null // 解码失败返回null
            
            // 创建字节输出流
            val outputStream = ByteArrayOutputStream()
            
            // 压缩图片到输出流
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, outputStream)
            
            // 回收bitmap内存
            bitmap.recycle()
            
            // 返回压缩后的字节数组
            outputStream.toByteArray()
        } catch (e: Exception) {
            e.printStackTrace() // 打印异常信息
            null // 压缩失败返回null
        }
    }
    
    /**
     * 获取音频文件信息
     * @param audioFile 音频文件
     * @return 音频文件信息
     */
    suspend fun getAudioInfo(audioFile: File): AudioInfo? = withContext(Dispatchers.IO) {
        try {
            // 创建媒体元数据检索器
            val retriever = MediaMetadataRetriever()
            retriever.setDataSource(audioFile.absolutePath) // 设置音频文件路径
            
            // 获取音频时长（毫秒）
            val duration = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)?.toLongOrNull() ?: 0L
            
            // 获取音频比特率
            val bitrate = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_BITRATE)?.toIntOrNull() ?: 0
            
            // 获取音频采样率
            val sampleRate = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_CAPTURE_FRAMERATE)?.toIntOrNull() ?: 0
            
            // 释放检索器资源
            retriever.release()
            
            // 返回音频信息对象
            AudioInfo(
                duration = duration,
                bitrate = bitrate,
                sampleRate = sampleRate,
                fileSize = audioFile.length()
            )
        } catch (e: Exception) {
            e.printStackTrace() // 打印异常信息
            null // 获取信息失败返回null
        }
    }
    
    /**
     * 估算音频压缩后的大小
     * @param originalFile 原始音频文件
     * @param compressionRatio 压缩比例
     * @return 预估的压缩后文件大小（字节）
     */
    suspend fun estimateCompressedAudioSize(
        originalFile: File,
        compressionRatio: Float = AUDIO_COMPRESSION_RATIO
    ): Long = withContext(Dispatchers.IO) {
        try {
            val originalSize = originalFile.length() // 获取原始文件大小
            (originalSize * compressionRatio).toLong() // 计算压缩后预估大小
        } catch (e: Exception) {
            e.printStackTrace() // 打印异常信息
            0L // 计算失败返回0
        }
    }
    
    /**
     * 检查文件是否需要压缩
     * @param file 待检查的文件
     * @param maxSizeBytes 最大文件大小限制（字节）
     * @return 是否需要压缩
     */
    fun shouldCompress(file: File, maxSizeBytes: Long): Boolean {
        return file.exists() && file.length() > maxSizeBytes // 文件存在且大小超过限制时需要压缩
    }
    
    /**
     * 计算图片采样率
     * @param options 图片选项
     * @param reqWidth 目标宽度
     * @param reqHeight 目标高度
     * @return 采样率
     */
    private fun calculateInSampleSize(
        options: BitmapFactory.Options,
        reqWidth: Int,
        reqHeight: Int
    ): Int {
        val height = options.outHeight // 原始图片高度
        val width = options.outWidth // 原始图片宽度
        var inSampleSize = 1 // 初始采样率为1（不缩放）
        
        // 如果原始尺寸大于目标尺寸，计算合适的采样率
        if (height > reqHeight || width > reqWidth) {
            val halfHeight = height / 2 // 高度的一半
            val halfWidth = width / 2 // 宽度的一半
            
            // 计算最大的采样率，确保压缩后的尺寸仍大于目标尺寸
            while ((halfHeight / inSampleSize) >= reqHeight && (halfWidth / inSampleSize) >= reqWidth) {
                inSampleSize *= 2 // 采样率翻倍
            }
        }
        
        return inSampleSize // 返回计算出的采样率
    }
}

/**
 * 音频文件信息数据类
 */
data class AudioInfo(
    val duration: Long, // 音频时长（毫秒）
    val bitrate: Int, // 比特率
    val sampleRate: Int, // 采样率
    val fileSize: Long // 文件大小（字节）
)