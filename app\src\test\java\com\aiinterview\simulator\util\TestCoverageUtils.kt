package com.aiinterview.simulator.util

import org.junit.runner.RunWith
import org.junit.runners.Suite

/**
 * 测试覆盖率工具类
 * 提供测试覆盖率统计和报告生成功能
 */
object TestCoverageUtils {
    
    /**
     * 生成测试覆盖率报告的配置
     */
    data class CoverageConfig(
        val minCoveragePercentage: Double = 80.0,
        val excludePackages: List<String> = listOf(
            "*.di.*",           // 依赖注入模块
            "*.dto.*",          // 数据传输对象
            "*.model.*",        // 数据模型（部分）
            "*.BuildConfig",    // 构建配置
            "*.*Test*",         // 测试类
            "*.R",              // 资源文件
            "*.R$*"             // 资源文件子类
        ),
        val includePackages: List<String> = listOf(
            "com.aiinterview.simulator.data.repository.*",
            "com.aiinterview.simulator.presentation.viewmodel.*",
            "com.aiinterview.simulator.data.service.*",
            "com.aiinterview.simulator.domain.*"
        )
    )
    
    /**
     * 测试覆盖率报告数据
     */
    data class CoverageReport(
        val totalLines: Int,
        val coveredLines: Int,
        val coveragePercentage: Double,
        val packageCoverage: Map<String, PackageCoverage>,
        val uncoveredClasses: List<String>
    ) {
        val isPassingThreshold: Boolean
            get() = coveragePercentage >= 80.0
    }
    
    /**
     * 包级别的覆盖率数据
     */
    data class PackageCoverage(
        val packageName: String,
        val totalLines: Int,
        val coveredLines: Int,
        val coveragePercentage: Double,
        val classes: List<ClassCoverage>
    )
    
    /**
     * 类级别的覆盖率数据
     */
    data class ClassCoverage(
        val className: String,
        val totalLines: Int,
        val coveredLines: Int,
        val coveragePercentage: Double,
        val methods: List<MethodCoverage>
    )
    
    /**
     * 方法级别的覆盖率数据
     */
    data class MethodCoverage(
        val methodName: String,
        val totalLines: Int,
        val coveredLines: Int,
        val coveragePercentage: Double,
        val isCovered: Boolean
    )
    
    /**
     * 生成覆盖率报告的HTML格式
     */
    fun generateHtmlReport(report: CoverageReport): String {
        return buildString {
            appendLine("<!DOCTYPE html>")
            appendLine("<html>")
            appendLine("<head>")
            appendLine("    <title>AI面试模拟器 - 测试覆盖率报告</title>")
            appendLine("    <meta charset='UTF-8'>")
            appendLine("    <style>")
            appendLine("        body { font-family: Arial, sans-serif; margin: 20px; }")
            appendLine("        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }")
            appendLine("        .summary { margin: 20px 0; }")
            appendLine("        .package { margin: 10px 0; border: 1px solid #ddd; border-radius: 5px; }")
            appendLine("        .package-header { background-color: #e9e9e9; padding: 10px; font-weight: bold; }")
            appendLine("        .class-item { padding: 5px 20px; border-bottom: 1px solid #eee; }")
            appendLine("        .coverage-high { color: green; }")
            appendLine("        .coverage-medium { color: orange; }")
            appendLine("        .coverage-low { color: red; }")
            appendLine("        .progress-bar { width: 100%; height: 20px; background-color: #f0f0f0; border-radius: 10px; }")
            appendLine("        .progress-fill { height: 100%; border-radius: 10px; }")
            appendLine("    </style>")
            appendLine("</head>")
            appendLine("<body>")
            
            // 头部信息
            appendLine("    <div class='header'>")
            appendLine("        <h1>AI面试模拟器 - 测试覆盖率报告</h1>")
            appendLine("        <p>生成时间: ${java.time.LocalDateTime.now()}</p>")
            appendLine("    </div>")
            
            // 总体摘要
            appendLine("    <div class='summary'>")
            appendLine("        <h2>总体覆盖率</h2>")
            appendLine("        <p>总行数: ${report.totalLines}</p>")
            appendLine("        <p>覆盖行数: ${report.coveredLines}</p>")
            appendLine("        <p>覆盖率: <span class='${getCoverageClass(report.coveragePercentage)}'>${String.format("%.2f", report.coveragePercentage)}%</span></p>")
            appendLine("        <div class='progress-bar'>")
            appendLine("            <div class='progress-fill' style='width: ${report.coveragePercentage}%; background-color: ${getCoverageColor(report.coveragePercentage)};'></div>")
            appendLine("        </div>")
            appendLine("        <p>是否达标: ${if (report.isPassingThreshold) "✅ 是" else "❌ 否"}</p>")
            appendLine("    </div>")
            
            // 包级别详情
            appendLine("    <div class='packages'>")
            appendLine("        <h2>包级别覆盖率</h2>")
            report.packageCoverage.forEach { (packageName, packageCoverage) ->
                appendLine("        <div class='package'>")
                appendLine("            <div class='package-header'>")
                appendLine("                $packageName - ${String.format("%.2f", packageCoverage.coveragePercentage)}%")
                appendLine("            </div>")
                packageCoverage.classes.forEach { classCoverage ->
                    appendLine("            <div class='class-item'>")
                    appendLine("                ${classCoverage.className}: <span class='${getCoverageClass(classCoverage.coveragePercentage)}'>${String.format("%.2f", classCoverage.coveragePercentage)}%</span>")
                    appendLine("            </div>")
                }
                appendLine("        </div>")
            }
            appendLine("    </div>")
            
            // 未覆盖的类
            if (report.uncoveredClasses.isNotEmpty()) {
                appendLine("    <div class='uncovered'>")
                appendLine("        <h2>未覆盖的类</h2>")
                appendLine("        <ul>")
                report.uncoveredClasses.forEach { className ->
                    appendLine("            <li>$className</li>")
                }
                appendLine("        </ul>")
                appendLine("    </div>")
            }
            
            appendLine("</body>")
            appendLine("</html>")
        }
    }
    
    /**
     * 根据覆盖率获取CSS类名
     */
    private fun getCoverageClass(percentage: Double): String {
        return when {
            percentage >= 80.0 -> "coverage-high"
            percentage >= 60.0 -> "coverage-medium"
            else -> "coverage-low"
        }
    }
    
    /**
     * 根据覆盖率获取颜色
     */
    private fun getCoverageColor(percentage: Double): String {
        return when {
            percentage >= 80.0 -> "#4CAF50"  // 绿色
            percentage >= 60.0 -> "#FF9800"  // 橙色
            else -> "#F44336"                // 红色
        }
    }
    
    /**
     * 验证覆盖率是否达标
     */
    fun validateCoverage(report: CoverageReport, config: CoverageConfig): ValidationResult {
        val issues = mutableListOf<String>()
        
        // 检查总体覆盖率
        if (report.coveragePercentage < config.minCoveragePercentage) {
            issues.add("总体覆盖率 ${String.format("%.2f", report.coveragePercentage)}% 低于要求的 ${config.minCoveragePercentage}%")
        }
        
        // 检查包级别覆盖率
        report.packageCoverage.forEach { (packageName, packageCoverage) ->
            if (packageCoverage.coveragePercentage < config.minCoveragePercentage) {
                issues.add("包 $packageName 的覆盖率 ${String.format("%.2f", packageCoverage.coveragePercentage)}% 低于要求")
            }
        }
        
        // 检查关键类的覆盖率
        val criticalClasses = listOf("Repository", "ViewModel", "Service")
        report.packageCoverage.values.forEach { packageCoverage ->
            packageCoverage.classes.forEach { classCoverage ->
                val isCritical = criticalClasses.any { classCoverage.className.contains(it) }
                if (isCritical && classCoverage.coveragePercentage < 90.0) {
                    issues.add("关键类 ${classCoverage.className} 的覆盖率 ${String.format("%.2f", classCoverage.coveragePercentage)}% 低于90%")
                }
            }
        }
        
        return ValidationResult(
            isValid = issues.isEmpty(),
            issues = issues,
            totalCoverage = report.coveragePercentage,
            threshold = config.minCoveragePercentage
        )
    }
    
    /**
     * 覆盖率验证结果
     */
    data class ValidationResult(
        val isValid: Boolean,
        val issues: List<String>,
        val totalCoverage: Double,
        val threshold: Double
    ) {
        fun printReport() {
            println("=== 测试覆盖率验证报告 ===")
            println("总体覆盖率: ${String.format("%.2f", totalCoverage)}%")
            println("要求阈值: ${String.format("%.2f", threshold)}%")
            println("验证结果: ${if (isValid) "✅ 通过" else "❌ 未通过"}")
            
            if (issues.isNotEmpty()) {
                println("\n发现的问题:")
                issues.forEachIndexed { index, issue ->
                    println("${index + 1}. $issue")
                }
            }
            println("========================")
        }
    }
}

/**
 * 测试套件 - 用于运行所有单元测试
 */
@RunWith(Suite::class)
@Suite.SuiteClasses(
    // Repository测试
    com.aiinterview.simulator.data.repository.AuthRepositoryTest::class,
    com.aiinterview.simulator.data.repository.InterviewRepositoryTest::class,
    com.aiinterview.simulator.data.repository.ProgressAnalysisRepositoryTest::class,
    
    // ViewModel测试
    com.aiinterview.simulator.presentation.viewmodel.AuthViewModelTest::class,
    com.aiinterview.simulator.presentation.viewmodel.InterviewViewModelTest::class,
    
    // Service测试
    com.aiinterview.simulator.data.speech.SpeechRecognitionServiceTest::class
)
class AllUnitTestsSuite

/**
 * 测试运行器 - 用于执行测试并生成覆盖率报告
 */
object TestRunner {
    
    /**
     * 运行所有测试并生成覆盖率报告
     */
    fun runTestsWithCoverage(): TestCoverageUtils.ValidationResult {
        println("开始运行单元测试...")
        
        // 这里应该集成实际的测试运行器和覆盖率工具
        // 由于是示例，我们创建模拟数据
        val mockReport = createMockCoverageReport()
        val config = TestCoverageUtils.CoverageConfig()
        
        // 生成HTML报告
        val htmlReport = TestCoverageUtils.generateHtmlReport(mockReport)
        // 在实际项目中，这里应该将报告写入文件
        println("HTML覆盖率报告已生成")
        
        // 验证覆盖率
        val validationResult = TestCoverageUtils.validateCoverage(mockReport, config)
        validationResult.printReport()
        
        return validationResult
    }
    
    /**
     * 创建模拟的覆盖率报告（用于演示）
     */
    private fun createMockCoverageReport(): TestCoverageUtils.CoverageReport {
        val repositoryPackage = TestCoverageUtils.PackageCoverage(
            packageName = "com.aiinterview.simulator.data.repository",
            totalLines = 500,
            coveredLines = 425,
            coveragePercentage = 85.0,
            classes = listOf(
                TestCoverageUtils.ClassCoverage(
                    className = "AuthRepositoryImpl",
                    totalLines = 150,
                    coveredLines = 135,
                    coveragePercentage = 90.0,
                    methods = emptyList()
                ),
                TestCoverageUtils.ClassCoverage(
                    className = "InterviewRepositoryImpl",
                    totalLines = 200,
                    coveredLines = 170,
                    coveragePercentage = 85.0,
                    methods = emptyList()
                )
            )
        )
        
        val viewModelPackage = TestCoverageUtils.PackageCoverage(
            packageName = "com.aiinterview.simulator.presentation.viewmodel",
            totalLines = 300,
            coveredLines = 255,
            coveragePercentage = 85.0,
            classes = listOf(
                TestCoverageUtils.ClassCoverage(
                    className = "AuthViewModel",
                    totalLines = 100,
                    coveredLines = 90,
                    coveragePercentage = 90.0,
                    methods = emptyList()
                ),
                TestCoverageUtils.ClassCoverage(
                    className = "InterviewViewModel",
                    totalLines = 120,
                    coveredLines = 102,
                    coveragePercentage = 85.0,
                    methods = emptyList()
                )
            )
        )
        
        return TestCoverageUtils.CoverageReport(
            totalLines = 800,
            coveredLines = 680,
            coveragePercentage = 85.0,
            packageCoverage = mapOf(
                "repository" to repositoryPackage,
                "viewmodel" to viewModelPackage
            ),
            uncoveredClasses = listOf(
                "com.aiinterview.simulator.data.service.AudioProcessingService",
                "com.aiinterview.simulator.util.NetworkUtils"
            )
        )
    }
}