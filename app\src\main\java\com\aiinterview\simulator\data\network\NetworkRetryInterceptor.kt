package com.aiinterview.simulator.data.network

import kotlinx.coroutines.delay
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 网络重试拦截器
 * 在网络请求失败时自动重试，提高请求成功率
 */
@Singleton
class NetworkRetryInterceptor @Inject constructor() : Interceptor {
    
    companion object {
        private const val MAX_RETRY_COUNT = 3 // 最大重试次数
        private const val INITIAL_RETRY_DELAY = 1000L // 初始重试延迟（毫秒）
        private const val RETRY_DELAY_MULTIPLIER = 2.0 // 重试延迟倍数（指数退避）
    }
    
    /**
     * 拦截网络请求并实现重试逻辑
     */
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request() // 获取原始请求
        var response: Response? = null // 初始化响应为null
        var exception: IOException? = null // 初始化异常为null
        var retryCount = 0 // 初始化重试计数器
        
        // 重试循环
        while (retryCount <= MAX_RETRY_COUNT) {
            try {
                // 如果不是第一次请求，需要等待重试延迟
                if (retryCount > 0) {
                    val delay = calculateRetryDelay(retryCount) // 计算重试延迟时间
                    Thread.sleep(delay) // 等待指定时间后重试
                }
                
                response?.close() // 关闭之前的响应（如果存在）
                response = chain.proceed(request) // 执行网络请求
                
                // 检查响应是否成功
                if (response.isSuccessful) {
                    return response // 请求成功，返回响应
                } else if (!shouldRetry(response.code)) {
                    return response // 不应该重试的错误码，直接返回响应
                }
                
            } catch (e: IOException) {
                exception = e // 保存异常信息
                
                // 检查是否应该重试此类异常
                if (!shouldRetryException(e)) {
                    throw e // 不应该重试的异常，直接抛出
                }
            }
            
            retryCount++ // 增加重试计数
        }
        
        // 所有重试都失败了
        response?.let { return it } // 如果有响应，返回最后一次的响应
        throw exception ?: IOException("网络请求失败，已达到最大重试次数") // 抛出异常
    }
    
    /**
     * 计算重试延迟时间（指数退避算法）
     * @param retryCount 当前重试次数
     * @return 延迟时间（毫秒）
     */
    private fun calculateRetryDelay(retryCount: Int): Long {
        return (INITIAL_RETRY_DELAY * Math.pow(RETRY_DELAY_MULTIPLIER, (retryCount - 1).toDouble())).toLong()
    }
    
    /**
     * 判断HTTP状态码是否应该重试
     * @param statusCode HTTP状态码
     * @return 是否应该重试
     */
    private fun shouldRetry(statusCode: Int): Boolean {
        return when (statusCode) {
            408, // Request Timeout - 请求超时
            429, // Too Many Requests - 请求过多
            500, // Internal Server Error - 服务器内部错误
            502, // Bad Gateway - 网关错误
            503, // Service Unavailable - 服务不可用
            504  // Gateway Timeout - 网关超时
            -> true
            else -> false // 其他状态码不重试
        }
    }
    
    /**
     * 判断异常是否应该重试
     * @param exception 捕获的异常
     * @return 是否应该重试
     */
    private fun shouldRetryException(exception: IOException): Boolean {
        return when (exception) {
            is SocketTimeoutException -> true // 套接字超时异常，应该重试
            is UnknownHostException -> true // 未知主机异常，应该重试
            is IOException -> {
                // 检查IOException的具体消息
                val message = exception.message?.lowercase() ?: ""
                message.contains("timeout") || // 包含超时关键字
                message.contains("connection reset") || // 连接重置
                message.contains("connection refused") || // 连接被拒绝
                message.contains("network is unreachable") // 网络不可达
            }
            else -> false // 其他异常不重试
        }
    }
}

/**
 * 支持协程的网络重试工具类
 */
@Singleton
class CoroutineNetworkRetry @Inject constructor() {
    
    /**
     * 执行带重试的网络请求
     * @param maxRetries 最大重试次数
     * @param initialDelay 初始延迟时间
     * @param maxDelay 最大延迟时间
     * @param factor 延迟倍数
     * @param block 要执行的网络请求块
     * @return 请求结果
     */
    suspend fun <T> executeWithRetry(
        maxRetries: Int = MAX_RETRY_COUNT,
        initialDelay: Long = INITIAL_RETRY_DELAY,
        maxDelay: Long = 10000L,
        factor: Double = RETRY_DELAY_MULTIPLIER,
        block: suspend () -> T
    ): T {
        var currentDelay = initialDelay // 当前延迟时间
        var lastException: Exception? = null // 最后一次异常
        
        // 重试循环
        repeat(maxRetries + 1) { attempt ->
            try {
                return block() // 执行网络请求块
            } catch (e: Exception) {
                lastException = e // 保存异常
                
                // 如果是最后一次尝试，直接抛出异常
                if (attempt == maxRetries) {
                    throw e
                }
                
                // 检查是否应该重试此异常
                if (!shouldRetryException(e)) {
                    throw e // 不应该重试的异常，直接抛出
                }
                
                // 等待重试延迟时间
                delay(currentDelay)
                
                // 计算下次延迟时间（指数退避，但不超过最大延迟）
                currentDelay = (currentDelay * factor).toLong().coerceAtMost(maxDelay)
            }
        }
        
        // 理论上不会执行到这里，但为了类型安全
        throw lastException ?: Exception("未知错误")
    }
    
    /**
     * 判断异常是否应该重试（协程版本）
     */
    private fun shouldRetryException(exception: Exception): Boolean {
        return when (exception) {
            is SocketTimeoutException -> true // 套接字超时
            is UnknownHostException -> true // 未知主机
            is IOException -> {
                val message = exception.message?.lowercase() ?: ""
                message.contains("timeout") || // 超时
                message.contains("connection") || // 连接问题
                message.contains("network") // 网络问题
            }
            else -> false // 其他异常不重试
        }
    }
}