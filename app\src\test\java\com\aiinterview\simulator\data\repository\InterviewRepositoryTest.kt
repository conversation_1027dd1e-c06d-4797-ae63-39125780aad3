package com.aiinterview.simulator.data.repository

import app.cash.turbine.test
import com.aiinterview.simulator.data.api.InterviewApi
import com.aiinterview.simulator.data.dao.InterviewSessionDao
import com.aiinterview.simulator.data.dao.PositionDao
import com.aiinterview.simulator.data.dto.request.AnswerRequest
import com.aiinterview.simulator.data.dto.request.StartInterviewRequest
import com.aiinterview.simulator.data.dto.response.InterviewResultResponse
import com.aiinterview.simulator.data.dto.response.InterviewSessionResponse
import com.aiinterview.simulator.data.dto.response.QuestionResponse
import com.aiinterview.simulator.data.model.InterviewSession
import com.aiinterview.simulator.data.model.Position
import com.aiinterview.simulator.domain.util.Resource
import com.aiinterview.simulator.util.MainCoroutineRule
import com.aiinterview.simulator.util.MockDataFactory
import com.aiinterview.simulator.util.TestUtils
import com.google.common.truth.Truth.assertThat
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.*
import java.io.IOException

/**
 * InterviewRepository单元测试类
 * 测试面试相关的Repository功能
 */
@ExperimentalCoroutinesApi
class InterviewRepositoryTest {

    @get:Rule
    val mainCoroutineRule = MainCoroutineRule()

    @Mock
    private lateinit var interviewApi: InterviewApi

    @Mock
    private lateinit var positionDao: PositionDao

    @Mock
    private lateinit var interviewSessionDao: InterviewSessionDao

    private lateinit var interviewRepository: InterviewRepository

    // 测试数据
    private val testPosition = MockDataFactory.createMockPosition()
    private val testPositions = listOf(testPosition)
    private val testInterviewSession = MockDataFactory.createMockInterviewSession()

    @Before
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        interviewRepository = InterviewRepositoryImpl(
            interviewApi,
            positionDao,
            interviewSessionDao
        )
    }

    /**
     * 测试获取岗位列表成功场景
     */
    @Test
    fun `getPositions should return success when api call succeeds`() = runTest {
        // Given - 准备测试数据
        val apiResponse = MockDataFactory.createMockApiResponse(testPositions)
        
        // Mock DAO返回空缓存
        whenever(positionDao.getAllPositions()).thenReturn(flowOf(emptyList()))
        // Mock API调用成功
        whenever(interviewApi.getPositions()).thenReturn(apiResponse)

        // When - 获取岗位列表
        interviewRepository.getPositions().test {
            // Then - 验证结果
            // 第一个发射应该是Loading状态
            val loadingResult = awaitItem()
            assertThat(loadingResult).isInstanceOf(Resource.Loading::class.java)

            // 第二个发射应该是Success状态
            val successResult = awaitItem()
            assertThat(successResult).isInstanceOf(Resource.Success::class.java)
            assertThat(successResult.data).isEqualTo(testPositions)

            awaitComplete()
        }

        // 验证交互
        verify(positionDao).getAllPositions()
        verify(interviewApi).getPositions()
        verify(positionDao).insertPositions(testPositions)
    }

    /**
     * 测试获取岗位列表时先返回缓存数据
     */
    @Test
    fun `getPositions should emit cached data first then network data`() = runTest {
        // Given - 准备缓存数据和网络数据
        val cachedPositions = listOf(MockDataFactory.createMockPosition(id = "cached_1"))
        val networkPositions = listOf(MockDataFactory.createMockPosition(id = "network_1"))
        val apiResponse = MockDataFactory.createMockApiResponse(networkPositions)

        whenever(positionDao.getAllPositions()).thenReturn(flowOf(cachedPositions))
        whenever(interviewApi.getPositions()).thenReturn(apiResponse)

        // When - 获取岗位列表
        interviewRepository.getPositions().test {
            // Then - 验证结果顺序
            val loadingResult = awaitItem()
            assertThat(loadingResult).isInstanceOf(Resource.Loading::class.java)

            // 缓存数据
            val cachedResult = awaitItem()
            assertThat(cachedResult).isInstanceOf(Resource.Success::class.java)
            assertThat(cachedResult.data).isEqualTo(cachedPositions)

            // 网络数据
            val networkResult = awaitItem()
            assertThat(networkResult).isInstanceOf(Resource.Success::class.java)
            assertThat(networkResult.data).isEqualTo(networkPositions)

            awaitComplete()
        }
    }

    /**
     * 测试获取岗位列表失败场景
     */
    @Test
    fun `getPositions should return error when api call fails`() = runTest {
        // Given - 准备失败响应
        val errorResponse = MockDataFactory.createMockErrorResponse("服务器错误")
        
        whenever(positionDao.getAllPositions()).thenReturn(flowOf(emptyList()))
        whenever(interviewApi.getPositions()).thenReturn(errorResponse)

        // When - 获取岗位列表
        interviewRepository.getPositions().test {
            // Then - 验证结果
            val loadingResult = awaitItem()
            assertThat(loadingResult).isInstanceOf(Resource.Loading::class.java)

            val errorResult = awaitItem()
            assertThat(errorResult).isInstanceOf(Resource.Error::class.java)
            assertThat(errorResult.message).isEqualTo("服务器错误")

            awaitComplete()
        }
    }

    /**
     * 测试网络异常场景
     */
    @Test
    fun `getPositions should return error when network exception occurs`() = runTest {
        // Given - 模拟网络异常
        whenever(positionDao.getAllPositions()).thenReturn(flowOf(emptyList()))
        whenever(interviewApi.getPositions()).thenThrow(IOException("网络连接失败"))

        // When - 获取岗位列表
        interviewRepository.getPositions().test {
            // Then - 验证结果
            val loadingResult = awaitItem()
            assertThat(loadingResult).isInstanceOf(Resource.Loading::class.java)

            val errorResult = awaitItem()
            assertThat(errorResult).isInstanceOf(Resource.Error::class.java)
            assertThat(errorResult.message).contains("网络")

            awaitComplete()
        }
    }

    /**
     * 测试根据ID获取岗位成功场景
     */
    @Test
    fun `getPositionById should return success when api call succeeds`() = runTest {
        // Given - 准备测试数据
        val positionId = "position_001"
        val apiResponse = MockDataFactory.createMockApiResponse(testPosition)

        // Mock缓存中没有数据
        whenever(positionDao.getPositionById(positionId)).thenReturn(null)
        whenever(interviewApi.getPositionById(positionId)).thenReturn(apiResponse)

        // When - 获取岗位详情
        interviewRepository.getPositionById(positionId).test {
            // Then - 验证结果
            val loadingResult = awaitItem()
            assertThat(loadingResult).isInstanceOf(Resource.Loading::class.java)

            val successResult = awaitItem()
            assertThat(successResult).isInstanceOf(Resource.Success::class.java)
            assertThat(successResult.data).isEqualTo(testPosition)

            awaitComplete()
        }

        // 验证交互
        verify(positionDao).getPositionById(positionId)
        verify(interviewApi).getPositionById(positionId)
        verify(positionDao).insertPosition(testPosition)
    }

    /**
     * 测试开始面试成功场景
     */
    @Test
    fun `startInterview should return success when api call succeeds`() = runTest {
        // Given - 准备测试数据
        val userId = TestUtils.createTestUserId()
        val positionId = "position_001"
        val expectedRequest = StartInterviewRequest(userId, positionId)
        val sessionResponse = InterviewSessionResponse(
            sessionId = TestUtils.createTestSessionId(),
            questions = listOf(MockDataFactory.createMockQuestion()),
            currentQuestionIndex = 0
        )
        val apiResponse = MockDataFactory.createMockApiResponse(sessionResponse)

        whenever(interviewApi.startInterview(expectedRequest)).thenReturn(apiResponse)

        // When - 开始面试
        interviewRepository.startInterview(userId, positionId).test {
            // Then - 验证结果
            val loadingResult = awaitItem()
            assertThat(loadingResult).isInstanceOf(Resource.Loading::class.java)

            val successResult = awaitItem()
            assertThat(successResult).isInstanceOf(Resource.Success::class.java)
            assertThat(successResult.data).isEqualTo(sessionResponse)

            awaitComplete()
        }

        verify(interviewApi).startInterview(expectedRequest)
    }

    /**
     * 测试提交答案成功场景
     */
    @Test
    fun `submitAnswer should return success when api call succeeds`() = runTest {
        // Given - 准备测试数据
        val sessionId = TestUtils.createTestSessionId()
        val questionId = TestUtils.createTestQuestionId()
        val audioUrl = "https://example.com/audio.mp3"
        val transcription = "这是我的回答"
        val duration = 120
        val expectedRequest = AnswerRequest(sessionId, questionId, audioUrl, transcription, duration)
        val questionResponse = QuestionResponse(
            nextQuestion = MockDataFactory.createMockQuestion(),
            isLastQuestion = false,
            currentQuestionIndex = 1
        )
        val apiResponse = MockDataFactory.createMockApiResponse(questionResponse)

        whenever(interviewApi.submitAnswer(expectedRequest)).thenReturn(apiResponse)

        // When - 提交答案
        interviewRepository.submitAnswer(sessionId, questionId, audioUrl, transcription, duration).test {
            // Then - 验证结果
            val loadingResult = awaitItem()
            assertThat(loadingResult).isInstanceOf(Resource.Loading::class.java)

            val successResult = awaitItem()
            assertThat(successResult).isInstanceOf(Resource.Success::class.java)
            assertThat(successResult.data).isEqualTo(questionResponse)

            awaitComplete()
        }

        verify(interviewApi).submitAnswer(expectedRequest)
    }

    /**
     * 测试获取面试结果成功场景
     */
    @Test
    fun `getInterviewResult should return success when api call succeeds`() = runTest {
        // Given - 准备测试数据
        val sessionId = TestUtils.createTestSessionId()
        val interviewResult = InterviewResultResponse(
            evaluation = MockDataFactory.createMockInterviewEvaluation(sessionId = sessionId),
            session = testInterviewSession
        )
        val apiResponse = MockDataFactory.createMockApiResponse(interviewResult)

        whenever(interviewApi.getInterviewResult(sessionId)).thenReturn(apiResponse)

        // When - 获取面试结果
        interviewRepository.getInterviewResult(sessionId).test {
            // Then - 验证结果
            val loadingResult = awaitItem()
            assertThat(loadingResult).isInstanceOf(Resource.Loading::class.java)

            val successResult = awaitItem()
            assertThat(successResult).isInstanceOf(Resource.Success::class.java)
            assertThat(successResult.data).isEqualTo(interviewResult)

            awaitComplete()
        }

        verify(interviewApi).getInterviewResult(sessionId)
    }

    /**
     * 测试获取用户面试会话列表
     */
    @Test
    fun `getUserInterviewSessions should return sessions from dao`() = runTest {
        // Given - 准备测试数据
        val userId = TestUtils.createTestUserId()
        val sessions = listOf(testInterviewSession)

        whenever(interviewSessionDao.getSessionsByUser(userId)).thenReturn(flowOf(sessions))

        // When - 获取用户面试会话
        interviewRepository.getUserInterviewSessions(userId).test {
            // Then - 验证结果
            val result = awaitItem()
            assertThat(result).isEqualTo(sessions)
            awaitComplete()
        }

        verify(interviewSessionDao).getSessionsByUser(userId)
    }

    /**
     * 测试保存面试会话
     */
    @Test
    fun `saveInterviewSession should call dao insert`() = runTest {
        // When - 保存面试会话
        interviewRepository.saveInterviewSession(testInterviewSession)

        // Then - 验证DAO调用
        verify(interviewSessionDao).insertSession(testInterviewSession)
    }

    /**
     * 测试更新面试会话
     */
    @Test
    fun `updateInterviewSession should call dao update`() = runTest {
        // When - 更新面试会话
        interviewRepository.updateInterviewSession(testInterviewSession)

        // Then - 验证DAO调用
        verify(interviewSessionDao).updateSession(testInterviewSession)
    }

    /**
     * 测试根据ID获取面试会话
     */
    @Test
    fun `getInterviewSession should return session from dao`() = runTest {
        // Given - 准备测试数据
        val sessionId = TestUtils.createTestSessionId()
        whenever(interviewSessionDao.getSessionById(sessionId)).thenReturn(testInterviewSession)

        // When - 获取面试会话
        val result = interviewRepository.getInterviewSession(sessionId)

        // Then - 验证结果
        assertThat(result).isEqualTo(testInterviewSession)
        verify(interviewSessionDao).getSessionById(sessionId)
    }

    /**
     * 测试获取不存在的面试会话
     */
    @Test
    fun `getInterviewSession should return null when session not found`() = runTest {
        // Given - 会话不存在
        val sessionId = "non_existent_session"
        whenever(interviewSessionDao.getSessionById(sessionId)).thenReturn(null)

        // When - 获取面试会话
        val result = interviewRepository.getInterviewSession(sessionId)

        // Then - 验证结果
        assertThat(result).isNull()
        verify(interviewSessionDao).getSessionById(sessionId)
    }
}