package com.aiinterview.simulator.data.api

import com.aiinterview.simulator.data.dto.request.LoginRequest
import com.aiinterview.simulator.data.dto.request.RegisterRequest
import com.aiinterview.simulator.data.dto.request.RefreshTokenRequest
import com.aiinterview.simulator.data.dto.response.ApiResponse
import com.aiinterview.simulator.data.dto.response.AuthResponse
import retrofit2.http.Body
import retrofit2.http.POST

interface AuthApi {
    @POST("auth/register")
    suspend fun register(@Body request: RegisterRequest): ApiResponse<AuthResponse>
    
    @POST("auth/login")
    suspend fun login(@Body request: LoginRequest): ApiResponse<AuthResponse>
    
    @POST("auth/refresh")
    suspend fun refreshToken(@Body request: RefreshTokenRequest): ApiResponse<AuthResponse>
}