package com.aiinterview.simulator.data.dto.request

/**
 * AI评价生成请求
 */
data class EvaluationRequest(
    val sessionId: String,
    val positionType: String,
    val questions: List<QuestionForEvaluation>,
    val answers: List<AnswerForEvaluation>,
    val totalDuration: Int, // 总面试时长（秒）
    val timeLimit: Int // 面试时间限制（秒）
)

data class QuestionForEvaluation(
    val id: String,
    val type: String,
    val content: String,
    val keyPoints: List<String>,
    val timeLimit: Int
)

data class AnswerForEvaluation(
    val questionId: String,
    val transcription: String,
    val duration: Int,
    val submittedAt: Long
)

/**
 * 大语言模型API请求格式（适配文心一言/通义千问等）
 */
data class LLMEvaluationRequest(
    val messages: List<LLMMessage>,
    val temperature: Double = 0.7,
    val max_tokens: Int = 2000,
    val top_p: Double = 0.9
)

data class LLMMessage(
    val role: String, // "system", "user", "assistant"
    val content: String
)