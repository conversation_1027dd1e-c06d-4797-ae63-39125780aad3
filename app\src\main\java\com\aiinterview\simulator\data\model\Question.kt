package com.aiinterview.simulator.data.model

data class Question(
    val id: String,
    val type: QuestionType,
    val category: String,
    val title: String,
    val content: String,
    val backgroundInfo: String? = null,
    val keyPoints: List<String> = emptyList(),
    val timeLimit: Int, // 答题时间限制（秒）
    val difficulty: Int // 难度等级 1-5
)

enum class QuestionType(val displayName: String) {
    COMPREHENSIVE_ANALYSIS("综合分析"),
    PLANNING_ORGANIZATION("计划组织"),
    INTERPERSONAL_RELATIONS("人际关系"),
    EMERGENCY_RESPONSE("应急应变"),
    VERBAL_EXPRESSION("言语表达")
}