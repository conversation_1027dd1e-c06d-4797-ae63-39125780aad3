package com.aiinterview.simulator.presentation.component

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material.icons.filled.Storage
import androidx.compose.material.icons.filled.Wifi
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.aiinterview.simulator.data.permission.PermissionType

/**
 * 权限请求对话框组件
 * 用于向用户展示权限请求的友好界面
 */
@Composable
fun PermissionDialog(
    permissionType: PermissionType,     // 权限类型
    isVisible: Boolean,                 // 是否显示对话框
    onGrantPermission: () -> Unit,      // 授权权限回调
    onDenyPermission: () -> Unit,       // 拒绝权限回调
    onDismiss: () -> Unit              // 关闭对话框回调
) {
    // 只有在可见时才显示对话框
    if (isVisible) {
        Dialog(onDismissRequest = onDismiss) {
            // 对话框卡片容器
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                shape = RoundedCornerShape(16.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // 权限图标
                    Icon(
                        imageVector = getPermissionIcon(permissionType),
                        contentDescription = permissionType.title,
                        modifier = Modifier.size(64.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 权限标题
                    Text(
                        text = permissionType.title,
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        textAlign = TextAlign.Center
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 权限描述
                    Text(
                        text = permissionType.description,
                        style = MaterialTheme.typography.bodyMedium,
                        textAlign = TextAlign.Center,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    Spacer(modifier = Modifier.height(24.dp))
                    
                    // 按钮行
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        // 拒绝按钮
                        OutlinedButton(
                            onClick = onDenyPermission,
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("拒绝")
                        }
                        
                        // 允许按钮
                        Button(
                            onClick = onGrantPermission,
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("允许")
                        }
                    }
                }
            }
        }
    }
}

/**
 * 权限被永久拒绝的对话框
 * 引导用户前往设置页面手动开启权限
 */
@Composable
fun PermissionPermanentlyDeniedDialog(
    permissionType: PermissionType,     // 权限类型
    isVisible: Boolean,                 // 是否显示对话框
    onGoToSettings: () -> Unit,         // 前往设置回调
    onCancel: () -> Unit,              // 取消回调
    onDismiss: () -> Unit              // 关闭对话框回调
) {
    // 只有在可见时才显示对话框
    if (isVisible) {
        Dialog(onDismissRequest = onDismiss) {
            // 对话框卡片容器
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                shape = RoundedCornerShape(16.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // 警告图标
                    Icon(
                        imageVector = Icons.Default.Warning,
                        contentDescription = "权限被拒绝",
                        modifier = Modifier.size(64.dp),
                        tint = MaterialTheme.colorScheme.error
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 标题
                    Text(
                        text = "需要${permissionType.title}",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        textAlign = TextAlign.Center
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 说明文字
                    Text(
                        text = "您已拒绝了${permissionType.title}，请前往设置中手动开启此权限，否则相关功能将无法使用。",
                        style = MaterialTheme.typography.bodyMedium,
                        textAlign = TextAlign.Center,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 操作步骤
                    Text(
                        text = "设置路径：设置 > 应用管理 > AI面试模拟器 > 权限",
                        style = MaterialTheme.typography.bodySmall,
                        textAlign = TextAlign.Center,
                        color = MaterialTheme.colorScheme.primary
                    )
                    
                    Spacer(modifier = Modifier.height(24.dp))
                    
                    // 按钮行
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        // 取消按钮
                        OutlinedButton(
                            onClick = onCancel,
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("取消")
                        }
                        
                        // 前往设置按钮
                        Button(
                            onClick = onGoToSettings,
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("前往设置")
                        }
                    }
                }
            }
        }
    }
}

/**
 * 多权限请求对话框
 * 用于一次性请求多个权限
 */
@Composable
fun MultiplePermissionsDialog(
    permissions: List<PermissionType>,  // 权限类型列表
    isVisible: Boolean,                 // 是否显示对话框
    onGrantAll: () -> Unit,            // 授权所有权限回调
    onDeny: () -> Unit,                // 拒绝回调
    onDismiss: () -> Unit              // 关闭对话框回调
) {
    // 只有在可见时才显示对话框
    if (isVisible) {
        Dialog(onDismissRequest = onDismiss) {
            // 对话框卡片容器
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                shape = RoundedCornerShape(16.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(24.dp)
                ) {
                    // 标题
                    Text(
                        text = "应用权限申请",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.fillMaxWidth(),
                        textAlign = TextAlign.Center
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 说明文字
                    Text(
                        text = "为了提供完整的面试体验，应用需要以下权限：",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 权限列表
                    permissions.forEach { permission ->
                        PermissionItem(permissionType = permission)
                        Spacer(modifier = Modifier.height(12.dp))
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 按钮行
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        // 拒绝按钮
                        OutlinedButton(
                            onClick = onDeny,
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("拒绝")
                        }
                        
                        // 允许所有按钮
                        Button(
                            onClick = onGrantAll,
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("允许所有")
                        }
                    }
                }
            }
        }
    }
}

/**
 * 权限项组件
 * 显示单个权限的信息
 */
@Composable
private fun PermissionItem(permissionType: PermissionType) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 权限图标
        Icon(
            imageVector = getPermissionIcon(permissionType),
            contentDescription = permissionType.title,
            modifier = Modifier.size(24.dp),
            tint = MaterialTheme.colorScheme.primary
        )
        
        Spacer(modifier = Modifier.width(12.dp))
        
        // 权限信息
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = permissionType.title,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = permissionType.description,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 根据权限类型获取对应的图标
 * @param permissionType 权限类型
 * @return 对应的图标
 */
private fun getPermissionIcon(permissionType: PermissionType): ImageVector {
    return when (permissionType) {
        PermissionType.MICROPHONE -> Icons.Default.Mic      // 录音权限图标
        PermissionType.STORAGE -> Icons.Default.Storage     // 存储权限图标
        PermissionType.NETWORK_STATE,
        PermissionType.INTERNET -> Icons.Default.Wifi       // 网络权限图标
    }
}