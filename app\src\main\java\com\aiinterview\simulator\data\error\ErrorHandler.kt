package com.aiinterview.simulator.data.error

import android.content.Context
import com.aiinterview.simulator.R
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import retrofit2.HttpException
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 统一错误处理器
 * 负责处理应用中的各种异常并提供用户友好的错误信息
 */
@Singleton
class ErrorHandler @Inject constructor(
    private val context: Context // 注入应用上下文
) {
    
    // 错误事件流，用于向UI层发送错误信息
    private val _errorEvents = MutableSharedFlow<ErrorEvent>()
    val errorEvents: SharedFlow<ErrorEvent> = _errorEvents.asSharedFlow()
    
    /**
     * 处理异常并返回用户友好的错误信息
     * @param throwable 要处理的异常
     * @param context 错误发生的上下文（可选）
     * @return 处理后的应用错误对象
     */
    fun handleError(throwable: Throwable, context: String? = null): AppError {
        val appError = when (throwable) {
            // 网络相关异常
            is HttpException -> handleHttpException(throwable)
            is SocketTimeoutException -> AppError.TimeoutError
            is UnknownHostException -> AppError.NetworkError
            is IOException -> handleIOException(throwable)
            
            // 业务逻辑异常
            is BusinessException -> handleBusinessException(throwable)
            
            // 语音识别异常
            is SpeechRecognitionException -> handleSpeechException(throwable)
            
            // 数据库异常
            is DatabaseException -> handleDatabaseException(throwable)
            
            // 权限异常
            is PermissionException -> handlePermissionException(throwable)
            
            // 其他未知异常
            else -> AppError.UnknownError(throwable.message ?: "未知错误")
        }
        
        // 记录错误日志
        logError(appError, throwable, context)
        
        return appError
    }
    
    /**
     * 发送错误事件到UI层
     * @param error 应用错误对象
     * @param showToUser 是否向用户显示错误信息
     */
    suspend fun emitError(error: AppError, showToUser: Boolean = true) {
        val errorEvent = ErrorEvent(
            error = error,
            timestamp = System.currentTimeMillis(),
            showToUser = showToUser
        )
        _errorEvents.emit(errorEvent) // 发送错误事件
    }
    
    /**
     * 处理HTTP异常
     * @param exception HTTP异常
     * @return 对应的应用错误
     */
    private fun handleHttpException(exception: HttpException): AppError {
        return when (exception.code()) {
            400 -> AppError.BadRequestError("请求参数错误")
            401 -> AppError.UnauthorizedError
            403 -> AppError.ForbiddenError("访问被拒绝")
            404 -> AppError.NotFoundError("请求的资源不存在")
            408 -> AppError.TimeoutError
            429 -> AppError.TooManyRequestsError("请求过于频繁，请稍后重试")
            500 -> AppError.ServerError("服务器内部错误")
            502 -> AppError.BadGatewayError("网关错误")
            503 -> AppError.ServiceUnavailableError("服务暂时不可用")
            504 -> AppError.GatewayTimeoutError("网关超时")
            else -> AppError.NetworkError
        }
    }
    
    /**
     * 处理IO异常
     * @param exception IO异常
     * @return 对应的应用错误
     */
    private fun handleIOException(exception: IOException): AppError {
        val message = exception.message?.lowercase() ?: ""
        return when {
            message.contains("timeout") -> AppError.TimeoutError
            message.contains("connection reset") -> AppError.ConnectionResetError("连接被重置")
            message.contains("connection refused") -> AppError.ConnectionRefusedError("连接被拒绝")
            message.contains("network is unreachable") -> AppError.NetworkUnreachableError("网络不可达")
            message.contains("no internet connection") -> AppError.NoInternetError("无网络连接")
            else -> AppError.NetworkError
        }
    }
    
    /**
     * 处理业务逻辑异常
     * @param exception 业务异常
     * @return 对应的应用错误
     */
    private fun handleBusinessException(exception: BusinessException): AppError {
        return when (exception.errorCode) {
            BusinessErrorCode.USER_NOT_FOUND -> AppError.UserNotFoundError
            BusinessErrorCode.INTERVIEW_NOT_FOUND -> AppError.InterviewNotFoundError
            BusinessErrorCode.INVALID_CREDENTIALS -> AppError.InvalidCredentialsError("用户名或密码错误")
            BusinessErrorCode.TOKEN_EXPIRED -> AppError.TokenExpiredError
            BusinessErrorCode.INSUFFICIENT_PERMISSIONS -> AppError.InsufficientPermissionsError("权限不足")
            BusinessErrorCode.RESOURCE_LOCKED -> AppError.ResourceLockedError("资源被锁定")
            BusinessErrorCode.QUOTA_EXCEEDED -> AppError.QuotaExceededError("配额已超限")
            else -> AppError.BusinessLogicError(exception.message ?: "业务逻辑错误")
        }
    }
    
    /**
     * 处理语音识别异常
     * @param exception 语音识别异常
     * @return 对应的应用错误
     */
    private fun handleSpeechException(exception: SpeechRecognitionException): AppError {
        return when (exception.errorType) {
            SpeechErrorType.MICROPHONE_PERMISSION_DENIED -> AppError.MicrophonePermissionError
            SpeechErrorType.AUDIO_RECORDING_FAILED -> AppError.AudioRecordingError("录音失败")
            SpeechErrorType.SPEECH_RECOGNITION_FAILED -> AppError.SpeechRecognitionError
            SpeechErrorType.AUDIO_FORMAT_NOT_SUPPORTED -> AppError.AudioFormatError("音频格式不支持")
            SpeechErrorType.NETWORK_ERROR -> AppError.NetworkError
            SpeechErrorType.SERVICE_UNAVAILABLE -> AppError.SpeechServiceUnavailableError("语音服务不可用")
            else -> AppError.SpeechRecognitionError
        }
    }
    
    /**
     * 处理数据库异常
     * @param exception 数据库异常
     * @return 对应的应用错误
     */
    private fun handleDatabaseException(exception: DatabaseException): AppError {
        return when (exception.errorType) {
            DatabaseErrorType.CONNECTION_FAILED -> AppError.DatabaseConnectionError("数据库连接失败")
            DatabaseErrorType.QUERY_FAILED -> AppError.DatabaseQueryError("数据库查询失败")
            DatabaseErrorType.CONSTRAINT_VIOLATION -> AppError.DatabaseConstraintError("数据约束违反")
            DatabaseErrorType.DISK_FULL -> AppError.DiskFullError("存储空间不足")
            DatabaseErrorType.CORRUPTION -> AppError.DatabaseCorruptionError("数据库损坏")
            else -> AppError.DatabaseError("数据库操作失败")
        }
    }
    
    /**
     * 处理权限异常
     * @param exception 权限异常
     * @return 对应的应用错误
     */
    private fun handlePermissionException(exception: PermissionException): AppError {
        return when (exception.permission) {
            "android.permission.RECORD_AUDIO" -> AppError.MicrophonePermissionError
            "android.permission.WRITE_EXTERNAL_STORAGE" -> AppError.StoragePermissionError("存储权限被拒绝")
            "android.permission.CAMERA" -> AppError.CameraPermissionError("相机权限被拒绝")
            "android.permission.ACCESS_NETWORK_STATE" -> AppError.NetworkPermissionError("网络权限被拒绝")
            else -> AppError.PermissionDeniedError("权限被拒绝: ${exception.permission}")
        }
    }
    
    /**
     * 记录错误日志
     * @param appError 应用错误对象
     * @param originalThrowable 原始异常
     * @param context 错误上下文
     */
    private fun logError(appError: AppError, originalThrowable: Throwable, context: String?) {
        val logMessage = buildString {
            append("错误类型: ${appError.javaClass.simpleName}\n")
            append("错误代码: ${appError.code}\n")
            append("错误信息: ${appError.message}\n")
            append("用户信息: ${appError.userMessage}\n")
            if (context != null) {
                append("错误上下文: $context\n")
            }
            append("原始异常: ${originalThrowable.javaClass.simpleName}\n")
            append("异常信息: ${originalThrowable.message}\n")
            append("堆栈跟踪: ${originalThrowable.stackTraceToString()}")
        }
        
        // 这里可以集成日志框架，如Timber或自定义日志系统
        println("=== 错误日志 ===")
        println(logMessage)
        println("===============")
        
        // 可以将错误信息发送到崩溃报告服务
        // CrashReportingService.reportError(appError, originalThrowable, context)
    }
    
    /**
     * 获取错误的用户友好提示信息
     * @param error 应用错误对象
     * @return 用户友好的错误提示
     */
    fun getUserFriendlyMessage(error: AppError): String {
        return when (error) {
            is AppError.NetworkError -> context.getString(R.string.error_network)
            is AppError.TimeoutError -> context.getString(R.string.error_timeout)
            is AppError.UnauthorizedError -> context.getString(R.string.error_unauthorized)
            is AppError.TokenExpiredError -> context.getString(R.string.error_token_expired)
            is AppError.SpeechRecognitionError -> context.getString(R.string.error_speech_recognition)
            is AppError.MicrophonePermissionError -> context.getString(R.string.error_microphone_permission)
            is AppError.ServerError -> context.getString(R.string.error_server)
            is AppError.NoInternetError -> context.getString(R.string.error_no_internet)
            else -> error.userMessage
        }
    }
}

/**
 * 错误事件数据类
 * 用于在应用中传递错误信息
 */
data class ErrorEvent(
    val error: AppError, // 应用错误对象
    val timestamp: Long, // 错误发生时间戳
    val showToUser: Boolean = true // 是否向用户显示错误信息
)