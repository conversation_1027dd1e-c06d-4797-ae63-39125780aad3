package com.aiinterview.simulator.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.aiinterview.simulator.presentation.theme.*
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 主题设置视图模型
 * 管理主题设置相关的业务逻辑和状态
 */
@HiltViewModel
class ThemeSettingsViewModel @Inject constructor(
    private val themeManager: ThemeManager          // 主题管理器，用于持久化主题设置
) : ViewModel() {
    
    /**
     * 主题配置状态流
     * 提供当前的主题配置给UI层使用
     */
    val themeConfig: StateFlow<ThemeConfig> = themeManager.themeConfig.stateIn(
        scope = viewModelScope,                     // 在ViewModel的生命周期内保持活跃
        started = kotlinx.coroutines.flow.SharingStarted.WhileSubscribed(5000), // 订阅者存在时保持活跃，5秒延迟停止
        initialValue = ThemeConfig()                // 提供初始默认配置
    )
    
    /**
     * 深色主题状态流
     * 提供当前的深色主题状态给UI层使用
     */
    val isDarkTheme: StateFlow<Boolean> = themeManager.isDarkTheme.stateIn(
        scope = viewModelScope,                     // 在ViewModel的生命周期内保持活跃
        started = kotlinx.coroutines.flow.SharingStarted.WhileSubscribed(5000), // 订阅者存在时保持活跃，5秒延迟停止
        initialValue = false                        // 初始值为浅色主题
    )
    
    /**
     * 动态颜色状态流
     * 提供当前的动态颜色状态给UI层使用
     */
    val useDynamicColors: StateFlow<Boolean> = themeManager.useDynamicColors.stateIn(
        scope = viewModelScope,                     // 在ViewModel的生命周期内保持活跃
        started = kotlinx.coroutines.flow.SharingStarted.WhileSubscribed(5000), // 订阅者存在时保持活跃，5秒延迟停止
        initialValue = true                         // 初始值为启用动态颜色
    )
    
    /**
     * 高对比度状态流
     * 提供当前的高对比度状态给UI层使用
     */
    val useHighContrast: StateFlow<Boolean> = themeManager.useHighContrast.stateIn(
        scope = viewModelScope,                     // 在ViewModel的生命周期内保持活跃
        started = kotlinx.coroutines.flow.SharingStarted.WhileSubscribed(5000), // 订阅者存在时保持活跃，5秒延迟停止
        initialValue = false                        // 初始值为不使用高对比度
    )
    
    /**
     * 字体大小状态流
     * 提供当前的字体大小状态给UI层使用
     */
    val fontSize: StateFlow<FontSize> = themeManager.fontSize.stateIn(
        scope = viewModelScope,                     // 在ViewModel的生命周期内保持活跃
        started = kotlinx.coroutines.flow.SharingStarted.WhileSubscribed(5000), // 订阅者存在时保持活跃，5秒延迟停止
        initialValue = FontSize.MEDIUM              // 初始值为中等字体
    )
    
    /**
     * 圆角大小状态流
     * 提供当前的圆角大小状态给UI层使用
     */
    val cornerRadius: StateFlow<CornerRadius> = themeManager.cornerRadius.stateIn(
        scope = viewModelScope,                     // 在ViewModel的生命周期内保持活跃
        started = kotlinx.coroutines.flow.SharingStarted.WhileSubscribed(5000), // 订阅者存在时保持活跃，5秒延迟停止
        initialValue = CornerRadius.MEDIUM          // 初始值为中等圆角
    )
    
    /**
     * 跟随系统主题状态流
     * 提供当前的跟随系统主题状态给UI层使用
     */
    val followSystemTheme: StateFlow<Boolean> = themeManager.followSystemTheme.stateIn(
        scope = viewModelScope,                     // 在ViewModel的生命周期内保持活跃
        started = kotlinx.coroutines.flow.SharingStarted.WhileSubscribed(5000), // 订阅者存在时保持活跃，5秒延迟停止
        initialValue = true                         // 初始值为跟随系统主题
    )
    
    /**
     * 设置深色主题
     * 更新深色主题设置并持久化
     * @param isDark 是否启用深色主题
     */
    fun setDarkTheme(isDark: Boolean) {
        viewModelScope.launch {                     // 在协程中执行异步操作
            try {
                themeManager.setDarkTheme(isDark)   // 调用主题管理器保存设置
            } catch (e: Exception) {
                // 处理保存失败的情况
                // 可以在这里添加错误处理逻辑，比如显示错误提示
                e.printStackTrace()                 // 打印错误堆栈，便于调试
            }
        }
    }
    
    /**
     * 设置动态颜色
     * 更新动态颜色设置并持久化
     * @param useDynamic 是否启用动态颜色
     */
    fun setDynamicColors(useDynamic: Boolean) {
        viewModelScope.launch {                     // 在协程中执行异步操作
            try {
                themeManager.setDynamicColors(useDynamic) // 调用主题管理器保存设置
            } catch (e: Exception) {
                // 处理保存失败的情况
                e.printStackTrace()                 // 打印错误堆栈，便于调试
            }
        }
    }
    
    /**
     * 设置高对比度
     * 更新高对比度设置并持久化
     * @param useHighContrast 是否启用高对比度
     */
    fun setHighContrast(useHighContrast: Boolean) {
        viewModelScope.launch {                     // 在协程中执行异步操作
            try {
                themeManager.setHighContrast(useHighContrast) // 调用主题管理器保存设置
            } catch (e: Exception) {
                // 处理保存失败的情况
                e.printStackTrace()                 // 打印错误堆栈，便于调试
            }
        }
    }
    
    /**
     * 设置字体大小
     * 更新字体大小设置并持久化
     * @param fontSize 字体大小枚举值
     */
    fun setFontSize(fontSize: FontSize) {
        viewModelScope.launch {                     // 在协程中执行异步操作
            try {
                themeManager.setFontSize(fontSize)  // 调用主题管理器保存设置
            } catch (e: Exception) {
                // 处理保存失败的情况
                e.printStackTrace()                 // 打印错误堆栈，便于调试
            }
        }
    }
    
    /**
     * 设置圆角大小
     * 更新圆角大小设置并持久化
     * @param cornerRadius 圆角大小枚举值
     */
    fun setCornerRadius(cornerRadius: CornerRadius) {
        viewModelScope.launch {                     // 在协程中执行异步操作
            try {
                themeManager.setCornerRadius(cornerRadius) // 调用主题管理器保存设置
            } catch (e: Exception) {
                // 处理保存失败的情况
                e.printStackTrace()                 // 打印错误堆栈，便于调试
            }
        }
    }
    
    /**
     * 设置跟随系统主题
     * 更新跟随系统主题设置并持久化
     * @param followSystem 是否跟随系统主题
     */
    fun setFollowSystemTheme(followSystem: Boolean) {
        viewModelScope.launch {                     // 在协程中执行异步操作
            try {
                themeManager.setFollowSystemTheme(followSystem) // 调用主题管理器保存设置
            } catch (e: Exception) {
                // 处理保存失败的情况
                e.printStackTrace()                 // 打印错误堆栈，便于调试
            }
        }
    }
    
    /**
     * 重置所有主题设置
     * 将所有主题设置恢复为默认值
     */
    fun resetThemeSettings() {
        viewModelScope.launch {                     // 在协程中执行异步操作
            try {
                themeManager.resetThemeSettings()   // 调用主题管理器重置所有设置
            } catch (e: Exception) {
                // 处理重置失败的情况
                e.printStackTrace()                 // 打印错误堆栈，便于调试
            }
        }
    }
    
    /**
     * 批量更新主题配置
     * 一次性更新多个主题设置
     * @param config 新的主题配置
     */
    fun updateThemeConfig(config: ThemeConfig) {
        viewModelScope.launch {                     // 在协程中执行异步操作
            try {
                themeManager.updateThemeConfig(config) // 调用主题管理器批量更新设置
            } catch (e: Exception) {
                // 处理更新失败的情况
                e.printStackTrace()                 // 打印错误堆栈，便于调试
            }
        }
    }
    
    /**
     * 切换深色主题
     * 在深色和浅色主题之间切换
     */
    fun toggleDarkTheme() {
        val currentIsDark = themeConfig.value.isDarkTheme // 获取当前深色主题状态
        setDarkTheme(!currentIsDark)                // 切换到相反状态
    }
    
    /**
     * 切换动态颜色
     * 在启用和禁用动态颜色之间切换
     */
    fun toggleDynamicColors() {
        val currentUseDynamic = themeConfig.value.useDynamicColors // 获取当前动态颜色状态
        setDynamicColors(!currentUseDynamic)        // 切换到相反状态
    }
    
    /**
     * 切换高对比度
     * 在启用和禁用高对比度之间切换
     */
    fun toggleHighContrast() {
        val currentUseHighContrast = themeConfig.value.useHighContrast // 获取当前高对比度状态
        setHighContrast(!currentUseHighContrast)    // 切换到相反状态
    }
    
    /**
     * 获取下一个字体大小
     * 循环切换到下一个字体大小选项
     */
    fun nextFontSize() {
        val currentFontSize = themeConfig.value.fontSize // 获取当前字体大小
        val fontSizes = FontSize.values()           // 获取所有字体大小选项
        val currentIndex = fontSizes.indexOf(currentFontSize) // 获取当前选项的索引
        val nextIndex = (currentIndex + 1) % fontSizes.size   // 计算下一个选项的索引（循环）
        setFontSize(fontSizes[nextIndex])           // 设置下一个字体大小
    }
    
    /**
     * 获取下一个圆角大小
     * 循环切换到下一个圆角大小选项
     */
    fun nextCornerRadius() {
        val currentCornerRadius = themeConfig.value.cornerRadius // 获取当前圆角大小
        val cornerRadiuses = CornerRadius.values()  // 获取所有圆角大小选项
        val currentIndex = cornerRadiuses.indexOf(currentCornerRadius) // 获取当前选项的索引
        val nextIndex = (currentIndex + 1) % cornerRadiuses.size       // 计算下一个选项的索引（循环）
        setCornerRadius(cornerRadiuses[nextIndex])  // 设置下一个圆角大小
    }
    
    /**
     * 检查是否支持动态颜色
     * 判断当前设备是否支持动态颜色功能
     * @return 是否支持动态颜色
     */
    fun supportsDynamicColors(): Boolean {
        return android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S // Android 12+支持动态颜色
    }
    
    /**
     * 获取当前主题描述
     * 返回当前主题配置的文字描述
     * @return 主题描述字符串
     */
    fun getCurrentThemeDescription(): String {
        val config = themeConfig.value              // 获取当前主题配置
        val themeMode = if (config.isDarkTheme) "深色" else "浅色" // 主题模式描述
        val dynamicColors = if (config.useDynamicColors && supportsDynamicColors()) "动态颜色" else "静态颜色" // 动态颜色描述
        val contrast = if (config.useHighContrast) "高对比度" else "标准对比度" // 对比度描述
        val fontSize = config.fontSize.displayName  // 字体大小描述
        val cornerRadius = config.cornerRadius.displayName // 圆角大小描述
        
        return "${themeMode}主题，${dynamicColors}，${contrast}，${fontSize}字体，${cornerRadius}" // 组合描述字符串
    }
}