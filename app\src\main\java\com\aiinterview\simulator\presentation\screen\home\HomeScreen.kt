package com.aiinterview.simulator.presentation.screen.home

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.History
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.aiinterview.simulator.presentation.viewmodel.InterviewViewModel
import com.aiinterview.simulator.presentation.viewmodel.AuthViewModel
import com.aiinterview.simulator.domain.util.Resource
import com.aiinterview.simulator.presentation.theme.ComponentStyles
import com.aiinterview.simulator.presentation.theme.CustomShapes
import com.aiinterview.simulator.presentation.theme.Dimensions
import com.aiinterview.simulator.presentation.theme.ResponsiveStyles

/**
 * 首页屏幕组件
 * 显示面试岗位选择和快速功能入口
 * @param onNavigateToLogin 导航到登录页面的回调
 * @param onNavigateToPositionSelection 导航到岗位选择页面的回调
 * @param onNavigateToEvaluationHistory 导航到评价历史页面的回调
 * @param viewModel 面试视图模型
 * @param authViewModel 认证视图模型
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    onNavigateToLogin: () -> Unit,
    onNavigateToPositionSelection: () -> Unit = {},
    onNavigateToEvaluationHistory: () -> Unit = {},
    viewModel: InterviewViewModel = hiltViewModel(),
    authViewModel: AuthViewModel = hiltViewModel()
) {
    // 收集岗位数据状态
    val positionsState by viewModel.positionsState.collectAsState()
    
    // 页面初始化时加载岗位数据
    LaunchedEffect(Unit) {
        viewModel.loadPositions()  // 加载可选岗位列表
    }
    
    // 主页面布局
    Column(
        modifier = Modifier
            .fillMaxSize()                              // 填充整个屏幕
            .padding(16.dp)                             // 设置内边距
    ) {
        // 页面标题栏
        Row(
            modifier = Modifier.fillMaxWidth(),          // 填充宽度
            horizontalArrangement = Arrangement.SpaceBetween, // 两端对齐
            verticalAlignment = Alignment.CenterVertically    // 垂直居中
        ) {
            Text(
                text = "AI面试模拟器",                    // 页面标题
                style = MaterialTheme.typography.headlineMedium // 使用中等标题样式
            )
            
            // 右侧功能按钮组
            Row {
                // 评价历史按钮
                IconButton(onClick = onNavigateToEvaluationHistory) {
                    Icon(
                        Icons.Default.History,           // 历史图标
                        contentDescription = "评价历史"   // 无障碍描述
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))        // 标题下方间距
        
        // 快速开始面试按钮 - 使用统一的强调卡片样式
        Card(
            modifier = Modifier
                .fillMaxWidth()                          // 填充宽度
                .padding(bottom = ResponsiveStyles.getResponsiveSpacing()), // 使用响应式底部边距
            onClick = onNavigateToPositionSelection,     // 点击导航到岗位选择
            colors = ComponentStyles.Card.emphasizedCardColors(), // 使用强调卡片颜色样式
            elevation = ComponentStyles.Card.mediumElevation,     // 使用中等阴影高度
            shape = CustomShapes.InterviewCard           // 使用面试卡片专用形状
        ) {
            Column(
                modifier = Modifier.padding(ResponsiveStyles.getResponsivePadding()), // 使用响应式内边距
                horizontalAlignment = Alignment.CenterHorizontally // 水平居中
            ) {
                Text(
                    text = "开始面试练习",                // 按钮标题
                    style = ResponsiveStyles.getResponsiveTextStyle(MaterialTheme.typography.titleLarge), // 使用响应式大标题样式
                    color = MaterialTheme.colorScheme.onPrimaryContainer // 设置文字颜色
                )
                Spacer(modifier = Modifier.height(ResponsiveStyles.getResponsiveSpacing() / 2)) // 使用响应式文字间距
                Text(
                    text = "选择岗位，开始AI面试模拟",      // 按钮描述
                    style = ResponsiveStyles.getResponsiveTextStyle(MaterialTheme.typography.bodyMedium), // 使用响应式中等正文样式
                    color = MaterialTheme.colorScheme.onPrimaryContainer // 设置文字颜色
                )
            }
        }
        
        // 根据岗位数据状态显示不同内容
        when (positionsState) {
            is Resource.Loading -> {
                // 加载状态：显示进度指示器
                Box(
                    modifier = Modifier.fillMaxSize(),   // 填充剩余空间
                    contentAlignment = Alignment.Center  // 居中对齐
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally // 水平居中
                    ) {
                        CircularProgressIndicator()      // 圆形进度指示器
                        Spacer(modifier = Modifier.height(16.dp)) // 间距
                        Text(
                            text = "正在加载岗位信息...",  // 加载提示文字
                            style = MaterialTheme.typography.bodyMedium, // 使用中等正文样式
                            color = MaterialTheme.colorScheme.onSurfaceVariant // 设置文字颜色
                        )
                    }
                }
            }
            
            is Resource.Success -> {
                // 成功状态：显示岗位列表
                val positions = positionsState.data ?: emptyList() // 获取岗位数据
                
                Text(
                    text = "热门岗位推荐",                 // 列表标题
                    style = MaterialTheme.typography.titleMedium, // 使用中等标题样式
                    modifier = Modifier.padding(bottom = 12.dp)   // 底部边距
                )
                
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(ResponsiveStyles.getResponsiveSpacing()) // 使用响应式列表项间距
                ) {
                    items(positions) { position ->       // 遍历岗位列表
                        Card(
                            modifier = Modifier.fillMaxWidth(), // 填充宽度
                            onClick = {
                                viewModel.selectPosition(position) // 选择岗位
                                onNavigateToPositionSelection()    // 导航到岗位选择页面
                            },
                            colors = ComponentStyles.Card.standardCardColors(), // 使用标准卡片颜色样式
                            elevation = ComponentStyles.Card.lowElevation,      // 使用低阴影高度
                            shape = CustomShapes.InterviewCard  // 使用面试卡片专用形状
                        ) {
                            Column(
                                modifier = Modifier.padding(ResponsiveStyles.getResponsivePadding()) // 使用响应式卡片内边距
                            ) {
                                Text(
                                    text = position.name,        // 岗位名称
                                    style = ResponsiveStyles.getResponsiveTextStyle(MaterialTheme.typography.titleMedium), // 使用响应式中等标题样式
                                    color = MaterialTheme.colorScheme.onSurface   // 设置文字颜色
                                )
                                Spacer(modifier = Modifier.height(Dimensions.SpaceExtraSmall)) // 使用统一的小间距
                                Text(
                                    text = position.category,    // 岗位类别
                                    style = ResponsiveStyles.getResponsiveTextStyle(MaterialTheme.typography.bodyMedium),  // 使用响应式中等正文样式
                                    color = MaterialTheme.colorScheme.onSurfaceVariant // 设置文字颜色
                                )
                                Spacer(modifier = Modifier.height(Dimensions.SpaceSmall)) // 使用统一的中等间距
                                Text(
                                    text = position.description, // 岗位描述
                                    style = ResponsiveStyles.getResponsiveTextStyle(MaterialTheme.typography.bodySmall),   // 使用响应式小正文样式
                                    color = MaterialTheme.colorScheme.onSurfaceVariant, // 设置文字颜色
                                    maxLines = 2,                // 限制最大行数，避免过长
                                    overflow = androidx.compose.ui.text.style.TextOverflow.Ellipsis // 超出部分显示省略号
                                )
                            }
                        }
                    }
                }
            }
            
            is Resource.Error -> {
                // 错误状态：显示错误信息
                Box(
                    modifier = Modifier.fillMaxSize(),   // 填充剩余空间
                    contentAlignment = Alignment.Center  // 居中对齐
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally // 水平居中
                    ) {
                        Text(
                            text = "加载失败",             // 错误标题
                            style = MaterialTheme.typography.titleMedium, // 使用中等标题样式
                            color = MaterialTheme.colorScheme.error       // 使用错误颜色
                        )
                        Spacer(modifier = Modifier.height(8.dp))         // 间距
                        Text(
                            text = positionsState.message ?: "请检查网络连接", // 错误详情
                            style = MaterialTheme.typography.bodyMedium,    // 使用中等正文样式
                            color = MaterialTheme.colorScheme.onSurfaceVariant // 设置文字颜色
                        )
                        Spacer(modifier = Modifier.height(16.dp))        // 间距
                        Button(
                            onClick = { viewModel.loadPositions() }     // 重试按钮
                        ) {
                            Text("重试")                 // 重试按钮文字
                        }
                    }
                }
            }
            
            null -> {
                // 初始状态：显示欢迎信息
                Box(
                    modifier = Modifier.fillMaxSize(),   // 填充剩余空间
                    contentAlignment = Alignment.Center  // 居中对齐
                ) {
                    Text(
                        text = "欢迎使用AI面试模拟器",     // 欢迎文字
                        style = MaterialTheme.typography.bodyLarge, // 使用大正文样式
                        color = MaterialTheme.colorScheme.onSurfaceVariant // 设置文字颜色
                    )
                }
            }
        }
    }
}