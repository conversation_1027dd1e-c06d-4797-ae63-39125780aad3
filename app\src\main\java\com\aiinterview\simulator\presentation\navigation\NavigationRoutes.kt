package com.aiinterview.simulator.presentation.navigation

/**
 * 导航路由定义类
 * 统一管理应用中所有的导航路由常量
 */
object NavigationRoutes {
    // 认证相关路由
    const val LOGIN = "login"                           // 登录页面路由
    const val REGISTER = "register"                     // 注册页面路由
    
    // 主要功能路由
    const val HOME = "home"                             // 首页路由
    const val POSITION_SELECTION = "position_selection" // 岗位选择页面路由
    const val INTERVIEW_FLOW = "interview_flow"         // 面试流程页面路由
    const val INTERVIEW_QUESTION = "interview_question/{sessionId}" // 面试问题页面路由（带会话ID参数）
    const val SPEECH_RECOGNITION = "speech_recognition/{sessionId}/{questionId}" // 语音识别页面路由
    const val ANSWER_PROCESSING = "answer_processing/{sessionId}" // 答案处理页面路由
    
    // 评价相关路由
    const val EVALUATION_REPORT = "evaluation_report/{sessionId}" // 评价报告页面路由
    const val EVALUATION_HISTORY = "evaluation_history"  // 评价历史页面路由
    const val EVALUATION_DETAIL = "evaluation_detail/{recordId}" // 评价详情页面路由
    
    // 历史记录相关路由
    const val INTERVIEW_HISTORY = "interview_history"    // 面试历史页面路由
    const val INTERVIEW_RECORD_DETAIL = "interview_record_detail/{recordId}" // 面试记录详情路由
    
    // 进步分析相关路由
    const val PROGRESS_ANALYSIS = "progress_analysis"    // 进步分析页面路由
    const val RETRY_PRACTICE = "retry_practice/{recordId}" // 重新练习页面路由
    
    // 设置相关路由
    const val SETTINGS = "settings"                      // 设置页面路由
    const val THEME_SETTINGS = "theme_settings"          // 主题设置页面路由
    const val OFFLINE_RECORDINGS = "offline_recordings"  // 离线录音页面路由
    const val PROFILE = "profile"                        // 个人资料页面路由
    
    /**
     * 构建带参数的路由
     * @param route 基础路由模板
     * @param vararg params 路由参数
     * @return 完整的路由字符串
     */
    fun buildRoute(route: String, vararg params: String): String {
        var result = route                                // 初始化结果为基础路由
        params.forEach { param ->                         // 遍历所有参数
            result = result.replaceFirst("{.*?}".toRegex(), param) // 替换第一个参数占位符
        }
        return result                                     // 返回构建好的路由
    }
}