package com.aiinterview.simulator.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.aiinterview.simulator.data.dto.response.EvaluationResponse
import com.aiinterview.simulator.data.util.EvaluationRecordSaver
import com.aiinterview.simulator.data.repository.AuthRepository
import com.aiinterview.simulator.data.model.DimensionScore
import com.aiinterview.simulator.domain.util.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 评价报告ViewModel
 */
@HiltViewModel
class EvaluationReportViewModel @Inject constructor(
    private val evaluationRecordSaver: EvaluationRecordSaver,
    private val authRepository: AuthRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(EvaluationReportUiState())
    val uiState: StateFlow<EvaluationReportUiState> = _uiState.asStateFlow()
    
    /**
     * 加载评价报告
     */
    fun loadEvaluationReport(sessionId: String, positionId: String? = null, positionName: String? = null) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            try {
                // TODO: 从Repository获取评价数据
                // val evaluation = evaluationRepository.getEvaluation(sessionId)
                // 暂时使用模拟数据
                val mockEvaluation = createMockEvaluation(sessionId)
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    evaluation = mockEvaluation,
                    expandedSections = setOf("overall") // 默认展开总体评价
                )
                
                // 自动保存评价记录
                if (positionId != null && positionName != null) {
                    saveEvaluationRecord(sessionId, positionId, positionName, mockEvaluation)
                }
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "加载评价报告失败：${e.message}"
                )
            }
        }
    }
    
    /**
     * 保存评价记录
     */
    private fun saveEvaluationRecord(
        sessionId: String,
        positionId: String,
        positionName: String,
        evaluation: EvaluationResponse
    ) {
        viewModelScope.launch {
            try {
                val userId = authRepository.getCurrentUserId().first()
                if (userId != null) {
                    // 转换维度评分格式
                    val dimensionScores = mapOf(
                        "content_completeness" to DimensionScore(
                            score = evaluation.dimensions.contentCompleteness.score,
                            maxScore = 100.0,
                            feedback = evaluation.dimensions.contentCompleteness.feedback
                        ),
                        "logic" to DimensionScore(
                            score = evaluation.dimensions.logicalStructure.score,
                            maxScore = 100.0,
                            feedback = evaluation.dimensions.logicalStructure.feedback
                        ),
                        "fluency" to DimensionScore(
                            score = evaluation.dimensions.languageFluency.score,
                            maxScore = 100.0,
                            feedback = evaluation.dimensions.languageFluency.feedback
                        ),
                        "clarity" to DimensionScore(
                            score = evaluation.dimensions.expressionClarity.score,
                            maxScore = 100.0,
                            feedback = evaluation.dimensions.expressionClarity.feedback
                        ),
                        "time_management" to DimensionScore(
                            score = evaluation.dimensions.timeManagement.score,
                            maxScore = 100.0,
                            feedback = evaluation.dimensions.timeManagement.feedback
                        )
                    )
                    
                    evaluationRecordSaver.saveEvaluationRecordFromSession(
                        sessionId = sessionId,
                        userId = userId,
                        positionId = positionId,
                        positionName = positionName,
                        overallScore = evaluation.overallScore,
                        dimensionScores = dimensionScores,
                        feedback = evaluation.feedback,
                        suggestions = evaluation.suggestions
                    ).collect { resource ->
                        when (resource) {
                            is Resource.Success -> {
                                // 评价记录保存成功，可以显示提示
                                android.util.Log.d("EvaluationReport", "评价记录保存成功")
                            }
                            is Resource.Error -> {
                                // 评价记录保存失败，记录日志但不影响用户体验
                                android.util.Log.e("EvaluationReport", "评价记录保存失败: ${resource.message}")
                            }
                            is Resource.Loading -> {
                                // 保存中，可以显示加载状态
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e("EvaluationReport", "保存评价记录时发生异常", e)
            }
        }
    }
    
    /**
     * 切换章节展开状态
     */
    fun toggleSectionExpanded(sectionId: String) {
        val currentExpanded = _uiState.value.expandedSections
        val newExpanded = if (currentExpanded.contains(sectionId)) {
            currentExpanded - sectionId
        } else {
            currentExpanded + sectionId
        }
        
        _uiState.value = _uiState.value.copy(expandedSections = newExpanded)
    }
    
    /**
     * 重新加载评价报告
     */
    fun retry(sessionId: String) {
        loadEvaluationReport(sessionId)
    }
    
    /**
     * 创建模拟评价数据（用于开发测试）
     */
    private fun createMockEvaluation(sessionId: String): EvaluationResponse {
        return EvaluationResponse(
            sessionId = sessionId,
            overallScore = 82.5,
            overallGrade = "良好",
            dimensions = com.aiinterview.simulator.data.dto.response.EvaluationDimensions(
                contentCompleteness = com.aiinterview.simulator.data.dto.response.DimensionScore(
                    score = 85.0,
                    grade = "良好",
                    feedback = "答题要点覆盖较为全面，能够抓住问题的核心要素",
                    keyStrengths = listOf("思路清晰", "要点完整", "结合实际"),
                    improvementAreas = listOf("可以增加更多具体案例", "论述深度可以进一步加强")
                ),
                logicalStructure = com.aiinterview.simulator.data.dto.response.DimensionScore(
                    score = 78.0,
                    grade = "良好",
                    feedback = "逻辑结构基本清晰，层次分明",
                    keyStrengths = listOf("条理清楚", "层次分明"),
                    improvementAreas = listOf("论证过程可以更加严密", "结论总结需要加强")
                ),
                languageFluency = com.aiinterview.simulator.data.dto.response.DimensionScore(
                    score = 88.0,
                    grade = "优秀",
                    feedback = "语言表达流畅自然，用词准确",
                    keyStrengths = listOf("表达流畅", "用词准确", "语调自然"),
                    improvementAreas = listOf("可以适当增加语言的感染力")
                ),
                expressionClarity = com.aiinterview.simulator.data.dto.response.DimensionScore(
                    score = 80.0,
                    grade = "良好",
                    feedback = "表达清晰明了，重点突出",
                    keyStrengths = listOf("重点突出", "表达清晰"),
                    improvementAreas = listOf("语速可以适当放慢", "停顿节奏需要优化")
                ),
                timeManagement = com.aiinterview.simulator.data.dto.response.DimensionScore(
                    score = 75.0,
                    grade = "中等",
                    feedback = "时间掌控基本合理，但仍有优化空间",
                    keyStrengths = listOf("整体时间控制在范围内"),
                    improvementAreas = listOf("各题时间分配需要更加均衡", "可以预留更多思考时间")
                )
            ),
            feedback = "本次面试表现整体良好，展现了扎实的专业基础和较好的表达能力。在答题要点的全面性和语言流畅度方面表现突出，逻辑结构也比较清晰。需要在时间管理和论证深度方面进一步提升。",
            suggestions = listOf(
                "加强时间管理，合理分配各题答题时间",
                "增加具体案例和实际经验的分享",
                "提升论证的逻辑性和严密性",
                "适当放慢语速，增强表达的感染力",
                "多练习结构化思维，提升答题的条理性"
            ),
            encouragement = "您在本次面试中展现了良好的专业素养和沟通能力，继续保持这种积极的学习态度，相信您一定能够在未来的面试中取得更好的成绩！",
            timeAnalysis = com.aiinterview.simulator.data.dto.response.TimeAnalysis(
                totalTime = 1080, // 18分钟
                timeLimit = 1200, // 20分钟
                timeUtilization = 0.9,
                questionTimeBreakdown = listOf(
                    com.aiinterview.simulator.data.dto.response.QuestionTimeAnalysis(
                        questionId = "q1",
                        questionType = "综合分析",
                        timeSpent = 360,
                        recommendedTime = 300,
                        efficiency = "适中"
                    ),
                    com.aiinterview.simulator.data.dto.response.QuestionTimeAnalysis(
                        questionId = "q2",
                        questionType = "计划组织",
                        timeSpent = 420,
                        recommendedTime = 360,
                        efficiency = "过长"
                    ),
                    com.aiinterview.simulator.data.dto.response.QuestionTimeAnalysis(
                        questionId = "q3",
                        questionType = "人际关系",
                        timeSpent = 300,
                        recommendedTime = 360,
                        efficiency = "高效"
                    )
                ),
                timeManagementFeedback = "整体时间控制良好，但第二题用时偏长，建议加强时间分配的练习"
            ),
            createdAt = System.currentTimeMillis()
        )
    }
}

/**
 * 评价报告UI状态
 */
data class EvaluationReportUiState(
    val isLoading: Boolean = false,
    val evaluation: EvaluationResponse? = null,
    val error: String? = null,
    val expandedSections: Set<String> = emptySet()
)