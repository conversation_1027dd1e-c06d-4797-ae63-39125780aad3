package com.aiinterview.simulator.data.api

import com.aiinterview.simulator.data.dto.request.StartInterviewRequest
import com.aiinterview.simulator.data.dto.request.AnswerRequest
import com.aiinterview.simulator.data.dto.response.ApiResponse
import com.aiinterview.simulator.data.dto.response.InterviewSessionResponse
import com.aiinterview.simulator.data.dto.response.QuestionResponse
import com.aiinterview.simulator.data.dto.response.InterviewResultResponse
import com.aiinterview.simulator.data.model.Position
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path

interface InterviewApi {
    @GET("positions")
    suspend fun getPositions(): ApiResponse<List<Position>>
    
    @GET("positions/{id}")
    suspend fun getPositionById(@Path("id") positionId: String): ApiResponse<Position>
    
    @POST("interviews/start")
    suspend fun startInterview(@Body request: StartInterviewRequest): ApiResponse<InterviewSessionResponse>
    
    @POST("interviews/answer")
    suspend fun submitAnswer(@Body request: AnswerRequest): ApiResponse<QuestionResponse>
    
    @GET("interviews/{sessionId}/result")
    suspend fun getInterviewResult(@Path("sessionId") sessionId: String): ApiResponse<InterviewResultResponse>
}