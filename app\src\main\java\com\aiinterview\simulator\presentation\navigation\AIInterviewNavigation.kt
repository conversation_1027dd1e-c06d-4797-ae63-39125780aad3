package com.aiinterview.simulator.presentation.navigation

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.aiinterview.simulator.presentation.screen.auth.LoginScreen
import com.aiinterview.simulator.presentation.screen.auth.RegisterScreen
import com.aiinterview.simulator.presentation.screen.home.HomeScreen
import com.aiinterview.simulator.presentation.screen.evaluation.EvaluationHistoryScreen
import com.aiinterview.simulator.presentation.screen.evaluation.EvaluationDetailScreen
import com.aiinterview.simulator.presentation.screen.evaluation.EvaluationReportScreen
import com.aiinterview.simulator.presentation.screen.history.InterviewHistoryScreen
import com.aiinterview.simulator.presentation.screen.history.InterviewRecordDetailScreen
import com.aiinterview.simulator.presentation.screen.analysis.ProgressAnalysisScreen
import com.aiinterview.simulator.presentation.screen.analysis.RetryPracticeScreen
import com.aiinterview.simulator.presentation.screen.profile.ProfileScreen
import com.aiinterview.simulator.presentation.screen.settings.OfflineRecordingsScreen
import com.aiinterview.simulator.presentation.screen.interview.InterviewFlowManagementScreen
import com.aiinterview.simulator.presentation.screen.interview.InterviewQuestionScreen
import com.aiinterview.simulator.presentation.screen.interview.SpeechRecognitionScreen
import com.aiinterview.simulator.presentation.screen.interview.AnswerProcessingScreen
import com.aiinterview.simulator.presentation.viewmodel.AuthViewModel

/**
 * AI面试应用主导航组件
 * 管理整个应用的页面导航和路由
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AIInterviewNavigation() {
    // 创建导航控制器
    val navController = rememberNavController()
    // 获取认证视图模型
    val authViewModel: AuthViewModel = hiltViewModel()
    // 监听用户登录状态
    val isLoggedIn by authViewModel.isLoggedIn.collectAsState()
    
    // 应用启动时检查登录状态
    LaunchedEffect(Unit) {
        authViewModel.checkLoginStatus()  // 检查用户是否已登录
    }
    
    // 根据登录状态确定起始页面
    val startDestination = if (isLoggedIn) NavigationRoutes.HOME else NavigationRoutes.LOGIN
    
    // 获取当前导航栈状态，用于判断是否显示底部导航栏
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route
    
    // 主布局结构
    Scaffold(
        bottomBar = {
            // 根据当前路由决定是否显示底部导航栏
            if (shouldShowBottomBar(currentRoute)) {
                BottomNavigationBar(
                    navController = navController,
                    onNavigate = { route ->
                        // 底部导航栏点击处理
                        navController.navigate(route) {
                            // 避免重复导航到同一页面
                            popUpTo(navController.graph.startDestinationId) {
                                saveState = true    // 保存页面状态
                            }
                            launchSingleTop = true  // 单例模式启动
                            restoreState = true     // 恢复页面状态
                        }
                    }
                )
            }
        }
    ) { paddingValues ->
        // 导航主机，管理所有页面路由
        NavHost(
            navController = navController,
            startDestination = startDestination,
            modifier = Modifier.padding(paddingValues), // 应用底部导航栏的内边距
            // 配置页面切换动画
            enterTransition = { NavigationAnimations.enterTransition(targetState, initialState) },
            exitTransition = { NavigationAnimations.exitTransition(targetState, initialState) },
            popEnterTransition = { NavigationAnimations.popEnterTransition(targetState, initialState) },
            popExitTransition = { NavigationAnimations.popExitTransition(targetState, initialState) }
        ) {
            // 登录页面路由
            composable(NavigationRoutes.LOGIN) {
                LoginScreen(
                    onNavigateToHome = {
                        // 登录成功后导航到首页，清除登录页面栈
                        navController.navigate(NavigationRoutes.HOME) {
                            popUpTo(NavigationRoutes.LOGIN) { inclusive = true }
                        }
                    },
                    onNavigateToRegister = {
                        // 导航到注册页面
                        navController.navigate(NavigationRoutes.REGISTER)
                    }
                )
            }
            
            // 注册页面路由
            composable(NavigationRoutes.REGISTER) {
                RegisterScreen(
                    onNavigateToHome = {
                        // 注册成功后导航到首页，清除注册页面栈
                        navController.navigate(NavigationRoutes.HOME) {
                            popUpTo(NavigationRoutes.REGISTER) { inclusive = true }
                        }
                    },
                    onNavigateToLogin = {
                        // 返回登录页面
                        navController.popBackStack()
                    }
                )
            }
            
            // 首页路由
            composable(NavigationRoutes.HOME) {
                HomeScreen(
                    onNavigateToLogin = {
                        // 退出登录，导航到登录页面
                        navController.navigate(NavigationRoutes.LOGIN) {
                            popUpTo(NavigationRoutes.HOME) { inclusive = true }
                        }
                    },
                    onNavigateToPositionSelection = {
                        // 导航到岗位选择页面
                        navController.navigate(NavigationRoutes.POSITION_SELECTION)
                    },
                    onNavigateToEvaluationHistory = {
                        // 导航到评价历史页面
                        navController.navigate(NavigationRoutes.EVALUATION_HISTORY)
                    }
                )
            }
            
            // 岗位选择页面路由（待实现）
            composable(NavigationRoutes.POSITION_SELECTION) {
                // TODO: 实现岗位选择页面
                Box(modifier = Modifier.fillMaxSize()) {
                    Text("岗位选择页面 - 待实现")
                }
            }
            
            // 面试流程管理页面路由
            composable(NavigationRoutes.INTERVIEW_FLOW) {
                InterviewFlowManagementScreen(
                    onNavigateToQuestion = { sessionId ->
                        // 导航到面试问题页面
                        navController.navigate(
                            NavigationRoutes.buildRoute(NavigationRoutes.INTERVIEW_QUESTION, sessionId)
                        )
                    },
                    onNavigateBack = {
                        // 返回上一页面
                        navController.popBackStack()
                    }
                )
            }
            
            // 面试问题页面路由
            composable(NavigationRoutes.INTERVIEW_QUESTION) { backStackEntry ->
                val sessionId = backStackEntry.arguments?.getString("sessionId") ?: ""
                InterviewQuestionScreen(
                    sessionId = sessionId,
                    onNavigateToSpeechRecognition = { questionId ->
                        // 导航到语音识别页面
                        navController.navigate(
                            NavigationRoutes.buildRoute(NavigationRoutes.SPEECH_RECOGNITION, sessionId, questionId)
                        )
                    },
                    onNavigateToEvaluation = {
                        // 导航到评价报告页面
                        navController.navigate(
                            NavigationRoutes.buildRoute(NavigationRoutes.EVALUATION_REPORT, sessionId)
                        )
                    },
                    onNavigateBack = {
                        // 返回上一页面
                        navController.popBackStack()
                    }
                )
            }
            
            // 语音识别页面路由
            composable(NavigationRoutes.SPEECH_RECOGNITION) { backStackEntry ->
                val sessionId = backStackEntry.arguments?.getString("sessionId") ?: ""
                val questionId = backStackEntry.arguments?.getString("questionId") ?: ""
                SpeechRecognitionScreen(
                    sessionId = sessionId,
                    questionId = questionId,
                    onNavigateToAnswerProcessing = {
                        // 导航到答案处理页面
                        navController.navigate(
                            NavigationRoutes.buildRoute(NavigationRoutes.ANSWER_PROCESSING, sessionId)
                        )
                    },
                    onNavigateBack = {
                        // 返回上一页面
                        navController.popBackStack()
                    }
                )
            }
            
            // 答案处理页面路由
            composable(NavigationRoutes.ANSWER_PROCESSING) { backStackEntry ->
                val sessionId = backStackEntry.arguments?.getString("sessionId") ?: ""
                AnswerProcessingScreen(
                    sessionId = sessionId,
                    onNavigateToNextQuestion = {
                        // 返回面试问题页面继续下一题
                        navController.popBackStack()
                    },
                    onNavigateToEvaluation = {
                        // 导航到评价报告页面
                        navController.navigate(
                            NavigationRoutes.buildRoute(NavigationRoutes.EVALUATION_REPORT, sessionId)
                        )
                    },
                    onNavigateBack = {
                        // 返回上一页面
                        navController.popBackStack()
                    }
                )
            }
            
            // 评价报告页面路由
            composable(NavigationRoutes.EVALUATION_REPORT) { backStackEntry ->
                val sessionId = backStackEntry.arguments?.getString("sessionId") ?: ""
                EvaluationReportScreen(
                    sessionId = sessionId,
                    onNavigateToHome = {
                        // 返回首页
                        navController.navigate(NavigationRoutes.HOME) {
                            popUpTo(NavigationRoutes.HOME) { inclusive = false }
                        }
                    },
                    onNavigateBack = {
                        // 返回上一页面
                        navController.popBackStack()
                    }
                )
            }
            
            // 评价历史页面路由
            composable(NavigationRoutes.EVALUATION_HISTORY) {
                EvaluationHistoryScreen(
                    onNavigateToDetail = { recordId ->
                        // 导航到评价详情页面
                        navController.navigate(
                            NavigationRoutes.buildRoute(NavigationRoutes.EVALUATION_DETAIL, recordId)
                        )
                    },
                    onNavigateBack = {
                        // 返回上一页面
                        navController.popBackStack()
                    }
                )
            }
            
            // 评价详情页面路由
            composable(NavigationRoutes.EVALUATION_DETAIL) { backStackEntry ->
                val recordId = backStackEntry.arguments?.getString("recordId") ?: ""
                EvaluationDetailScreen(
                    recordId = recordId,
                    onNavigateBack = {
                        // 返回上一页面
                        navController.popBackStack()
                    }
                )
            }
            
            // 面试历史页面路由
            composable(NavigationRoutes.INTERVIEW_HISTORY) {
                InterviewHistoryScreen(
                    onNavigateToDetail = { recordId ->
                        // 导航到面试记录详情页面
                        navController.navigate(
                            NavigationRoutes.buildRoute(NavigationRoutes.INTERVIEW_RECORD_DETAIL, recordId)
                        )
                    },
                    onNavigateBack = {
                        // 返回上一页面
                        navController.popBackStack()
                    }
                )
            }
            
            // 面试记录详情页面路由
            composable(NavigationRoutes.INTERVIEW_RECORD_DETAIL) { backStackEntry ->
                val recordId = backStackEntry.arguments?.getString("recordId") ?: ""
                InterviewRecordDetailScreen(
                    recordId = recordId,
                    onNavigateBack = {
                        // 返回上一页面
                        navController.popBackStack()
                    }
                )
            }
            
            // 进步分析页面路由
            composable(NavigationRoutes.PROGRESS_ANALYSIS) {
                ProgressAnalysisScreen(
                    onNavigateToRetryPractice = { recordId ->
                        // 导航到重新练习页面
                        navController.navigate(
                            NavigationRoutes.buildRoute(NavigationRoutes.RETRY_PRACTICE, recordId)
                        )
                    },
                    onNavigateBack = {
                        // 返回上一页面
                        navController.popBackStack()
                    }
                )
            }
            
            // 重新练习页面路由
            composable(NavigationRoutes.RETRY_PRACTICE) { backStackEntry ->
                val recordId = backStackEntry.arguments?.getString("recordId") ?: ""
                RetryPracticeScreen(
                    recordId = recordId,
                    onNavigateToInterview = { sessionId ->
                        // 导航到面试流程页面
                        navController.navigate(NavigationRoutes.INTERVIEW_FLOW)
                    },
                    onNavigateBack = {
                        // 返回上一页面
                        navController.popBackStack()
                    }
                )
            }
            
            // 个人中心页面路由
            composable(NavigationRoutes.PROFILE) {
                ProfileScreen(
                    onNavigateToLogin = {
                        // 退出登录，导航到登录页面
                        navController.navigate(NavigationRoutes.LOGIN) {
                            popUpTo(NavigationRoutes.HOME) { inclusive = true }
                        }
                    },
                    onNavigateToSettings = {
                        // 导航到设置页面
                        navController.navigate(NavigationRoutes.SETTINGS)
                    },
                    onNavigateToThemeSettings = {
                        // 导航到主题设置页面
                        navController.navigate(NavigationRoutes.THEME_SETTINGS)
                    },
                    onNavigateToOfflineRecordings = {
                        // 导航到离线录音页面
                        navController.navigate(NavigationRoutes.OFFLINE_RECORDINGS)
                    }
                )
            }
            
            // 设置页面路由（待实现）
            composable(NavigationRoutes.SETTINGS) {
                // TODO: 实现设置页面
                Box(modifier = Modifier.fillMaxSize()) {
                    Text("设置页面 - 待实现")
                }
            }
            
            // 主题设置页面路由
            composable(NavigationRoutes.THEME_SETTINGS) {
                com.aiinterview.simulator.presentation.screen.settings.ThemeSettingsScreen(
                    onNavigateBack = {
                        // 返回上一页面
                        navController.popBackStack()
                    }
                )
            }
            
            // 离线录音页面路由
            composable(NavigationRoutes.OFFLINE_RECORDINGS) {
                OfflineRecordingsScreen(
                    onNavigateBack = {
                        // 返回上一页面
                        navController.popBackStack()
                    }
                )
            }
        }
    }
}