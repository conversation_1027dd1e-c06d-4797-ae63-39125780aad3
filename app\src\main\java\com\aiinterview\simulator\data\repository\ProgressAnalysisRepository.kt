package com.aiinterview.simulator.data.repository

import com.aiinterview.simulator.data.dao.InterviewRecordDao
import com.aiinterview.simulator.data.model.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.abs

/**
 * 进步分析Repository
 * 负责分析用户的面试历史数据，生成进步趋势和改进建议
 */
@Singleton
class ProgressAnalysisRepository @Inject constructor(
    private val interviewRecordDao: InterviewRecordDao
) {
    
    /**
     * 生成用户的进步分析报告
     * @param userId 用户ID
     * @return 进步分析结果
     */
    suspend fun generateProgressAnalysis(userId: String): ProgressAnalysisResult = withContext(Dispatchers.IO) {
        // 获取用户所有已完成的面试记录
        val records = interviewRecordDao.getRecordsByUser(userId)
            .filter { it.status == "COMPLETED" && it.overallScore != null }
            .sortedBy { it.startTime }
        
        if (records.isEmpty()) {
            // 如果没有记录，返回空的分析结果
            return@withContext ProgressAnalysisResult(
                overallTrend = OverallTrend(
                    trendDirection = TrendDirection.STABLE,
                    improvementRate = 0.0,
                    currentLevel = PerformanceLevel.POOR,
                    description = "暂无足够数据进行分析"
                ),
                scoreHistory = emptyList(),
                categoryAnalysis = emptyList(),
                timeAnalysis = TimeAnalysis(
                    weeklyStats = emptyList(),
                    monthlyStats = emptyList(),
                    totalPracticeTime = 0,
                    averageSessionDuration = 0
                ),
                recommendations = listOf("开始进行面试练习以获得个性化建议")
            )
        }
        
        // 生成各项分析数据
        val scoreHistory = generateScoreHistory(records)
        val overallTrend = analyzeOverallTrend(records)
        val categoryAnalysis = analyzeCategoryProgress(records)
        val timeAnalysis = analyzeTimePatterns(records)
        val recommendations = generateRecommendations(records, overallTrend, categoryAnalysis)
        
        ProgressAnalysisResult(
            overallTrend = overallTrend,
            scoreHistory = scoreHistory,
            categoryAnalysis = categoryAnalysis,
            timeAnalysis = timeAnalysis,
            recommendations = recommendations
        )
    }
    
    /**
     * 生成分数历史记录点
     */
    private fun generateScoreHistory(records: List<InterviewRecord>): List<ScorePoint> {
        return records.mapNotNull { record ->
            record.overallScore?.let { score ->
                ScorePoint(
                    timestamp = record.startTime,
                    score = score,
                    positionName = record.positionName,
                    category = record.positionCategory
                )
            }
        }
    }
    
    /**
     * 分析总体趋势
     */
    private fun analyzeOverallTrend(records: List<InterviewRecord>): OverallTrend {
        val scores = records.mapNotNull { it.overallScore }
        if (scores.size < 2) {
            val currentScore = scores.firstOrNull() ?: 0.0
            return OverallTrend(
                trendDirection = TrendDirection.STABLE,
                improvementRate = 0.0,
                currentLevel = PerformanceLevel.fromScore(currentScore),
                description = "需要更多面试记录来分析趋势"
            )
        }
        
        // 计算线性回归斜率来判断趋势
        val n = scores.size
        val xSum = (0 until n).sum()
        val ySum = scores.sum()
        val xySum = scores.mapIndexed { index, score -> index * score }.sum()
        val x2Sum = (0 until n).map { it * it }.sum()
        
        val slope = (n * xySum - xSum * ySum) / (n * x2Sum - xSum * xSum)
        
        // 计算改进率
        val firstScore = scores.first()
        val lastScore = scores.last()
        val improvementRate = if (firstScore > 0) {
            ((lastScore - firstScore) / firstScore) * 100
        } else 0.0
        
        // 判断趋势方向
        val trendDirection = when {
            slope > 1.0 -> TrendDirection.RISING
            slope < -1.0 -> TrendDirection.FALLING
            abs(slope) <= 1.0 && calculateVariance(scores) > 100 -> TrendDirection.FLUCTUATING
            else -> TrendDirection.STABLE
        }
        
        val currentLevel = PerformanceLevel.fromScore(lastScore)
        val description = generateTrendDescription(trendDirection, improvementRate, currentLevel)
        
        return OverallTrend(
            trendDirection = trendDirection,
            improvementRate = improvementRate,
            currentLevel = currentLevel,
            description = description
        )
    }
    
    /**
     * 分析各类别的进步情况
     */
    private fun analyzeCategoryProgress(records: List<InterviewRecord>): List<CategoryProgress> {
        return records.groupBy { it.positionCategory }
            .map { (category, categoryRecords) ->
                val scores = categoryRecords.mapNotNull { it.overallScore }
                val averageScore = scores.average()
                val bestScore = scores.maxOrNull() ?: 0.0
                
                // 计算该类别的改进率
                val improvementRate = if (scores.size >= 2) {
                    val firstScore = scores.first()
                    val lastScore = scores.last()
                    if (firstScore > 0) ((lastScore - firstScore) / firstScore) * 100 else 0.0
                } else 0.0
                
                // 判断趋势
                val trend = when {
                    improvementRate > 5 -> TrendDirection.RISING
                    improvementRate < -5 -> TrendDirection.FALLING
                    else -> TrendDirection.STABLE
                }
                
                CategoryProgress(
                    category = category,
                    averageScore = averageScore,
                    bestScore = bestScore,
                    interviewCount = categoryRecords.size,
                    improvementRate = improvementRate,
                    trend = trend
                )
            }
            .sortedByDescending { it.averageScore }
    }
    
    /**
     * 分析时间模式
     */
    private fun analyzeTimePatterns(records: List<InterviewRecord>): TimeAnalysis {
        val totalPracticeTime = records.sumOf { it.duration } / 60 // 转换为分钟
        val averageSessionDuration = if (records.isNotEmpty()) {
            totalPracticeTime / records.size
        } else 0
        
        val weeklyStats = generateWeeklyStats(records)
        val monthlyStats = generateMonthlyStats(records)
        
        return TimeAnalysis(
            weeklyStats = weeklyStats,
            monthlyStats = monthlyStats,
            totalPracticeTime = totalPracticeTime,
            averageSessionDuration = averageSessionDuration
        )
    }
    
    /**
     * 生成周统计数据
     */
    private fun generateWeeklyStats(records: List<InterviewRecord>): List<WeeklyStats> {
        val calendar = Calendar.getInstance()
        val weeklyGroups = records.groupBy { record ->
            calendar.timeInMillis = record.startTime
            calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            calendar.timeInMillis
        }
        
        return weeklyGroups.map { (weekStart, weekRecords) ->
            val scores = weekRecords.mapNotNull { it.overallScore }
            WeeklyStats(
                weekStart = weekStart,
                interviewCount = weekRecords.size,
                averageScore = if (scores.isNotEmpty()) scores.average() else 0.0,
                totalDuration = weekRecords.sumOf { it.duration } / 60
            )
        }.sortedBy { it.weekStart }
    }
    
    /**
     * 生成月统计数据
     */
    private fun generateMonthlyStats(records: List<InterviewRecord>): List<MonthlyStats> {
        val calendar = Calendar.getInstance()
        val monthlyGroups = records.groupBy { record ->
            calendar.timeInMillis = record.startTime
            calendar.set(Calendar.DAY_OF_MONTH, 1)
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            calendar.timeInMillis
        }
        
        val monthlyStatsList = monthlyGroups.map { (monthStart, monthRecords) ->
            val scores = monthRecords.mapNotNull { it.overallScore }
            MonthlyStats(
                monthStart = monthStart,
                interviewCount = monthRecords.size,
                averageScore = if (scores.isNotEmpty()) scores.average() else 0.0,
                totalDuration = monthRecords.sumOf { it.duration } / 60,
                improvementFromLastMonth = 0.0 // 先设为0，后面计算
            )
        }.sortedBy { it.monthStart }
        
        // 计算相比上月的改进率
        return monthlyStatsList.mapIndexed { index, stats ->
            val improvementFromLastMonth = if (index > 0) {
                val lastMonthScore = monthlyStatsList[index - 1].averageScore
                if (lastMonthScore > 0) {
                    ((stats.averageScore - lastMonthScore) / lastMonthScore) * 100
                } else 0.0
            } else 0.0
            
            stats.copy(improvementFromLastMonth = improvementFromLastMonth)
        }
    }
    
    /**
     * 生成个性化建议
     */
    private fun generateRecommendations(
        records: List<InterviewRecord>,
        overallTrend: OverallTrend,
        categoryAnalysis: List<CategoryProgress>
    ): List<String> {
        val recommendations = mutableListOf<String>()
        
        // 基于总体趋势的建议
        when (overallTrend.trendDirection) {
            TrendDirection.RISING -> {
                recommendations.add("🎉 表现持续改善！继续保持当前的练习节奏")
                recommendations.add("💪 可以尝试挑战更高难度的岗位类型")
            }
            TrendDirection.FALLING -> {
                recommendations.add("📈 最近表现有所下滑，建议回顾之前的优秀表现")
                recommendations.add("🎯 重点练习基础题型，巩固核心技能")
            }
            TrendDirection.FLUCTUATING -> {
                recommendations.add("⚖️ 表现起伏较大，建议制定更稳定的练习计划")
                recommendations.add("📝 总结每次面试的得失，形成稳定的答题模式")
            }
            TrendDirection.STABLE -> {
                recommendations.add("🔄 表现稳定，可以尝试突破舒适区")
                recommendations.add("🚀 增加练习频率或尝试新的岗位类别")
            }
        }
        
        // 基于类别分析的建议
        if (categoryAnalysis.isNotEmpty()) {
            val weakestCategory = categoryAnalysis.minByOrNull { it.averageScore }
            val strongestCategory = categoryAnalysis.maxByOrNull { it.averageScore }
            
            weakestCategory?.let {
                recommendations.add("🎯 ${it.category}类岗位是薄弱环节，建议重点练习")
            }
            
            strongestCategory?.let {
                if (it.averageScore >= 85) {
                    recommendations.add("⭐ ${it.category}类岗位表现优秀，可以作为优势项目")
                }
            }
        }
        
        // 基于练习频率的建议
        val recentRecords = records.filter { 
            System.currentTimeMillis() - it.startTime < 7 * 24 * 60 * 60 * 1000 // 最近一周
        }
        
        when {
            recentRecords.isEmpty() -> {
                recommendations.add("📅 建议保持定期练习，每周至少2-3次")
            }
            recentRecords.size >= 5 -> {
                recommendations.add("🎯 练习频率很高，注意劳逸结合，质量比数量更重要")
            }
            recentRecords.size <= 1 -> {
                recommendations.add("⏰ 建议增加练习频率，保持面试状态")
            }
        }
        
        // 基于当前水平的建议
        when (overallTrend.currentLevel) {
            PerformanceLevel.EXCELLENT -> {
                recommendations.add("🏆 当前水平优秀，可以挑战更复杂的面试场景")
            }
            PerformanceLevel.GOOD -> {
                recommendations.add("👍 表现良好，继续努力向优秀水平迈进")
            }
            PerformanceLevel.AVERAGE -> {
                recommendations.add("📚 建议系统学习面试技巧，提升答题质量")
            }
            PerformanceLevel.POOR -> {
                recommendations.add("💡 建议从基础开始，多练习常见题型")
            }
        }
        
        return recommendations.take(6) // 最多返回6条建议
    }
    
    /**
     * 计算方差
     */
    private fun calculateVariance(scores: List<Double>): Double {
        val mean = scores.average()
        return scores.map { (it - mean) * (it - mean) }.average()
    }
    
    /**
     * 生成趋势描述
     */
    private fun generateTrendDescription(
        direction: TrendDirection,
        improvementRate: Double,
        currentLevel: PerformanceLevel
    ): String {
        val rateText = when {
            abs(improvementRate) < 5 -> "变化不大"
            improvementRate > 0 -> "提升了${String.format("%.1f", improvementRate)}%"
            else -> "下降了${String.format("%.1f", abs(improvementRate))}%"
        }
        
        return when (direction) {
            TrendDirection.RISING -> "面试表现呈上升趋势，相比初期${rateText}，当前水平为${currentLevel.displayName}"
            TrendDirection.FALLING -> "面试表现有所下滑，相比之前${rateText}，当前水平为${currentLevel.displayName}"
            TrendDirection.STABLE -> "面试表现保持稳定，${rateText}，当前水平为${currentLevel.displayName}"
            TrendDirection.FLUCTUATING -> "面试表现起伏较大，整体${rateText}，当前水平为${currentLevel.displayName}"
        }
    }
    
    /**
     * 获取用户在指定类别的历史记录（用于重新练习）
     * @param userId 用户ID
     * @param category 岗位类别
     * @param limit 返回记录数量限制
     * @return 该类别的历史面试记录
     */
    suspend fun getRecordsForRetry(
        userId: String, 
        category: String, 
        limit: Int = 10
    ): List<InterviewRecordModel> = withContext(Dispatchers.IO) {
        val records = interviewRecordDao.getRecordsByCategory(userId, category)
            .take(limit)
        
        records.map { it.toModel() }
    }
    
    /**
     * 获取推荐的练习类别（基于薄弱环节）
     * @param userId 用户ID
     * @return 推荐练习的类别列表，按优先级排序
     */
    suspend fun getRecommendedPracticeCategories(userId: String): List<String> = withContext(Dispatchers.IO) {
        val analysis = generateProgressAnalysis(userId)
        
        // 按平均分数升序排列，分数低的优先推荐
        analysis.categoryAnalysis
            .sortedBy { it.averageScore }
            .map { it.category }
    }
    
    // 扩展函数：Entity转Model（简化版，实际应该在Repository中统一处理）
    private fun InterviewRecord.toModel(): InterviewRecordModel {
        // 这里应该使用完整的转换逻辑，暂时简化
        return InterviewRecordModel(
            id = id,
            sessionId = sessionId,
            userId = userId,
            positionId = positionId,
            positionName = positionName,
            positionCategory = positionCategory,
            startTime = startTime,
            endTime = endTime,
            duration = duration,
            status = InterviewRecordStatus.valueOf(status),
            questions = emptyList(), // 简化处理
            answers = emptyList(), // 简化处理
            audioFiles = emptyList(), // 简化处理
            evaluationId = evaluationId,
            overallScore = overallScore,
            createdAt = createdAt,
            updatedAt = updatedAt,
            isSynced = isSynced,
            syncedAt = syncedAt
        )
    }
}