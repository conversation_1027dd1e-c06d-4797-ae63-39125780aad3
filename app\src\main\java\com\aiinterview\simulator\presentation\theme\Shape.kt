package com.aiinterview.simulator.presentation.theme

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Shapes
import androidx.compose.ui.unit.dp

/**
 * 应用形状定义
 * 定义应用中使用的所有形状样式，遵循Material Design 3规范
 */
val Shapes = Shapes(
    // 超小圆角 - 用于小型组件如芯片、标签
    extraSmall = RoundedCornerShape(4.dp),
    
    // 小圆角 - 用于按钮、输入框等小型交互元素
    small = RoundedCornerShape(8.dp),
    
    // 中等圆角 - 用于卡片、对话框等中型组件
    medium = RoundedCornerShape(12.dp),
    
    // 大圆角 - 用于大型卡片、底部表单等
    large = RoundedCornerShape(16.dp),
    
    // 超大圆角 - 用于全屏对话框、大型容器
    extraLarge = RoundedCornerShape(24.dp)
)

/**
 * 自定义形状定义
 * 为特定组件提供专用的形状样式
 */
object CustomShapes {
    // 面试卡片形状 - 稍大的圆角，营造友好感
    val InterviewCard = RoundedCornerShape(16.dp)
    
    // 录音按钮形状 - 圆形，突出录音功能
    val RecordingButton = RoundedCornerShape(50)
    
    // 评分卡片形状 - 中等圆角，保持专业感
    val ScoreCard = RoundedCornerShape(12.dp)
    
    // 进度条形状 - 小圆角，保持简洁
    val ProgressBar = RoundedCornerShape(8.dp)
    
    // 底部导航栏形状 - 顶部圆角，底部直角
    val BottomNavigation = RoundedCornerShape(
        topStart = 16.dp,
        topEnd = 16.dp,
        bottomStart = 0.dp,
        bottomEnd = 0.dp
    )
    
    // 顶部应用栏形状 - 底部圆角，顶部直角
    val TopAppBar = RoundedCornerShape(
        topStart = 0.dp,
        topEnd = 0.dp,
        bottomStart = 16.dp,
        bottomEnd = 16.dp
    )
    
    // 浮动操作按钮形状 - 大圆角，突出重要操作
    val FloatingActionButton = RoundedCornerShape(16.dp)
    
    // 对话框形状 - 中等圆角，保持现代感
    val Dialog = RoundedCornerShape(20.dp)
    
    // 底部表单形状 - 顶部大圆角，营造抽屉效果
    val BottomSheet = RoundedCornerShape(
        topStart = 24.dp,
        topEnd = 24.dp,
        bottomStart = 0.dp,
        bottomEnd = 0.dp
    )
    
    // 输入框形状 - 小圆角，保持简洁
    val TextField = RoundedCornerShape(8.dp)
    
    // 芯片形状 - 超大圆角，营造胶囊效果
    val Chip = RoundedCornerShape(50)
    
    // 标签形状 - 小圆角，保持紧凑
    val Tag = RoundedCornerShape(6.dp)
    
    // 头像形状 - 圆形
    val Avatar = RoundedCornerShape(50)
    
    // 图片形状 - 中等圆角，保持美观
    val Image = RoundedCornerShape(12.dp)
    
    // 分割线形状 - 极小圆角，保持细腻
    val Divider = RoundedCornerShape(1.dp)
    
    // 开关形状 - 超大圆角，营造胶囊效果
    val Switch = RoundedCornerShape(50)
    
    // 滑块形状 - 超大圆角
    val Slider = RoundedCornerShape(50)
    
    // 复选框形状 - 小圆角，保持识别性
    val Checkbox = RoundedCornerShape(4.dp)
    
    // 单选按钮形状 - 圆形
    val RadioButton = RoundedCornerShape(50)
}