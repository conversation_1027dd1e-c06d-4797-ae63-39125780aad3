package com.aiinterview.simulator.data.audio

import android.content.Context
import com.aiinterview.simulator.data.dto.response.RecognitionResult
import com.aiinterview.simulator.domain.util.ErrorHandler
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class OfflineRecordingManager @Inject constructor(
    private val context: Context
) {
    private val _pendingUploads = MutableStateFlow<List<PendingRecording>>(emptyList())
    val pendingUploads: StateFlow<List<PendingRecording>> = _pendingUploads.asStateFlow()
    
    private val _uploadProgress = MutableStateFlow<Map<String, UploadProgress>>(emptyMap())
    val uploadProgress: StateFlow<Map<String, UploadProgress>> = _uploadProgress.asStateFlow()
    
    private val gson = Gson()
    
    init {
        loadPendingRecordings()
    }
    
    suspend fun saveOfflineRecording(
        questionId: String,
        audioFile: File,
        transcription: String,
        sessionId: String? = null
    ): String {
        val recordingId = generateRecordingId()
        val offlineDir = getOfflineRecordingsDirectory()
        
        // Copy audio file to offline directory
        val offlineAudioFile = File(offlineDir, "${recordingId}.m4a")
        audioFile.copyTo(offlineAudioFile, overwrite = true)
        
        // Create pending recording record
        val pendingRecording = PendingRecording(
            id = recordingId,
            questionId = questionId,
            sessionId = sessionId,
            audioFilePath = offlineAudioFile.absolutePath,
            transcription = transcription,
            createdAt = System.currentTimeMillis(),
            status = RecordingStatus.PENDING,
            retryCount = 0
        )
        
        // Save to pending list
        val currentList = _pendingUploads.value.toMutableList()
        currentList.add(pendingRecording)
        _pendingUploads.value = currentList
        
        // Persist to storage
        savePendingRecordings()
        
        return recordingId
    }
    
    suspend fun uploadPendingRecording(recordingId: String): Boolean {
        val recording = _pendingUploads.value.find { it.id == recordingId } ?: return false
        
        try {
            // Update status to uploading
            updateRecordingStatus(recordingId, RecordingStatus.UPLOADING)
            updateUploadProgress(recordingId, UploadProgress(0, "开始上传..."))
            
            // Simulate upload process (replace with actual upload logic)
            val audioFile = File(recording.audioFilePath)
            if (!audioFile.exists()) {
                updateRecordingStatus(recordingId, RecordingStatus.FAILED)
                return false
            }
            
            // Upload audio file
            updateUploadProgress(recordingId, UploadProgress(30, "上传音频文件..."))
            // TODO: Implement actual file upload
            
            // Upload transcription
            updateUploadProgress(recordingId, UploadProgress(70, "上传识别结果..."))
            // TODO: Implement actual transcription upload
            
            // Complete upload
            updateUploadProgress(recordingId, UploadProgress(100, "上传完成"))
            updateRecordingStatus(recordingId, RecordingStatus.COMPLETED)
            
            // Clean up local file
            audioFile.delete()
            
            // Remove from pending list
            removePendingRecording(recordingId)
            
            return true
            
        } catch (e: Exception) {
            // Handle upload failure
            val updatedRecording = recording.copy(
                status = RecordingStatus.FAILED,
                retryCount = recording.retryCount + 1,
                errorMessage = ErrorHandler.getErrorMessage(e)
            )
            updatePendingRecording(updatedRecording)
            updateUploadProgress(recordingId, UploadProgress(0, "上传失败: ${e.message}"))
            
            return false
        }
    }
    
    suspend fun retryFailedUploads() {
        val failedRecordings = _pendingUploads.value.filter { 
            it.status == RecordingStatus.FAILED && it.retryCount < 3 
        }
        
        failedRecordings.forEach { recording ->
            uploadPendingRecording(recording.id)
        }
    }
    
    suspend fun uploadAllPending() {
        val pendingRecordings = _pendingUploads.value.filter { 
            it.status == RecordingStatus.PENDING 
        }
        
        pendingRecordings.forEach { recording ->
            uploadPendingRecording(recording.id)
        }
    }
    
    fun getPendingRecordingsCount(): Int {
        return _pendingUploads.value.count { it.status == RecordingStatus.PENDING }
    }
    
    fun getFailedRecordingsCount(): Int {
        return _pendingUploads.value.count { it.status == RecordingStatus.FAILED }
    }
    
    fun getTotalPendingSize(): Long {
        return _pendingUploads.value.sumOf { recording ->
            val file = File(recording.audioFilePath)
            if (file.exists()) file.length() else 0L
        }
    }
    
    suspend fun clearCompletedRecordings() {
        val activeRecordings = _pendingUploads.value.filter { 
            it.status != RecordingStatus.COMPLETED 
        }
        _pendingUploads.value = activeRecordings
        savePendingRecordings()
    }
    
    suspend fun deleteRecording(recordingId: String) {
        val recording = _pendingUploads.value.find { it.id == recordingId }
        recording?.let {
            // Delete audio file
            val audioFile = File(it.audioFilePath)
            if (audioFile.exists()) {
                audioFile.delete()
            }
            
            // Remove from list
            removePendingRecording(recordingId)
        }
    }
    
    private fun updateRecordingStatus(recordingId: String, status: RecordingStatus) {
        val currentList = _pendingUploads.value.toMutableList()
        val index = currentList.indexOfFirst { it.id == recordingId }
        if (index != -1) {
            currentList[index] = currentList[index].copy(status = status)
            _pendingUploads.value = currentList
            savePendingRecordings()
        }
    }
    
    private fun updatePendingRecording(recording: PendingRecording) {
        val currentList = _pendingUploads.value.toMutableList()
        val index = currentList.indexOfFirst { it.id == recording.id }
        if (index != -1) {
            currentList[index] = recording
            _pendingUploads.value = currentList
            savePendingRecordings()
        }
    }
    
    private fun updateUploadProgress(recordingId: String, progress: UploadProgress) {
        val currentProgress = _uploadProgress.value.toMutableMap()
        currentProgress[recordingId] = progress
        _uploadProgress.value = currentProgress
    }
    
    private fun removePendingRecording(recordingId: String) {
        val currentList = _pendingUploads.value.toMutableList()
        currentList.removeAll { it.id == recordingId }
        _pendingUploads.value = currentList
        savePendingRecordings()
        
        // Remove from progress tracking
        val currentProgress = _uploadProgress.value.toMutableMap()
        currentProgress.remove(recordingId)
        _uploadProgress.value = currentProgress
    }
    
    private fun generateRecordingId(): String {
        return "offline_${System.currentTimeMillis()}_${(1000..9999).random()}"
    }
    
    private fun getOfflineRecordingsDirectory(): File {
        val offlineDir = File(context.filesDir, "offline_recordings")
        if (!offlineDir.exists()) {
            offlineDir.mkdirs()
        }
        return offlineDir
    }
    
    private fun getPendingRecordingsFile(): File {
        return File(context.filesDir, "pending_recordings.json")
    }
    
    private fun loadPendingRecordings() {
        try {
            val file = getPendingRecordingsFile()
            if (file.exists()) {
                val jsonString = file.readText()
                val type = object : TypeToken<List<PendingRecording>>() {}.type
                val recordings = gson.fromJson<List<PendingRecording>>(jsonString, type)
                _pendingUploads.value = recordings ?: emptyList()
            }
        } catch (e: Exception) {
            // If loading fails, start with empty list
            _pendingUploads.value = emptyList()
        }
    }
    
    private fun savePendingRecordings() {
        try {
            val file = getPendingRecordingsFile()
            val jsonString = gson.toJson(_pendingUploads.value)
            file.writeText(jsonString)
        } catch (e: Exception) {
            // Log error but don't throw
        }
    }
}

data class PendingRecording(
    val id: String,
    val questionId: String,
    val sessionId: String? = null,
    val audioFilePath: String,
    val transcription: String,
    val createdAt: Long,
    val status: RecordingStatus,
    val retryCount: Int = 0,
    val errorMessage: String? = null
)

enum class RecordingStatus {
    PENDING, UPLOADING, COMPLETED, FAILED
}

data class UploadProgress(
    val percentage: Int,
    val message: String
)