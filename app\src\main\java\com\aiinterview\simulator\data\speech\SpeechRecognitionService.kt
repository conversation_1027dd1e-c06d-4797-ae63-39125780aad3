package com.aiinterview.simulator.data.speech

import android.content.Context
import android.util.Base64
import com.aiinterview.simulator.data.api.BaiduASRApi
import com.aiinterview.simulator.data.api.SpeechRecognitionApi
import com.aiinterview.simulator.data.dto.request.BaiduASRRequest
import com.aiinterview.simulator.data.dto.request.SpeechRecognitionRequest
import com.aiinterview.simulator.data.dto.response.RecognitionResult
import com.aiinterview.simulator.domain.util.ErrorHandler
import kotlinx.coroutines.delay
import java.io.File
import java.io.FileInputStream
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SpeechRecognitionService @Inject constructor(
    private val context: Context,
    private val speechRecognitionApi: SpeechRecognitionApi,
    private val baiduASRApi: BaiduASRApi
) {
    
    companion object {
        private const val MAX_RETRY_COUNT = 3
        private const val RETRY_DELAY_MS = 1000L
        private const val MIN_CONFIDENCE_THRESHOLD = 0.7 // 最低置信度阈值
        private const val BAIDU_API_KEY = "your_baidu_api_key"
        private const val BAIDU_SECRET_KEY = "your_baidu_secret_key"
    }
    
    private var baiduAccessToken: String? = null
    private var tokenExpireTime: Long = 0
    
    enum class Provider {
        BAIDU, TENCENT, AUTO
    }
    
    suspend fun recognizeAudio(
        audioFile: File,
        provider: Provider = Provider.AUTO
    ): RecognitionResult {
        var lastError: Exception? = null
        
        // 根据提供商选择进行识别
        val providers = when (provider) {
            Provider.BAIDU -> listOf(Provider.BAIDU)
            Provider.TENCENT -> listOf(Provider.TENCENT)
            Provider.AUTO -> listOf(Provider.BAIDU, Provider.TENCENT)
        }
        
        for (currentProvider in providers) {
            for (attempt in 1..MAX_RETRY_COUNT) {
                try {
                    val result = when (currentProvider) {
                        Provider.BAIDU -> recognizeWithBaidu(audioFile)
                        Provider.TENCENT -> recognizeWithTencent(audioFile)
                        Provider.AUTO -> throw IllegalStateException("AUTO should not reach here")
                    }
                    
                    // 检查识别结果质量
                    if (result.success && result.confidence >= MIN_CONFIDENCE_THRESHOLD) {
                        return result
                    } else if (result.success) {
                        // 置信度较低，但仍然返回结果
                        return result.copy(
                            errorMessage = "识别置信度较低 (${(result.confidence * 100).toInt()}%)"
                        )
                    }
                } catch (e: Exception) {
                    lastError = e
                    if (attempt < MAX_RETRY_COUNT) {
                        delay(RETRY_DELAY_MS * attempt) // 指数退避
                    }
                }
            }
        }
        
        // 所有尝试都失败了
        return RecognitionResult(
            text = "",
            confidence = 0.0,
            duration = 0L,
            provider = provider.name,
            success = false,
            errorMessage = lastError?.message ?: "语音识别失败"
        )
    }
    
    private suspend fun recognizeWithBaidu(audioFile: File): RecognitionResult {
        try {
            // 获取访问令牌
            val token = getBaiduAccessToken()
            
            // 读取音频文件并转换为Base64
            val audioBytes = audioFile.readBytes()
            val audioBase64 = Base64.encodeToString(audioBytes, Base64.NO_WRAP)
            
            // 构建请求
            val request = BaiduASRRequest(
                format = getAudioFormat(audioFile),
                rate = 16000,
                channel = 1,
                cuid = "ai_interview_${System.currentTimeMillis()}",
                token = token,
                len = audioBytes.size,
                speech = audioBase64,
                dev_pid = 1537
            )
            
            // 发送识别请求
            val response = baiduASRApi.recognizeAudio(request)
            
            return when (response.err_no) {
                0 -> {
                    val text = response.result?.joinToString("") ?: ""
                    RecognitionResult(
                        text = text,
                        confidence = if (text.isNotEmpty()) 0.85 else 0.0, // 百度API不返回置信度，使用估算值
                        duration = audioFile.length(),
                        provider = "BAIDU",
                        success = text.isNotEmpty()
                    )
                }
                else -> {
                    RecognitionResult(
                        text = "",
                        confidence = 0.0,
                        duration = audioFile.length(),
                        provider = "BAIDU",
                        success = false,
                        errorMessage = "百度ASR错误: ${response.err_msg}"
                    )
                }
            }
        } catch (e: Exception) {
            return RecognitionResult(
                text = "",
                confidence = 0.0,
                duration = audioFile.length(),
                provider = "BAIDU",
                success = false,
                errorMessage = "百度ASR异常: ${e.message}"
            )
        }
    }
    
    private suspend fun recognizeWithTencent(audioFile: File): RecognitionResult {
        // TODO: 实现腾讯云ASR识别
        // 这里先返回一个模拟结果
        return RecognitionResult(
            text = "",
            confidence = 0.0,
            duration = audioFile.length(),
            provider = "TENCENT",
            success = false,
            errorMessage = "腾讯云ASR暂未实现"
        )
    }
    
    private suspend fun getBaiduAccessToken(): String {
        // 检查token是否过期
        if (baiduAccessToken != null && System.currentTimeMillis() < tokenExpireTime) {
            return baiduAccessToken!!
        }
        
        try {
            val response = baiduASRApi.getAccessToken(
                clientId = BAIDU_API_KEY,
                clientSecret = BAIDU_SECRET_KEY
            )
            
            val accessToken = response["access_token"] as? String
            val expiresIn = (response["expires_in"] as? Number)?.toLong() ?: 0L
            
            if (accessToken != null) {
                baiduAccessToken = accessToken
                tokenExpireTime = System.currentTimeMillis() + (expiresIn * 1000) - 300000 // 提前5分钟过期
                return accessToken
            } else {
                throw Exception("获取访问令牌失败")
            }
        } catch (e: Exception) {
            throw Exception("获取百度访问令牌失败: ${e.message}")
        }
    }
    
    private fun getAudioFormat(audioFile: File): String {
        return when (audioFile.extension.lowercase()) {
            "m4a" -> "m4a"
            "mp3" -> "mp3"
            "wav" -> "wav"
            "pcm" -> "pcm"
            else -> "m4a"
        }
    }
    
    fun validateRecognitionResult(result: RecognitionResult): Boolean {
        return result.success && 
               result.text.isNotBlank() && 
               result.confidence >= MIN_CONFIDENCE_THRESHOLD
    }
    
    fun getConfidenceLevel(confidence: Double): String {
        return when {
            confidence >= 0.9 -> "很高"
            confidence >= 0.8 -> "高"
            confidence >= 0.7 -> "中等"
            confidence >= 0.6 -> "较低"
            else -> "很低"
        }
    }
    
    // 离线降级处理：返回提示用户手动输入的结果
    fun createOfflineFallbackResult(audioFile: File): RecognitionResult {
        return RecognitionResult(
            text = "[语音识别失败，请手动输入回答内容]",
            confidence = 0.0,
            duration = audioFile.length(),
            provider = "OFFLINE",
            success = false,
            errorMessage = "网络连接失败，建议手动输入回答内容"
        )
    }
}