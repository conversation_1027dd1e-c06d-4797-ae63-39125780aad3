# AI面试模拟器 - 测试指南

## 概述

本项目包含完整的测试套件，包括单元测试和UI测试，确保应用的质量和稳定性。

## 测试结构

```
app/src/
├── test/                           # 单元测试
│   └── java/com/aiinterview/simulator/
│       ├── data/
│       │   ├── repository/         # Repository层测试
│       │   ├── service/           # Service层测试
│       │   └── speech/            # 语音处理测试
│       ├── presentation/
│       │   └── viewmodel/         # ViewModel测试
│       └── util/                  # 测试工具类
│           ├── TestUtils.kt       # 通用测试工具
│           ├── MockDataFactory.kt # Mock数据工厂
│           └── TestCoverageUtils.kt # 覆盖率工具
│
└── androidTest/                    # UI测试
    └── java/com/aiinterview/simulator/
        ├── integration/            # 集成测试
        ├── presentation/
        │   └── screen/            # 界面测试
        ├── suite/                 # 测试套件
        └── util/                  # UI测试工具
            ├── ComposeTestUtils.kt    # Compose测试工具
            ├── MockTestData.kt        # UI测试数据
            └── UITestReportGenerator.kt # 测试报告生成器
```

## 快速开始

### 1. 运行所有测试

使用提供的脚本：

```bash
# Windows
scripts\run_tests.bat

# 选择选项 3 运行所有测试
```

### 2. 单独运行单元测试

```bash
./gradlew test
```

### 3. 单独运行UI测试

```bash
# 确保设备已连接或模拟器已启动
./gradlew connectedAndroidTest
```

### 4. 生成覆盖率报告

```bash
./gradlew testDebugUnitTestCoverage
```

## 测试类型详解

### 单元测试

#### Repository测试
- **AuthRepositoryTest**: 测试用户认证相关功能
  - 用户注册、登录、Token刷新
  - 错误处理和网络异常
  - 本地数据存储

- **InterviewRepositoryTest**: 测试面试相关功能
  - 岗位列表获取和缓存
  - 面试会话管理
  - 答案提交和结果获取

- **ProgressAnalysisRepositoryTest**: 测试进步分析功能
  - 历史数据分析
  - 进步趋势计算
  - 统计数据生成

#### ViewModel测试
- **AuthViewModelTest**: 测试认证界面逻辑
  - 状态管理和数据流
  - 用户交互响应
  - 错误状态处理

- **InterviewViewModelTest**: 测试面试界面逻辑
  - 面试流程控制
  - 数据状态同步
  - 用户操作响应

#### Service测试
- **SpeechRecognitionServiceTest**: 测试语音识别服务
  - 语音转文字功能
  - 错误处理和重试机制
  - 离线模式支持

### UI测试

#### 界面测试
- **LoginScreenTest**: 登录界面测试
  - 输入验证和表单提交
  - 错误提示显示
  - 导航和状态变化

- **HomeScreenTest**: 主界面测试
  - 岗位列表显示和交互
  - 搜索和筛选功能
  - 下拉刷新和加载状态

#### 集成测试
- **InterviewFlowEndToEndTest**: 端到端面试流程测试
  - 完整用户流程验证
  - 多界面交互测试
  - 异常情况处理

## 测试工具和辅助类

### 单元测试工具

#### TestUtils
```kotlin
// 创建测试数据
val userId = TestUtils.createTestUserId()
val timestamp = TestUtils.createTestTimestamp()

// 验证数据有效性
val isValid = TestDataValidator.isValidUserData(id, phone, nickname)
```

#### MockDataFactory
```kotlin
// 创建Mock用户数据
val mockUser = MockDataFactory.createMockUser()

// 创建Mock面试会话
val mockSession = MockDataFactory.createMockInterviewSession()

// 创建Mock API响应
val mockResponse = MockDataFactory.createMockApiResponse(data)
```

#### MainCoroutineRule
```kotlin
@get:Rule
val mainCoroutineRule = MainCoroutineRule()

// 在测试中使用协程
@Test
fun testCoroutineFunction() = runTest {
    // 测试代码
}
```

### UI测试工具

#### ComposeTestUtils
```kotlin
// 等待元素出现
composeTestRule.waitForElementToAppear(hasText("登录"))

// 输入文本
composeTestRule.inputText("手机号", "13800138000")

// 点击按钮
composeTestRule.clickButton("登录")

// 验证文本存在
composeTestRule.assertTextExists("登录成功")
```

#### MockTestData
```kotlin
// 创建测试岗位数据
val positions = MockTestData.createTestPositions()

// 创建测试用户数据
val user = MockTestData.createTestUser()

// 获取错误消息
val errorMsg = MockTestData.ErrorMessages.NETWORK_ERROR
```

## 测试覆盖率

### 覆盖率目标
- **总体覆盖率**: ≥ 80%
- **Repository层**: ≥ 90%
- **ViewModel层**: ≥ 90%
- **Service层**: ≥ 85%

### 查看覆盖率报告

1. 运行覆盖率测试：
```bash
./gradlew testDebugUnitTestCoverage
```

2. 打开报告：
```
app/build/reports/coverage/test/debug/index.html
```

### 覆盖率验证

```kotlin
// 使用覆盖率工具验证
val report = TestCoverageUtils.generateSampleReport()
val config = TestCoverageUtils.CoverageConfig()
val validation = TestCoverageUtils.validateCoverage(report, config)

validation.printReport()
```

## 持续集成

### GitHub Actions配置

```yaml
name: Android CI

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up JDK 11
      uses: actions/setup-java@v2
      with:
        java-version: '11'
        distribution: 'adopt'
    
    - name: Run Unit Tests
      run: ./gradlew test
    
    - name: Run UI Tests
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: 29
        script: ./gradlew connectedAndroidTest
    
    - name: Generate Coverage Report
      run: ./gradlew testDebugUnitTestCoverage
    
    - name: Upload Coverage to Codecov
      uses: codecov/codecov-action@v1
```

## 最佳实践

### 编写测试的原则

1. **AAA模式**: Arrange（准备）、Act（执行）、Assert（断言）
2. **单一职责**: 每个测试只验证一个功能点
3. **独立性**: 测试之间不应相互依赖
4. **可重复性**: 测试结果应该一致
5. **快速执行**: 单元测试应该快速完成

### 命名规范

```kotlin
// 单元测试命名: 方法名_场景_期望结果
@Test
fun login_withValidCredentials_returnsSuccess()

@Test
fun login_withInvalidCredentials_returnsError()

// UI测试命名: 界面名_操作_期望结果
@Test
fun loginScreen_inputValidPhone_enablesGetCodeButton()

@Test
fun homeScreen_clickPosition_navigatesToDetail()
```

### Mock使用指南

```kotlin
// 使用Mockito创建Mock对象
@Mock
private lateinit var authRepository: AuthRepository

// 设置Mock行为
whenever(authRepository.login(any(), any()))
    .thenReturn(flowOf(Resource.Success(authResponse)))

// 验证Mock调用
verify(authRepository).login("13800138000", "123456")
verify(authRepository, times(1)).logout()
verify(authRepository, never()).register(any(), any())
```

### 异步测试

```kotlin
// 测试Flow
@Test
fun testFlow() = runTest {
    repository.getData().test {
        val loadingItem = awaitItem()
        assertThat(loadingItem).isInstanceOf(Resource.Loading::class.java)
        
        val successItem = awaitItem()
        assertThat(successItem).isInstanceOf(Resource.Success::class.java)
        
        awaitComplete()
    }
}

// 测试协程
@Test
fun testCoroutine() = runTest {
    val result = viewModel.performAction()
    assertThat(result).isEqualTo(expectedResult)
}
```

## 故障排除

### 常见问题

1. **测试超时**
   - 增加超时时间
   - 检查网络连接
   - 验证Mock设置

2. **UI测试失败**
   - 确保设备/模拟器正常运行
   - 检查权限设置
   - 验证测试数据

3. **覆盖率不足**
   - 添加缺失的测试用例
   - 检查排除配置
   - 验证测试执行

### 调试技巧

```kotlin
// 添加日志输出
@Test
fun debugTest() {
    println("测试开始")
    // 测试代码
    println("测试结束")
}

// 使用断点调试
@Test
fun debugWithBreakpoint() {
    val result = repository.getData()
    // 在这里设置断点
    assertThat(result).isNotNull()
}
```

## 贡献指南

### 添加新测试

1. 确定测试类型（单元测试/UI测试）
2. 选择合适的测试位置
3. 使用现有的工具类和Mock数据
4. 遵循命名规范和最佳实践
5. 更新测试套件配置

### 代码审查清单

- [ ] 测试覆盖了所有重要功能
- [ ] 测试命名清晰明确
- [ ] 使用了合适的断言
- [ ] Mock设置正确
- [ ] 测试独立且可重复
- [ ] 添加了必要的注释

## 联系方式

如有测试相关问题，请联系开发团队或提交Issue。