package com.aiinterview.simulator.presentation.theme

import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * DataStore扩展属性
 * 创建主题设置的DataStore实例
 */
private val Context.themeDataStore: DataStore<Preferences> by preferencesDataStore(
    name = "theme_preferences"              // DataStore文件名
)

/**
 * 主题设置管理器
 * 负责保存和读取用户的主题偏好设置
 */
@Singleton
class ThemeManager @Inject constructor(
    @ApplicationContext private val context: Context // 应用上下文，用于访问DataStore
) {
    
    // DataStore实例，用于持久化主题设置
    private val dataStore = context.themeDataStore
    
    companion object {
        // 定义DataStore中的键名常量
        private val IS_DARK_THEME_KEY = booleanPreferencesKey("is_dark_theme")         // 深色主题开关键
        private val USE_DYNAMIC_COLORS_KEY = booleanPreferencesKey("use_dynamic_colors") // 动态颜色开关键
        private val USE_HIGH_CONTRAST_KEY = booleanPreferencesKey("use_high_contrast")  // 高对比度开关键
        private val FONT_SIZE_KEY = stringPreferencesKey("font_size")                   // 字体大小键
        private val CORNER_RADIUS_KEY = stringPreferencesKey("corner_radius")           // 圆角大小键
        private val FOLLOW_SYSTEM_THEME_KEY = booleanPreferencesKey("follow_system_theme") // 跟随系统主题键
    }
    
    /**
     * 深色主题设置流
     * 监听深色主题开关状态
     */
    val isDarkTheme: Flow<Boolean> = dataStore.data.map { preferences ->
        preferences[IS_DARK_THEME_KEY] ?: false     // 默认为浅色主题
    }
    
    /**
     * 动态颜色设置流
     * 监听动态颜色开关状态
     */
    val useDynamicColors: Flow<Boolean> = dataStore.data.map { preferences ->
        preferences[USE_DYNAMIC_COLORS_KEY] ?: true // 默认启用动态颜色
    }
    
    /**
     * 高对比度设置流
     * 监听高对比度开关状态
     */
    val useHighContrast: Flow<Boolean> = dataStore.data.map { preferences ->
        preferences[USE_HIGH_CONTRAST_KEY] ?: false // 默认不使用高对比度
    }
    
    /**
     * 字体大小设置流
     * 监听字体大小设置
     */
    val fontSize: Flow<FontSize> = dataStore.data.map { preferences ->
        val fontSizeName = preferences[FONT_SIZE_KEY] ?: FontSize.MEDIUM.name // 默认中等字体
        try {
            FontSize.valueOf(fontSizeName)          // 尝试解析字体大小枚举
        } catch (e: IllegalArgumentException) {
            FontSize.MEDIUM                         // 解析失败时使用默认值
        }
    }
    
    /**
     * 圆角大小设置流
     * 监听圆角大小设置
     */
    val cornerRadius: Flow<CornerRadius> = dataStore.data.map { preferences ->
        val cornerRadiusName = preferences[CORNER_RADIUS_KEY] ?: CornerRadius.MEDIUM.name // 默认中等圆角
        try {
            CornerRadius.valueOf(cornerRadiusName)  // 尝试解析圆角大小枚举
        } catch (e: IllegalArgumentException) {
            CornerRadius.MEDIUM                     // 解析失败时使用默认值
        }
    }
    
    /**
     * 跟随系统主题设置流
     * 监听是否跟随系统主题设置
     */
    val followSystemTheme: Flow<Boolean> = dataStore.data.map { preferences ->
        preferences[FOLLOW_SYSTEM_THEME_KEY] ?: true // 默认跟随系统主题
    }
    
    /**
     * 完整主题配置流
     * 组合所有主题设置，提供完整的主题配置
     */
    val themeConfig: Flow<ThemeConfig> = dataStore.data.map { preferences ->
        ThemeConfig(
            isDarkTheme = preferences[IS_DARK_THEME_KEY] ?: false,              // 深色主题设置
            useDynamicColors = preferences[USE_DYNAMIC_COLORS_KEY] ?: true,     // 动态颜色设置
            useHighContrast = preferences[USE_HIGH_CONTRAST_KEY] ?: false,      // 高对比度设置
            fontSize = try {                                                    // 字体大小设置
                FontSize.valueOf(preferences[FONT_SIZE_KEY] ?: FontSize.MEDIUM.name)
            } catch (e: IllegalArgumentException) {
                FontSize.MEDIUM                                                 // 默认中等字体
            },
            cornerRadius = try {                                                // 圆角大小设置
                CornerRadius.valueOf(preferences[CORNER_RADIUS_KEY] ?: CornerRadius.MEDIUM.name)
            } catch (e: IllegalArgumentException) {
                CornerRadius.MEDIUM                                             // 默认中等圆角
            }
        )
    }
    
    /**
     * 设置深色主题
     * 保存深色主题开关状态
     * @param isDark 是否启用深色主题
     */
    suspend fun setDarkTheme(isDark: Boolean) {
        dataStore.edit { preferences ->
            preferences[IS_DARK_THEME_KEY] = isDark // 保存深色主题设置
        }
    }
    
    /**
     * 设置动态颜色
     * 保存动态颜色开关状态
     * @param useDynamic 是否启用动态颜色
     */
    suspend fun setDynamicColors(useDynamic: Boolean) {
        dataStore.edit { preferences ->
            preferences[USE_DYNAMIC_COLORS_KEY] = useDynamic // 保存动态颜色设置
        }
    }
    
    /**
     * 设置高对比度
     * 保存高对比度开关状态
     * @param useHighContrast 是否启用高对比度
     */
    suspend fun setHighContrast(useHighContrast: Boolean) {
        dataStore.edit { preferences ->
            preferences[USE_HIGH_CONTRAST_KEY] = useHighContrast // 保存高对比度设置
        }
    }
    
    /**
     * 设置字体大小
     * 保存字体大小设置
     * @param fontSize 字体大小枚举值
     */
    suspend fun setFontSize(fontSize: FontSize) {
        dataStore.edit { preferences ->
            preferences[FONT_SIZE_KEY] = fontSize.name // 保存字体大小设置
        }
    }
    
    /**
     * 设置圆角大小
     * 保存圆角大小设置
     * @param cornerRadius 圆角大小枚举值
     */
    suspend fun setCornerRadius(cornerRadius: CornerRadius) {
        dataStore.edit { preferences ->
            preferences[CORNER_RADIUS_KEY] = cornerRadius.name // 保存圆角大小设置
        }
    }
    
    /**
     * 设置跟随系统主题
     * 保存是否跟随系统主题设置
     * @param followSystem 是否跟随系统主题
     */
    suspend fun setFollowSystemTheme(followSystem: Boolean) {
        dataStore.edit { preferences ->
            preferences[FOLLOW_SYSTEM_THEME_KEY] = followSystem // 保存跟随系统主题设置
        }
    }
    
    /**
     * 重置所有主题设置
     * 将所有主题设置恢复为默认值
     */
    suspend fun resetThemeSettings() {
        dataStore.edit { preferences ->
            preferences.clear()                     // 清除所有设置，恢复默认值
        }
    }
    
    /**
     * 批量更新主题配置
     * 一次性更新多个主题设置
     * @param config 新的主题配置
     */
    suspend fun updateThemeConfig(config: ThemeConfig) {
        dataStore.edit { preferences ->
            preferences[IS_DARK_THEME_KEY] = config.isDarkTheme         // 更新深色主题设置
            preferences[USE_DYNAMIC_COLORS_KEY] = config.useDynamicColors // 更新动态颜色设置
            preferences[USE_HIGH_CONTRAST_KEY] = config.useHighContrast // 更新高对比度设置
            preferences[FONT_SIZE_KEY] = config.fontSize.name           // 更新字体大小设置
            preferences[CORNER_RADIUS_KEY] = config.cornerRadius.name   // 更新圆角大小设置
        }
    }
}

/**
 * 主题管理器组合函数
 * 在Compose中使用主题管理器的便捷函数
 * 注意：实际使用时需要通过Hilt在ViewModel中注入ThemeManager
 */
@Composable
fun rememberThemeManager(): ThemeManager? {
    // 这个函数主要用于文档说明
    // 实际使用时应该通过ViewModel获取ThemeManager实例
    // 例如：val themeManager = hiltViewModel<ThemeSettingsViewModel>().themeManager
    return null // 返回null，提示开发者使用正确的方式获取实例
}

/**
 * 主题配置状态组合函数
 * 获取当前的主题配置状态
 * @param themeManager 主题管理器实例
 * @return 当前的主题配置
 */
@Composable
fun rememberThemeConfig(themeManager: ThemeManager): ThemeConfig {
    val themeConfig by themeManager.themeConfig.collectAsState(
        initial = ThemeConfig()                 // 提供初始默认配置
    )
    return themeConfig                          // 返回当前主题配置
}

/**
 * 深色主题状态组合函数
 * 获取当前的深色主题状态
 * @param themeManager 主题管理器实例
 * @return 是否启用深色主题
 */
@Composable
fun rememberDarkThemeState(themeManager: ThemeManager): Boolean {
    val isDarkTheme by themeManager.isDarkTheme.collectAsState(
        initial = false                         // 初始值为浅色主题
    )
    return isDarkTheme                          // 返回深色主题状态
}

/**
 * 动态颜色状态组合函数
 * 获取当前的动态颜色状态
 * @param themeManager 主题管理器实例
 * @return 是否启用动态颜色
 */
@Composable
fun rememberDynamicColorsState(themeManager: ThemeManager): Boolean {
    val useDynamicColors by themeManager.useDynamicColors.collectAsState(
        initial = true                          // 初始值为启用动态颜色
    )
    return useDynamicColors                     // 返回动态颜色状态
}

/**
 * 高对比度状态组合函数
 * 获取当前的高对比度状态
 * @param themeManager 主题管理器实例
 * @return 是否启用高对比度
 */
@Composable
fun rememberHighContrastState(themeManager: ThemeManager): Boolean {
    val useHighContrast by themeManager.useHighContrast.collectAsState(
        initial = false                         // 初始值为不使用高对比度
    )
    return useHighContrast                      // 返回高对比度状态
}

/**
 * 字体大小状态组合函数
 * 获取当前的字体大小状态
 * @param themeManager 主题管理器实例
 * @return 当前字体大小设置
 */
@Composable
fun rememberFontSizeState(themeManager: ThemeManager): FontSize {
    val fontSize by themeManager.fontSize.collectAsState(
        initial = FontSize.MEDIUM               // 初始值为中等字体
    )
    return fontSize                             // 返回字体大小状态
}

/**
 * 圆角大小状态组合函数
 * 获取当前的圆角大小状态
 * @param themeManager 主题管理器实例
 * @return 当前圆角大小设置
 */
@Composable
fun rememberCornerRadiusState(themeManager: ThemeManager): CornerRadius {
    val cornerRadius by themeManager.cornerRadius.collectAsState(
        initial = CornerRadius.MEDIUM           // 初始值为中等圆角
    )
    return cornerRadius                         // 返回圆角大小状态
}