package com.aiinterview.simulator.presentation.screen.legal

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController

/**
 * 隐私政策页面
 * 显示应用的隐私政策内容，符合中国法律法规要求
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PrivacyPolicyScreen(
    navController: NavController
) {
    val scrollState = rememberScrollState()
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "隐私政策",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Medium
                    )
                },
                navigationIcon = {
                    IconButton(onClick = { navController.navigateUp() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(scrollState)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 生效日期
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Text(
                    text = "生效日期：2024年1月1日\n最后更新：2024年1月1日",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(16.dp),
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }
            
            // 引言
            PrivacySection(
                title = "引言",
                content = """
                    欢迎使用AI面试模拟器！我们深知个人信息对您的重要性，并会尽全力保护您的个人信息安全可靠。我们致力于维持您对我们的信任，恪守以下原则，保护您的个人信息：权责一致原则、目的明确原则、选择同意原则、最少够用原则、确保安全原则、主体参与原则、公开透明原则等。
                    
                    本隐私政策适用于您通过AI面试模拟器移动应用程序使用我们的产品和服务。请您仔细阅读并充分理解本隐私政策，特别是以粗体标识的条款。如果您不同意本隐私政策的任何内容，您应立即停止使用我们的服务。
                """.trimIndent()
            )
            
            // 信息收集
            PrivacySection(
                title = "我们如何收集和使用您的个人信息",
                content = """
                    我们会出于本政策所述的以下目的，收集和使用您的个人信息：
                    
                    **1. 账户注册与登录**
                    • 手机号码：用于账户注册、登录验证和安全保护
                    • 验证码：用于验证您的身份和账户安全
                    • 昵称和头像：用于个性化您的账户信息
                    
                    **2. 面试模拟服务**
                    • 语音数据：用于语音识别和面试回答分析
                    • 面试记录：包括您的回答内容、评价结果等，用于提供个性化服务
                    • 岗位选择：用于为您推荐合适的面试题目
                    
                    **3. 设备信息**
                    • 设备型号、操作系统版本：用于适配和优化应用性能
                    • 网络信息：用于确保服务的正常运行
                    • 应用崩溃日志：用于改进应用稳定性（不包含个人敏感信息）
                    
                    **4. 使用统计**
                    • 功能使用情况：用于了解用户需求，改进产品功能
                    • 性能数据：用于优化应用性能和用户体验
                """.trimIndent()
            )
            
            // 信息使用
            PrivacySection(
                title = "我们如何使用您的信息",
                content = """
                    我们会基于您的同意或为履行与您的合同或为履行我们的法定义务或为维护我们的合法权益，将收集到的信息用于以下目的：
                    
                    **1. 提供服务**
                    • 为您提供面试模拟、语音识别、AI评价等核心功能
                    • 保存您的面试记录和进步分析
                    • 为您推荐个性化的面试内容
                    
                    **2. 改进服务**
                    • 分析用户使用习惯，优化产品功能
                    • 进行数据统计和分析，改进服务质量
                    • 开发新功能和服务
                    
                    **3. 安全保障**
                    • 验证用户身份，防止欺诈行为
                    • 检测和防范安全威胁
                    • 保护用户和平台的合法权益
                    
                    **4. 客户服务**
                    • 回应您的咨询和投诉
                    • 向您发送服务相关通知
                    • 提供技术支持
                """.trimIndent()
            )
            
            // 信息共享
            PrivacySection(
                title = "我们如何共享、转让、公开披露您的个人信息",
                content = """
                    **共享**
                    我们不会与任何公司、组织和个人共享您的个人信息，但以下情况除外：
                    • 事先获得您的明确同意
                    • 根据适用的法律法规、法律程序的要求
                    • 在法律要求或允许的范围内，为维护社会公共利益
                    • 与我们的关联公司共享（仅限于实现本政策声明目的所必需）
                    
                    **转让**
                    我们不会将您的个人信息转让给任何公司、组织和个人，但以下情况除外：
                    • 事先获得您的明确同意
                    • 涉及合并、收购或破产清算时，如涉及个人信息转让，我们会要求新的持有您个人信息的公司继续受本政策的约束
                    
                    **公开披露**
                    我们仅会在以下情况下公开披露您的个人信息：
                    • 获得您的明确同意后
                    • 基于法律的披露：在法律、法律程序、诉讼或政府主管部门强制性要求的情况下
                """.trimIndent()
            )
            
            // 信息保护
            PrivacySection(
                title = "我们如何保护您的个人信息",
                content = """
                    我们非常重视个人信息安全，并采取一切合理可行的措施，保护您的个人信息：
                    
                    **技术措施**
                    • 使用加密技术确保数据传输安全
                    • 采用访问控制机制，确保只有授权人员才可访问个人信息
                    • 定期进行安全审计和漏洞扫描
                    • 建立数据备份和恢复机制
                    
                    **管理措施**
                    • 建立专门的个人信息保护团队
                    • 对员工进行数据保护培训
                    • 制定严格的数据访问权限管理制度
                    • 与第三方服务商签署数据保护协议
                    
                    **物理措施**
                    • 服务器托管在具备相应安全资质的数据中心
                    • 实施严格的数据中心访问控制
                    • 配备防火、防水、防盗等安全设施
                    
                    **数据存储**
                    • 您的个人信息将存储在中华人民共和国境内
                    • 如需跨境传输，我们将严格按照法律法规要求执行
                """.trimIndent()
            )
            
            // 用户权利
            PrivacySection(
                title = "您的权利",
                content = """
                    按照中国相关的法律、法规、标准，我们保障您对自己的个人信息行使以下权利：
                    
                    **访问权**
                    您有权访问您的个人信息，法律法规规定的例外情况除外。您可以通过以下方式访问您的个人信息：
                    • 在应用内查看和管理您的账户信息
                    • 查看您的面试记录和评价历史
                    
                    **更正权**
                    当您发现我们处理的关于您的个人信息有错误时，您有权要求我们做出更正。您可以通过应用内设置或联系我们进行更正。
                    
                    **删除权**
                    在以下情形中，您可以向我们提出删除个人信息的请求：
                    • 我们处理个人信息的行为违反法律法规
                    • 我们收集、使用您的个人信息，却未征得您的同意
                    • 我们处理个人信息的行为违反了与您的约定
                    • 您不再使用我们的产品或服务，或您注销了账号
                    
                    **撤回同意权**
                    您可以通过删除信息、关闭设备功能、在应用内设置中进行隐私设置等方式改变您授权我们继续收集个人信息的范围或撤回您的授权。
                    
                    **注销权**
                    您可以在应用内申请注销您的账户。账户注销后，我们将停止为您提供产品或服务，并根据您的要求删除您的个人信息。
                """.trimIndent()
            )
            
            // 未成年人保护
            PrivacySection(
                title = "未成年人的个人信息保护",
                content = """
                    我们非常重视对未成年人个人信息的保护。如果您是18周岁以下的未成年人，建议您请您的父母或监护人仔细阅读本隐私政策，并在征得您的父母或监护人同意的前提下使用我们的服务或向我们提供信息。
                    
                    对于经父母或监护人同意使用我们的产品或服务而收集未成年人个人信息的情况，我们只会在法律法规允许、父母或监护人明确同意或者保护未成年人所必要的情况下使用、共享、转让或披露此信息。
                    
                    如果我们发现自己在未事先获得可证实的父母或监护人同意的情况下收集了未成年人的个人信息，则会设法尽快删除相关数据。
                """.trimIndent()
            )
            
            // 政策更新
            PrivacySection(
                title = "本隐私政策如何更新",
                content = """
                    我们可能适时修订本政策的条款，该等修订构成本政策的一部分。如该等修订造成您在本政策下权利的实质减少或重大变更，我们将在修订生效前通过在主页上显著位置提示或向您发送电子邮件或以其他方式通知您。
                    
                    在该种情况下，若您继续使用我们的服务，即表示同意受经修订的本政策的约束。
                    
                    本政策所指的重大变更包括但不限于：
                    • 我们的服务模式发生重大变化
                    • 个人信息共享、转让或公开披露的主要对象发生变化
                    • 您参与个人信息处理方面的权利及其行使方式发生重大变化
                    • 我们负责处理个人信息安全的责任部门、联络方式及投诉渠道发生变化
                """.trimIndent()
            )
            
            // 联系我们
            PrivacySection(
                title = "如何联系我们",
                content = """
                    如果您对本隐私政策有任何疑问、意见或建议，通过以下方式与我们联系：
                    
                    **邮箱：** <EMAIL>
                    **客服电话：** 400-123-4567（工作日 9:00-18:00）
                    **通信地址：** 中国北京市朝阳区XXX大厦XX层
                    **邮政编码：** 100000
                    
                    我们将在收到您的反馈后尽快回复您。一般情况下，我们将在15个工作日内回复。
                    
                    如果您对我们的回复不满意，特别是我们的个人信息处理行为损害了您的合法权益，您还可以向网信、电信、公安及工商等监管部门进行投诉或举报。
                """.trimIndent()
            )
        }
    }
}

/**
 * 隐私政策章节组件
 */
@Composable
private fun PrivacySection(
    title: String,
    content: String
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
            
            Text(
                text = content,
                style = MaterialTheme.typography.bodyMedium,
                lineHeight = MaterialTheme.typography.bodyMedium.lineHeight * 1.4
            )
        }
    }
}