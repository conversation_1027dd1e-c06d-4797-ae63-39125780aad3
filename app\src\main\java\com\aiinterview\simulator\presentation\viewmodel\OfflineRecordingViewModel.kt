package com.aiinterview.simulator.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.aiinterview.simulator.data.audio.OfflineRecordingManager
import com.aiinterview.simulator.data.audio.PendingRecording
import com.aiinterview.simulator.data.audio.RecordingStatus
import com.aiinterview.simulator.data.audio.UploadProgress
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import java.io.File
import javax.inject.Inject

@HiltViewModel
class OfflineRecordingViewModel @Inject constructor(
    private val offlineRecordingManager: OfflineRecordingManager
) : ViewModel() {
    
    val pendingUploads: StateFlow<List<PendingRecording>> = offlineRecordingManager.pendingUploads
    val uploadProgress: StateFlow<Map<String, UploadProgress>> = offlineRecordingManager.uploadProgress
    
    fun saveOfflineRecording(
        questionId: String,
        audioFile: File,
        transcription: String,
        sessionId: String? = null
    ) {
        viewModelScope.launch {
            offlineRecordingManager.saveOfflineRecording(questionId, audioFile, transcription, sessionId)
        }
    }
    
    fun uploadPendingRecording(recordingId: String) {
        viewModelScope.launch {
            offlineRecordingManager.uploadPendingRecording(recordingId)
        }
    }
    
    fun retryFailedUploads() {
        viewModelScope.launch {
            offlineRecordingManager.retryFailedUploads()
        }
    }
    
    fun uploadAllPending() {
        viewModelScope.launch {
            offlineRecordingManager.uploadAllPending()
        }
    }
    
    fun deleteRecording(recordingId: String) {
        viewModelScope.launch {
            offlineRecordingManager.deleteRecording(recordingId)
        }
    }
    
    fun clearCompletedRecordings() {
        viewModelScope.launch {
            offlineRecordingManager.clearCompletedRecordings()
        }
    }
    
    fun getPendingRecordingsCount(): Int {
        return offlineRecordingManager.getPendingRecordingsCount()
    }
    
    fun getFailedRecordingsCount(): Int {
        return offlineRecordingManager.getFailedRecordingsCount()
    }
    
    fun getTotalPendingSize(): Long {
        return offlineRecordingManager.getTotalPendingSize()
    }
    
    fun formatFileSize(bytes: Long): String {
        return when {
            bytes < 1024 -> "${bytes}B"
            bytes < 1024 * 1024 -> "${bytes / 1024}KB"
            else -> "${bytes / (1024 * 1024)}MB"
        }
    }
    
    fun getStatusDescription(status: RecordingStatus): String {
        return when (status) {
            RecordingStatus.PENDING -> "等待上传"
            RecordingStatus.UPLOADING -> "正在上传"
            RecordingStatus.COMPLETED -> "已完成"
            RecordingStatus.FAILED -> "上传失败"
        }
    }
    
    fun getStatusColor(status: RecordingStatus): androidx.compose.ui.graphics.Color {
        return when (status) {
            RecordingStatus.PENDING -> androidx.compose.ui.graphics.Color(0xFF2196F3)
            RecordingStatus.UPLOADING -> androidx.compose.ui.graphics.Color(0xFFFF9800)
            RecordingStatus.COMPLETED -> androidx.compose.ui.graphics.Color(0xFF4CAF50)
            RecordingStatus.FAILED -> androidx.compose.ui.graphics.Color(0xFFF44336)
        }
    }
}