package com.aiinterview.simulator.presentation.screen.interview

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.aiinterview.simulator.data.dto.response.RecognitionResult
import com.aiinterview.simulator.data.model.Question
import com.aiinterview.simulator.presentation.viewmodel.SpeechRecognitionViewModel
import com.aiinterview.simulator.domain.util.Resource
import java.io.File

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SpeechRecognitionScreen(
    question: Question,
    audioFile: File,
    onRecognitionComplete: (String) -> Unit,
    onNavigateBack: () -> Unit,
    onRetryRecording: () -> Unit,
    viewModel: SpeechRecognitionViewModel = hiltViewModel()
) {
    val scrollState = rememberScrollState()
    
    val recognitionState by viewModel.recognitionState.collectAsState()
    val currentResult by viewModel.currentRecognitionResult.collectAsState()
    val isRecognizing by viewModel.isRecognizing.collectAsState()
    
    var editableText by remember { mutableStateOf("") }
    var isEditing by remember { mutableStateOf(false) }
    
    // 开始语音识别
    LaunchedEffect(audioFile) {
        viewModel.recognizeAudio(audioFile)
    }
    
    // 更新可编辑文本
    LaunchedEffect(currentResult) {
        currentResult?.let { result ->
            if (!isEditing) {
                editableText = result.text
            }
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(scrollState)
    ) {
        // 顶部应用栏
        TopAppBar(
            title = { Text("语音识别结果") },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            }
        )
        
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 题目信息
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = question.type.displayName,
                        style = MaterialTheme.typography.labelMedium,
                        color = MaterialTheme.colorScheme.primary
                    )
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    Text(
                        text = question.title,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 识别状态
            when (recognitionState) {
                is Resource.Loading -> {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.primaryContainer
                        )
                    ) {
                        Row(
                            modifier = Modifier.padding(16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(24.dp),
                                strokeWidth = 2.dp
                            )
                            
                            Spacer(modifier = Modifier.width(12.dp))
                            
                            Text(
                                text = "正在识别语音内容...",
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }
                }
                
                is Resource.Success -> {
                    currentResult?.let { result ->
                        // 识别结果卡片
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                        ) {
                            Column(
                                modifier = Modifier.padding(16.dp)
                            ) {
                                // 识别质量指示器
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Text(
                                        text = "识别质量",
                                        style = MaterialTheme.typography.labelMedium
                                    )
                                    
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Icon(
                                            imageVector = when {
                                                result.confidence >= 0.8 -> Icons.Default.CheckCircle
                                                result.confidence >= 0.6 -> Icons.Default.Warning
                                                else -> Icons.Default.Error
                                            },
                                            contentDescription = null,
                                            tint = when {
                                                result.confidence >= 0.8 -> MaterialTheme.colorScheme.primary
                                                result.confidence >= 0.6 -> MaterialTheme.colorScheme.tertiary
                                                else -> MaterialTheme.colorScheme.error
                                            },
                                            modifier = Modifier.size(16.dp)
                                        )
                                        
                                        Spacer(modifier = Modifier.width(4.dp))
                                        
                                        Text(
                                            text = "${(result.confidence * 100).toInt()}%",
                                            style = MaterialTheme.typography.labelSmall,
                                            color = when {
                                                result.confidence >= 0.8 -> MaterialTheme.colorScheme.primary
                                                result.confidence >= 0.6 -> MaterialTheme.colorScheme.tertiary
                                                else -> MaterialTheme.colorScheme.error
                                            }
                                        )
                                    }
                                }
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                Text(
                                    text = viewModel.getRecognitionQualityMessage(),
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                                
                                Spacer(modifier = Modifier.height(16.dp))
                                
                                // 识别文本
                                Text(
                                    text = "识别内容",
                                    style = MaterialTheme.typography.labelMedium,
                                    fontWeight = FontWeight.Bold
                                )
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                if (isEditing) {
                                    OutlinedTextField(
                                        value = editableText,
                                        onValueChange = { editableText = it },
                                        modifier = Modifier.fillMaxWidth(),
                                        minLines = 3,
                                        maxLines = 10,
                                        placeholder = { Text("请输入或编辑回答内容...") }
                                    )
                                } else {
                                    SelectionContainer {
                                        Card(
                                            modifier = Modifier.fillMaxWidth(),
                                            colors = CardDefaults.cardColors(
                                                containerColor = MaterialTheme.colorScheme.surfaceVariant
                                            )
                                        ) {
                                            Text(
                                                text = if (result.text.isNotBlank()) result.text else "未识别到有效内容",
                                                style = MaterialTheme.typography.bodyMedium,
                                                modifier = Modifier.padding(12.dp),
                                                color = if (result.text.isNotBlank()) {
                                                    MaterialTheme.colorScheme.onSurfaceVariant
                                                } else {
                                                    MaterialTheme.colorScheme.outline
                                                }
                                            )
                                        }
                                    }
                                }
                                
                                Spacer(modifier = Modifier.height(16.dp))
                                
                                // 操作按钮
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    // 编辑按钮
                                    if (!isEditing) {
                                        OutlinedButton(
                                            onClick = { isEditing = true },
                                            modifier = Modifier.weight(1f)
                                        ) {
                                            Icon(
                                                Icons.Default.Edit,
                                                contentDescription = null,
                                                modifier = Modifier.size(16.dp)
                                            )
                                            Spacer(modifier = Modifier.width(4.dp))
                                            Text("编辑")
                                        }
                                    } else {
                                        OutlinedButton(
                                            onClick = {
                                                viewModel.updateRecognitionText(editableText)
                                                isEditing = false
                                            },
                                            modifier = Modifier.weight(1f)
                                        ) {
                                            Icon(
                                                Icons.Default.Check,
                                                contentDescription = null,
                                                modifier = Modifier.size(16.dp)
                                            )
                                            Spacer(modifier = Modifier.width(4.dp))
                                            Text("保存")
                                        }
                                    }
                                    
                                    // 重新识别按钮
                                    OutlinedButton(
                                        onClick = { viewModel.retryRecognition(audioFile) },
                                        modifier = Modifier.weight(1f),
                                        enabled = !isRecognizing
                                    ) {
                                        Icon(
                                            Icons.Default.Refresh,
                                            contentDescription = null,
                                            modifier = Modifier.size(16.dp)
                                        )
                                        Spacer(modifier = Modifier.width(4.dp))
                                        Text("重新识别")
                                    }
                                }
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                // 重新录制按钮
                                OutlinedButton(
                                    onClick = onRetryRecording,
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    Icon(
                                        Icons.Default.Mic,
                                        contentDescription = null,
                                        modifier = Modifier.size(16.dp)
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text("重新录制")
                                }
                            }
                        }
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        // 识别详情
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                        ) {
                            Column(
                                modifier = Modifier.padding(16.dp)
                            ) {
                                Text(
                                    text = "识别详情",
                                    style = MaterialTheme.typography.titleSmall,
                                    fontWeight = FontWeight.Bold
                                )
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "服务提供商",
                                        style = MaterialTheme.typography.bodySmall
                                    )
                                    Text(
                                        text = result.provider,
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.primary
                                    )
                                }
                                
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "音频时长",
                                        style = MaterialTheme.typography.bodySmall
                                    )
                                    Text(
                                        text = "${result.duration / 1000}秒",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.primary
                                    )
                                }
                                
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "置信度等级",
                                        style = MaterialTheme.typography.bodySmall
                                    )
                                    Text(
                                        text = viewModel.getConfidenceDescription(result.confidence),
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.primary
                                    )
                                }
                            }
                        }
                    }
                }
                
                is Resource.Error -> {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.errorContainer
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    Icons.Default.Error,
                                    contentDescription = null,
                                    tint = MaterialTheme.colorScheme.onErrorContainer
                                )
                                
                                Spacer(modifier = Modifier.width(8.dp))
                                
                                Text(
                                    text = "识别失败",
                                    style = MaterialTheme.typography.titleSmall,
                                    color = MaterialTheme.colorScheme.onErrorContainer,
                                    fontWeight = FontWeight.Bold
                                )
                            }
                            
                            Spacer(modifier = Modifier.height(8.dp))
                            
                            Text(
                                text = recognitionState.message ?: "未知错误",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onErrorContainer
                            )
                            
                            Spacer(modifier = Modifier.height(12.dp))
                            
                            Row(
                                horizontalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                OutlinedButton(
                                    onClick = { viewModel.recognizeAudio(audioFile) },
                                    modifier = Modifier.weight(1f)
                                ) {
                                    Text("重试")
                                }
                                
                                OutlinedButton(
                                    onClick = onRetryRecording,
                                    modifier = Modifier.weight(1f)
                                ) {
                                    Text("重新录制")
                                }
                            }
                        }
                    }
                }
                
                null -> {
                    // Initial state
                }
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 确认按钮
            Button(
                onClick = {
                    val finalText = if (isEditing) editableText else (currentResult?.text ?: "")
                    onRecognitionComplete(finalText)
                },
                modifier = Modifier.fillMaxWidth(),
                enabled = currentResult != null && (currentResult.text.isNotBlank() || editableText.isNotBlank())
            ) {
                Text("确认并继续")
            }
        }
    }
}