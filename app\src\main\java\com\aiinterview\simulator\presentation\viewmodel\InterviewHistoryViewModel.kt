package com.aiinterview.simulator.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.aiinterview.simulator.data.model.*
import com.aiinterview.simulator.data.repository.InterviewRecordRepository
import com.aiinterview.simulator.data.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 面试历史记录ViewModel
 * 负责管理面试历史记录的数据状态、搜索筛选逻辑和用户交互
 */

@HiltViewModel
class InterviewHistoryViewModel @Inject constructor(
    private val interviewRecordRepository: InterviewRecordRepository,
    private val authRepository: AuthRepository
) : ViewModel() {
    
    // UI状态管理：包含加载状态、数据和错误信息
    private val _uiState = MutableStateFlow(InterviewHistoryUiState())
    val uiState: StateFlow<InterviewHistoryUiState> = _uiState.asStateFlow()
    
    // 搜索关键词状态：用户输入的搜索文本
    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()
    
    // 类别筛选状态：当前选中的岗位类别
    private val _filterCategory = MutableStateFlow<String?>(null)
    val filterCategory: StateFlow<String?> = _filterCategory.asStateFlow()
    
    // 时间范围筛选状态：当前选中的时间范围
    private val _timeRange = MutableStateFlow(TimeRange.ALL)
    val timeRange: StateFlow<TimeRange> = _timeRange.asStateFlow()
    
    // 排序方式状态：当前选中的排序规则
    private val _sortOrder = MutableStateFlow(SortOrder.TIME_DESC)
    val sortOrder: StateFlow<SortOrder> = _sortOrder.asStateFlow()
    
    init {
        // 初始化时加载面试历史记录
        loadInterviewHistory()
        // 开始监听搜索和筛选条件的变化
        observeSearchAndFilter()
    }
    
    /**
     * 加载面试历史记录
     * 从数据库获取当前用户的所有面试记录摘要信息
     */
    private fun loadInterviewHistory() {
        viewModelScope.launch {
            try {
                // 设置加载状态，清除之前的错误信息
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                // 获取当前登录用户
                val currentUser = authRepository.getCurrentUser()
                if (currentUser != null) {
                    // 获取用户的面试记录摘要，使用Flow持续监听数据变化
                    interviewRecordRepository.getRecordSummariesByUser(currentUser.id)
                        .catch { e ->
                            // 处理数据流中的异常
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                error = "加载历史记录失败: ${e.message}"
                            )
                        }
                        .collect { records ->
                            // 成功获取数据，更新UI状态
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                allRecords = records,
                                error = null
                            )
                        }
                } else {
                    // 用户未登录的情况
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "用户未登录"
                    )
                }
            } catch (e: Exception) {
                // 处理其他异常情况
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "加载历史记录失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 监听搜索和筛选条件变化
     */
    private fun observeSearchAndFilter() {
        viewModelScope.launch {
            combine(
                _uiState.map { it.allRecords },
                _searchQuery,
                _filterCategory,
                _timeRange,
                _sortOrder
            ) { records, query, category, timeRange, sortOrder ->
                filterAndSortRecords(records, query, category, timeRange, sortOrder)
            }.collect { filteredRecords ->
                _uiState.value = _uiState.value.copy(
                    filteredRecords = filteredRecords
                )
            }
        }
    }
    
    /**
     * 筛选和排序记录
     * 使用内存中的筛选逻辑，提供实时响应
     */
    private suspend fun filterAndSortRecords(
        records: List<InterviewRecordSummary>,
        query: String,
        category: String?,
        timeRange: TimeRange,
        sortOrder: SortOrder
    ): List<InterviewRecordSummary> {
        var filteredRecords = records
        
        // 搜索筛选：支持在岗位名称和类别中搜索
        if (query.isNotBlank()) {
            filteredRecords = filteredRecords.filter { record ->
                record.positionName.contains(query, ignoreCase = true) ||
                record.positionCategory.contains(query, ignoreCase = true)
            }
        }
        
        // 类别筛选：精确匹配岗位类别
        if (category != null) {
            filteredRecords = filteredRecords.filter { record ->
                record.positionCategory == category
            }
        }
        
        // 时间范围筛选：根据选择的时间范围过滤记录
        val currentTime = System.currentTimeMillis()
        filteredRecords = when (timeRange) {
            TimeRange.TODAY -> {
                // 今天：过去24小时内的记录
                val todayStart = currentTime - (24 * 60 * 60 * 1000)
                filteredRecords.filter { it.startTime >= todayStart }
            }
            TimeRange.WEEK -> {
                // 本周：过去7天内的记录
                val weekStart = currentTime - (7 * 24 * 60 * 60 * 1000)
                filteredRecords.filter { it.startTime >= weekStart }
            }
            TimeRange.MONTH -> {
                // 本月：过去30天内的记录
                val monthStart = currentTime - (30 * 24 * 60 * 60 * 1000)
                filteredRecords.filter { it.startTime >= monthStart }
            }
            TimeRange.ALL -> filteredRecords // 全部：不进行时间筛选
        }
        
        // 排序：根据选择的排序方式对记录进行排序
        return when (sortOrder) {
            SortOrder.TIME_DESC -> filteredRecords.sortedByDescending { it.startTime } // 时间倒序（最新在前）
            SortOrder.TIME_ASC -> filteredRecords.sortedBy { it.startTime } // 时间正序（最早在前）
            SortOrder.SCORE_DESC -> filteredRecords.sortedByDescending { it.overallScore ?: 0.0 } // 分数倒序（高分在前）
            SortOrder.SCORE_ASC -> filteredRecords.sortedBy { it.overallScore ?: 0.0 } // 分数正序（低分在前）
            SortOrder.POSITION_NAME -> filteredRecords.sortedBy { it.positionName } // 岗位名称字母顺序
        }
    }
    
    /**
     * 执行高级搜索和筛选
     * 使用数据库级别的搜索和筛选，适用于大量数据的情况
     */
    fun performAdvancedSearch(
        keyword: String? = null,
        category: String? = null,
        timeRange: TimeRange = TimeRange.ALL,
        status: InterviewRecordStatus? = null
    ) {
        viewModelScope.launch {
            try {
                // 设置加载状态
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                // 获取当前登录用户
                val currentUser = authRepository.getCurrentUser()
                if (currentUser != null) {
                    // 计算时间范围
                    val currentTime = System.currentTimeMillis()
                    val (startTime, endTime) = when (timeRange) {
                        TimeRange.TODAY -> {
                            val todayStart = currentTime - (24 * 60 * 60 * 1000)
                            todayStart to currentTime
                        }
                        TimeRange.WEEK -> {
                            val weekStart = currentTime - (7 * 24 * 60 * 60 * 1000)
                            weekStart to currentTime
                        }
                        TimeRange.MONTH -> {
                            val monthStart = currentTime - (30 * 24 * 60 * 60 * 1000)
                            monthStart to currentTime
                        }
                        TimeRange.ALL -> null to null
                    }
                    
                    // 执行数据库级别的搜索和筛选
                    val records = interviewRecordRepository.searchAndFilterRecords(
                        userId = currentUser.id,
                        keyword = keyword?.takeIf { it.isNotBlank() },
                        category = category,
                        startTime = startTime,
                        endTime = endTime,
                        status = status
                    )
                    
                    // 转换为摘要格式
                    val summaries = records.map { record ->
                        InterviewRecordSummary(
                            id = record.id,
                            sessionId = record.sessionId,
                            positionName = record.positionName,
                            positionCategory = record.positionCategory,
                            startTime = record.startTime,
                            duration = record.duration,
                            overallScore = record.overallScore,
                            status = record.status,
                            isSynced = record.isSynced
                        )
                    }
                    
                    // 更新UI状态
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        allRecords = summaries,
                        filteredRecords = summaries,
                        error = null
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "用户未登录"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "搜索失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 设置搜索关键词
     */
    fun setSearchQuery(query: String) {
        _searchQuery.value = query
    }
    
    /**
     * 设置类别筛选
     */
    fun setFilterCategory(category: String?) {
        _filterCategory.value = category
    }
    
    /**
     * 设置时间范围筛选
     */
    fun setTimeRange(timeRange: TimeRange) {
        _timeRange.value = timeRange
    }
    
    /**
     * 设置排序方式
     */
    fun setSortOrder(sortOrder: SortOrder) {
        _sortOrder.value = sortOrder
    }
    
    /**
     * 清除所有筛选条件
     */
    fun clearFilters() {
        _searchQuery.value = ""
        _filterCategory.value = null
        _timeRange.value = TimeRange.ALL
        _sortOrder.value = SortOrder.TIME_DESC
    }
    
    /**
     * 刷新历史记录
     */
    fun refreshHistory() {
        loadInterviewHistory()
    }
    
    /**
     * 删除面试记录
     */
    fun deleteRecord(recordId: String) {
        viewModelScope.launch {
            try {
                interviewRecordRepository.deleteRecord(recordId)
                // 刷新列表
                refreshHistory()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "删除记录失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 获取可用的类别列表
     */
    fun getAvailableCategories(): List<String> {
        return _uiState.value.allRecords
            .map { it.positionCategory }
            .distinct()
            .sorted()
    }
    
    /**
     * 获取统计信息
     */
    fun getStatistics(): InterviewHistoryStatistics {
        val records = _uiState.value.allRecords
        val completedRecords = records.filter { it.status == InterviewRecordStatus.COMPLETED }
        
        return InterviewHistoryStatistics(
            totalInterviews = records.size,
            completedInterviews = completedRecords.size,
            averageScore = completedRecords.mapNotNull { it.overallScore }.average().takeIf { !it.isNaN() } ?: 0.0,
            bestScore = completedRecords.mapNotNull { it.overallScore }.maxOrNull() ?: 0.0,
            totalDuration = records.sumOf { it.duration },
            categoryCounts = records.groupBy { it.positionCategory }.mapValues { it.value.size }
        )
    }
    
    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

/**
 * 面试历史UI状态
 */
data class InterviewHistoryUiState(
    val isLoading: Boolean = false,
    val allRecords: List<InterviewRecordSummary> = emptyList(),
    val filteredRecords: List<InterviewRecordSummary> = emptyList(),
    val error: String? = null
)

/**
 * 时间范围枚举
 */
enum class TimeRange {
    ALL,
    TODAY,
    WEEK,
    MONTH;
    
    val displayName: String
        get() = when (this) {
            ALL -> "全部"
            TODAY -> "今天"
            WEEK -> "本周"
            MONTH -> "本月"
        }
}

/**
 * 排序方式枚举
 */
enum class SortOrder {
    TIME_DESC,
    TIME_ASC,
    SCORE_DESC,
    SCORE_ASC,
    POSITION_NAME;
    
    val displayName: String
        get() = when (this) {
            TIME_DESC -> "时间倒序"
            TIME_ASC -> "时间正序"
            SCORE_DESC -> "分数倒序"
            SCORE_ASC -> "分数正序"
            POSITION_NAME -> "岗位名称"
        }
}

/**
 * 面试历史统计信息
 */
data class InterviewHistoryStatistics(
    val totalInterviews: Int,
    val completedInterviews: Int,
    val averageScore: Double,
    val bestScore: Double,
    val totalDuration: Int, // 总时长（秒）
    val categoryCounts: Map<String, Int> // 各类别面试次数
)