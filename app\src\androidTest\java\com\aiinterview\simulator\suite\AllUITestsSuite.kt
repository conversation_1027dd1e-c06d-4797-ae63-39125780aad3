package com.aiinterview.simulator.suite

import com.aiinterview.simulator.integration.InterviewFlowEndToEndTest
import com.aiinterview.simulator.presentation.screen.auth.LoginScreenTest
import com.aiinterview.simulator.presentation.screen.home.HomeScreenTest
import org.junit.runner.RunWith
import org.junit.runners.Suite

/**
 * UI测试套件
 * 运行所有的UI测试用例
 */
@RunWith(Suite::class)
@Suite.SuiteClasses(
    // 认证相关测试
    LoginScreenTest::class,
    
    // 主界面测试
    HomeScreenTest::class,
    
    // 端到端集成测试
    InterviewFlowEndToEndTest::class
)
class AllUITestsSuite

/**
 * 快速UI测试套件
 * 只运行关键的UI测试用例，用于快速验证
 */
@RunWith(Suite::class)
@Suite.SuiteClasses(
    LoginScreenTest::class,
    HomeScreenTest::class
)
class QuickUITestsSuite

/**
 * 集成测试套件
 * 只运行端到端的集成测试
 */
@RunWith(Suite::class)
@Suite.SuiteClasses(
    InterviewFlowEndToEndTest::class
)
class IntegrationTestsSuite