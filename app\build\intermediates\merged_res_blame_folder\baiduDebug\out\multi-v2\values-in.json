{"logs": [{"outputFile": "com.aiinterview.simulator.app-mergeBaiduDebugResources-55:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\73f51c4b00c3d8b5bd0709c291d17c64\\transformed\\material3-1.1.2\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,281,389,501,579,681,801,944,1063,1209,1292,1392,1481,1581,1695,1811,1916,2052,2186,2311,2485,2603,2719,2839,2959,3049,3146,3262,3391,3489,3592,3695,3832,3975,4080,4175,4247,4324,4409,4490,4585,4661,4740,4835,4930,5023,5119,5202,5301,5396,5495,5608,5684,5785", "endColumns": "114,110,107,111,77,101,119,142,118,145,82,99,88,99,113,115,104,135,133,124,173,117,115,119,119,89,96,115,128,97,102,102,136,142,104,94,71,76,84,80,94,75,78,94,94,92,95,82,98,94,98,112,75,100,90", "endOffsets": "165,276,384,496,574,676,796,939,1058,1204,1287,1387,1476,1576,1690,1806,1911,2047,2181,2306,2480,2598,2714,2834,2954,3044,3141,3257,3386,3484,3587,3690,3827,3970,4075,4170,4242,4319,4404,4485,4580,4656,4735,4830,4925,5018,5114,5197,5296,5391,5490,5603,5679,5780,5871"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,331,439,1453,1531,1633,1753,1896,2015,2161,2244,2344,2433,2533,2647,2763,2868,3004,3138,3263,3437,3555,3671,3791,3911,4001,4098,4214,4343,4441,4544,4647,4784,4927,5032,5325,5483,6208,6365,6547,6905,6981,7060,7155,7250,7343,7439,7522,7621,7716,7815,7928,8004,8105", "endColumns": "114,110,107,111,77,101,119,142,118,145,82,99,88,99,113,115,104,135,133,124,173,117,115,119,119,89,96,115,128,97,102,102,136,142,104,94,71,76,84,80,94,75,78,94,94,92,95,82,98,94,98,112,75,100,90", "endOffsets": "215,326,434,546,1526,1628,1748,1891,2010,2156,2239,2339,2428,2528,2642,2758,2863,2999,3133,3258,3432,3550,3666,3786,3906,3996,4093,4209,4338,4436,4539,4642,4779,4922,5027,5122,5392,5555,6288,6441,6637,6976,7055,7150,7245,7338,7434,7517,7616,7711,7810,7923,7999,8100,8191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d78fa9dcb2040782f62e575b1696aadd\\transformed\\ui-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,277,375,475,561,644,735,822,907,977,1044,1126,1209,1281,1359,1425", "endColumns": "89,81,97,99,85,82,90,86,84,69,66,81,82,71,77,65,118", "endOffsets": "190,272,370,470,556,639,730,817,902,972,1039,1121,1204,1276,1354,1420,1539"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1281,1371,5127,5225,5397,5560,5643,5734,5821,5906,5976,6043,6125,6293,6642,6720,6786", "endColumns": "89,81,97,99,85,82,90,86,84,69,66,81,82,71,77,65,118", "endOffsets": "1366,1448,5220,5320,5478,5638,5729,5816,5901,5971,6038,6120,6203,6360,6715,6781,6900"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fd044ab7a523edb1345beec00f65097\\transformed\\core-1.12.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "551,646,748,845,942,1048,1166,6446", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "641,743,840,937,1043,1161,1276,6542"}}]}]}