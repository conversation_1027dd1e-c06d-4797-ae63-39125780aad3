package com.aiinterview.simulator.data.repository

import com.aiinterview.simulator.data.api.InterviewApi
import com.aiinterview.simulator.data.dao.PositionDao
import com.aiinterview.simulator.data.dao.InterviewSessionDao
import com.aiinterview.simulator.data.dto.request.StartInterviewRequest
import com.aiinterview.simulator.data.dto.request.AnswerRequest
import com.aiinterview.simulator.data.dto.response.InterviewSessionResponse
import com.aiinterview.simulator.data.dto.response.QuestionResponse
import com.aiinterview.simulator.data.dto.response.InterviewResultResponse
import com.aiinterview.simulator.data.model.Position
import com.aiinterview.simulator.data.model.InterviewSession
import com.aiinterview.simulator.domain.util.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import javax.inject.Singleton

interface InterviewRepository {
    fun getPositions(): Flow<Resource<List<Position>>>
    suspend fun getPositionById(positionId: String): Flow<Resource<Position>>
    suspend fun startInterview(userId: String, positionId: String): Flow<Resource<InterviewSessionResponse>>
    suspend fun submitAnswer(sessionId: String, questionId: String, audioUrl: String, transcription: String, duration: Int): Flow<Resource<QuestionResponse>>
    suspend fun getInterviewResult(sessionId: String): Flow<Resource<InterviewResultResponse>>
    fun getUserInterviewSessions(userId: String): Flow<List<InterviewSession>>
    suspend fun saveInterviewSession(session: InterviewSession)
    suspend fun updateInterviewSession(session: InterviewSession)
    suspend fun getInterviewSession(sessionId: String): InterviewSession?
}

@Singleton
class InterviewRepositoryImpl @Inject constructor(
    private val interviewApi: InterviewApi,
    private val positionDao: PositionDao,
    private val interviewSessionDao: InterviewSessionDao
) : InterviewRepository {
    override fun getPositions(): Flow<Resource<List<Position>>> = flow {
        try {
            emit(Resource.Loading())
            
            // First emit cached data
            val cachedPositions = positionDao.getAllPositions()
            cachedPositions.collect { positions ->
                if (positions.isNotEmpty()) {
                    emit(Resource.Success(positions))
                }
            }
            
            // Then fetch from network
            val response = interviewApi.getPositions()
            if (response.success && response.data != null) {
                positionDao.insertPositions(response.data)
                emit(Resource.Success(response.data))
            } else {
                emit(Resource.Error(response.message ?: "获取岗位列表失败"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "网络错误"))
        }
    }
    
    override suspend fun getPositionById(positionId: String): Flow<Resource<Position>> = flow {
        try {
            emit(Resource.Loading())
            
            // First try local cache
            val cachedPosition = positionDao.getPositionById(positionId)
            if (cachedPosition != null) {
                emit(Resource.Success(cachedPosition))
            }
            
            // Then fetch from network
            val response = interviewApi.getPositionById(positionId)
            if (response.success && response.data != null) {
                positionDao.insertPosition(response.data)
                emit(Resource.Success(response.data))
            } else {
                emit(Resource.Error(response.message ?: "获取岗位详情失败"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "网络错误"))
        }
    }
    
    override suspend fun startInterview(userId: String, positionId: String): Flow<Resource<InterviewSessionResponse>> = flow {
        try {
            emit(Resource.Loading())
            val request = StartInterviewRequest(userId, positionId)
            val response = interviewApi.startInterview(request)
            
            if (response.success && response.data != null) {
                emit(Resource.Success(response.data))
            } else {
                emit(Resource.Error(response.message ?: "开始面试失败"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "网络错误"))
        }
    }
    
    override suspend fun submitAnswer(
        sessionId: String,
        questionId: String,
        audioUrl: String,
        transcription: String,
        duration: Int
    ): Flow<Resource<QuestionResponse>> = flow {
        try {
            emit(Resource.Loading())
            val request = AnswerRequest(sessionId, questionId, audioUrl, transcription, duration)
            val response = interviewApi.submitAnswer(request)
            
            if (response.success && response.data != null) {
                emit(Resource.Success(response.data))
            } else {
                emit(Resource.Error(response.message ?: "提交答案失败"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "网络错误"))
        }
    }
    
    override suspend fun getInterviewResult(sessionId: String): Flow<Resource<InterviewResultResponse>> = flow {
        try {
            emit(Resource.Loading())
            val response = interviewApi.getInterviewResult(sessionId)
            
            if (response.success && response.data != null) {
                emit(Resource.Success(response.data))
            } else {
                emit(Resource.Error(response.message ?: "获取面试结果失败"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "网络错误"))
        }
    }
    
    override fun getUserInterviewSessions(userId: String): Flow<List<InterviewSession>> {
        return interviewSessionDao.getSessionsByUser(userId)
    }
    
    override suspend fun saveInterviewSession(session: InterviewSession) {
        interviewSessionDao.insertSession(session)
    }
    
    override suspend fun updateInterviewSession(session: InterviewSession) {
        interviewSessionDao.updateSession(session)
    }
    
    override suspend fun getInterviewSession(sessionId: String): InterviewSession? {
        return interviewSessionDao.getSessionById(sessionId)
    }
}