package com.aiinterview.simulator.data.dto.request

/**
 * AI问题生成请求DTO
 */
data class QuestionGenerationRequest(
    val positionType: String,
    val questionType: String,
    val difficulty: Int,
    val context: String? = null,
    val previousQuestions: List<String> = emptyList(),
    val candidateProfile: CandidateProfile? = null
)

data class CandidateProfile(
    val education: String? = null,
    val major: String? = null,
    val workExperience: Int? = null,
    val skills: List<String> = emptyList()
)

/**
 * 文心一言API请求格式
 */
data class WenxinQuestionRequest(
    val messages: List<WenxinMessage>,
    val temperature: Double = 0.7,
    val top_p: Double = 0.8,
    val penalty_score: Double = 1.0,
    val max_output_tokens: Int = 1000,
    val system: String? = null
)

data class WenxinMessage(
    val role: String, // "user", "assistant", "system"
    val content: String
)

/**
 * 通义千问API请求格式
 */
data class TongyiQuestionRequest(
    val model: String = "qwen-turbo",
    val input: TongyiInput,
    val parameters: TongyiParameters = TongyiParameters()
)

data class TongyiInput(
    val messages: List<TongyiMessage>
)

data class TongyiMessage(
    val role: String, // "system", "user", "assistant"
    val content: String
)

data class TongyiParameters(
    val temperature: Double = 0.7,
    val top_p: Double = 0.8,
    val max_tokens: Int = 1000,
    val result_format: String = "message"
)