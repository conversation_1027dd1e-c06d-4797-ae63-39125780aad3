{"logs": [{"outputFile": "com.aiinterview.simulator.app-mergeOfficialDebugResources-55:/values-sr/values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fd044ab7a523edb1345beec00f65097\\transformed\\core-1.12.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "558,656,758,855,959,1063,1168,6424", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "651,753,850,954,1058,1163,1279,6520"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\73f51c4b00c3d8b5bd0709c291d17c64\\transformed\\material3-1.1.2\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,275,398,508,588,678,786,918,1033,1173,1254,1350,1441,1535,1647,1768,1869,2006,2142,2271,2447,2568,2684,2806,2925,3017,3111,3224,3350,3446,3544,3649,3786,3931,4036,4134,4207,4287,4372,4456,4559,4635,4714,4807,4906,4995,5089,5172,5276,5369,5466,5595,5671,5772", "endColumns": "109,109,122,109,79,89,107,131,114,139,80,95,90,93,111,120,100,136,135,128,175,120,115,121,118,91,93,112,125,95,97,104,136,144,104,97,72,79,84,83,102,75,78,92,98,88,93,82,103,92,96,128,75,100,92", "endOffsets": "160,270,393,503,583,673,781,913,1028,1168,1249,1345,1436,1530,1642,1763,1864,2001,2137,2266,2442,2563,2679,2801,2920,3012,3106,3219,3345,3441,3539,3644,3781,3926,4031,4129,4202,4282,4367,4451,4554,4630,4709,4802,4901,4990,5084,5167,5271,5364,5461,5590,5666,5767,5860"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,325,448,1468,1548,1638,1746,1878,1993,2133,2214,2310,2401,2495,2607,2728,2829,2966,3102,3231,3407,3528,3644,3766,3885,3977,4071,4184,4310,4406,4504,4609,4746,4891,4996,5292,5451,6182,6340,6525,6895,6971,7050,7143,7242,7331,7425,7508,7612,7705,7802,7931,8007,8108", "endColumns": "109,109,122,109,79,89,107,131,114,139,80,95,90,93,111,120,100,136,135,128,175,120,115,121,118,91,93,112,125,95,97,104,136,144,104,97,72,79,84,83,102,75,78,92,98,88,93,82,103,92,96,128,75,100,92", "endOffsets": "210,320,443,553,1543,1633,1741,1873,1988,2128,2209,2305,2396,2490,2602,2723,2824,2961,3097,3226,3402,3523,3639,3761,3880,3972,4066,4179,4305,4401,4499,4604,4741,4886,4991,5089,5360,5526,6262,6419,6623,6966,7045,7138,7237,7326,7420,7503,7607,7700,7797,7926,8002,8103,8196"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d78fa9dcb2040782f62e575b1696aadd\\transformed\\ui-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,386,487,573,650,741,833,918,989,1059,1139,1224,1297,1376,1446", "endColumns": "96,86,96,100,85,76,90,91,84,70,69,79,84,72,78,69,117", "endOffsets": "197,284,381,482,568,645,736,828,913,984,1054,1134,1219,1292,1371,1441,1559"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1284,1381,5094,5191,5365,5531,5608,5699,5791,5876,5947,6017,6097,6267,6628,6707,6777", "endColumns": "96,86,96,100,85,76,90,91,84,70,69,79,84,72,78,69,117", "endOffsets": "1376,1463,5186,5287,5446,5603,5694,5786,5871,5942,6012,6092,6177,6335,6702,6772,6890"}}]}]}