package com.aiinterview.simulator.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.aiinterview.simulator.data.audio.AudioRecorder
import com.aiinterview.simulator.data.permission.PermissionManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.io.File
import javax.inject.Inject

/**
 * 录音ViewModel，负责管理录音相关的UI状态和业务逻辑
 * 协调AudioRecorder和UI之间的交互
 */
@HiltViewModel
class RecordingViewModel @Inject constructor(
    private val audioRecorder: AudioRecorder, // 音频录制器
    private val permissionManager: PermissionManager // 权限管理器
) : ViewModel() {
    
    // 录音状态，直接从AudioRecorder获取
    val recordingState: StateFlow<AudioRecorder.RecordingState> = audioRecorder.recordingState
    
    // 录音时长，从AudioRecorder获取并在ViewModel中更新
    private val _recordingDuration = MutableStateFlow(0L)
    val recordingDuration: StateFlow<Long> = _recordingDuration.asStateFlow()
    
    // 当前录音文件
    val currentRecordingFile: StateFlow<File?> = audioRecorder.currentRecordingFile
    
    // 错误消息
    val errorMessage: StateFlow<String?> = audioRecorder.errorMessage
    
    // 权限状态
    private val _hasRecordingPermission = MutableStateFlow(false)
    val hasRecordingPermission: StateFlow<Boolean> = _hasRecordingPermission.asStateFlow()
    
    // 录音质量指示器（音量级别）
    private val _volumeLevel = MutableStateFlow(0f)
    val volumeLevel: StateFlow<Float> = _volumeLevel.asStateFlow()
    
    // 计时器Job，用于更新录音时长
    private var timerJob: Job? = null
    
    // 录音开始时间
    private var recordingStartTime: Long = 0
    
    // 暂停前的累计时长
    private var pausedDuration: Long = 0
    
    init {
        // 初始化时检查录音权限
        checkRecordingPermission()
        
        // 监听录音状态变化
        viewModelScope.launch {
            recordingState.collect { state ->
                when (state) {
                    AudioRecorder.RecordingState.RECORDING -> {
                        // 开始录音时启动计时器
                        startTimer()
                    }
                    AudioRecorder.RecordingState.PAUSED -> {
                        // 暂停时停止计时器并记录累计时长
                        stopTimer()
                        pausedDuration = _recordingDuration.value
                    }
                    AudioRecorder.RecordingState.STOPPED,
                    AudioRecorder.RecordingState.IDLE,
                    AudioRecorder.RecordingState.ERROR -> {
                        // 停止、空闲或错误状态时停止计时器
                        stopTimer()
                    }
                }
            }
        }
    }
    
    /**
     * 开始录音
     * @param questionId 问题ID
     */
    fun startRecording(questionId: String) {
        viewModelScope.launch {
            // 检查录音权限
            if (!hasRecordingPermission.value) {
                // 权限不足，需要请求权限
                return@launch
            }
            
            // 重置计时相关变量
            recordingStartTime = System.currentTimeMillis()
            pausedDuration = 0
            _recordingDuration.value = 0L
            
            // 开始录音
            val success = audioRecorder.startRecording(questionId)
            if (!success) {
                // 录音启动失败，错误信息已在AudioRecorder中设置
            }
        }
    }
    
    /**
     * 停止录音
     * @return 录音文件，如果录音失败则返回null
     */
    fun stopRecording(): File? {
        stopTimer() // 停止计时器
        return audioRecorder.stopRecording()
    }
    
    /**
     * 暂停录音
     */
    fun pauseRecording() {
        audioRecorder.pauseRecording()
    }
    
    /**
     * 恢复录音
     */
    fun resumeRecording() {
        audioRecorder.resumeRecording()
        // 恢复录音时重新设置开始时间
        recordingStartTime = System.currentTimeMillis()
    }
    
    /**
     * 取消录音
     */
    fun cancelRecording() {
        stopTimer() // 停止计时器
        audioRecorder.cancelRecording()
        
        // 重置ViewModel状态
        _recordingDuration.value = 0L
        pausedDuration = 0
    }
    
    /**
     * 重置录音状态
     */
    fun resetRecording() {
        stopTimer()
        audioRecorder.resetRecording()
        
        // 重置ViewModel状态
        _recordingDuration.value = 0L
        pausedDuration = 0
        recordingStartTime = 0
    }
    
    /**
     * 检查录音权限
     */
    fun checkRecordingPermission() {
        _hasRecordingPermission.value = audioRecorder.hasRecordingPermission()
    }
    
    /**
     * 请求录音权限
     */
    fun requestRecordingPermission() {
        viewModelScope.launch {
            val granted = permissionManager.requestRecordingPermission()
            _hasRecordingPermission.value = granted
        }
    }
    
    /**
     * 格式化录音时长
     * @param duration 时长（毫秒）
     * @return 格式化的时长字符串
     */
    fun formatDuration(duration: Long): String {
        return audioRecorder.formatDuration(duration)
    }
    
    /**
     * 获取录音状态描述
     * @param state 录音状态
     * @return 状态描述文本
     */
    fun getRecordingStateDescription(state: AudioRecorder.RecordingState): String {
        return when (state) {
            AudioRecorder.RecordingState.IDLE -> "准备录音"
            AudioRecorder.RecordingState.RECORDING -> "正在录音"
            AudioRecorder.RecordingState.PAUSED -> "录音已暂停"
            AudioRecorder.RecordingState.STOPPED -> "录音已完成"
            AudioRecorder.RecordingState.ERROR -> "录音出错"
        }
    }
    
    /**
     * 获取录音时长提示信息
     * @param duration 当前录音时长（毫秒）
     * @param timeLimit 时间限制（秒）
     * @return 提示信息
     */
    fun getDurationHint(duration: Long, timeLimit: Int): String {
        val durationSeconds = duration / 1000
        val remainingSeconds = timeLimit - durationSeconds
        
        return when {
            remainingSeconds <= 0 -> "录音时间已到，建议结束录音"
            remainingSeconds <= 30 -> "剩余时间：${remainingSeconds}秒"
            remainingSeconds <= 60 -> "剩余时间：1分钟内"
            else -> "建议录音时长：${timeLimit / 60}分钟"
        }
    }
    
    /**
     * 检查录音时长是否合适
     * @param duration 录音时长（毫秒）
     * @return 是否合适
     */
    fun isRecordingDurationAppropriate(duration: Long): Boolean {
        val durationSeconds = duration / 1000
        // 录音时长应该在10秒到10分钟之间
        return durationSeconds >= 10 && durationSeconds <= 600
    }
    
    /**
     * 获取录音质量提示
     * @param duration 录音时长（毫秒）
     * @return 质量提示信息
     */
    fun getRecordingQualityHint(duration: Long): String {
        val durationSeconds = duration / 1000
        
        return when {
            durationSeconds < 10 -> "录音时间过短，建议至少录制10秒"
            durationSeconds < 30 -> "录音时间较短，建议详细回答"
            durationSeconds > 600 -> "录音时间过长，建议控制在10分钟内"
            durationSeconds > 300 -> "录音时间较长，注意重点突出"
            else -> "录音时长合适"
        }
    }
    
    /**
     * 清理旧的录音文件
     */
    fun cleanupOldRecordings() {
        viewModelScope.launch {
            audioRecorder.cleanupOldRecordings()
        }
    }
    
    /**
     * 开始计时器
     */
    private fun startTimer() {
        stopTimer() // 先停止之前的计时器
        
        timerJob = viewModelScope.launch {
            while (recordingState.value == AudioRecorder.RecordingState.RECORDING) {
                val currentTime = System.currentTimeMillis()
                val elapsedTime = currentTime - recordingStartTime + pausedDuration
                _recordingDuration.value = elapsedTime
                
                // 模拟音量级别变化（实际应用中可以从AudioRecorder获取真实音量）
                _volumeLevel.value = (0.1f..1.0f).random()
                
                delay(100) // 每100毫秒更新一次
            }
        }
    }
    
    /**
     * 停止计时器
     */
    private fun stopTimer() {
        timerJob?.cancel()
        timerJob = null
        _volumeLevel.value = 0f // 重置音量级别
    }
    
    /**
     * 获取录音文件大小（格式化）
     * @param file 录音文件
     * @return 格式化的文件大小
     */
    fun getFormattedFileSize(file: File?): String {
        if (file == null || !file.exists()) return "0 KB"
        
        val bytes = file.length()
        return when {
            bytes < 1024 -> "${bytes} B"
            bytes < 1024 * 1024 -> "${bytes / 1024} KB"
            else -> "${bytes / (1024 * 1024)} MB"
        }
    }
    
    /**
     * 检查录音文件是否有效
     * @param file 录音文件
     * @return 是否有效
     */
    fun isRecordingFileValid(file: File?): Boolean {
        return file != null && file.exists() && file.length() > 0
    }
    
    override fun onCleared() {
        super.onCleared()
        // ViewModel销毁时停止计时器和清理资源
        stopTimer()
        // 注意：不要在这里调用audioRecorder.resetRecording()，
        // 因为用户可能还需要使用录音文件
    }
}