package com.aiinterview.simulator.presentation.screen.legal

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController

/**
 * 用户服务协议页面
 * 显示应用的用户服务协议内容，符合中国法律法规要求
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun UserAgreementScreen(
    navController: NavController
) {
    val scrollState = rememberScrollState()
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "用户服务协议",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Medium
                    )
                },
                navigationIcon = {
                    IconButton(onClick = { navController.navigateUp() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(scrollState)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 生效日期
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Text(
                    text = "生效日期：2024年1月1日\n最后更新：2024年1月1日",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(16.dp),
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }
            
            // 引言
            AgreementSection(
                title = "引言",
                content = """
                    欢迎您使用AI面试模拟器！
                    
                    本协议是您（用户）与AI面试模拟器运营方（以下简称"我们"）之间关于您使用AI面试模拟器服务所订立的协议。请您仔细阅读本协议，特别是免除或者限制责任的条款、法律适用和争议解决条款。
                    
                    **请您审慎阅读并充分理解各条款内容，特别是免除或者限制责任的条款，以及开通或使用某项服务的单独协议。限制、免责条款可能以加粗形式提示您注意。**
                    
                    除非您已阅读并接受本协议所有条款，否则您无权使用本服务。您的使用行为将视为对本协议的接受，并同意接受本协议各项条款的约束。
                """.trimIndent()
            )
            
            // 服务内容
            AgreementSection(
                title = "服务内容",
                content = """
                    AI面试模拟器是一款面试练习应用，主要提供以下服务：
                    
                    **1. 面试模拟服务**
                    • 提供多种岗位类型的面试题目
                    • 支持语音回答录制和识别
                    • 提供AI智能评价和建议
                    • 面试流程完整模拟
                    
                    **2. 学习管理服务**
                    • 面试记录保存和管理
                    • 个人进步分析和统计
                    • 学习建议和改进方向
                    
                    **3. 账户服务**
                    • 用户注册和登录
                    • 个人信息管理
                    • 数据同步和备份
                    
                    **服务范围说明：**
                    • 本服务仅供学习和练习使用，不保证用户面试成功
                    • 我们保留随时修改、中断或终止部分或全部服务的权利
                    • 具体服务内容以实际提供为准
                """.trimIndent()
            )
            
            // 用户义务
            AgreementSection(
                title = "用户义务与行为规范",
                content = """
                    作为AI面试模拟器的用户，您需要遵守以下义务和行为规范：
                    
                    **1. 注册义务**
                    • 提供真实、准确、完整的注册信息
                    • 及时更新注册信息，确保信息的有效性
                    • 妥善保管账户信息，对账户安全负责
                    
                    **2. 使用规范**
                    • 仅将本服务用于合法目的
                    • 不得利用本服务从事任何违法违规活动
                    • 不得干扰或破坏服务的正常运行
                    • 不得恶意攻击系统或盗取他人信息
                    
                    **3. 内容规范**
                    • 不得上传、传播违法违规内容
                    • 不得发布虚假、误导性信息
                    • 不得侵犯他人知识产权或其他合法权益
                    • 不得传播病毒、恶意代码等有害程序
                    
                    **4. 禁止行为**
                    • 禁止逆向工程、反编译或破解应用
                    • 禁止使用外挂、插件等第三方工具
                    • 禁止批量注册账户或恶意刷量
                    • 禁止商业化使用本服务（除非获得授权）
                """.trimIndent()
            )
            
            // 知识产权
            AgreementSection(
                title = "知识产权",
                content = """
                    **1. 平台知识产权**
                    AI面试模拟器的所有内容，包括但不限于：
                    • 软件、程序、代码
                    • 文字、图片、音频、视频
                    • 界面设计、版式设计
                    • 商标、标识、域名
                    
                    均受中华人民共和国著作权法、商标法、专利法及其他相关法律法规保护，相关知识产权归我们或相关权利人所有。
                    
                    **2. 用户内容**
                    • 您对自己创建、上传的内容享有知识产权
                    • 您授权我们在提供服务范围内使用您的内容
                    • 您保证拥有相关内容的合法权利
                    • 如因您的内容侵权，您需承担相应责任
                    
                    **3. 侵权处理**
                    • 如发现侵权行为，请及时联系我们
                    • 我们将依法处理侵权投诉
                    • 对于恶意投诉，我们保留追究责任的权利
                """.trimIndent()
            )
            
            // 隐私保护
            AgreementSection(
                title = "隐私保护",
                content = """
                    我们高度重视用户隐私保护，具体的隐私保护措施请参见《隐私政策》。
                    
                    **主要原则：**
                    • 最小化收集：只收集必要的个人信息
                    • 透明使用：明确告知信息使用目的
                    • 安全保护：采用技术和管理措施保护信息安全
                    • 用户控制：尊重用户对个人信息的控制权
                    
                    **特别说明：**
                    • 语音数据仅用于语音识别和服务改进
                    • 面试记录仅用于为您提供个性化服务
                    • 我们不会将您的个人信息出售给第三方
                    • 您可以随时查看、修改或删除个人信息
                """.trimIndent()
            )
            
            // 免责声明
            AgreementSection(
                title = "免责声明",
                content = """
                    **1. 服务性质**
                    • 本服务仅供学习和练习使用，不构成任何就业承诺
                    • AI评价结果仅供参考，不代表真实面试官的观点
                    • 我们不保证使用本服务后能够通过面试或获得工作
                    
                    **2. 技术限制**
                    • 语音识别准确率受技术限制，可能存在误差
                    • 网络中断、设备故障等可能影响服务使用
                    • 我们将尽力保证服务稳定，但不承担因技术问题导致的损失
                    
                    **3. 第三方服务**
                    • 本应用可能包含第三方服务或链接
                    • 第三方服务的可用性和质量由第三方负责
                    • 我们不对第三方服务承担责任
                    
                    **4. 不可抗力**
                    因不可抗力（包括但不限于自然灾害、政府行为、网络攻击等）导致的服务中断或数据丢失，我们不承担责任。
                """.trimIndent()
            )
            
            // 服务变更与终止
            AgreementSection(
                title = "服务变更与终止",
                content = """
                    **1. 服务变更**
                    • 我们保留随时修改、升级或终止服务的权利
                    • 重大变更将提前通知用户
                    • 您可以选择接受变更或停止使用服务
                    
                    **2. 服务终止**
                    我们可能在以下情况下终止为您提供服务：
                    • 您违反本协议的约定
                    • 您长期未使用服务（超过12个月）
                    • 法律法规要求或政府部门要求
                    • 服务升级或技术调整需要
                    
                    **3. 账户注销**
                    • 您可以随时申请注销账户
                    • 注销后您的个人信息将被删除
                    • 注销前请备份重要数据
                    • 注销后无法恢复账户和数据
                    
                    **4. 数据处理**
                    • 服务终止后，我们将按照法律要求处理您的数据
                    • 重要数据将保留合理期限供您导出
                    • 超过保留期限的数据将被安全删除
                """.trimIndent()
            )
            
            // 争议解决
            AgreementSection(
                title = "争议解决",
                content = """
                    **1. 适用法律**
                    本协议的订立、执行和解释及争议的解决均应适用中华人民共和国法律法规。
                    
                    **2. 争议解决方式**
                    如双方就本协议内容或其执行发生任何争议，双方应尽量友好协商解决；协商不成时，任何一方均可向我们所在地的人民法院提起诉讼。
                    
                    **3. 管辖法院**
                    因本协议产生的争议，由我们所在地有管辖权的人民法院管辖。
                    
                    **4. 协议效力**
                    • 本协议的任何条款无论因何种原因完全或部分无效或不具有执行力，本协议的其余条款仍应有效并且有约束力
                    • 本协议的标题仅为方便而设，在解释本协议时应被忽略
                """.trimIndent()
            )
            
            // 联系方式
            AgreementSection(
                title = "联系我们",
                content = """
                    如果您对本协议有任何疑问或建议，请通过以下方式联系我们：
                    
                    **客服邮箱：** <EMAIL>
                    **客服电话：** 400-123-4567（工作日 9:00-18:00）
                    **官方网站：** www.aiinterview.com
                    **通信地址：** 中国北京市朝阳区XXX大厦XX层
                    **邮政编码：** 100000
                    
                    我们将在收到您的反馈后尽快回复。感谢您对AI面试模拟器的支持与信任！
                """.trimIndent()
            )
            
            // 协议更新
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.secondaryContainer
                )
            ) {
                Text(
                    text = "本协议自发布之日起生效。我们保留随时修改本协议的权利，修订后的协议将在应用内公布。继续使用服务即表示您接受修订后的协议。",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(16.dp),
                    color = MaterialTheme.colorScheme.onSecondaryContainer,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

/**
 * 协议章节组件
 */
@Composable
private fun AgreementSection(
    title: String,
    content: String
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
            
            Text(
                text = content,
                style = MaterialTheme.typography.bodyMedium,
                lineHeight = MaterialTheme.typography.bodyMedium.lineHeight * 1.4
            )
        }
    }
}