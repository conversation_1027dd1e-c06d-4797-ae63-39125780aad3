package com.aiinterview.simulator.data.dto.response

data class InterviewSessionResponse(
    val sessionId: String,
    val status: String,
    val currentQuestion: QuestionDto,
    val totalQuestions: Int,
    val currentQuestionIndex: Int
)

data class QuestionResponse(
    val nextQuestion: QuestionDto?,
    val isLastQuestion: Boolean,
    val sessionStatus: String
)

data class QuestionDto(
    val id: String,
    val type: String,
    val content: String,
    val timeLimit: Int,
    val backgroundInfo: String? = null
)

data class InterviewResultResponse(
    val sessionId: String,
    val overallScore: Double,
    val dimensions: Map<String, DimensionScore>,
    val feedback: String,
    val suggestions: List<String>,
    val completedAt: Long
)

data class DimensionScore(
    val score: Double,
    val maxScore: Double,
    val feedback: String
)