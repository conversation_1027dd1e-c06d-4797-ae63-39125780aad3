package com.aiinterview.simulator.data.permission

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 权限管理器
 * 负责处理应用所需的各种权限申请和检查
 */
@Singleton
class PermissionManager @Inject constructor(
    @ApplicationContext private val context: Context // 注入应用上下文
) {
    
    companion object {
        // 录音权限请求码
        const val MICROPHONE_PERMISSION_REQUEST_CODE = 1001
        // 存储权限请求码
        const val STORAGE_PERMISSION_REQUEST_CODE = 1002
        // 网络状态权限请求码
        const val NETWORK_STATE_PERMISSION_REQUEST_CODE = 1003
        // 所有权限请求码
        const val ALL_PERMISSIONS_REQUEST_CODE = 1004
    }
    
    /**
     * 检查录音权限是否已授予
     * @return true表示已授权，false表示未授权
     */
    fun hasMicrophonePermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            context, 
            Manifest.permission.RECORD_AUDIO
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 检查网络状态权限是否已授予
     * @return true表示已授权，false表示未授权
     */
    fun hasNetworkStatePermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            context, 
            Manifest.permission.ACCESS_NETWORK_STATE
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 检查存储权限是否已授予（仅适用于Android 10以下）
     * @return true表示已授权或不需要权限，false表示未授权
     */
    fun hasStoragePermission(): Boolean {
        // Android 10及以上版本不需要存储权限
        return if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
            true
        } else {
            ContextCompat.checkSelfPermission(
                context, 
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 检查所有必需权限是否已授予
     * @return true表示所有权限都已授权，false表示有权限未授权
     */
    fun hasAllRequiredPermissions(): Boolean {
        return hasMicrophonePermission() && 
               hasNetworkStatePermission() && 
               hasStoragePermission()
    }
    
    /**
     * 请求录音权限
     * @param activity 当前活动实例
     */
    fun requestMicrophonePermission(activity: Activity) {
        // 检查是否已有权限
        if (!hasMicrophonePermission()) {
            // 请求录音权限
            ActivityCompat.requestPermissions(
                activity,
                arrayOf(Manifest.permission.RECORD_AUDIO),
                MICROPHONE_PERMISSION_REQUEST_CODE
            )
        }
    }
    
    /**
     * 请求存储权限（仅适用于Android 10以下）
     * @param activity 当前活动实例
     */
    fun requestStoragePermission(activity: Activity) {
        // 检查是否需要请求存储权限
        if (!hasStoragePermission() && android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.Q) {
            // 请求存储权限
            ActivityCompat.requestPermissions(
                activity,
                arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE),
                STORAGE_PERMISSION_REQUEST_CODE
            )
        }
    }
    
    /**
     * 请求所有必需权限
     * @param activity 当前活动实例
     */
    fun requestAllRequiredPermissions(activity: Activity) {
        // 创建需要请求的权限列表
        val permissionsToRequest = mutableListOf<String>()
        
        // 检查录音权限
        if (!hasMicrophonePermission()) {
            permissionsToRequest.add(Manifest.permission.RECORD_AUDIO)
        }
        
        // 检查存储权限（仅Android 10以下）
        if (!hasStoragePermission() && android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.Q) {
            permissionsToRequest.add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
        }
        
        // 如果有需要请求的权限，则发起请求
        if (permissionsToRequest.isNotEmpty()) {
            ActivityCompat.requestPermissions(
                activity,
                permissionsToRequest.toTypedArray(),
                ALL_PERMISSIONS_REQUEST_CODE
            )
        }
    }
    
    /**
     * 检查是否应该显示权限说明
     * @param activity 当前活动实例
     * @param permission 权限名称
     * @return true表示应该显示说明，false表示不需要
     */
    fun shouldShowPermissionRationale(activity: Activity, permission: String): Boolean {
        return ActivityCompat.shouldShowRequestPermissionRationale(activity, permission)
    }
    
    /**
     * 检查是否应该显示录音权限说明
     * @param activity 当前活动实例
     * @return true表示应该显示说明，false表示不需要
     */
    fun shouldShowMicrophonePermissionRationale(activity: Activity): Boolean {
        return shouldShowPermissionRationale(activity, Manifest.permission.RECORD_AUDIO)
    }
    
    /**
     * 检查是否应该显示存储权限说明
     * @param activity 当前活动实例
     * @return true表示应该显示说明，false表示不需要
     */
    fun shouldShowStoragePermissionRationale(activity: Activity): Boolean {
        return shouldShowPermissionRationale(activity, Manifest.permission.WRITE_EXTERNAL_STORAGE)
    }
    
    /**
     * 获取所有必需权限的列表
     * @return 权限名称数组
     */
    fun getRequiredPermissions(): Array<String> {
        val permissions = mutableListOf<String>()
        
        // 添加录音权限
        permissions.add(Manifest.permission.RECORD_AUDIO)
        
        // 添加网络权限（通常在manifest中声明，不需要运行时请求）
        permissions.add(Manifest.permission.INTERNET)
        permissions.add(Manifest.permission.ACCESS_NETWORK_STATE)
        
        // 添加存储权限（仅Android 10以下）
        if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.Q) {
            permissions.add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
        }
        
        return permissions.toTypedArray()
    }
}