package com.aiinterview.simulator.presentation.screen.history

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.selection.selectable
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.aiinterview.simulator.presentation.viewmodel.TimeRange

/**
 * 筛选对话框组件
 * 允许用户选择岗位类别和时间范围进行筛选
 * @param availableCategories 可用的岗位类别列表
 * @param selectedCategory 当前选中的类别
 * @param selectedTimeRange 当前选中的时间范围
 * @param onCategorySelected 类别选择回调函数
 * @param onTimeRangeSelected 时间范围选择回调函数
 * @param onDismiss 关闭对话框回调函数
 * @param onClearAll 清除所有筛选条件回调函数
 */
@Composable
fun FilterDialog(
    availableCategories: List<String>,
    selectedCategory: String?,
    selectedTimeRange: TimeRange,
    onCategorySelected: (String?) -> Unit,
    onTimeRangeSelected: (TimeRange) -> Unit,
    onDismiss: () -> Unit,
    onClearAll: () -> Unit
) {
    // 本地状态管理：临时存储用户的选择，确认后才应用
    var tempSelectedCategory by remember { mutableStateOf(selectedCategory) }
    var tempSelectedTimeRange by remember { mutableStateOf(selectedTimeRange) }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { 
            Text("筛选条件") 
        },
        text = {
            Column(
                modifier = Modifier.fillMaxWidth()
            ) {
                // 岗位类别筛选部分
                Text(
                    text = "岗位类别",
                    style = MaterialTheme.typography.titleSmall,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                // "全部类别"选项
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = tempSelectedCategory == null,
                            onClick = { tempSelectedCategory = null }
                        )
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = tempSelectedCategory == null,
                        onClick = { tempSelectedCategory = null }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("全部类别")
                }
                
                // 具体类别选项列表
                availableCategories.forEach { category ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .selectable(
                                selected = tempSelectedCategory == category,
                                onClick = { tempSelectedCategory = category }
                            )
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = tempSelectedCategory == category,
                            onClick = { tempSelectedCategory = category }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(category)
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 时间范围筛选部分
                Text(
                    text = "时间范围",
                    style = MaterialTheme.typography.titleSmall,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                // 时间范围选项列表
                TimeRange.values().forEach { timeRange ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .selectable(
                                selected = tempSelectedTimeRange == timeRange,
                                onClick = { tempSelectedTimeRange = timeRange }
                            )
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = tempSelectedTimeRange == timeRange,
                            onClick = { tempSelectedTimeRange = timeRange }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(timeRange.displayName)
                    }
                }
            }
        },
        confirmButton = {
            // 应用筛选按钮
            TextButton(
                onClick = {
                    // 应用用户的选择
                    onCategorySelected(tempSelectedCategory)
                    onTimeRangeSelected(tempSelectedTimeRange)
                    onDismiss()
                }
            ) {
                Text("应用")
            }
        },
        dismissButton = {
            Row {
                // 清除全部按钮
                TextButton(
                    onClick = {
                        // 清除所有筛选条件
                        tempSelectedCategory = null
                        tempSelectedTimeRange = TimeRange.ALL
                        onClearAll()
                    }
                ) {
                    Text("清除全部")
                }
                
                // 取消按钮
                TextButton(onClick = onDismiss) {
                    Text("取消")
                }
            }
        }
    )
}