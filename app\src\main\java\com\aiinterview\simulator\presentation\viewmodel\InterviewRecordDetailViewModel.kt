package com.aiinterview.simulator.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.aiinterview.simulator.data.model.InterviewRecordModel
import com.aiinterview.simulator.data.repository.InterviewRecordRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 面试记录详情页面的ViewModel
 * 负责管理单个面试记录的详细信息加载和状态管理
 */
@HiltViewModel
class InterviewRecordDetailViewModel @Inject constructor(
    private val interviewRecordRepository: InterviewRecordRepository // 注入面试记录仓库
) : ViewModel() {
    
    // UI状态管理：包含加载状态、记录数据和错误信息
    private val _uiState = MutableStateFlow(InterviewRecordDetailUiState())
    val uiState: StateFlow<InterviewRecordDetailUiState> = _uiState.asStateFlow()
    
    /**
     * 加载面试记录详情
     * @param recordId 面试记录ID
     */
    fun loadRecordDetail(recordId: String) {
        viewModelScope.launch {
            try {
                // 设置加载状态，清除之前的错误信息
                _uiState.value = _uiState.value.copy(
                    isLoading = true, // 开始加载
                    error = null // 清除错误信息
                )
                
                // 从仓库获取面试记录详情
                val record = interviewRecordRepository.getRecordById(recordId)
                
                if (record != null) {
                    // 成功获取记录，更新UI状态
                    _uiState.value = _uiState.value.copy(
                        isLoading = false, // 结束加载
                        record = record, // 设置记录数据
                        error = null // 确保没有错误信息
                    )
                } else {
                    // 记录不存在的情况
                    _uiState.value = _uiState.value.copy(
                        isLoading = false, // 结束加载
                        error = "面试记录不存在" // 设置错误信息
                    )
                }
            } catch (e: Exception) {
                // 处理加载过程中的异常
                _uiState.value = _uiState.value.copy(
                    isLoading = false, // 结束加载
                    error = "加载面试记录失败: ${e.message}" // 设置错误信息
                )
            }
        }
    }
    
    /**
     * 刷新记录详情
     * @param recordId 面试记录ID
     */
    fun refreshRecordDetail(recordId: String) {
        loadRecordDetail(recordId) // 重新加载记录详情
    }
    
    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null) // 清除错误信息
    }
    
    /**
     * 删除当前记录
     * @param recordId 面试记录ID
     * @param onDeleted 删除成功后的回调函数
     */
    fun deleteRecord(recordId: String, onDeleted: () -> Unit) {
        viewModelScope.launch {
            try {
                // 执行删除操作
                interviewRecordRepository.deleteRecord(recordId)
                // 删除成功，执行回调
                onDeleted()
            } catch (e: Exception) {
                // 删除失败，设置错误信息
                _uiState.value = _uiState.value.copy(
                    error = "删除记录失败: ${e.message}"
                )
            }
        }
    }
}

/**
 * 面试记录详情页面的UI状态
 * @param isLoading 是否正在加载
 * @param record 面试记录详细数据
 * @param error 错误信息
 */
data class InterviewRecordDetailUiState(
    val isLoading: Boolean = false, // 加载状态标志
    val record: InterviewRecordModel? = null, // 面试记录数据
    val error: String? = null // 错误信息
)