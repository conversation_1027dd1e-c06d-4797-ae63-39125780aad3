@echo off
REM AI面试模拟器 - 多渠道打包脚本
REM 此脚本用于构建所有应用商店渠道的发布版本

echo ========================================
echo AI面试模拟器 - 多渠道打包
echo ========================================

REM 检查是否存在密钥库配置文件
if not exist "keystore.properties" (
    echo 错误：未找到 keystore.properties 文件
    echo 请先配置签名密钥库信息
    pause
    exit /b 1
)

REM 清理之前的构建文件
echo 正在清理之前的构建文件...
call gradlew clean

REM 定义渠道列表
set CHANNELS=official huawei xiaomi oppo vivo tencent qihoo360 baidu

REM 逐个构建各渠道版本
for %%c in (%CHANNELS%) do (
    echo.
    echo ========================================
    echo 正在构建 %%c 渠道版本...
    echo ========================================
    call gradlew assemble%%cRelease
    
    if !ERRORLEVEL! neq 0 (
        echo %%c 渠道构建失败！
        pause
        exit /b 1
    )
    
    echo %%c 渠道构建完成
)

echo.
echo ========================================
echo 所有渠道构建完成！
echo ========================================

REM 显示生成的APK文件
echo 生成的APK文件：
for %%c in (%CHANNELS%) do (
    echo %%c 渠道：
    dir /b app\build\outputs\apk\%%c\release\*.apk 2>nul
    echo.
)

echo 多渠道打包完成！
pause