package com.aiinterview.simulator

import android.app.Activity
import android.app.Application
import android.os.Bundle
import java.util.concurrent.atomic.AtomicInteger

/**
 * 应用程序生命周期回调类
 * 用于监控应用程序的前台/后台状态变化
 */
class AppLifecycleCallbacks : Application.ActivityLifecycleCallbacks {
    
    // 活动的Activity数量计数器
    private val activeActivityCount = AtomicInteger(0)
    
    // 应用是否在前台的标志
    @Volatile
    private var isAppInForeground = false
    
    /**
     * Activity创建时的回调
     * @param activity 被创建的Activity
     * @param savedInstanceState 保存的实例状态
     */
    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
        // Activity创建时不需要特殊处理
    }
    
    /**
     * Activity开始时的回调
     * @param activity 开始的Activity
     */
    override fun onActivityStarted(activity: Activity) {
        val count = activeActivityCount.incrementAndGet() // 增加活动Activity计数
        
        // 如果是第一个Activity启动，说明应用进入前台
        if (count == 1 && !isAppInForeground) {
            isAppInForeground = true // 标记应用在前台
            onAppEnteredForeground() // 处理应用进入前台事件
        }
    }
    
    /**
     * Activity恢复时的回调
     * @param activity 恢复的Activity
     */
    override fun onActivityResumed(activity: Activity) {
        // Activity恢复时不需要特殊处理
    }
    
    /**
     * Activity暂停时的回调
     * @param activity 暂停的Activity
     */
    override fun onActivityPaused(activity: Activity) {
        // Activity暂停时不需要特殊处理
    }
    
    /**
     * Activity停止时的回调
     * @param activity 停止的Activity
     */
    override fun onActivityStopped(activity: Activity) {
        val count = activeActivityCount.decrementAndGet() // 减少活动Activity计数
        
        // 如果没有活动的Activity，说明应用进入后台
        if (count == 0 && isAppInForeground) {
            isAppInForeground = false // 标记应用在后台
            onAppEnteredBackground() // 处理应用进入后台事件
        }
    }
    
    /**
     * Activity保存实例状态时的回调
     * @param activity 保存状态的Activity
     * @param outState 输出状态Bundle
     */
    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
        // Activity保存状态时不需要特殊处理
    }
    
    /**
     * Activity销毁时的回调
     * @param activity 被销毁的Activity
     */
    override fun onActivityDestroyed(activity: Activity) {
        // Activity销毁时不需要特殊处理
    }
    
    /**
     * 应用进入前台时的处理
     */
    private fun onAppEnteredForeground() {
        try {
            // 可以在这里执行应用进入前台时的操作
            // 例如：刷新数据、恢复网络连接、启动定时任务等
            
            // 记录应用进入前台的时间
            val enterForegroundTime = System.currentTimeMillis()
            
            // 可以发送分析事件
            // AnalyticsManager.trackEvent("app_entered_foreground")
            
        } catch (e: Exception) {
            e.printStackTrace() // 打印异常信息
        }
    }
    
    /**
     * 应用进入后台时的处理
     */
    private fun onAppEnteredBackground() {
        try {
            // 可以在这里执行应用进入后台时的操作
            // 例如：保存数据、暂停网络请求、清理缓存等
            
            // 记录应用进入后台的时间
            val enterBackgroundTime = System.currentTimeMillis()
            
            // 可以发送分析事件
            // AnalyticsManager.trackEvent("app_entered_background")
            
            // 执行内存清理（可选）
            // 这里可以触发轻量级的内存清理
            System.gc() // 建议进行垃圾回收
            
        } catch (e: Exception) {
            e.printStackTrace() // 打印异常信息
        }
    }
    
    /**
     * 获取当前应用是否在前台
     * @return 应用是否在前台
     */
    fun isAppInForeground(): Boolean {
        return isAppInForeground
    }
    
    /**
     * 获取当前活动的Activity数量
     * @return 活动Activity数量
     */
    fun getActiveActivityCount(): Int {
        return activeActivityCount.get()
    }
}