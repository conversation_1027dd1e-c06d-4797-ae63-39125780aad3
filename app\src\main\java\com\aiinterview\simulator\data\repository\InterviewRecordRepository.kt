package com.aiinterview.simulator.data.repository

import com.aiinterview.simulator.data.dao.InterviewRecordDao
import com.aiinterview.simulator.data.dao.InterviewRecordStatistics
import com.aiinterview.simulator.data.model.*
import com.aiinterview.simulator.data.util.InterviewRecordJsonConverter
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class InterviewRecordRepository @Inject constructor(
    private val interviewRecordDao: InterviewRecordDao
) {
    
    /**
     * 获取用户的所有面试记录
     */
    fun getRecordsByUser(userId: String): Flow<List<InterviewRecordModel>> {
        return interviewRecordDao.getRecordsByUser(userId).map { records ->
            records.map { it.toModel() }
        }
    }
    
    /**
     * 获取用户的面试记录摘要
     */
    fun getRecordSummariesByUser(userId: String): Flow<List<InterviewRecordSummary>> {
        return interviewRecordDao.getRecordSummariesByUser(userId).map { summaries ->
            summaries.map { summary ->
                summary.copy(
                    status = InterviewRecordStatus.valueOf(summary.status.toString())
                )
            }
        }
    }
    
    /**
     * 根据ID获取面试记录
     */
    suspend fun getRecordById(recordId: String): InterviewRecordModel? {
        return interviewRecordDao.getRecordById(recordId)?.toModel()
    }
    
    /**
     * 根据会话ID获取面试记录
     */
    suspend fun getRecordBySessionId(sessionId: String): InterviewRecordModel? {
        return interviewRecordDao.getRecordBySessionId(sessionId)?.toModel()
    }
    
    /**
     * 获取指定时间范围内的面试记录
     */
    suspend fun getRecordsByTimeRange(
        userId: String, 
        startTime: Long, 
        endTime: Long
    ): List<InterviewRecordModel> {
        return interviewRecordDao.getRecordsByTimeRange(userId, startTime, endTime)
            .map { it.toModel() }
    }
    
    /**
     * 根据岗位类别获取面试记录
     */
    suspend fun getRecordsByCategory(userId: String, category: String): List<InterviewRecordModel> {
        return interviewRecordDao.getRecordsByCategory(userId, category)
            .map { it.toModel() }
    }
    
    /**
     * 搜索面试记录
     * 支持在岗位名称和类别中搜索关键词
     */
    suspend fun searchRecords(userId: String, keyword: String): List<InterviewRecordModel> {
        return interviewRecordDao.searchRecords(userId, keyword)
            .map { it.toModel() }
    }
    
    /**
     * 根据多个条件筛选面试记录
     * @param userId 用户ID
     * @param category 岗位类别（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param status 面试状态（可选）
     */
    suspend fun filterRecords(
        userId: String,
        category: String? = null,
        startTime: Long? = null,
        endTime: Long? = null,
        status: InterviewRecordStatus? = null
    ): List<InterviewRecordModel> {
        return interviewRecordDao.filterRecords(
            userId = userId,
            category = category,
            startTime = startTime,
            endTime = endTime,
            status = status?.name
        ).map { it.toModel() }
    }
    
    /**
     * 组合搜索和筛选功能
     * 支持关键词搜索和条件筛选的组合
     */
    suspend fun searchAndFilterRecords(
        userId: String,
        keyword: String? = null,
        category: String? = null,
        startTime: Long? = null,
        endTime: Long? = null,
        status: InterviewRecordStatus? = null
    ): List<InterviewRecordModel> {
        return interviewRecordDao.searchAndFilterRecords(
            userId = userId,
            keyword = keyword,
            category = category,
            startTime = startTime,
            endTime = endTime,
            status = status?.name
        ).map { it.toModel() }
    }
    
    /**
     * 获取用户的面试记录统计信息
     */
    suspend fun getRecordStatistics(userId: String): InterviewRecordStatistics? {
        return interviewRecordDao.getRecordStatistics(userId)
    }
    
    /**
     * 保存面试记录
     */
    suspend fun saveRecord(record: InterviewRecordModel) {
        interviewRecordDao.insertRecord(record.toEntity())
    }
    
    /**
     * 从面试会话创建面试记录
     */
    suspend fun createRecordFromSession(
        session: InterviewSession,
        position: Position,
        audioFiles: List<AudioFileInfo>
    ): InterviewRecordModel {
        val record = InterviewRecordModel(
            id = generateRecordId(),
            sessionId = session.id,
            userId = session.userId,
            positionId = session.positionId,
            positionName = position.name,
            positionCategory = position.category,
            startTime = session.startTime,
            endTime = session.endTime ?: System.currentTimeMillis(),
            duration = calculateDuration(session.startTime, session.endTime),
            status = mapSessionStatusToRecordStatus(session.status),
            questions = session.questions,
            answers = session.answers.map { answer ->
                val audioFile = audioFiles.find { it.questionId == answer.questionId }
                AnswerWithAudio(
                    questionId = answer.questionId,
                    questionContent = session.questions.find { it.id == answer.questionId }?.content ?: "",
                    audioFilePath = audioFile?.filePath ?: answer.audioUrl,
                    audioUrl = answer.audioUrl,
                    transcription = answer.transcription,
                    duration = answer.duration,
                    submittedAt = answer.submittedAt,
                    audioFileSize = audioFile?.fileSize ?: 0L
                )
            },
            audioFiles = audioFiles,
            evaluationId = session.evaluation?.sessionId,
            overallScore = session.evaluation?.overallScore,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis()
        )
        
        saveRecord(record)
        return record
    }
    
    /**
     * 更新面试记录
     */
    suspend fun updateRecord(record: InterviewRecordModel) {
        val updatedRecord = record.copy(updatedAt = System.currentTimeMillis())
        interviewRecordDao.updateRecord(updatedRecord.toEntity())
    }
    
    /**
     * 更新同步状态
     */
    suspend fun updateSyncStatus(recordId: String, isSynced: Boolean) {
        val syncedAt = if (isSynced) System.currentTimeMillis() else null
        interviewRecordDao.updateSyncStatus(
            recordId = recordId,
            isSynced = isSynced,
            syncedAt = syncedAt,
            updatedAt = System.currentTimeMillis()
        )
    }
    
    /**
     * 删除面试记录
     */
    suspend fun deleteRecord(recordId: String) {
        interviewRecordDao.deleteRecordById(recordId)
    }
    
    /**
     * 删除指定时间之前的记录
     */
    suspend fun deleteRecordsBefore(userId: String, beforeTime: Long) {
        interviewRecordDao.deleteRecordsBefore(userId, beforeTime)
    }
    
    /**
     * 获取未同步的记录
     */
    suspend fun getUnsyncedRecords(): List<InterviewRecordModel> {
        return interviewRecordDao.getUnsyncedRecords().map { it.toModel() }
    }
    
    /**
     * 获取记录总数
     */
    suspend fun getRecordCount(userId: String): Int {
        return interviewRecordDao.getRecordCount(userId)
    }
    
    // 私有辅助方法
    
    private fun generateRecordId(): String {
        return "record_${System.currentTimeMillis()}_${(1000..9999).random()}"
    }
    
    private fun calculateDuration(startTime: Long, endTime: Long?): Int {
        return if (endTime != null) {
            ((endTime - startTime) / 1000).toInt()
        } else {
            0
        }
    }
    
    private fun mapSessionStatusToRecordStatus(status: InterviewStatus): InterviewRecordStatus {
        return when (status) {
            InterviewStatus.COMPLETED -> InterviewRecordStatus.COMPLETED
            InterviewStatus.CANCELLED -> InterviewRecordStatus.CANCELLED
            else -> InterviewRecordStatus.INTERRUPTED
        }
    }
    
    // 扩展函数：Entity转Model
    private fun InterviewRecord.toModel(): InterviewRecordModel {
        return InterviewRecordModel(
            id = id,
            sessionId = sessionId,
            userId = userId,
            positionId = positionId,
            positionName = positionName,
            positionCategory = positionCategory,
            startTime = startTime,
            endTime = endTime,
            duration = duration,
            status = InterviewRecordStatus.valueOf(status),
            questions = InterviewRecordJsonConverter.questionsFromJson(questionsJson),
            answers = InterviewRecordJsonConverter.answersFromJson(answersJson),
            audioFiles = InterviewRecordJsonConverter.audioFilesFromJson(audioFilesJson),
            evaluationId = evaluationId,
            overallScore = overallScore,
            createdAt = createdAt,
            updatedAt = updatedAt,
            isSynced = isSynced,
            syncedAt = syncedAt
        )
    }
    
    // 扩展函数：Model转Entity
    private fun InterviewRecordModel.toEntity(): InterviewRecord {
        return InterviewRecord(
            id = id,
            sessionId = sessionId,
            userId = userId,
            positionId = positionId,
            positionName = positionName,
            positionCategory = positionCategory,
            startTime = startTime,
            endTime = endTime,
            duration = duration,
            status = status.name,
            questionsJson = InterviewRecordJsonConverter.questionsToJson(questions),
            answersJson = InterviewRecordJsonConverter.answersToJson(answers),
            audioFilesJson = InterviewRecordJsonConverter.audioFilesToJson(audioFiles),
            evaluationId = evaluationId,
            overallScore = overallScore,
            createdAt = createdAt,
            updatedAt = updatedAt,
            isSynced = isSynced,
            syncedAt = syncedAt
        )
    }
}