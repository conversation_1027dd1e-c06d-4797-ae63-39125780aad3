package com.aiinterview.simulator.di

import android.content.Context
import com.aiinterview.simulator.data.api.BaiduASRApi
import com.aiinterview.simulator.data.api.SpeechRecognitionApi
import com.aiinterview.simulator.data.repository.SpeechRecognitionRepository
import com.aiinterview.simulator.data.repository.SpeechRecognitionRepositoryImpl
import com.aiinterview.simulator.data.speech.SpeechRecognitionService
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class SpeechModule {
    
    @Binds
    @Singleton
    abstract fun bindSpeechRecognitionRepository(
        speechRecognitionRepositoryImpl: SpeechRecognitionRepositoryImpl
    ): SpeechRecognitionRepository
    
    companion object {
        @Provides
        @Singleton
        fun provideSpeechRecognitionService(
            @ApplicationContext context: Context,
            speechRecognition<PERSON>pi: SpeechR<PERSON>ognition<PERSON>pi,
            baiduASRApi: BaiduAS<PERSON>pi
        ): SpeechRecognitionService {
            return SpeechRecognitionService(context, speechRecognitionApi, baiduASRApi)
        }
    }
}