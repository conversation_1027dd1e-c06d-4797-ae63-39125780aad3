package com.aiinterview.simulator.util

import android.content.Context
import com.aiinterview.simulator.BuildConfig
import java.text.SimpleDateFormat
import java.util.*

/**
 * 构建信息管理类
 * 用于管理和展示应用的构建相关信息，包括版本、渠道、签名等
 */
class BuildInfoManager(private val context: Context) {
    
    private val versionManager by lazy { VersionManager(context) }
    private val signatureVerifier by lazy { SignatureVerifier(context) }
    
    /**
     * 获取完整的构建信息
     * @return 包含所有构建相关信息的数据类
     */
    fun getBuildInfo(): BuildInfo {
        return BuildInfo(
            appName = "AI面试模拟器",
            packageName = BuildConfig.APPLICATION_ID,
            versionName = BuildConfig.VERSION_NAME,
            versionCode = BuildConfig.VERSION_CODE,
            buildType = ChannelUtil.getBuildType(),
            channel = ChannelUtil.getCurrentChannel(),
            channelDisplayName = ChannelUtil.getChannelDisplayName(),
            isDebug = ChannelUtil.isDebugBuild(),
            isLoggingEnabled = ChannelUtil.isLoggingEnabled(),
            buildTime = versionManager.getBuildTime(),
            signatureSHA1 = signatureVerifier.getCurrentSignatureSHA1() ?: "未知",
            isSignatureValid = signatureVerifier.verifySignature(),
            isRepackaged = signatureVerifier.isRepackaged()
        )
    }
    
    /**
     * 生成用于分享的构建信息文本
     * @return 格式化的构建信息字符串
     */
    fun getShareableBuildInfo(): String {
        val buildInfo = getBuildInfo()
        
        return buildString {
            appendLine("📱 ${buildInfo.appName}")
            appendLine("🏷️ 版本：${buildInfo.versionName} (${buildInfo.versionCode})")
            appendLine("📦 渠道：${buildInfo.channelDisplayName}")
            appendLine("🔧 构建：${buildInfo.buildType}")
            appendLine("⏰ 时间：${buildInfo.buildTime}")
            
            if (buildInfo.isDebug) {
                appendLine("🐛 调试模式")
            }
            
            appendLine("🔐 签名：${if (buildInfo.isSignatureValid) "✅ 有效" else "❌ 无效"}")
            
            if (buildInfo.isRepackaged) {
                appendLine("⚠️ 疑似被重新打包")
            }
        }
    }
    
    /**
     * 生成详细的技术报告
     * @return 包含详细技术信息的报告字符串
     */
    fun generateTechnicalReport(): String {
        val buildInfo = getBuildInfo()
        
        return buildString {
            appendLine("=== AI面试模拟器技术报告 ===")
            appendLine()
            
            // 应用基本信息
            appendLine("【应用信息】")
            appendLine("应用名称：${buildInfo.appName}")
            appendLine("包名：${buildInfo.packageName}")
            appendLine("版本名称：${buildInfo.versionName}")
            appendLine("版本号：${buildInfo.versionCode}")
            appendLine()
            
            // 构建信息
            appendLine("【构建信息】")
            appendLine("构建类型：${buildInfo.buildType}")
            appendLine("渠道标识：${buildInfo.channel}")
            appendLine("渠道名称：${buildInfo.channelDisplayName}")
            appendLine("构建时间：${buildInfo.buildTime}")
            appendLine("调试模式：${if (buildInfo.isDebug) "启用" else "禁用"}")
            appendLine("日志输出：${if (buildInfo.isLoggingEnabled) "启用" else "禁用"}")
            appendLine()
            
            // 安全信息
            appendLine("【安全信息】")
            appendLine("签名SHA1：${buildInfo.signatureSHA1}")
            appendLine("签名验证：${if (buildInfo.isSignatureValid) "通过" else "失败"}")
            appendLine("重新打包检查：${if (buildInfo.isRepackaged) "疑似被重新打包" else "正常"}")
            appendLine()
            
            // 系统信息
            appendLine("【系统信息】")
            appendLine("Android版本：${android.os.Build.VERSION.RELEASE}")
            appendLine("API级别：${android.os.Build.VERSION.SDK_INT}")
            appendLine("设备型号：${android.os.Build.MODEL}")
            appendLine("设备厂商：${android.os.Build.MANUFACTURER}")
            appendLine("CPU架构：${android.os.Build.CPU_ABI}")
            appendLine()
            
            // 渠道特定信息
            appendLine("【渠道信息】")
            appendLine("客服联系：${ChannelUtil.getCustomerServiceInfo()}")
            val storeUrl = ChannelUtil.getStoreDownloadUrl()
            if (storeUrl != null) {
                appendLine("商店链接：$storeUrl")
            }
            appendLine()
            
            // 统计配置
            appendLine("【统计配置】")
            val analyticsConfig = ChannelUtil.getAnalyticsConfig()
            analyticsConfig.forEach { (key, value) ->
                appendLine("$key: $value")
            }
            appendLine()
            
            appendLine("报告生成时间：${SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())}")
            appendLine("=== 报告结束 ===")
        }
    }
    
    /**
     * 检查构建完整性
     * @return 构建完整性检查结果
     */
    fun checkBuildIntegrity(): BuildIntegrityResult {
        val buildInfo = getBuildInfo()
        val issues = mutableListOf<String>()
        
        // 检查签名
        if (!buildInfo.isSignatureValid) {
            issues.add("应用签名验证失败")
        }
        
        // 检查是否被重新打包
        if (buildInfo.isRepackaged) {
            issues.add("应用疑似被重新打包")
        }
        
        // 检查版本信息
        if (buildInfo.versionName.isBlank()) {
            issues.add("版本名称为空")
        }
        
        if (buildInfo.versionCode <= 0) {
            issues.add("版本号无效")
        }
        
        // 检查渠道信息
        if (buildInfo.channel.isBlank()) {
            issues.add("渠道信息缺失")
        }
        
        return BuildIntegrityResult(
            isValid = issues.isEmpty(),
            issues = issues,
            buildInfo = buildInfo
        )
    }
    
    /**
     * 获取构建环境信息
     * @return 构建环境相关信息
     */
    fun getBuildEnvironmentInfo(): Map<String, String> {
        return mapOf(
            "gradle_version" to "8.0",
            "kotlin_version" to "1.9.10",
            "android_gradle_plugin" to "8.1.2",
            "compile_sdk" to "34",
            "min_sdk" to "24",
            "target_sdk" to "34",
            "build_tools" to "34.0.0",
            "java_version" to "1.8"
        )
    }
    
    /**
     * 构建信息数据类
     */
    data class BuildInfo(
        val appName: String,
        val packageName: String,
        val versionName: String,
        val versionCode: Int,
        val buildType: String,
        val channel: String,
        val channelDisplayName: String,
        val isDebug: Boolean,
        val isLoggingEnabled: Boolean,
        val buildTime: String,
        val signatureSHA1: String,
        val isSignatureValid: Boolean,
        val isRepackaged: Boolean
    )
    
    /**
     * 构建完整性检查结果
     */
    data class BuildIntegrityResult(
        val isValid: Boolean,
        val issues: List<String>,
        val buildInfo: BuildInfo
    )
}