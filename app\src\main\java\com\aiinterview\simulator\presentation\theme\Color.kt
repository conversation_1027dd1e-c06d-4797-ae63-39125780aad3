package com.aiinterview.simulator.presentation.theme

import androidx.compose.ui.graphics.Color

/**
 * 应用颜色定义
 * 定义应用中使用的所有颜色常量，支持完整的Material Design 3颜色系统
 */

// 主色调系列 - 蓝色系，遵循Material Design 3色调规范
val Primary0 = Color(0xFF000000)           // 纯黑色 - 最深主色调
val Primary10 = Color(0xFF0D47A1)          // 深蓝色 - 用于深色强调和文字
val Primary20 = Color(0xFF1565C0)          // 较深蓝色 - 用于深色模式容器
val Primary30 = Color(0xFF1976D2)          // 中深蓝色 - 用于深色模式主要元素
val Primary40 = Color(0xFF1976D2)          // 主要蓝色 - 浅色模式主色调
val Primary50 = Color(0xFF2196F3)          // 标准蓝色 - 中性主色调
val Primary60 = Color(0xFF42A5F5)          // 中浅蓝色 - 用于悬停状态
val Primary70 = Color(0xFF64B5F6)          // 较浅蓝色 - 用于激活状态
val Primary80 = Color(0xFF90CAF9)          // 浅蓝色 - 深色模式主色调
val Primary90 = Color(0xFFE3F2FD)          // 极浅蓝色 - 浅色模式背景色调
val Primary95 = Color(0xFFF1F8FE)          // 接近白色的蓝色 - 最浅背景
val Primary99 = Color(0xFFFEFEFF)          // 几乎白色 - 纯净背景
val Primary100 = Color(0xFFFFFFFF)         // 纯白色 - 最亮主色调

// 次要色调系列 - 青色系，提供丰富的次要色彩层次
val Secondary0 = Color(0xFF000000)         // 纯黑色 - 最深次要色调
val Secondary10 = Color(0xFF006064)        // 深青色 - 用于深色强调和文字
val Secondary20 = Color(0xFF00838F)        // 较深青色 - 用于深色模式容器
val Secondary30 = Color(0xFF0097A7)        // 中深青色 - 用于深色模式次要元素
val Secondary40 = Color(0xFF00ACC1)        // 主要青色 - 浅色模式次要色调
val Secondary50 = Color(0xFF00BCD4)        // 标准青色 - 中性次要色调
val Secondary60 = Color(0xFF26C6DA)        // 中浅青色 - 用于悬停状态
val Secondary70 = Color(0xFF4DD0E1)        // 较浅青色 - 用于激活状态
val Secondary80 = Color(0xFF80DEEA)        // 浅青色 - 深色模式次要色调
val Secondary90 = Color(0xFFE0F7FA)        // 极浅青色 - 浅色模式次要背景
val Secondary95 = Color(0xFFF0FBFC)        // 接近白色的青色 - 最浅次要背景
val Secondary99 = Color(0xFFFEFEFF)        // 几乎白色 - 纯净次要背景
val Secondary100 = Color(0xFFFFFFFF)       // 纯白色 - 最亮次要色调

// 第三色调系列 - 紫色系，提供强调和装饰色彩
val Tertiary0 = Color(0xFF000000)          // 纯黑色 - 最深第三色调
val Tertiary10 = Color(0xFF4A148C)         // 深紫色 - 用于深色强调和文字
val Tertiary20 = Color(0xFF6A1B9A)         // 较深紫色 - 用于深色模式容器
val Tertiary30 = Color(0xFF7B1FA2)         // 中深紫色 - 用于深色模式第三元素
val Tertiary40 = Color(0xFF7B1FA2)         // 主要紫色 - 浅色模式第三色调
val Tertiary50 = Color(0xFF9C27B0)         // 标准紫色 - 中性第三色调
val Tertiary60 = Color(0xFFAB47BC)         // 中浅紫色 - 用于悬停状态
val Tertiary70 = Color(0xFFBA68C8)         // 较浅紫色 - 用于激活状态
val Tertiary80 = Color(0xFFCE93D8)         // 浅紫色 - 深色模式第三色调
val Tertiary90 = Color(0xFFF3E5F5)         // 极浅紫色 - 浅色模式第三背景
val Tertiary95 = Color(0xFFF9F2FA)         // 接近白色的紫色 - 最浅第三背景
val Tertiary99 = Color(0xFFFEFEFF)         // 几乎白色 - 纯净第三背景
val Tertiary100 = Color(0xFFFFFFFF)        // 纯白色 - 最亮第三色调

// 错误色系列 - 红色系，用于错误状态和警告信息
val Error0 = Color(0xFF000000)             // 纯黑色 - 最深错误色调
val Error10 = Color(0xFFB71C1C)            // 深错误红色 - 用于深色强调和文字
val Error20 = Color(0xFFC62828)            // 较深错误红色 - 用于深色模式容器
val Error30 = Color(0xFFD32F2F)            // 中深错误红色 - 用于深色模式错误元素
val Error40 = Color(0xFFD32F2F)            // 主要错误红色 - 浅色模式错误色调
val Error50 = Color(0xFFF44336)            // 标准错误红色 - 中性错误色调
val Error60 = Color(0xFFE57373)            // 中浅错误红色 - 用于悬停状态
val Error70 = Color(0xFFEF5350)            // 较浅错误红色 - 用于激活状态
val Error80 = Color(0xFFEF9A9A)            // 浅错误红色 - 深色模式错误色调
val Error90 = Color(0xFFFFEBEE)            // 极浅错误红色 - 浅色模式错误背景
val Error95 = Color(0xFFFFF5F5)            // 接近白色的红色 - 最浅错误背景
val Error99 = Color(0xFFFEFEFF)            // 几乎白色 - 纯净错误背景
val Error100 = Color(0xFFFFFFFF)           // 纯白色 - 最亮错误色调

// 成功色系列 - 绿色系
val Success40 = Color(0xFF388E3C)          // 主要成功绿色
val Success80 = Color(0xFFA5D6A7)          // 浅成功绿色 - 深色模式
val Success90 = Color(0xFFE8F5E8)          // 极浅成功绿色 - 成功背景
val Success10 = Color(0xFF1B5E20)          // 深成功绿色

// 警告色系列 - 橙色系
val Warning40 = Color(0xFFF57C00)          // 主要警告橙色
val Warning80 = Color(0xFFFFCC02)          // 浅警告橙色 - 深色模式
val Warning90 = Color(0xFFFFF8E1)          // 极浅警告橙色 - 警告背景
val Warning10 = Color(0xFFE65100)          // 深警告橙色

// 中性色系列 - 灰色系
val Neutral10 = Color(0xFF1C1B1F)          // 最深灰色 - 深色模式背景
val Neutral20 = Color(0xFF313033)          // 深灰色 - 深色模式表面
val Neutral30 = Color(0xFF484649)          // 中深灰色
val Neutral40 = Color(0xFF605D62)          // 中灰色
val Neutral50 = Color(0xFF787579)          // 标准灰色
val Neutral60 = Color(0xFF939094)          // 中浅灰色
val Neutral70 = Color(0xFFAEAAAE)          // 浅灰色
val Neutral80 = Color(0xFFC9C5CA)          // 很浅灰色
val Neutral90 = Color(0xFFE6E1E5)          // 极浅灰色 - 浅色模式背景
val Neutral95 = Color(0xFFF4EFF4)          // 接近白色 - 浅色模式表面
val Neutral99 = Color(0xFFFFFBFE)          // 纯白色

// 中性变体色系列 - 用于边框和分割线
val NeutralVariant30 = Color(0xFF49454F)   // 深色边框
val NeutralVariant50 = Color(0xFF79747E)   // 标准边框
val NeutralVariant60 = Color(0xFF938F99)   // 浅色边框
val NeutralVariant80 = Color(0xFFCAC4D0)   // 很浅边框
val NeutralVariant90 = Color(0xFFE7E0EC)   // 极浅边框

// 特殊功能色
val InterviewActive = Color(0xFF4CAF50)     // 面试进行中状态色
val InterviewPending = Color(0xFFFF9800)    // 面试等待状态色
val InterviewCompleted = Color(0xFF2196F3)  // 面试完成状态色
val InterviewCancelled = Color(0xFF9E9E9E)  // 面试取消状态色

val RecordingActive = Color(0xFFE53935)     // 录音进行中状态色
val RecordingPaused = Color(0xFFFF9800)     // 录音暂停状态色
val RecordingStopped = Color(0xFF757575)    // 录音停止状态色

val ScoreExcellent = Color(0xFF4CAF50)      // 优秀分数颜色
val ScoreGood = Color(0xFF8BC34A)           // 良好分数颜色
val ScoreAverage = Color(0xFFFF9800)        // 一般分数颜色
val ScorePoor = Color(0xFFFF5722)           // 较差分数颜色
val ScoreFail = Color(0xFFF44336)           // 不及格分数颜色

// 渐变色定义
val GradientPrimary = listOf(Primary40, Primary80)      // 主色调渐变
val GradientSecondary = listOf(Secondary40, Secondary80) // 次要色调渐变
val GradientSuccess = listOf(Success40, Success80)       // 成功色渐变
val GradientWarning = listOf(Warning40, Warning80)       // 警告色渐变
val GradientError = listOf(Error40, Error80)             // 错误色渐变