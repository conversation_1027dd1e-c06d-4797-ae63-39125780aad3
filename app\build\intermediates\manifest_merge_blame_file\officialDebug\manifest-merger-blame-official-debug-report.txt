1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.aiinterview.simulator.debug"
4    android:versionCode="1"
5    android:versionName="1.0.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:5:5-67
11-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:6:5-79
12-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.RECORD_AUDIO" />
13-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:7:5-71
13-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:7:22-68
14    <uses-permission
14-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:8:5-9:38
15        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
15-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:8:22-78
16        android:maxSdkVersion="28" />
16-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:9:9-35
17
18    <permission
18-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
19        android:name="com.aiinterview.simulator.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
19-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
20        android:protectionLevel="signature" />
20-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
21
22    <uses-permission android:name="com.aiinterview.simulator.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
22-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
22-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
23
24    <application
24-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:11:5-32:19
25        android:name="com.aiinterview.simulator.AIInterviewApplication"
25-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:12:9-47
26        android:allowBackup="true"
26-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:13:9-35
27        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
27-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
28        android:dataExtractionRules="@xml/data_extraction_rules"
28-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:14:9-65
29        android:debuggable="true"
30        android:extractNativeLibs="false"
31        android:fullBackupContent="@xml/backup_rules"
31-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:15:9-54
32        android:icon="@android:drawable/sym_def_app_icon"
32-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:16:9-58
33        android:label="@string/app_name"
33-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:17:9-41
34        android:roundIcon="@android:drawable/sym_def_app_icon"
34-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:18:9-63
35        android:supportsRtl="true"
35-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:19:9-35
36        android:theme="@style/Theme.AIInterviewSimulator" >
36-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:20:9-58
37        <activity
37-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:23:9-31:20
38            android:name="com.aiinterview.simulator.presentation.MainActivity"
38-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:24:13-54
39            android:exported="true"
39-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:25:13-36
40            android:theme="@style/Theme.AIInterviewSimulator" >
40-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:26:13-62
41            <intent-filter>
41-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:27:13-30:29
42                <action android:name="android.intent.action.MAIN" />
42-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:28:17-69
42-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:28:25-66
43
44                <category android:name="android.intent.category.LAUNCHER" />
44-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:29:17-77
44-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:29:27-74
45            </intent-filter>
46        </activity>
47        <activity
47-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2546df139fad0738ad0957958c32c5fd\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
48            android:name="androidx.activity.ComponentActivity"
48-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2546df139fad0738ad0957958c32c5fd\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
49            android:exported="true" />
49-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2546df139fad0738ad0957958c32c5fd\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
50        <activity
50-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a837338b3977ec042f451025b5b4e064\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
51            android:name="androidx.compose.ui.tooling.PreviewActivity"
51-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a837338b3977ec042f451025b5b4e064\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
52            android:exported="true" />
52-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a837338b3977ec042f451025b5b4e064\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
53
54        <provider
54-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
55            android:name="androidx.startup.InitializationProvider"
55-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
56            android:authorities="com.aiinterview.simulator.debug.androidx-startup"
56-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
57            android:exported="false" >
57-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
58            <meta-data
58-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
59                android:name="androidx.emoji2.text.EmojiCompatInitializer"
59-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
60                android:value="androidx.startup" />
60-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
61            <meta-data
61-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9432865878371e9545627d83de8d1340\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
62                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
62-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9432865878371e9545627d83de8d1340\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
63                android:value="androidx.startup" />
63-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9432865878371e9545627d83de8d1340\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
64            <meta-data
64-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
65                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
65-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
66                android:value="androidx.startup" />
66-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
67        </provider>
68
69        <service
69-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6a89eba57c9d14e67fa63b3b7fec273e\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
70            android:name="androidx.room.MultiInstanceInvalidationService"
70-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6a89eba57c9d14e67fa63b3b7fec273e\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
71            android:directBootAware="true"
71-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6a89eba57c9d14e67fa63b3b7fec273e\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
72            android:exported="false" />
72-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6a89eba57c9d14e67fa63b3b7fec273e\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
73
74        <receiver
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
75            android:name="androidx.profileinstaller.ProfileInstallReceiver"
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
76            android:directBootAware="false"
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
77            android:enabled="true"
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
78            android:exported="true"
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
79            android:permission="android.permission.DUMP" >
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
81                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
82            </intent-filter>
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
84                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
85            </intent-filter>
86            <intent-filter>
86-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
87                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
87-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
87-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
88            </intent-filter>
89            <intent-filter>
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
90                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
91            </intent-filter>
92        </receiver>
93    </application>
94
95</manifest>
