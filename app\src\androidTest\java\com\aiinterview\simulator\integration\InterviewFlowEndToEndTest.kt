package com.aiinterview.simulator.integration

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.aiinterview.simulator.presentation.MainActivity
import com.aiinterview.simulator.util.ComposeTestUtils
import com.aiinterview.simulator.util.ComposeTestUtils.assertButtonClickable
import com.aiinterview.simulator.util.ComposeTestUtils.assertTextExists
import com.aiinterview.simulator.util.ComposeTestUtils.clickBottomNavItem
import com.aiinterview.simulator.util.ComposeTestUtils.clickButton
import com.aiinterview.simulator.util.ComposeTestUtils.inputText
import com.aiinterview.simulator.util.ComposeTestUtils.waitForLoadingToComplete
import com.aiinterview.simulator.util.MockTestData
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * 面试流程端到端测试类
 * 测试完整的用户面试流程，从登录到面试完成
 */
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class InterviewFlowEndToEndTest {

    @get:Rule(order = 0)
    val hiltRule = HiltAndroidRule(this)

    @get:Rule(order = 1)
    val composeTestRule = createAndroidComposeRule<MainActivity>()

    @Before
    fun setUp() {
        hiltRule.inject()
    }

    /**
     * 测试完整的面试流程
     * 包括：登录 -> 选择岗位 -> 开始面试 -> 回答问题 -> 查看评价
     */
    @Test
    fun completeInterviewFlow_fromLoginToEvaluation_worksCorrectly() {
        // Step 1: 用户登录
        performLogin()
        
        // Step 2: 选择面试岗位
        selectInterviewPosition()
        
        // Step 3: 开始面试
        startInterview()
        
        // Step 4: 回答面试问题
        answerInterviewQuestions()
        
        // Step 5: 查看面试评价
        viewInterviewEvaluation()
        
        // Step 6: 查看历史记录
        viewInterviewHistory()
    }

    /**
     * 执行用户登录流程
     */
    private fun performLogin() {
        // 验证登录界面显示
        composeTestRule.assertTextExists("用户登录")
        
        // 输入登录信息
        composeTestRule.inputText("手机号", "13800138000")
        composeTestRule.clickButton("获取验证码")
        composeTestRule.waitForLoadingToComplete()
        
        composeTestRule.inputText("验证码", "123456")
        composeTestRule.assertButtonClickable("登录")
        
        // 执行登录
        composeTestRule.clickButton("登录")
        composeTestRule.waitForLoadingToComplete()
        
        // 验证登录成功，进入主界面
        composeTestRule.assertTextExists("AI面试模拟器")
        composeTestRule.assertTextExists("选择面试岗位")
    }

    /**
     * 选择面试岗位
     */
    private fun selectInterviewPosition() {
        // 验证岗位列表显示
        composeTestRule.waitForLoadingToComplete()
        composeTestRule.assertTextExists("公务员-行政管理")
        composeTestRule.assertTextExists("公务员-财务管理")
        composeTestRule.assertTextExists("公务员-人力资源")
        
        // 选择第一个岗位
        composeTestRule.clickButton("公务员-行政管理")
        
        // 验证岗位详情页面
        composeTestRule.assertTextExists("岗位详情")
        composeTestRule.assertTextExists("负责行政管理相关工作")
        composeTestRule.assertTextExists("面试时长：20分钟")
        composeTestRule.assertTextExists("题目数量：4题")
        
        // 确认选择岗位
        composeTestRule.assertButtonClickable("开始面试")
    }

    /**
     * 开始面试
     */
    private fun startInterview() {
        // 点击开始面试
        composeTestRule.clickButton("开始面试")
        composeTestRule.waitForLoadingToComplete()
        
        // 验证面试准备界面
        composeTestRule.assertTextExists("面试准备")
        composeTestRule.assertTextExists("请确保您处于安静的环境中")
        composeTestRule.assertTextExists("请允许录音权限")
        
        // 检查权限并开始
        composeTestRule.clickButton("检查权限")
        composeTestRule.waitForLoadingToComplete()
        
        composeTestRule.clickButton("开始面试")
        composeTestRule.waitForLoadingToComplete()
        
        // 验证进入面试界面
        composeTestRule.assertTextExists("面试进行中")
        composeTestRule.assertTextExists("第1题")
        composeTestRule.assertTextExists("剩余时间")
    }

    /**
     * 回答面试问题
     */
    private fun answerInterviewQuestions() {
        val totalQuestions = 4
        
        for (questionIndex in 1..totalQuestions) {
            // 验证当前题目显示
            composeTestRule.assertTextExists("第${questionIndex}题")
            
            // 等待AI问题播报完成
            composeTestRule.waitForLoadingToComplete(timeoutMillis = 15000)
            
            // 验证问题内容显示
            composeTestRule.assertTextExists("请仔细听题并准备回答")
            
            // 开始录音回答
            composeTestRule.assertButtonClickable("开始录音")
            composeTestRule.clickButton("开始录音")
            
            // 验证录音状态
            composeTestRule.assertTextExists("录音中...")
            composeTestRule.onNode(hasContentDescription("录音时间"))
                .assertExists()
                .assertIsDisplayed()
            
            // 模拟录音一段时间（实际测试中可能需要更短的时间）
            composeTestRule.waitUntil(timeoutMillis = 5000) {
                // 等待录音进行
                true
            }
            
            // 停止录音
            composeTestRule.clickButton("停止录音")
            composeTestRule.waitForLoadingToComplete()
            
            // 验证语音识别结果
            composeTestRule.assertTextExists("语音识别结果")
            composeTestRule.assertTextExists("请确认识别结果是否正确")
            
            // 确认识别结果
            composeTestRule.assertButtonClickable("确认提交")
            composeTestRule.clickButton("确认提交")
            composeTestRule.waitForLoadingToComplete()
            
            // 如果不是最后一题，验证下一题按钮
            if (questionIndex < totalQuestions) {
                composeTestRule.assertTextExists("答案已提交")
                composeTestRule.assertButtonClickable("下一题")
                composeTestRule.clickButton("下一题")
                composeTestRule.waitForLoadingToComplete()
            }
        }
        
        // 验证面试完成
        composeTestRule.assertTextExists("面试已完成")
        composeTestRule.assertTextExists("正在生成评价报告...")
    }

    /**
     * 查看面试评价
     */
    private fun viewInterviewEvaluation() {
        // 等待评价生成完成
        composeTestRule.waitForLoadingToComplete(timeoutMillis = 15000)
        
        // 验证评价报告界面
        composeTestRule.assertTextExists("面试评价报告")
        composeTestRule.assertTextExists("总体评分")
        
        // 验证各维度评分
        composeTestRule.assertTextExists("内容完整性")
        composeTestRule.assertTextExists("逻辑性")
        composeTestRule.assertTextExists("语言流畅度")
        composeTestRule.assertTextExists("时间掌控")
        
        // 验证详细反馈
        composeTestRule.assertTextExists("详细反馈")
        composeTestRule.assertTextExists("改进建议")
        
        // 滚动查看完整评价
        composeTestRule.onNodeWithTag("evaluation_content")
            .performScrollTo()
        
        // 验证操作按钮
        composeTestRule.assertButtonClickable("保存报告")
        composeTestRule.assertButtonClickable("重新练习")
        composeTestRule.assertButtonClickable("返回首页")
        
        // 保存报告
        composeTestRule.clickButton("保存报告")
        composeTestRule.waitForLoadingToComplete()
        composeTestRule.assertTextExists("报告已保存")
    }

    /**
     * 查看面试历史记录
     */
    private fun viewInterviewHistory() {
        // 导航到历史记录页面
        composeTestRule.clickBottomNavItem("历史记录")
        composeTestRule.waitForLoadingToComplete()
        
        // 验证历史记录列表
        composeTestRule.assertTextExists("面试历史")
        composeTestRule.assertTextExists("公务员-行政管理")
        
        // 点击查看详情
        composeTestRule.onNodeWithText("公务员-行政管理")
            .performClick()
        composeTestRule.waitForLoadingToComplete()
        
        // 验证历史记录详情
        composeTestRule.assertTextExists("面试详情")
        composeTestRule.assertTextExists("面试时间")
        composeTestRule.assertTextExists("总体评分")
        composeTestRule.assertTextExists("查看完整报告")
        
        // 验证可以重新练习
        composeTestRule.assertButtonClickable("重新练习")
    }

    /**
     * 测试面试过程中的异常处理
     */
    @Test
    fun interviewFlow_withErrors_handlesGracefully() {
        // 执行登录
        performLogin()
        selectInterviewPosition()
        
        // 开始面试
        composeTestRule.clickButton("开始面试")
        composeTestRule.waitForLoadingToComplete()
        
        // 模拟网络错误
        composeTestRule.clickButton("开始面试")
        // 这里应该模拟网络错误的情况
        
        // 验证错误处理
        composeTestRule.assertTextExists("网络连接失败")
        composeTestRule.assertButtonClickable("重试")
        
        // 点击重试
        composeTestRule.clickButton("重试")
        composeTestRule.waitForLoadingToComplete()
        
        // 验证重试后正常进行
        composeTestRule.assertTextExists("面试进行中")
    }

    /**
     * 测试权限处理流程
     */
    @Test
    fun interviewFlow_permissionHandling_worksCorrectly() {
        // 执行登录和岗位选择
        performLogin()
        selectInterviewPosition()
        
        // 开始面试时检查权限
        composeTestRule.clickButton("开始面试")
        composeTestRule.waitForLoadingToComplete()
        
        // 验证权限检查界面
        composeTestRule.assertTextExists("需要录音权限")
        composeTestRule.assertTextExists("为了进行面试，需要您授权录音权限")
        
        // 点击授权
        composeTestRule.clickButton("授权权限")
        
        // 验证权限授权后的状态
        composeTestRule.waitForLoadingToComplete()
        composeTestRule.assertButtonClickable("开始面试")
    }

    /**
     * 测试面试暂停和恢复功能
     */
    @Test
    fun interviewFlow_pauseAndResume_worksCorrectly() {
        // 执行登录和开始面试
        performLogin()
        selectInterviewPosition()
        startInterview()
        
        // 在面试过程中暂停
        composeTestRule.clickButton("暂停面试")
        
        // 验证暂停状态
        composeTestRule.assertTextExists("面试已暂停")
        composeTestRule.assertTextExists("您可以稍后继续面试")
        composeTestRule.assertButtonClickable("继续面试")
        composeTestRule.assertButtonClickable("结束面试")
        
        // 继续面试
        composeTestRule.clickButton("继续面试")
        composeTestRule.waitForLoadingToComplete()
        
        // 验证恢复面试状态
        composeTestRule.assertTextExists("面试进行中")
        composeTestRule.assertTextExists("第1题")
    }

    /**
     * 测试离线模式处理
     */
    @Test
    fun interviewFlow_offlineMode_worksCorrectly() {
        // 执行登录和开始面试
        performLogin()
        selectInterviewPosition()
        startInterview()
        
        // 模拟网络断开
        // 在实际测试中，这里需要模拟网络状态
        
        // 继续录音（离线模式）
        composeTestRule.clickButton("开始录音")
        composeTestRule.waitUntil(timeoutMillis = 3000) { true }
        composeTestRule.clickButton("停止录音")
        
        // 验证离线提示
        composeTestRule.assertTextExists("网络连接已断开")
        composeTestRule.assertTextExists("录音已保存，将在网络恢复后上传")
        
        // 验证可以继续面试
        composeTestRule.assertButtonClickable("继续面试")
    }
}