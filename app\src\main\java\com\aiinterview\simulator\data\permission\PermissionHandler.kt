package com.aiinterview.simulator.data.permission

import android.app.Activity
import android.content.pm.PackageManager
import androidx.core.app.ActivityCompat
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 权限处理器
 * 负责处理权限请求结果和权限状态判断
 */
@Singleton
class PermissionHandler @Inject constructor(
    private val permissionManager: PermissionManager // 注入权限管理器
) {
    
    /**
     * 处理权限请求结果
     * @param activity 当前活动实例
     * @param requestCode 请求码
     * @param permissions 权限数组
     * @param grantResults 授权结果数组
     * @return 权限请求结果
     */
    fun handlePermissionResult(
        activity: Activity,
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ): PermissionRequestResult {
        // 创建权限结果映射
        val results = mutableMapOf<String, PermissionResult>()
        
        // 遍历权限和结果
        for (i in permissions.indices) {
            val permission = permissions[i]
            val isGranted = grantResults[i] == PackageManager.PERMISSION_GRANTED
            
            // 判断权限状态
            val status = when {
                isGranted -> PermissionStatus.GRANTED
                ActivityCompat.shouldShowRequestPermissionRationale(activity, permission) -> {
                    PermissionStatus.DENIED
                }
                else -> PermissionStatus.PERMANENTLY_DENIED
            }
            
            // 创建权限结果
            results[permission] = PermissionResult(
                permission = permission,
                status = status,
                shouldShowRationale = ActivityCompat.shouldShowRequestPermissionRationale(
                    activity, 
                    permission
                )
            )
        }
        
        // 检查是否所有权限都已授权
        val allGranted = results.values.all { it.status == PermissionStatus.GRANTED }
        
        return PermissionRequestResult(
            requestCode = requestCode,
            results = results,
            allGranted = allGranted
        )
    }
    
    /**
     * 检查特定权限的状态
     * @param activity 当前活动实例
     * @param permission 权限名称
     * @return 权限结果
     */
    fun checkPermissionStatus(activity: Activity, permission: String): PermissionResult {
        val isGranted = ActivityCompat.checkSelfPermission(
            activity, 
            permission
        ) == PackageManager.PERMISSION_GRANTED
        
        val status = when {
            isGranted -> PermissionStatus.GRANTED
            ActivityCompat.shouldShowRequestPermissionRationale(activity, permission) -> {
                PermissionStatus.DENIED
            }
            else -> PermissionStatus.NOT_REQUESTED
        }
        
        return PermissionResult(
            permission = permission,
            status = status,
            shouldShowRationale = ActivityCompat.shouldShowRequestPermissionRationale(
                activity, 
                permission
            )
        )
    }
    
    /**
     * 检查多个权限的状态
     * @param activity 当前活动实例
     * @param permissions 权限数组
     * @return 权限结果映射
     */
    fun checkMultiplePermissionsStatus(
        activity: Activity, 
        permissions: Array<String>
    ): Map<String, PermissionResult> {
        val results = mutableMapOf<String, PermissionResult>()
        
        // 遍历检查每个权限
        for (permission in permissions) {
            results[permission] = checkPermissionStatus(activity, permission)
        }
        
        return results
    }
    
    /**
     * 获取权限友好名称
     * @param permission 权限名称
     * @return 友好的权限名称
     */
    fun getPermissionFriendlyName(permission: String): String {
        return when (permission) {
            android.Manifest.permission.RECORD_AUDIO -> "录音权限"
            android.Manifest.permission.WRITE_EXTERNAL_STORAGE -> "存储权限"
            android.Manifest.permission.ACCESS_NETWORK_STATE -> "网络状态权限"
            android.Manifest.permission.INTERNET -> "网络访问权限"
            else -> "未知权限"
        }
    }
    
    /**
     * 获取权限描述
     * @param permission 权限名称
     * @return 权限描述
     */
    fun getPermissionDescription(permission: String): String {
        return when (permission) {
            android.Manifest.permission.RECORD_AUDIO -> 
                "应用需要录音权限来录制您的面试回答，这是面试功能的核心需求。"
            android.Manifest.permission.WRITE_EXTERNAL_STORAGE -> 
                "应用需要存储权限来保存录音文件和面试记录到设备存储中。"
            android.Manifest.permission.ACCESS_NETWORK_STATE -> 
                "应用需要网络状态权限来检查网络连接，确保面试数据能够正常传输。"
            android.Manifest.permission.INTERNET -> 
                "应用需要网络访问权限来连接服务器，获取面试题目和上传回答数据。"
            else -> "应用需要此权限来正常运行。"
        }
    }
    
    /**
     * 判断权限是否为关键权限
     * @param permission 权限名称
     * @return true表示是关键权限，false表示非关键权限
     */
    fun isCriticalPermission(permission: String): Boolean {
        return when (permission) {
            android.Manifest.permission.RECORD_AUDIO -> true  // 录音权限是关键权限
            android.Manifest.permission.INTERNET -> true      // 网络权限是关键权限
            else -> false
        }
    }
    
    /**
     * 获取权限被拒绝时的建议操作
     * @param permission 权限名称
     * @param isPermanentlyDenied 是否永久拒绝
     * @return 建议操作描述
     */
    fun getPermissionDeniedSuggestion(permission: String, isPermanentlyDenied: Boolean): String {
        val permissionName = getPermissionFriendlyName(permission)
        
        return if (isPermanentlyDenied) {
            "您已拒绝了${permissionName}，请前往设置 > 应用权限中手动开启此权限，否则相关功能将无法使用。"
        } else {
            "应用需要${permissionName}才能正常工作，请点击允许以继续使用。"
        }
    }
}