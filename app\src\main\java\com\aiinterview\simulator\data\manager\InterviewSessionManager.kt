package com.aiinterview.simulator.data.manager

import com.aiinterview.simulator.data.model.*
import com.aiinterview.simulator.data.repository.InterviewRepository
import com.aiinterview.simulator.data.service.AIQuestionGenerationService
import com.aiinterview.simulator.data.service.TextToSpeechService
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class InterviewSessionManager @Inject constructor(
    private val interviewRepository: InterviewRepository,
    private val aiQuestionService: AIQuestionGenerationService,
    private val ttsService: TextToSpeechService
) {
    
    private val _activeSession = MutableStateFlow<InterviewSession?>(null)
    val activeSession: StateFlow<InterviewSession?> = _activeSession.asStateFlow()
    
    private val _sessionProgress = MutableStateFlow(SessionProgress())
    val sessionProgress: StateFlow<SessionProgress> = _sessionProgress.asStateFlow()
    
    private val _currentAnswer = MutableStateFlow<Answer?>(null)
    val currentAnswer: StateFlow<Answer?> = _currentAnswer.asStateFlow()
    
    private val _technicalIssues = MutableStateFlow<List<TechnicalIssue>>(emptyList())
    val technicalIssues: StateFlow<List<TechnicalIssue>> = _technicalIssues.asStateFlow()
    
    private val _timeWarnings = MutableStateFlow<List<TimeWarning>>(emptyList())
    val timeWarnings: StateFlow<List<TimeWarning>> = _timeWarnings.asStateFlow()
    
    data class SessionProgress(
        val totalQuestions: Int = 0,
        val completedQuestions: Int = 0,
        val currentQuestionIndex: Int = 0,
        val totalTimeSpent: Long = 0L,
        val averageTimePerQuestion: Long = 0L,
        val remainingTime: Long = 0L
    )
    
    data class TechnicalIssue(
        val id: String,
        val type: TechnicalIssueType,
        val description: String,
        val occurredAt: Long,
        val questionId: String?,
        val resolved: Boolean = false,
        val resolution: String? = null
    )
    
    enum class TechnicalIssueType {
        AUDIO_RECORDING_FAILED,
        SPEECH_RECOGNITION_FAILED,
        TTS_PLAYBACK_FAILED,
        NETWORK_CONNECTION_LOST,
        FILE_UPLOAD_FAILED,
        QUESTION_GENERATION_FAILED,
        SYSTEM_ERROR
    }
    
    data class TimeWarning(
        val id: String,
        val type: TimeWarningType,
        val message: String,
        val triggeredAt: Long,
        val acknowledged: Boolean = false
    )
    
    enum class TimeWarningType {
        FIVE_MINUTES_REMAINING,
        ONE_MINUTE_REMAINING,
        THIRTY_SECONDS_REMAINING,
        TIME_UP,
        QUESTION_TIME_LIMIT_APPROACHING,
        QUESTION_TIME_LIMIT_EXCEEDED
    }
    
    /**
     * 创建新的面试会话
     */
    suspend fun createSession(
        userId: String,
        position: Position,
        useAIGeneration: Boolean = true
    ): Result<InterviewSession> {
        return try {
            // 生成面试问题
            val questions = if (useAIGeneration) {
                generateInterviewQuestions(position)
            } else {
                getPresetQuestions(position)
            }
            
            val session = InterviewSession(
                id = UUID.randomUUID().toString(),
                userId = userId,
                positionId = position.id,
                status = InterviewStatus.CREATED,
                startTime = System.currentTimeMillis(),
                endTime = null,
                currentQuestionIndex = 0,
                questions = questions,
                answers = emptyList(),
                evaluation = null
            )
            
            // 保存会话
            interviewRepository.saveInterviewSession(session)
            _activeSession.value = session
            
            // 初始化进度
            updateSessionProgress(session)
            
            Result.success(session)
        } catch (e: Exception) {
            recordTechnicalIssue(
                TechnicalIssueType.SYSTEM_ERROR,
                "创建面试会话失败: ${e.message}"
            )
            Result.failure(e)
        }
    }
    
    /**
     * 开始面试会话
     */
    suspend fun startSession(sessionId: String): Result<InterviewSession> {
        return try {
            val session = _activeSession.value ?: interviewRepository.getInterviewSession(sessionId)
            ?: return Result.failure(Exception("面试会话不存在"))
            
            val startedSession = session.copy(
                status = InterviewStatus.STARTED,
                startTime = System.currentTimeMillis()
            )
            
            interviewRepository.saveInterviewSession(startedSession)
            _activeSession.value = startedSession
            
            updateSessionProgress(startedSession)
            
            Result.success(startedSession)
        } catch (e: Exception) {
            recordTechnicalIssue(
                TechnicalIssueType.SYSTEM_ERROR,
                "开始面试会话失败: ${e.message}"
            )
            Result.failure(e)
        }
    }
    
    /**
     * 进入下一个问题
     */
    suspend fun proceedToNextQuestion(): Result<Question> {
        val session = _activeSession.value ?: return Result.failure(Exception("没有活跃的面试会话"))
        
        return try {
            if (session.currentQuestionIndex >= session.questions.size) {
                // 面试完成
                completeSession()
                return Result.failure(Exception("面试已完成"))
            }
            
            val nextQuestion = session.questions[session.currentQuestionIndex]
            val updatedSession = session.copy(
                status = InterviewStatus.IN_PROGRESS,
                currentQuestionIndex = session.currentQuestionIndex
            )
            
            interviewRepository.saveInterviewSession(updatedSession)
            _activeSession.value = updatedSession
            
            updateSessionProgress(updatedSession)
            
            Result.success(nextQuestion)
        } catch (e: Exception) {
            recordTechnicalIssue(
                TechnicalIssueType.SYSTEM_ERROR,
                "切换到下一个问题失败: ${e.message}",
                session.questions.getOrNull(session.currentQuestionIndex)?.id
            )
            Result.failure(e)
        }
    }
    
    /**
     * 提交问题回答
     */
    suspend fun submitAnswer(
        questionId: String,
        audioFile: java.io.File,
        transcription: String,
        duration: Int
    ): Result<Unit> {
        val session = _activeSession.value ?: return Result.failure(Exception("没有活跃的面试会话"))
        
        return try {
            val answer = Answer(
                questionId = questionId,
                audioUrl = audioFile.absolutePath, // 实际应用中应该上传到服务器
                transcription = transcription,
                duration = duration,
                submittedAt = System.currentTimeMillis()
            )
            
            _currentAnswer.value = answer
            
            val updatedAnswers = session.answers + answer
            val updatedSession = session.copy(
                answers = updatedAnswers,
                currentQuestionIndex = session.currentQuestionIndex + 1
            )
            
            interviewRepository.saveInterviewSession(updatedSession)
            _activeSession.value = updatedSession
            
            updateSessionProgress(updatedSession)
            
            Result.success(Unit)
        } catch (e: Exception) {
            recordTechnicalIssue(
                TechnicalIssueType.SYSTEM_ERROR,
                "提交回答失败: ${e.message}",
                questionId
            )
            Result.failure(e)
        }
    }
    
    /**
     * 处理录音失败
     */
    suspend fun handleRecordingFailure(
        questionId: String,
        errorMessage: String
    ): Result<RecordingRetryOptions> {
        recordTechnicalIssue(
            TechnicalIssueType.AUDIO_RECORDING_FAILED,
            "录音失败: $errorMessage",
            questionId
        )
        
        val retryOptions = RecordingRetryOptions(
            canRetryRecording = true,
            canSkipQuestion = true,
            canUseTextInput = true,
            maxRetryAttempts = 3,
            suggestedActions = listOf(
                "检查麦克风权限",
                "重新开始录音",
                "跳过当前问题",
                "使用文字输入"
            )
        )
        
        return Result.success(retryOptions)
    }
    
    /**
     * 处理语音识别失败
     */
    suspend fun handleSpeechRecognitionFailure(
        questionId: String,
        audioFile: java.io.File,
        errorMessage: String
    ): Result<SpeechRecognitionRetryOptions> {
        recordTechnicalIssue(
            TechnicalIssueType.SPEECH_RECOGNITION_FAILED,
            "语音识别失败: $errorMessage",
            questionId
        )
        
        val retryOptions = SpeechRecognitionRetryOptions(
            canRetryRecognition = true,
            canRetryWithDifferentProvider = true,
            canEditManually = true,
            canReRecord = true,
            availableProviders = listOf("百度", "腾讯", "讯飞"),
            suggestedActions = listOf(
                "尝试不同的识别服务",
                "手动编辑识别结果",
                "重新录制回答",
                "使用离线模式"
            )
        )
        
        return Result.success(retryOptions)
    }
    
    /**
     * 处理网络连接问题
     */
    suspend fun handleNetworkIssue(
        description: String,
        questionId: String? = null
    ): Result<NetworkRecoveryOptions> {
        recordTechnicalIssue(
            TechnicalIssueType.NETWORK_CONNECTION_LOST,
            "网络连接问题: $description",
            questionId
        )
        
        val recoveryOptions = NetworkRecoveryOptions(
            canContinueOffline = true,
            canRetryConnection = true,
            canSaveProgress = true,
            offlineCapabilities = listOf(
                "离线录音",
                "本地保存回答",
                "稍后同步数据"
            ),
            suggestedActions = listOf(
                "检查网络连接",
                "切换到离线模式",
                "保存当前进度",
                "稍后重试上传"
            )
        )
        
        return Result.success(recoveryOptions)
    }
    
    /**
     * 实现时间提醒功能
     */
    fun checkTimeWarnings(remainingTime: Long, questionTimeLimit: Int) {
        val currentTime = System.currentTimeMillis()
        
        // 总体时间提醒
        when {
            remainingTime <= 5 * 60 * 1000L && remainingTime > 4 * 60 * 1000L -> {
                triggerTimeWarning(
                    TimeWarningType.FIVE_MINUTES_REMAINING,
                    "面试时间还剩5分钟，请注意时间安排"
                )
            }
            remainingTime <= 1 * 60 * 1000L && remainingTime > 30 * 1000L -> {
                triggerTimeWarning(
                    TimeWarningType.ONE_MINUTE_REMAINING,
                    "面试时间还剩1分钟，请尽快完成当前问题"
                )
            }
            remainingTime <= 30 * 1000L && remainingTime > 0L -> {
                triggerTimeWarning(
                    TimeWarningType.THIRTY_SECONDS_REMAINING,
                    "面试时间还剩30秒，即将结束"
                )
            }
            remainingTime <= 0L -> {
                triggerTimeWarning(
                    TimeWarningType.TIME_UP,
                    "面试时间已到，系统将自动提交"
                )
            }
        }
        
        // 单题时间提醒
        val questionRemainingTime = questionTimeLimit * 1000L - (currentTime - getQuestionStartTime())
        when {
            questionRemainingTime <= 1 * 60 * 1000L && questionRemainingTime > 30 * 1000L -> {
                triggerTimeWarning(
                    TimeWarningType.QUESTION_TIME_LIMIT_APPROACHING,
                    "当前问题时间还剩1分钟"
                )
            }
            questionRemainingTime <= 0L -> {
                triggerTimeWarning(
                    TimeWarningType.QUESTION_TIME_LIMIT_EXCEEDED,
                    "当前问题时间已到，建议进入下一题"
                )
            }
        }
    }
    
    /**
     * 暂停面试会话
     */
    suspend fun pauseSession(): Result<Unit> {
        val session = _activeSession.value ?: return Result.failure(Exception("没有活跃的面试会话"))
        
        return try {
            val pausedSession = session.copy(status = InterviewStatus.IN_PROGRESS)
            interviewRepository.saveInterviewSession(pausedSession)
            _activeSession.value = pausedSession
            
            // 停止音频播放
            ttsService.stopAudio()
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 恢复面试会话
     */
    suspend fun resumeSession(): Result<Unit> {
        val session = _activeSession.value ?: return Result.failure(Exception("没有活跃的面试会话"))
        
        return try {
            val resumedSession = session.copy(status = InterviewStatus.IN_PROGRESS)
            interviewRepository.saveInterviewSession(resumedSession)
            _activeSession.value = resumedSession
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 完成面试会话
     */
    suspend fun completeSession(): Result<InterviewSession> {
        val session = _activeSession.value ?: return Result.failure(Exception("没有活跃的面试会话"))
        
        return try {
            val completedSession = session.copy(
                status = InterviewStatus.COMPLETED,
                endTime = System.currentTimeMillis()
            )
            
            interviewRepository.saveInterviewSession(completedSession)
            _activeSession.value = completedSession
            
            updateSessionProgress(completedSession)
            
            Result.success(completedSession)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 取消面试会话
     */
    suspend fun cancelSession(): Result<Unit> {
        val session = _activeSession.value ?: return Result.failure(Exception("没有活跃的面试会话"))
        
        return try {
            val cancelledSession = session.copy(
                status = InterviewStatus.CANCELLED,
                endTime = System.currentTimeMillis()
            )
            
            interviewRepository.saveInterviewSession(cancelledSession)
            _activeSession.value = null
            
            // 清理资源
            ttsService.stopAudio()
            ttsService.cleanupTempAudioFiles()
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 生成面试问题
     */
    private suspend fun generateInterviewQuestions(position: Position): List<Question> {
        val questions = mutableListOf<Question>()
        val questionTypes = QuestionType.values().toList().shuffled()
        val questionCount = minOf(position.questionCount, questionTypes.size)
        
        for (i in 0 until questionCount) {
            try {
                val questionType = questionTypes[i]
                val response = aiQuestionService.generateQuestion(
                    position = position,
                    questionType = questionType,
                    difficulty = 3,
                    previousQuestions = questions.map { it.content }
                )
                
                if (response.success && response.question != null) {
                    val generatedQuestion = response.question
                    val question = Question(
                        id = generatedQuestion.id,
                        type = questionType,
                        category = generatedQuestion.category,
                        title = generatedQuestion.title,
                        content = generatedQuestion.content,
                        backgroundInfo = generatedQuestion.backgroundInfo,
                        keyPoints = generatedQuestion.keyPoints,
                        timeLimit = generatedQuestion.timeLimit,
                        difficulty = generatedQuestion.difficulty
                    )
                    questions.add(question)
                } else {
                    // 使用预设问题作为降级
                    questions.add(createFallbackQuestion(questionType, i + 1))
                }
            } catch (e: Exception) {
                recordTechnicalIssue(
                    TechnicalIssueType.QUESTION_GENERATION_FAILED,
                    "生成问题失败: ${e.message}"
                )
                questions.add(createFallbackQuestion(questionTypes[i], i + 1))
            }
        }
        
        return questions
    }
    
    /**
     * 获取预设问题
     */
    private fun getPresetQuestions(position: Position): List<Question> {
        return listOf(
            createFallbackQuestion(QuestionType.COMPREHENSIVE_ANALYSIS, 1),
            createFallbackQuestion(QuestionType.PLANNING_ORGANIZATION, 2),
            createFallbackQuestion(QuestionType.INTERPERSONAL_RELATIONS, 3)
        ).take(position.questionCount)
    }
    
    /**
     * 创建降级问题
     */
    private fun createFallbackQuestion(questionType: QuestionType, index: Int): Question {
        return when (questionType) {
            QuestionType.COMPREHENSIVE_ANALYSIS -> Question(
                id = "fallback_comp_$index",
                type = questionType,
                category = questionType.displayName,
                title = "社会现象分析",
                content = "近年来，网络直播带货成为一种新兴的销售模式，既带来了商业机遇，也出现了一些问题。请你谈谈对网络直播带货现象的看法。",
                timeLimit = 300,
                difficulty = 3
            )
            QuestionType.PLANNING_ORGANIZATION -> Question(
                id = "fallback_plan_$index",
                type = questionType,
                category = questionType.displayName,
                title = "活动组织",
                content = "你所在的单位准备组织一次关爱空巢老人的志愿服务活动，领导让你负责此次活动的策划和组织工作。你会如何开展？",
                timeLimit = 300,
                difficulty = 3
            )
            QuestionType.INTERPERSONAL_RELATIONS -> Question(
                id = "fallback_inter_$index",
                type = questionType,
                category = questionType.displayName,
                title = "人际关系处理",
                content = "在工作中，你和同事小李对某个方案有不同意见，小李坚持自己的观点，不愿意采纳你的建议，这影响了项目进度。你会怎么处理？",
                timeLimit = 300,
                difficulty = 3
            )
            QuestionType.EMERGENCY_RESPONSE -> Question(
                id = "fallback_emerg_$index",
                type = questionType,
                category = questionType.displayName,
                title = "应急处理",
                content = "你正在主持一场重要会议，突然发生停电，会议室一片漆黑，参会人员有些慌乱。作为会议主持人，你会如何应对？",
                timeLimit = 300,
                difficulty = 3
            )
            QuestionType.VERBAL_EXPRESSION -> Question(
                id = "fallback_verbal_$index",
                type = questionType,
                category = questionType.displayName,
                title = "政策解释",
                content = "某项惠民政策发生了调整，一些群众对新政策不理解，甚至有抵触情绪。现在需要你向群众解释这项政策变化，你会怎么说？",
                timeLimit = 300,
                difficulty = 3
            )
        }
    }
    
    /**
     * 更新会话进度
     */
    private fun updateSessionProgress(session: InterviewSession) {
        val totalTime = if (session.endTime != null) {
            session.endTime - session.startTime
        } else {
            System.currentTimeMillis() - session.startTime
        }
        
        val averageTime = if (session.answers.isNotEmpty()) {
            totalTime / session.answers.size
        } else {
            0L
        }
        
        _sessionProgress.value = SessionProgress(
            totalQuestions = session.questions.size,
            completedQuestions = session.answers.size,
            currentQuestionIndex = session.currentQuestionIndex,
            totalTimeSpent = totalTime,
            averageTimePerQuestion = averageTime,
            remainingTime = calculateRemainingTime(session)
        )
    }
    
    /**
     * 计算剩余时间
     */
    private fun calculateRemainingTime(session: InterviewSession): Long {
        // 这里应该根据岗位配置计算总时间限制
        val totalTimeLimit = 30 * 60 * 1000L // 30分钟
        val elapsedTime = System.currentTimeMillis() - session.startTime
        return maxOf(0L, totalTimeLimit - elapsedTime)
    }
    
    /**
     * 记录技术问题
     */
    private fun recordTechnicalIssue(
        type: TechnicalIssueType,
        description: String,
        questionId: String? = null
    ) {
        val issue = TechnicalIssue(
            id = UUID.randomUUID().toString(),
            type = type,
            description = description,
            occurredAt = System.currentTimeMillis(),
            questionId = questionId
        )
        
        val currentIssues = _technicalIssues.value.toMutableList()
        currentIssues.add(issue)
        _technicalIssues.value = currentIssues
    }
    
    /**
     * 触发时间警告
     */
    private fun triggerTimeWarning(type: TimeWarningType, message: String) {
        val warning = TimeWarning(
            id = UUID.randomUUID().toString(),
            type = type,
            message = message,
            triggeredAt = System.currentTimeMillis()
        )
        
        val currentWarnings = _timeWarnings.value.toMutableList()
        currentWarnings.add(warning)
        _timeWarnings.value = currentWarnings
    }
    
    /**
     * 获取问题开始时间
     */
    private fun getQuestionStartTime(): Long {
        // 这里应该记录每个问题的开始时间
        // 为简化，返回当前时间
        return System.currentTimeMillis()
    }
    
    /**
     * 确认时间警告
     */
    fun acknowledgeTimeWarning(warningId: String) {
        val currentWarnings = _timeWarnings.value.toMutableList()
        val index = currentWarnings.indexOfFirst { it.id == warningId }
        if (index != -1) {
            currentWarnings[index] = currentWarnings[index].copy(acknowledged = true)
            _timeWarnings.value = currentWarnings
        }
    }
    
    /**
     * 解决技术问题
     */
    fun resolveTechnicalIssue(issueId: String, resolution: String) {
        val currentIssues = _technicalIssues.value.toMutableList()
        val index = currentIssues.indexOfFirst { it.id == issueId }
        if (index != -1) {
            currentIssues[index] = currentIssues[index].copy(
                resolved = true,
                resolution = resolution
            )
            _technicalIssues.value = currentIssues
        }
    }
    
    /**
     * 获取会话统计信息
     */
    fun getSessionStatistics(): SessionStatistics {
        val session = _activeSession.value
        val progress = _sessionProgress.value
        val issues = _technicalIssues.value
        
        return SessionStatistics(
            sessionId = session?.id,
            totalQuestions = progress.totalQuestions,
            completedQuestions = progress.completedQuestions,
            completionRate = if (progress.totalQuestions > 0) {
                progress.completedQuestions.toFloat() / progress.totalQuestions.toFloat()
            } else 0f,
            totalTimeSpent = progress.totalTimeSpent,
            averageTimePerQuestion = progress.averageTimePerQuestion,
            technicalIssuesCount = issues.size,
            resolvedIssuesCount = issues.count { it.resolved },
            timeWarningsCount = _timeWarnings.value.size
        )
    }
    
    data class RecordingRetryOptions(
        val canRetryRecording: Boolean,
        val canSkipQuestion: Boolean,
        val canUseTextInput: Boolean,
        val maxRetryAttempts: Int,
        val suggestedActions: List<String>
    )
    
    data class SpeechRecognitionRetryOptions(
        val canRetryRecognition: Boolean,
        val canRetryWithDifferentProvider: Boolean,
        val canEditManually: Boolean,
        val canReRecord: Boolean,
        val availableProviders: List<String>,
        val suggestedActions: List<String>
    )
    
    data class NetworkRecoveryOptions(
        val canContinueOffline: Boolean,
        val canRetryConnection: Boolean,
        val canSaveProgress: Boolean,
        val offlineCapabilities: List<String>,
        val suggestedActions: List<String>
    )
    
    data class SessionStatistics(
        val sessionId: String?,
        val totalQuestions: Int,
        val completedQuestions: Int,
        val completionRate: Float,
        val totalTimeSpent: Long,
        val averageTimePerQuestion: Long,
        val technicalIssuesCount: Int,
        val resolvedIssuesCount: Int,
        val timeWarningsCount: Int
    )
}