import java.util.Properties
import java.io.FileInputStream

plugins {
    id("com.android.application")
    id("org.jetbrains.kotlin.android")
    id("kotlin-kapt")
    id("dagger.hilt.android.plugin")
    id("kotlin-parcelize")
}

// 读取密钥库配置文件
val keystorePropertiesFile = rootProject.file("keystore.properties")
val keystoreProperties = Properties()
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(FileInputStream(keystorePropertiesFile))
}

android {
    namespace = "com.aiinterview.simulator"
    compileSdk = 34

    defaultConfig {
        applicationId = "com.aiinterview.simulator"
        minSdk = 24
        targetSdk = 34
        versionCode = 1
        versionName = "1.0.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary = true
        }
        
        // 多渠道打包的默认配置
        manifestPlaceholders["APP_NAME"] = "AI面试模拟器"
        manifestPlaceholders["CHANNEL"] = "official"
    }

    // 签名配置
    signingConfigs {
        create("release") {
            if (keystoreProperties.containsKey("keyAlias")) {
                keyAlias = keystoreProperties["keyAlias"] as String
                keyPassword = keystoreProperties["keyPassword"] as String
                storeFile = file(keystoreProperties["storeFile"] as String)
                storePassword = keystoreProperties["storePassword"] as String
            }
        }
    }

    buildTypes {
        debug {
            // 调试版本配置
            applicationIdSuffix = ".debug"
            versionNameSuffix = "-debug"
            isDebuggable = true
            isMinifyEnabled = false
            isShrinkResources = false
            
            // 启用测试覆盖率
            enableUnitTestCoverage = true
            enableAndroidTestCoverage = true
            
            // 调试版本的构建配置字段
            buildConfigField("String", "BUILD_TYPE", "\"debug\"")
            buildConfigField("boolean", "ENABLE_LOGGING", "true")
        }
        
        release {
            // 发布版本配置
            isMinifyEnabled = true // 启用代码混淆
            isShrinkResources = true // 启用资源压缩
            isDebuggable = false
            
            // ProGuard配置文件
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            
            // 使用发布签名
            signingConfig = signingConfigs.getByName("release")
            
            // 发布版本的构建配置字段
            buildConfigField("String", "BUILD_TYPE", "\"release\"")
            buildConfigField("boolean", "ENABLE_LOGGING", "false")
        }
        
        // 预发布版本（用于内测）
        create("staging") {
            initWith(getByName("release"))
            applicationIdSuffix = ".staging"
            versionNameSuffix = "-staging"
            isDebuggable = true
            
            // 预发布版本的构建配置字段
            buildConfigField("String", "BUILD_TYPE", "\"staging\"")
            buildConfigField("boolean", "ENABLE_LOGGING", "true")
        }
    }
    
    // 多渠道打包配置
    flavorDimensions += "channel"
    productFlavors {
        // 官方渠道
        create("official") {
            dimension = "channel"
            manifestPlaceholders["CHANNEL"] = "official"
            buildConfigField("String", "CHANNEL", "\"official\"")
        }
        
        // 华为应用市场
        create("huawei") {
            dimension = "channel"
            manifestPlaceholders["CHANNEL"] = "huawei"
            buildConfigField("String", "CHANNEL", "\"huawei\"")
        }
        
        // 小米应用商店
        create("xiaomi") {
            dimension = "channel"
            manifestPlaceholders["CHANNEL"] = "xiaomi"
            buildConfigField("String", "CHANNEL", "\"xiaomi\"")
        }
        
        // OPPO软件商店
        create("oppo") {
            dimension = "channel"
            manifestPlaceholders["CHANNEL"] = "oppo"
            buildConfigField("String", "CHANNEL", "\"oppo\"")
        }
        
        // vivo应用商店
        create("vivo") {
            dimension = "channel"
            manifestPlaceholders["CHANNEL"] = "vivo"
            buildConfigField("String", "CHANNEL", "\"vivo\"")
        }
        
        // 应用宝（腾讯）
        create("tencent") {
            dimension = "channel"
            manifestPlaceholders["CHANNEL"] = "tencent"
            buildConfigField("String", "CHANNEL", "\"tencent\"")
        }
        
        // 360手机助手
        create("qihoo360") {
            dimension = "channel"
            manifestPlaceholders["CHANNEL"] = "qihoo360"
            buildConfigField("String", "CHANNEL", "\"qihoo360\"")
        }
        
        // 百度手机助手
        create("baidu") {
            dimension = "channel"
            manifestPlaceholders["CHANNEL"] = "baidu"
            buildConfigField("String", "CHANNEL", "\"baidu\"")
        }
    }
    
    // 测试选项配置
    testOptions {
        unitTests {
            isIncludeAndroidResources = true
            isReturnDefaultValues = true
        }
        // 测试覆盖率配置
        unitTests.all {
            it.useJUnitPlatform()
            it.testLogging {
                events("passed", "skipped", "failed")
            }
        }
    }
    
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    
    kotlinOptions {
        jvmTarget = "1.8"
    }
    
    buildFeatures {
        compose = true
        buildConfig = true // 启用BuildConfig生成
    }
    
    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.4"
    }
    
    packaging {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
        }
    }
}

dependencies {
    // Core Android
    implementation("androidx.core:core-ktx:1.12.0")
    implementation("androidx.lifecycle:lifecycle-runtime-ktx:2.7.0")
    implementation("androidx.activity:activity-compose:1.8.2")
    
    // Jetpack Compose
    implementation(platform("androidx.compose:compose-bom:2023.10.01"))
    implementation("androidx.compose.ui:ui")
    implementation("androidx.compose.ui:ui-graphics")
    implementation("androidx.compose.ui:ui-tooling-preview")
    implementation("androidx.compose.material3:material3")
    implementation("androidx.compose.material:material-icons-extended")
    
    // Navigation
    implementation("androidx.navigation:navigation-compose:2.7.5")
    
    // ViewModel
    implementation("androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0")
    
    // Hilt Dependency Injection
    implementation("com.google.dagger:hilt-android:2.48")
    kapt("com.google.dagger:hilt-compiler:2.48")
    implementation("androidx.hilt:hilt-navigation-compose:1.1.0")
    
    // Room Database
    implementation("androidx.room:room-runtime:2.6.1")
    implementation("androidx.room:room-ktx:2.6.1")
    kapt("androidx.room:room-compiler:2.6.1")
    
    // Retrofit & OkHttp
    implementation("com.squareup.retrofit2:retrofit:2.9.0")
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
    implementation("com.squareup.okhttp3:okhttp:4.12.0")
    implementation("com.squareup.okhttp3:logging-interceptor:4.12.0")
    
    // Coroutines
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")
    
    // DataStore
    implementation("androidx.datastore:datastore-preferences:1.0.0")
    
    // Testing
    testImplementation("junit:junit:4.13.2")
    testImplementation("org.mockito:mockito-core:5.7.0")
    testImplementation("org.mockito:mockito-inline:5.7.0")
    testImplementation("org.mockito.kotlin:mockito-kotlin:5.2.1")
    testImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3")
    testImplementation("androidx.arch.core:core-testing:2.2.0")
    testImplementation("app.cash.turbine:turbine:1.0.0")
    testImplementation("com.google.truth:truth:1.1.4")
    testImplementation("androidx.room:room-testing:2.6.1")
    testImplementation("com.squareup.okhttp3:mockwebserver:4.12.0")
    
    // Android Testing
    androidTestImplementation("androidx.test.ext:junit:1.1.5")
    androidTestImplementation("androidx.test.espresso:espresso-core:3.5.1")
    androidTestImplementation("androidx.test:runner:1.5.2")
    androidTestImplementation("androidx.test:rules:1.5.0")
    androidTestImplementation(platform("androidx.compose:compose-bom:2023.10.01"))
    androidTestImplementation("androidx.compose.ui:ui-test-junit4")
    androidTestImplementation("androidx.navigation:navigation-testing:2.7.5")
    androidTestImplementation("com.google.dagger:hilt-android-testing:2.48")
    kaptAndroidTest("com.google.dagger:hilt-compiler:2.48")
    
    // Debug
    debugImplementation("androidx.compose.ui:ui-tooling")
    debugImplementation("androidx.compose.ui:ui-test-manifest")
}