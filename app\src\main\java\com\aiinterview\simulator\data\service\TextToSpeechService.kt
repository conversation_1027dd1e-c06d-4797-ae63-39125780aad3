package com.aiinterview.simulator.data.service

import android.content.Context
import android.media.MediaPlayer
import android.util.Base64
import com.aiinterview.simulator.data.api.TextToSpeechApi
import com.aiinterview.simulator.data.dto.request.*
import com.aiinterview.simulator.data.dto.response.TTSResponse
import kotlinx.coroutines.delay
import java.io.File
import java.io.FileOutputStream
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class TextToSpeechService @Inject constructor(
    private val context: Context,
    private val ttsApi: TextToSpeechApi
) {
    
    companion object {
        private const val MAX_RETRY_COUNT = 3
        private const val RETRY_DELAY_MS = 1000L
        
        // API配置
        private const val BAIDU_API_KEY = "your_baidu_api_key"
        private const val BAIDU_SECRET_KEY = "your_baidu_secret_key"
        private const val TENCENT_SECRET_ID = "your_tencent_secret_id"
        private const val TENCENT_SECRET_KEY = "your_tencent_secret_key"
        private const val IFLYTEK_APP_ID = "your_iflytek_app_id"
        private const val IFLYTEK_API_KEY = "your_iflytek_api_key"
    }
    
    private var baiduAccessToken: String? = null
    private var tokenExpireTime: Long = 0
    private var mediaPlayer: MediaPlayer? = null
    
    enum class TTSProvider {
        BAIDU, TENCENT, IFLYTEK, AUTO
    }
    
    enum class VoiceType {
        FEMALE_FRIENDLY,    // 亲和女声
        MALE_FRIENDLY,      // 亲和男声
        MALE_MATURE,        // 成熟男声
        FEMALE_WARM,        // 温暖女声
        MALE_EMOTIONAL,     // 情感男声
        FEMALE_EMOTIONAL    // 情感女声
    }
    
    /**
     * 文本转语音
     */
    suspend fun synthesizeText(
        text: String,
        voiceType: VoiceType = VoiceType.FEMALE_FRIENDLY,
        speed: Int = 5,
        provider: TTSProvider = TTSProvider.AUTO
    ): TTSResponse {
        var lastError: Exception? = null
        
        val providers = when (provider) {
            TTSProvider.BAIDU -> listOf(TTSProvider.BAIDU)
            TTSProvider.TENCENT -> listOf(TTSProvider.TENCENT)
            TTSProvider.IFLYTEK -> listOf(TTSProvider.IFLYTEK)
            TTSProvider.AUTO -> listOf(TTSProvider.BAIDU, TTSProvider.TENCENT, TTSProvider.IFLYTEK)
        }
        
        for (currentProvider in providers) {
            for (attempt in 1..MAX_RETRY_COUNT) {
                try {
                    val result = when (currentProvider) {
                        TTSProvider.BAIDU -> synthesizeWithBaidu(text, voiceType, speed)
                        TTSProvider.TENCENT -> synthesizeWithTencent(text, voiceType, speed)
                        TTSProvider.IFLYTEK -> synthesizeWithiFlytek(text, voiceType, speed)
                        TTSProvider.AUTO -> throw IllegalStateException("AUTO should not reach here")
                    }
                    
                    if (result.success) {
                        return result
                    }
                } catch (e: Exception) {
                    lastError = e
                    if (attempt < MAX_RETRY_COUNT) {
                        delay(RETRY_DELAY_MS * attempt)
                    }
                }
            }
        }
        
        // 所有尝试都失败了
        return TTSResponse(
            success = false,
            errorMessage = lastError?.message ?: "语音合成失败",
            provider = provider.name.lowercase()
        )
    }
    
    /**
     * 使用百度TTS合成语音
     */
    private suspend fun synthesizeWithBaidu(
        text: String,
        voiceType: VoiceType,
        speed: Int
    ): TTSResponse {
        try {
            val accessToken = getBaiduAccessToken()
            val voiceId = mapVoiceTypeToBaidu(voiceType)
            
            val request = BaiduTTSRequest(
                tex = text,
                tok = accessToken,
                cuid = "ai_interview_${System.currentTimeMillis()}",
                spd = speed,
                per = voiceId,
                aue = 3 // MP3格式
            )
            
            val response = ttsApi.synthesizeWithBaidu(
                authorization = "Bearer $accessToken",
                request = TTSRequest(
                    text = text,
                    voice = voiceType.name,
                    speed = speed
                )
            )
            
            return parseBaiduResponse(response.data)
            
        } catch (e: Exception) {
            return TTSResponse(
                success = false,
                errorMessage = "百度TTS合成失败: ${e.message}",
                provider = "baidu"
            )
        }
    }
    
    /**
     * 使用腾讯云TTS合成语音
     */
    private suspend fun synthesizeWithTencent(
        text: String,
        voiceType: VoiceType,
        speed: Int
    ): TTSResponse {
        try {
            val voiceId = mapVoiceTypeToTencent(voiceType)
            val sessionId = UUID.randomUUID().toString()
            
            val request = TencentTTSRequest(
                Text = text,
                SessionId = sessionId,
                VoiceType = voiceId,
                Speed = (speed - 5) * 0.5, // 转换为腾讯云的速度范围
                Volume = 5.0,
                SampleRate = 16000,
                Codec = "mp3"
            )
            
            val response = ttsApi.synthesizeWithTencent(
                authorization = generateTencentAuth(),
                request = TTSRequest(
                    text = text,
                    voice = voiceType.name,
                    speed = speed
                )
            )
            
            return parseTencentResponse(response.data)
            
        } catch (e: Exception) {
            return TTSResponse(
                success = false,
                errorMessage = "腾讯云TTS合成失败: ${e.message}",
                provider = "tencent"
            )
        }
    }
    
    /**
     * 使用讯飞TTS合成语音
     */
    private suspend fun synthesizeWithiFlytek(
        text: String,
        voiceType: VoiceType,
        speed: Int
    ): TTSResponse {
        try {
            val voiceName = mapVoiceTypeToiFlytek(voiceType)
            val encodedText = Base64.encodeToString(text.toByteArray(), Base64.NO_WRAP)
            
            val request = iFlyTekTTSRequest(
                common = iFlyTekCommon(app_id = IFLYTEK_APP_ID),
                business = iFlyTekBusiness(
                    vcn = voiceName,
                    speed = speed * 10, // 转换为讯飞的速度范围
                    volume = 50,
                    pitch = 50,
                    aue = "mp3"
                ),
                data = iFlyTekData(text = encodedText)
            )
            
            val response = ttsApi.synthesizeWithiFlytek(
                authorization = generateiFlyTekAuth(),
                request = TTSRequest(
                    text = text,
                    voice = voiceType.name,
                    speed = speed
                )
            )
            
            return parseiFlyTekResponse(response.data)
            
        } catch (e: Exception) {
            return TTSResponse(
                success = false,
                errorMessage = "讯飞TTS合成失败: ${e.message}",
                provider = "iflytek"
            )
        }
    }
    
    /**
     * 播放合成的语音
     */
    suspend fun playAudio(ttsResponse: TTSResponse): Boolean {
        return try {
            stopAudio() // 停止当前播放
            
            val audioFile = when {
                ttsResponse.audioUrl != null -> {
                    // 如果有URL，直接使用
                    null // MediaPlayer可以直接播放URL
                }
                ttsResponse.audioData != null -> {
                    // 如果有音频数据，保存到临时文件
                    saveAudioDataToFile(ttsResponse.audioData)
                }
                else -> {
                    throw IllegalArgumentException("没有可播放的音频数据")
                }
            }
            
            mediaPlayer = MediaPlayer().apply {
                if (ttsResponse.audioUrl != null) {
                    setDataSource(ttsResponse.audioUrl)
                } else if (audioFile != null) {
                    setDataSource(audioFile.absolutePath)
                }
                
                prepareAsync()
                setOnPreparedListener { start() }
                setOnCompletionListener { 
                    release()
                    mediaPlayer = null
                    // 清理临时文件
                    audioFile?.delete()
                }
                setOnErrorListener { _, _, _ ->
                    release()
                    mediaPlayer = null
                    audioFile?.delete()
                    false
                }
            }
            
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 停止音频播放
     */
    fun stopAudio() {
        mediaPlayer?.apply {
            if (isPlaying) {
                stop()
            }
            release()
        }
        mediaPlayer = null
    }
    
    /**
     * 检查是否正在播放
     */
    fun isPlaying(): Boolean {
        return mediaPlayer?.isPlaying == true
    }
    
    /**
     * 保存音频数据到临时文件
     */
    private fun saveAudioDataToFile(audioData: ByteArray): File {
        val tempDir = File(context.cacheDir, "tts_audio")
        if (!tempDir.exists()) {
            tempDir.mkdirs()
        }
        
        val audioFile = File(tempDir, "tts_${System.currentTimeMillis()}.mp3")
        FileOutputStream(audioFile).use { fos ->
            fos.write(audioData)
        }
        
        return audioFile
    }
    
    /**
     * 映射语音类型到百度TTS
     */
    private fun mapVoiceTypeToBaidu(voiceType: VoiceType): Int {
        return when (voiceType) {
            VoiceType.FEMALE_FRIENDLY -> 0 // 度小美
            VoiceType.MALE_FRIENDLY -> 1 // 度小宇
            VoiceType.MALE_MATURE -> 1 // 度小宇
            VoiceType.FEMALE_WARM -> 0 // 度小美
            VoiceType.MALE_EMOTIONAL -> 3 // 度逍遥
            VoiceType.FEMALE_EMOTIONAL -> 4 // 度丫丫
        }
    }
    
    /**
     * 映射语音类型到腾讯云TTS
     */
    private fun mapVoiceTypeToTencent(voiceType: VoiceType): Int {
        return when (voiceType) {
            VoiceType.FEMALE_FRIENDLY -> 0 // 亲和女声
            VoiceType.MALE_FRIENDLY -> 1 // 亲和男声
            VoiceType.MALE_MATURE -> 2 // 成熟男声
            VoiceType.FEMALE_WARM -> 4 // 温暖女声
            VoiceType.MALE_EMOTIONAL -> 5 // 情感男声
            VoiceType.FEMALE_EMOTIONAL -> 6 // 情感女声
        }
    }
    
    /**
     * 映射语音类型到讯飞TTS
     */
    private fun mapVoiceTypeToiFlytek(voiceType: VoiceType): String {
        return when (voiceType) {
            VoiceType.FEMALE_FRIENDLY -> "xiaoyan" // 亲和女声
            VoiceType.MALE_FRIENDLY -> "aisjiuxu" // 亲和男声
            VoiceType.MALE_MATURE -> "aisjiuxu" // 亲和男声
            VoiceType.FEMALE_WARM -> "aisxping" // 亲和女声
            VoiceType.MALE_EMOTIONAL -> "aisjiuxu" // 亲和男声
            VoiceType.FEMALE_EMOTIONAL -> "xiaoyan" // 亲和女声
        }
    }
    
    /**
     * 解析百度TTS响应
     */
    private fun parseBaiduResponse(response: Any?): TTSResponse {
        // 这里需要根据实际的百度TTS API响应格式进行解析
        // 由于API格式可能变化，这里提供一个通用的解析逻辑
        return try {
            // 假设响应包含base64编码的音频数据
            val audioData = Base64.decode(response.toString(), Base64.DEFAULT)
            
            TTSResponse(
                success = true,
                audioData = audioData,
                duration = estimateAudioDuration(audioData),
                format = "mp3",
                sampleRate = 16000,
                provider = "baidu",
                requestId = UUID.randomUUID().toString()
            )
        } catch (e: Exception) {
            TTSResponse(
                success = false,
                errorMessage = "解析百度TTS响应失败: ${e.message}",
                provider = "baidu"
            )
        }
    }
    
    /**
     * 解析腾讯云TTS响应
     */
    private fun parseTencentResponse(response: Any?): TTSResponse {
        return try {
            // 假设响应包含base64编码的音频数据
            val audioData = Base64.decode(response.toString(), Base64.DEFAULT)
            
            TTSResponse(
                success = true,
                audioData = audioData,
                duration = estimateAudioDuration(audioData),
                format = "mp3",
                sampleRate = 16000,
                provider = "tencent",
                requestId = UUID.randomUUID().toString()
            )
        } catch (e: Exception) {
            TTSResponse(
                success = false,
                errorMessage = "解析腾讯云TTS响应失败: ${e.message}",
                provider = "tencent"
            )
        }
    }
    
    /**
     * 解析讯飞TTS响应
     */
    private fun parseiFlyTekResponse(response: Any?): TTSResponse {
        return try {
            // 假设响应包含base64编码的音频数据
            val audioData = Base64.decode(response.toString(), Base64.DEFAULT)
            
            TTSResponse(
                success = true,
                audioData = audioData,
                duration = estimateAudioDuration(audioData),
                format = "mp3",
                sampleRate = 16000,
                provider = "iflytek",
                requestId = UUID.randomUUID().toString()
            )
        } catch (e: Exception) {
            TTSResponse(
                success = false,
                errorMessage = "解析讯飞TTS响应失败: ${e.message}",
                provider = "iflytek"
            )
        }
    }
    
    /**
     * 估算音频时长
     */
    private fun estimateAudioDuration(audioData: ByteArray): Long {
        // 这是一个简单的估算，实际应该根据音频格式和采样率计算
        // MP3格式大约每秒1KB数据（16kHz采样率）
        return (audioData.size / 1024L) * 1000L
    }
    
    /**
     * 获取百度访问令牌
     */
    private suspend fun getBaiduAccessToken(): String {
        if (baiduAccessToken != null && System.currentTimeMillis() < tokenExpireTime) {
            return baiduAccessToken!!
        }
        
        try {
            // 这里应该调用百度的token获取API
            // 由于具体实现依赖于实际的API，这里提供一个模拟实现
            baiduAccessToken = "mock_baidu_access_token"
            tokenExpireTime = System.currentTimeMillis() + 30 * 24 * 60 * 60 * 1000L // 30天
            return baiduAccessToken!!
        } catch (e: Exception) {
            throw Exception("获取百度TTS访问令牌失败: ${e.message}")
        }
    }
    
    /**
     * 生成腾讯云认证信息
     */
    private fun generateTencentAuth(): String {
        // 这里应该实现腾讯云的签名算法
        // 由于具体实现比较复杂，这里提供一个模拟实现
        return "TC3-HMAC-SHA256 Credential=$TENCENT_SECRET_ID/..."
    }
    
    /**
     * 生成讯飞认证信息
     */
    private fun generateiFlyTekAuth(): String {
        // 这里应该实现讯飞的签名算法
        // 由于具体实现比较复杂，这里提供一个模拟实现
        return "Bearer $IFLYTEK_API_KEY"
    }
    
    /**
     * 清理临时音频文件
     */
    fun cleanupTempAudioFiles() {
        val tempDir = File(context.cacheDir, "tts_audio")
        if (tempDir.exists()) {
            tempDir.listFiles()?.forEach { file ->
                if (file.lastModified() < System.currentTimeMillis() - 24 * 60 * 60 * 1000L) {
                    file.delete()
                }
            }
        }
    }
}