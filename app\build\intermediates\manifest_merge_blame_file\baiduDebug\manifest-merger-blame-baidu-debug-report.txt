1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.aiinterview.simulator.debug"
4    android:versionCode="1"
5    android:versionName="1.0.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:5:5-67
11-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:6:5-79
12-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.RECORD_AUDIO" />
13-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:7:5-71
13-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:7:22-68
14    <uses-permission
14-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:8:5-9:38
15        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
15-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:8:22-78
16        android:maxSdkVersion="28" />
16-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:9:9-35
17
18    <permission
18-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
19        android:name="com.aiinterview.simulator.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
19-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
20        android:protectionLevel="signature" />
20-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
21
22    <uses-permission android:name="com.aiinterview.simulator.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
22-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
22-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
23
24    <application
24-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:11:5-32:19
25        android:name="com.aiinterview.simulator.AIInterviewApplication"
25-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:12:9-47
26        android:allowBackup="true"
26-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:13:9-35
27        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
27-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
28        android:dataExtractionRules="@xml/data_extraction_rules"
28-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:14:9-65
29        android:debuggable="true"
30        android:extractNativeLibs="false"
31        android:fullBackupContent="@xml/backup_rules"
31-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:15:9-54
32        android:icon="@android:drawable/sym_def_app_icon"
32-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:16:9-58
33        android:label="@string/app_name"
33-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:17:9-41
34        android:roundIcon="@android:drawable/sym_def_app_icon"
34-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:18:9-63
35        android:supportsRtl="true"
35-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:19:9-35
36        android:testOnly="true"
37        android:theme="@style/Theme.AIInterviewSimulator" >
37-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:20:9-58
38        <activity
38-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:23:9-31:20
39            android:name="com.aiinterview.simulator.presentation.MainActivity"
39-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:24:13-54
40            android:exported="true"
40-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:25:13-36
41            android:theme="@style/Theme.AIInterviewSimulator" >
41-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:26:13-62
42            <intent-filter>
42-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:27:13-30:29
43                <action android:name="android.intent.action.MAIN" />
43-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:28:17-69
43-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:28:25-66
44
45                <category android:name="android.intent.category.LAUNCHER" />
45-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:29:17-77
45-->E:\health-report-tracker\app\src\main\AndroidManifest.xml:29:27-74
46            </intent-filter>
47        </activity>
48        <activity
48-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2546df139fad0738ad0957958c32c5fd\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
49            android:name="androidx.activity.ComponentActivity"
49-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2546df139fad0738ad0957958c32c5fd\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
50            android:exported="true" />
50-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2546df139fad0738ad0957958c32c5fd\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
51        <activity
51-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a837338b3977ec042f451025b5b4e064\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
52            android:name="androidx.compose.ui.tooling.PreviewActivity"
52-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a837338b3977ec042f451025b5b4e064\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
53            android:exported="true" />
53-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a837338b3977ec042f451025b5b4e064\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
54
55        <provider
55-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
56            android:name="androidx.startup.InitializationProvider"
56-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
57            android:authorities="com.aiinterview.simulator.debug.androidx-startup"
57-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
58            android:exported="false" >
58-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
59            <meta-data
59-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
60                android:name="androidx.emoji2.text.EmojiCompatInitializer"
60-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
61                android:value="androidx.startup" />
61-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
62            <meta-data
62-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9432865878371e9545627d83de8d1340\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
63                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
63-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9432865878371e9545627d83de8d1340\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
64                android:value="androidx.startup" />
64-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9432865878371e9545627d83de8d1340\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
65            <meta-data
65-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
66                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
66-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
67                android:value="androidx.startup" />
67-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
68        </provider>
69
70        <service
70-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6a89eba57c9d14e67fa63b3b7fec273e\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
71            android:name="androidx.room.MultiInstanceInvalidationService"
71-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6a89eba57c9d14e67fa63b3b7fec273e\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
72            android:directBootAware="true"
72-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6a89eba57c9d14e67fa63b3b7fec273e\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
73            android:exported="false" />
73-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6a89eba57c9d14e67fa63b3b7fec273e\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
74
75        <receiver
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
76            android:name="androidx.profileinstaller.ProfileInstallReceiver"
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
77            android:directBootAware="false"
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
78            android:enabled="true"
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
79            android:exported="true"
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
80            android:permission="android.permission.DUMP" >
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
81            <intent-filter>
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
82                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
83            </intent-filter>
84            <intent-filter>
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
85                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
85-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
85-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
86            </intent-filter>
87            <intent-filter>
87-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
88                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
88-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
88-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
89            </intent-filter>
90            <intent-filter>
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
91                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
91-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
91-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
92            </intent-filter>
93        </receiver>
94    </application>
95
96</manifest>
