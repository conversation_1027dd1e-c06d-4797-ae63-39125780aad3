{"logs": [{"outputFile": "com.aiinterview.simulator.app-mergeOfficialDebugResources-55:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fd044ab7a523edb1345beec00f65097\\transformed\\core-1.12.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "570,668,770,869,971,1075,1179,6544", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "663,765,864,966,1070,1174,1292,6640"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d78fa9dcb2040782f62e575b1696aadd\\transformed\\ui-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,392,492,579,658,750,842,929,1000,1068,1149,1234,1310,1388,1457", "endColumns": "98,87,99,99,86,78,91,91,86,70,67,80,84,75,77,68,121", "endOffsets": "199,287,387,487,574,653,745,837,924,995,1063,1144,1229,1305,1383,1452,1574"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1297,1396,5196,5296,5479,5643,5722,5814,5906,5993,6064,6132,6213,6385,6748,6826,6895", "endColumns": "98,87,99,99,86,78,91,91,86,70,67,80,84,75,77,68,121", "endOffsets": "1391,1479,5291,5391,5561,5717,5809,5901,5988,6059,6127,6208,6293,6456,6821,6890,7012"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\73f51c4b00c3d8b5bd0709c291d17c64\\transformed\\material3-1.1.2\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,285,403,520,595,685,794,934,1049,1192,1272,1369,1466,1563,1680,1803,1905,2051,2193,2319,2507,2629,2743,2863,2993,3091,3192,3312,3433,3531,3634,3735,3875,4023,4128,4232,4315,4392,4479,4562,4665,4741,4822,4920,5028,5122,5218,5302,5414,5511,5609,5737,5813,5919", "endColumns": "115,113,117,116,74,89,108,139,114,142,79,96,96,96,116,122,101,145,141,125,187,121,113,119,129,97,100,119,120,97,102,100,139,147,104,103,82,76,86,82,102,75,80,97,107,93,95,83,111,96,97,127,75,105,93", "endOffsets": "166,280,398,515,590,680,789,929,1044,1187,1267,1364,1461,1558,1675,1798,1900,2046,2188,2314,2502,2624,2738,2858,2988,3086,3187,3307,3428,3526,3629,3730,3870,4018,4123,4227,4310,4387,4474,4557,4660,4736,4817,4915,5023,5117,5213,5297,5409,5506,5604,5732,5808,5914,6008"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,335,453,1484,1559,1649,1758,1898,2013,2156,2236,2333,2430,2527,2644,2767,2869,3015,3157,3283,3471,3593,3707,3827,3957,4055,4156,4276,4397,4495,4598,4699,4839,4987,5092,5396,5566,6298,6461,6645,7017,7093,7174,7272,7380,7474,7570,7654,7766,7863,7961,8089,8165,8271", "endColumns": "115,113,117,116,74,89,108,139,114,142,79,96,96,96,116,122,101,145,141,125,187,121,113,119,129,97,100,119,120,97,102,100,139,147,104,103,82,76,86,82,102,75,80,97,107,93,95,83,111,96,97,127,75,105,93", "endOffsets": "216,330,448,565,1554,1644,1753,1893,2008,2151,2231,2328,2425,2522,2639,2762,2864,3010,3152,3278,3466,3588,3702,3822,3952,4050,4151,4271,4392,4490,4593,4694,4834,4982,5087,5191,5474,5638,6380,6539,6743,7088,7169,7267,7375,7469,7565,7649,7761,7858,7956,8084,8160,8266,8360"}}]}]}