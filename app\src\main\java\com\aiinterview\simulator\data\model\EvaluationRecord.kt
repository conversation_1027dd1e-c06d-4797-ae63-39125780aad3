package com.aiinterview.simulator.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 评价记录实体类 - 用于Room数据库存储
 */
@Entity(tableName = "evaluation_records")
data class EvaluationRecord(
    @PrimaryKey
    val id: String,
    val sessionId: String,
    val userId: String,
    val positionId: String,
    val positionName: String,
    val overallScore: Double,
    val dimensionScoresJson: String, // JSON格式存储维度评分
    val feedback: String,
    val suggestionsJson: String, // JSON格式存储建议列表
    val createdAt: Long,
    val isSynced: Boolean = false, // 是否已同步到云端
    val syncedAt: Long? = null // 同步时间
)

/**
 * 评价记录业务模型
 */
data class EvaluationRecordModel(
    val id: String,
    val sessionId: String,
    val userId: String,
    val positionId: String,
    val positionName: String,
    val overallScore: Double,
    val dimensionScores: Map<String, DimensionScore>,
    val feedback: String,
    val suggestions: List<String>,
    val createdAt: Long,
    val isSynced: Boolean = false,
    val syncedAt: Long? = null
)

/**
 * 评价记录摘要 - 用于列表显示
 */
data class EvaluationRecordSummary(
    val id: String,
    val sessionId: String,
    val positionName: String,
    val overallScore: Double,
    val createdAt: Long,
    val isSynced: Boolean
)