package com.aiinterview.simulator.util

import android.content.Context
import androidx.test.platform.app.InstrumentationRegistry
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

/**
 * UI测试报告生成器
 * 用于生成详细的UI测试报告
 */
object UITestReportGenerator {
    
    private val context: Context = InstrumentationRegistry.getInstrumentation().targetContext
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    
    /**
     * 测试结果数据类
     */
    data class TestResult(
        val testName: String,
        val className: String,
        val status: TestStatus,
        val duration: Long,
        val errorMessage: String? = null,
        val screenshots: List<String> = emptyList(),
        val timestamp: Long = System.currentTimeMillis()
    )
    
    /**
     * 测试状态枚举
     */
    enum class TestStatus {
        PASSED, FAILED, SKIPPED
    }
    
    /**
     * 测试套件报告数据类
     */
    data class TestSuiteReport(
        val suiteName: String,
        val totalTests: Int,
        val passedTests: Int,
        val failedTests: Int,
        val skippedTests: Int,
        val totalDuration: Long,
        val testResults: List<TestResult>,
        val generatedAt: Long = System.currentTimeMillis()
    ) {
        val successRate: Double
            get() = if (totalTests > 0) (passedTests.toDouble() / totalTests) * 100 else 0.0
    }
    
    /**
     * 生成HTML格式的测试报告
     */
    fun generateHtmlReport(report: TestSuiteReport): String {
        return buildString {
            appendLine("<!DOCTYPE html>")
            appendLine("<html lang='zh-CN'>")
            appendLine("<head>")
            appendLine("    <meta charset='UTF-8'>")
            appendLine("    <meta name='viewport' content='width=device-width, initial-scale=1.0'>")
            appendLine("    <title>AI面试模拟器 - UI测试报告</title>")
            appendLine("    <style>")
            appendLine(getReportStyles())
            appendLine("    </style>")
            appendLine("</head>")
            appendLine("<body>")
            
            // 报告头部
            appendLine("    <div class='header'>")
            appendLine("        <h1>AI面试模拟器 - UI测试报告</h1>")
            appendLine("        <div class='meta-info'>")
            appendLine("            <p>测试套件: ${report.suiteName}</p>")
            appendLine("            <p>生成时间: ${dateFormat.format(Date(report.generatedAt))}</p>")
            appendLine("            <p>测试设备: ${android.os.Build.MODEL} (Android ${android.os.Build.VERSION.RELEASE})</p>")
            appendLine("        </div>")
            appendLine("    </div>")
            
            // 测试摘要
            appendLine("    <div class='summary'>")
            appendLine("        <h2>测试摘要</h2>")
            appendLine("        <div class='summary-cards'>")
            appendLine("            <div class='card total'>")
            appendLine("                <h3>总测试数</h3>")
            appendLine("                <p class='number'>${report.totalTests}</p>")
            appendLine("            </div>")
            appendLine("            <div class='card passed'>")
            appendLine("                <h3>通过</h3>")
            appendLine("                <p class='number'>${report.passedTests}</p>")
            appendLine("            </div>")
            appendLine("            <div class='card failed'>")
            appendLine("                <h3>失败</h3>")
            appendLine("                <p class='number'>${report.failedTests}</p>")
            appendLine("            </div>")
            appendLine("            <div class='card skipped'>")
            appendLine("                <h3>跳过</h3>")
            appendLine("                <p class='number'>${report.skippedTests}</p>")
            appendLine("            </div>")
            appendLine("        </div>")
            appendLine("        <div class='success-rate'>")
            appendLine("            <h3>成功率: ${String.format("%.2f", report.successRate)}%</h3>")
            appendLine("            <div class='progress-bar'>")
            appendLine("                <div class='progress-fill' style='width: ${report.successRate}%;'></div>")
            appendLine("            </div>")
            appendLine("        </div>")
            appendLine("        <p>总耗时: ${formatDuration(report.totalDuration)}</p>")
            appendLine("    </div>")
            
            // 测试详情
            appendLine("    <div class='test-details'>")
            appendLine("        <h2>测试详情</h2>")
            appendLine("        <div class='test-list'>")
            
            report.testResults.forEach { testResult ->
                appendLine("            <div class='test-item ${testResult.status.name.lowercase()}'>")
                appendLine("                <div class='test-header'>")
                appendLine("                    <h4>${testResult.testName}</h4>")
                appendLine("                    <span class='status ${testResult.status.name.lowercase()}'>${getStatusText(testResult.status)}</span>")
                appendLine("                </div>")
                appendLine("                <div class='test-info'>")
                appendLine("                    <p>类名: ${testResult.className}</p>")
                appendLine("                    <p>耗时: ${formatDuration(testResult.duration)}</p>")
                appendLine("                    <p>执行时间: ${dateFormat.format(Date(testResult.timestamp))}</p>")
                appendLine("                </div>")
                
                if (testResult.errorMessage != null) {
                    appendLine("                <div class='error-message'>")
                    appendLine("                    <h5>错误信息:</h5>")
                    appendLine("                    <pre>${testResult.errorMessage}</pre>")
                    appendLine("                </div>")
                }
                
                if (testResult.screenshots.isNotEmpty()) {
                    appendLine("                <div class='screenshots'>")
                    appendLine("                    <h5>截图:</h5>")
                    testResult.screenshots.forEach { screenshot ->
                        appendLine("                    <img src='$screenshot' alt='测试截图' class='screenshot'>")
                    }
                    appendLine("                </div>")
                }
                
                appendLine("            </div>")
            }
            
            appendLine("        </div>")
            appendLine("    </div>")
            
            // 测试统计图表
            appendLine("    <div class='charts'>")
            appendLine("        <h2>测试统计</h2>")
            appendLine("        <div class='chart-container'>")
            appendLine("            <canvas id='testChart' width='400' height='200'></canvas>")
            appendLine("        </div>")
            appendLine("    </div>")
            
            // JavaScript for charts
            appendLine("    <script>")
            appendLine("        // 这里可以添加图表绘制代码")
            appendLine("        console.log('测试报告已生成');")
            appendLine("    </script>")
            
            appendLine("</body>")
            appendLine("</html>")
        }
    }
    
    /**
     * 获取报告样式
     */
    private fun getReportStyles(): String {
        return """
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
                color: #333;
            }
            
            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                border-radius: 10px;
                margin-bottom: 20px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            }
            
            .header h1 {
                margin: 0 0 15px 0;
                font-size: 2.5em;
            }
            
            .meta-info p {
                margin: 5px 0;
                opacity: 0.9;
            }
            
            .summary {
                background: white;
                padding: 25px;
                border-radius: 10px;
                margin-bottom: 20px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            
            .summary-cards {
                display: flex;
                gap: 20px;
                margin: 20px 0;
                flex-wrap: wrap;
            }
            
            .card {
                flex: 1;
                min-width: 150px;
                padding: 20px;
                border-radius: 8px;
                text-align: center;
                color: white;
            }
            
            .card.total { background: #3498db; }
            .card.passed { background: #2ecc71; }
            .card.failed { background: #e74c3c; }
            .card.skipped { background: #f39c12; }
            
            .card h3 {
                margin: 0 0 10px 0;
                font-size: 1em;
                opacity: 0.9;
            }
            
            .card .number {
                margin: 0;
                font-size: 2.5em;
                font-weight: bold;
            }
            
            .success-rate {
                margin: 20px 0;
            }
            
            .progress-bar {
                width: 100%;
                height: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                overflow: hidden;
                margin: 10px 0;
            }
            
            .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #2ecc71, #27ae60);
                transition: width 0.3s ease;
            }
            
            .test-details {
                background: white;
                padding: 25px;
                border-radius: 10px;
                margin-bottom: 20px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            
            .test-item {
                border: 1px solid #ddd;
                border-radius: 8px;
                margin: 15px 0;
                padding: 20px;
                transition: all 0.3s ease;
            }
            
            .test-item:hover {
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            }
            
            .test-item.passed {
                border-left: 5px solid #2ecc71;
                background-color: #f8fff9;
            }
            
            .test-item.failed {
                border-left: 5px solid #e74c3c;
                background-color: #fff8f8;
            }
            
            .test-item.skipped {
                border-left: 5px solid #f39c12;
                background-color: #fffbf0;
            }
            
            .test-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;
            }
            
            .test-header h4 {
                margin: 0;
                color: #2c3e50;
            }
            
            .status {
                padding: 5px 15px;
                border-radius: 20px;
                font-size: 0.9em;
                font-weight: bold;
                text-transform: uppercase;
            }
            
            .status.passed {
                background-color: #2ecc71;
                color: white;
            }
            
            .status.failed {
                background-color: #e74c3c;
                color: white;
            }
            
            .status.skipped {
                background-color: #f39c12;
                color: white;
            }
            
            .test-info p {
                margin: 5px 0;
                color: #7f8c8d;
                font-size: 0.9em;
            }
            
            .error-message {
                margin-top: 15px;
                padding: 15px;
                background-color: #fdf2f2;
                border-radius: 5px;
                border: 1px solid #fecaca;
            }
            
            .error-message h5 {
                margin: 0 0 10px 0;
                color: #dc2626;
            }
            
            .error-message pre {
                margin: 0;
                white-space: pre-wrap;
                word-wrap: break-word;
                font-size: 0.9em;
                color: #991b1b;
            }
            
            .screenshots {
                margin-top: 15px;
            }
            
            .screenshots h5 {
                margin: 0 0 10px 0;
                color: #374151;
            }
            
            .screenshot {
                max-width: 300px;
                max-height: 200px;
                margin: 5px;
                border: 1px solid #d1d5db;
                border-radius: 5px;
                cursor: pointer;
                transition: transform 0.2s ease;
            }
            
            .screenshot:hover {
                transform: scale(1.05);
            }
            
            .charts {
                background: white;
                padding: 25px;
                border-radius: 10px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            
            .chart-container {
                display: flex;
                justify-content: center;
                margin-top: 20px;
            }
            
            @media (max-width: 768px) {
                .summary-cards {
                    flex-direction: column;
                }
                
                .test-header {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 10px;
                }
                
                .screenshot {
                    max-width: 100%;
                }
            }
        """.trimIndent()
    }
    
    /**
     * 格式化持续时间
     */
    private fun formatDuration(millis: Long): String {
        val seconds = millis / 1000
        val minutes = seconds / 60
        val remainingSeconds = seconds % 60
        
        return when {
            minutes > 0 -> "${minutes}分${remainingSeconds}秒"
            seconds > 0 -> "${seconds}秒"
            else -> "${millis}毫秒"
        }
    }
    
    /**
     * 获取状态文本
     */
    private fun getStatusText(status: TestStatus): String {
        return when (status) {
            TestStatus.PASSED -> "通过"
            TestStatus.FAILED -> "失败"
            TestStatus.SKIPPED -> "跳过"
        }
    }
    
    /**
     * 保存报告到文件
     */
    fun saveReportToFile(report: TestSuiteReport, fileName: String = "ui_test_report.html"): File {
        val htmlContent = generateHtmlReport(report)
        val reportsDir = File(context.getExternalFilesDir(null), "test_reports")
        if (!reportsDir.exists()) {
            reportsDir.mkdirs()
        }
        
        val reportFile = File(reportsDir, fileName)
        reportFile.writeText(htmlContent)
        
        return reportFile
    }
    
    /**
     * 创建测试结果
     */
    fun createTestResult(
        testName: String,
        className: String,
        status: TestStatus,
        duration: Long,
        errorMessage: String? = null,
        screenshots: List<String> = emptyList()
    ): TestResult {
        return TestResult(
            testName = testName,
            className = className,
            status = status,
            duration = duration,
            errorMessage = errorMessage,
            screenshots = screenshots
        )
    }
    
    /**
     * 创建测试套件报告
     */
    fun createTestSuiteReport(
        suiteName: String,
        testResults: List<TestResult>
    ): TestSuiteReport {
        val totalTests = testResults.size
        val passedTests = testResults.count { it.status == TestStatus.PASSED }
        val failedTests = testResults.count { it.status == TestStatus.FAILED }
        val skippedTests = testResults.count { it.status == TestStatus.SKIPPED }
        val totalDuration = testResults.sumOf { it.duration }
        
        return TestSuiteReport(
            suiteName = suiteName,
            totalTests = totalTests,
            passedTests = passedTests,
            failedTests = failedTests,
            skippedTests = skippedTests,
            totalDuration = totalDuration,
            testResults = testResults
        )
    }
    
    /**
     * 生成示例报告（用于演示）
     */
    fun generateSampleReport(): TestSuiteReport {
        val sampleResults = listOf(
            createTestResult(
                testName = "loginScreen_initialState_displaysCorrectly",
                className = "LoginScreenTest",
                status = TestStatus.PASSED,
                duration = 1500
            ),
            createTestResult(
                testName = "loginScreen_loginSuccess_navigatesToHome",
                className = "LoginScreenTest",
                status = TestStatus.PASSED,
                duration = 2300
            ),
            createTestResult(
                testName = "homeScreen_positionClick_triggersCallback",
                className = "HomeScreenTest",
                status = TestStatus.FAILED,
                duration = 1800,
                errorMessage = "Expected callback to be called but it wasn't"
            ),
            createTestResult(
                testName = "completeInterviewFlow_fromLoginToEvaluation_worksCorrectly",
                className = "InterviewFlowEndToEndTest",
                status = TestStatus.PASSED,
                duration = 15000
            )
        )
        
        return createTestSuiteReport("AI面试模拟器UI测试", sampleResults)
    }
}