package com.aiinterview.simulator.data.repository

import com.aiinterview.simulator.data.dao.InterviewRecordDao
import com.aiinterview.simulator.data.model.*
import io.mockk.*
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * 进步分析Repository测试类
 * 测试进步分析功能的各种场景和边界条件
 */
class ProgressAnalysisRepositoryTest {
    
    private lateinit var progressAnalysisRepository: ProgressAnalysisRepository
    private lateinit var mockInterviewRecordDao: InterviewRecordDao
    
    @Before
    fun setup() {
        mockInterviewRecordDao = mockk()
        progressAnalysisRepository = ProgressAnalysisRepository(mockInterviewRecordDao)
    }
    
    @Test
    fun `generateProgressAnalysis - 无记录时返回空分析结果`() = runTest {
        // Given: 没有面试记录
        coEvery { mockInterviewRecordDao.getRecordsByUser("user1") } returns emptyList()
        
        // When: 生成进步分析
        val result = progressAnalysisRepository.generateProgressAnalysis("user1")
        
        // Then: 返回空的分析结果
        assertEquals(TrendDirection.STABLE, result.overallTrend.trendDirection)
        assertEquals(0.0, result.overallTrend.improvementRate, 0.01)
        assertEquals(PerformanceLevel.POOR, result.overallTrend.currentLevel)
        assertTrue(result.scoreHistory.isEmpty())
        assertTrue(result.categoryAnalysis.isEmpty())
        assertEquals(0, result.timeAnalysis.totalPracticeTime)
        assertTrue(result.recommendations.isNotEmpty()) // 应该有基础建议
    }
    
    @Test
    fun `generateProgressAnalysis - 单条记录时返回稳定趋势`() = runTest {
        // Given: 只有一条面试记录
        val singleRecord = createMockInterviewRecord(
            id = "record1",
            userId = "user1",
            positionCategory = "综合管理类",
            overallScore = 85.0,
            startTime = System.currentTimeMillis(),
            duration = 1800 // 30分钟
        )
        
        coEvery { mockInterviewRecordDao.getRecordsByUser("user1") } returns listOf(singleRecord)
        
        // When: 生成进步分析
        val result = progressAnalysisRepository.generateProgressAnalysis("user1")
        
        // Then: 验证结果
        assertEquals(TrendDirection.STABLE, result.overallTrend.trendDirection)
        assertEquals(0.0, result.overallTrend.improvementRate, 0.01)
        assertEquals(PerformanceLevel.GOOD, result.overallTrend.currentLevel)
        assertEquals(1, result.scoreHistory.size)
        assertEquals(1, result.categoryAnalysis.size)
        assertEquals(30, result.timeAnalysis.totalPracticeTime) // 30分钟
        assertEquals(30, result.timeAnalysis.averageSessionDuration)
    }
    
    @Test
    fun `generateProgressAnalysis - 多条记录显示上升趋势`() = runTest {
        // Given: 多条记录，分数逐渐提升
        val records = listOf(
            createMockInterviewRecord("record1", "user1", "综合管理类", 70.0, 
                System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000, 1800),
            createMockInterviewRecord("record2", "user1", "综合管理类", 75.0, 
                System.currentTimeMillis() - 5 * 24 * 60 * 60 * 1000, 1800),
            createMockInterviewRecord("record3", "user1", "综合管理类", 80.0, 
                System.currentTimeMillis() - 3 * 24 * 60 * 60 * 1000, 1800),
            createMockInterviewRecord("record4", "user1", "综合管理类", 85.0, 
                System.currentTimeMillis() - 1 * 24 * 60 * 60 * 1000, 1800)
        )
        
        coEvery { mockInterviewRecordDao.getRecordsByUser("user1") } returns records
        
        // When: 生成进步分析
        val result = progressAnalysisRepository.generateProgressAnalysis("user1")
        
        // Then: 验证上升趋势
        assertEquals(TrendDirection.RISING, result.overallTrend.trendDirection)
        assertTrue("改进率应该为正数", result.overallTrend.improvementRate > 0)
        assertEquals(PerformanceLevel.GOOD, result.overallTrend.currentLevel)
        assertEquals(4, result.scoreHistory.size)
        assertEquals(1, result.categoryAnalysis.size)
        
        // 验证类别分析
        val categoryProgress = result.categoryAnalysis.first()
        assertEquals("综合管理类", categoryProgress.category)
        assertEquals(77.5, categoryProgress.averageScore, 0.1) // (70+75+80+85)/4
        assertEquals(85.0, categoryProgress.bestScore, 0.1)
        assertEquals(4, categoryProgress.interviewCount)
        assertTrue("类别改进率应该为正数", categoryProgress.improvementRate > 0)
    }
    
    @Test
    fun `generateProgressAnalysis - 多类别分析`() = runTest {
        // Given: 不同类别的面试记录
        val records = listOf(
            createMockInterviewRecord("record1", "user1", "综合管理类", 80.0, 
                System.currentTimeMillis() - 6 * 24 * 60 * 60 * 1000, 1800),
            createMockInterviewRecord("record2", "user1", "综合管理类", 85.0, 
                System.currentTimeMillis() - 4 * 24 * 60 * 60 * 1000, 1800),
            createMockInterviewRecord("record3", "user1", "专业技术类", 75.0, 
                System.currentTimeMillis() - 3 * 24 * 60 * 60 * 1000, 1800),
            createMockInterviewRecord("record4", "user1", "专业技术类", 70.0, 
                System.currentTimeMillis() - 1 * 24 * 60 * 60 * 1000, 1800)
        )
        
        coEvery { mockInterviewRecordDao.getRecordsByUser("user1") } returns records
        
        // When: 生成进步分析
        val result = progressAnalysisRepository.generateProgressAnalysis("user1")
        
        // Then: 验证多类别分析
        assertEquals(2, result.categoryAnalysis.size)
        
        // 按平均分数降序排列，综合管理类应该在前
        val sortedCategories = result.categoryAnalysis.sortedByDescending { it.averageScore }
        assertEquals("综合管理类", sortedCategories[0].category)
        assertEquals(82.5, sortedCategories[0].averageScore, 0.1) // (80+85)/2
        assertEquals(TrendDirection.RISING, sortedCategories[0].trend)
        
        assertEquals("专业技术类", sortedCategories[1].category)
        assertEquals(72.5, sortedCategories[1].averageScore, 0.1) // (75+70)/2
        assertEquals(TrendDirection.FALLING, sortedCategories[1].trend)
    }
    
    @Test
    fun `generateProgressAnalysis - 时间分析功能`() = runTest {
        // Given: 跨越多周的面试记录
        val currentTime = System.currentTimeMillis()
        val records = listOf(
            createMockInterviewRecord("record1", "user1", "综合管理类", 80.0, 
                currentTime - 14 * 24 * 60 * 60 * 1000, 1800), // 2周前
            createMockInterviewRecord("record2", "user1", "综合管理类", 82.0, 
                currentTime - 7 * 24 * 60 * 60 * 1000, 1800),  // 1周前
            createMockInterviewRecord("record3", "user1", "综合管理类", 85.0, 
                currentTime - 1 * 24 * 60 * 60 * 1000, 3600)   // 1天前，1小时
        )
        
        coEvery { mockInterviewRecordDao.getRecordsByUser("user1") } returns records
        
        // When: 生成进步分析
        val result = progressAnalysisRepository.generateProgressAnalysis("user1")
        
        // Then: 验证时间分析
        val timeAnalysis = result.timeAnalysis
        assertEquals(90, timeAnalysis.totalPracticeTime) // 30+30+60分钟
        assertEquals(30, timeAnalysis.averageSessionDuration) // 90/3
        assertTrue("应该有周统计数据", timeAnalysis.weeklyStats.isNotEmpty())
        assertTrue("应该有月统计数据", timeAnalysis.monthlyStats.isNotEmpty())
    }
    
    @Test
    fun `generateProgressAnalysis - 生成个性化建议`() = runTest {
        // Given: 有明显薄弱类别的记录
        val records = listOf(
            createMockInterviewRecord("record1", "user1", "综合管理类", 90.0, 
                System.currentTimeMillis() - 3 * 24 * 60 * 60 * 1000, 1800),
            createMockInterviewRecord("record2", "user1", "综合管理类", 92.0, 
                System.currentTimeMillis() - 2 * 24 * 60 * 60 * 1000, 1800),
            createMockInterviewRecord("record3", "user1", "专业技术类", 65.0, 
                System.currentTimeMillis() - 1 * 24 * 60 * 60 * 1000, 1800)
        )
        
        coEvery { mockInterviewRecordDao.getRecordsByUser("user1") } returns records
        
        // When: 生成进步分析
        val result = progressAnalysisRepository.generateProgressAnalysis("user1")
        
        // Then: 验证建议生成
        assertTrue("应该有改进建议", result.recommendations.isNotEmpty())
        assertTrue("建议数量应该合理", result.recommendations.size <= 6)
        
        // 检查是否包含针对薄弱类别的建议
        val recommendationsText = result.recommendations.joinToString(" ")
        assertTrue("应该包含薄弱类别建议", 
            recommendationsText.contains("专业技术类") || recommendationsText.contains("薄弱"))
    }
    
    @Test
    fun `getRecordsForRetry - 返回指定类别的记录`() = runTest {
        // Given: 特定类别的记录
        val records = listOf(
            createMockInterviewRecord("record1", "user1", "综合管理类", 80.0),
            createMockInterviewRecord("record2", "user1", "专业技术类", 75.0),
            createMockInterviewRecord("record3", "user1", "综合管理类", 85.0)
        )
        
        coEvery { mockInterviewRecordDao.getRecordsByCategory("user1", "综合管理类") } returns 
            records.filter { it.positionCategory == "综合管理类" }
        
        // When: 获取重新练习记录
        val result = progressAnalysisRepository.getRecordsForRetry("user1", "综合管理类", 10)
        
        // Then: 验证结果
        assertEquals(2, result.size)
        assertTrue("所有记录都应该是综合管理类", 
            result.all { it.positionCategory == "综合管理类" })
    }
    
    @Test
    fun `getRecommendedPracticeCategories - 按薄弱程度排序`() = runTest {
        // Given: 不同表现的类别记录
        val records = listOf(
            createMockInterviewRecord("record1", "user1", "综合管理类", 90.0),
            createMockInterviewRecord("record2", "user1", "专业技术类", 70.0),
            createMockInterviewRecord("record3", "user1", "行政执法类", 80.0)
        )
        
        coEvery { mockInterviewRecordDao.getRecordsByUser("user1") } returns records
        
        // When: 获取推荐练习类别
        val result = progressAnalysisRepository.getRecommendedPracticeCategories("user1")
        
        // Then: 验证排序（分数低的在前）
        assertEquals(3, result.size)
        assertEquals("专业技术类", result[0]) // 70分，最低
        assertEquals("行政执法类", result[1]) // 80分，中等
        assertEquals("综合管理类", result[2]) // 90分，最高
    }
    
    // 辅助方法：创建模拟面试记录
    private fun createMockInterviewRecord(
        id: String = "record1",
        userId: String = "user1",
        positionCategory: String = "综合管理类",
        overallScore: Double? = 80.0,
        startTime: Long = System.currentTimeMillis(),
        duration: Int = 1800
    ): InterviewRecord {
        return InterviewRecord(
            id = id,
            sessionId = "session_$id",
            userId = userId,
            positionId = "position1",
            positionName = "测试岗位",
            positionCategory = positionCategory,
            startTime = startTime,
            endTime = startTime + duration * 1000,
            duration = duration,
            status = "COMPLETED",
            questionsJson = "[]",
            answersJson = "[]",
            audioFilesJson = "[]",
            evaluationId = "eval_$id",
            overallScore = overallScore,
            createdAt = startTime,
            updatedAt = startTime,
            isSynced = true,
            syncedAt = startTime
        )
    }
}