-- Merging decision tree log ---
manifest
ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:2:1-34:12
INJECTED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:2:1-34:12
INJECTED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:2:1-34:12
INJECTED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:2:1-34:12
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\dbc7298f8ad072dc5569e2320bc12cad\transformed\navigation-common-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\279872a76e8eac0b8ce485635b4ca7e3\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\93a78d6058bcb9c99c165ea262084b84\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\4df5696c7342ae1b311d79fd60107daa\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\5d7dff009915cf0b8745a0ee84456d91\transformed\navigation-compose-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\73f51c4b00c3d8b5bd0709c291d17c64\transformed\material3-1.1.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\6d8cd924a9cf02f0063c8361a41a9ebf\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\d9f98b8a42be3dabcc5485d7f3d38301\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a603a3b349484665dbaca8a017d3ab52\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\223e582811732ec84a11b69194056cf9\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0fd375897a389cb28beca43f7c262ec3\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\ac5fb3a29548f600959da45dba017655\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\9dd633556f57d346bc7dea03bde86cb9\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\fec5ddd509fbf26c9bec58de814242f8\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\148f8fb8ab6020372dea6d7b43441b4f\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a011408394204b20e30fb36a0a89a110\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\c8e3b48b3c0cb444c32a8db4d26ae137\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a0a6d88befcae6d9813043c86cd75460\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\3769a9787634b1aba2d4b5b9fd8fcead\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\66dfb19e75a9256459ea38ec58cf43b1\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a7cbe3338730503d22b9eafe3295e4b5\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\42ec0313f2a1f130cf06c535075632ba\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\1eeca5c6f851b6bd9ff19e4c122b69ae\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2546df139fad0738ad0957958c32c5fd\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a837338b3977ec042f451025b5b4e064\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\011839cb0bd20d2f74a75a2070e61a12\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9432865878371e9545627d83de8d1340\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\cfa62adecc1db49c7c0cd9b341afbe43\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d81370559aac4d4290f79c86542fb81e\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b9bf0c7298e94e20b9cad0d8517e71ef\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\87a91d84630924b568dfdc40ff42c450\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\09ba08d641d34d6975b94f32fd87afeb\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3cfd39d7f925e50e3e34236837813144\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\b78c762ad8b933b1fdc5e729107e312e\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\39f8f5b7af159a8b62e58a6667620ae4\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\65ee62cea27eb63af65608500a7e672f\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d78fa9dcb2040782f62e575b1696aadd\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d1b9f983a408e326f4b262de57832089\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\282c18d69cd69984610652f4a7d46511\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\55d506fbcec2f9fa2b9dc4139efca67a\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6a89eba57c9d14e67fa63b3b7fec273e\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1cc0dae2474ed4e4ea45ae0e309b8bb7\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f0ca55c98ea2a62f589d60191884fcd\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e69ee5bb1069d43d556c21504951e2c\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\fca5986f27e9308a47a5e565c6571803\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\375939ffc0ec8eb8258be600b9626d14\transformed\datastore-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\9992320932b03e855ba4c1c92b2bcd40\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\62b1e433706c0e55369478a34c4790bc\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d05685b3c5a1feb245beb615a085c857\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\50474261e82cd20e26a2e1911ff804f4\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f4644546782730ce5f8c72121d980106\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\07fd601961dda2c621aae39edb69e025\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\88f400cf962815a13a8071ea1623f0c4\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f4642891ef9de1422c52e4f5c944b07b\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
	package
		INJECTED from E:\health-report-tracker\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from E:\health-report-tracker\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from E:\health-report-tracker\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:6:5-79
	android:name
		ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.RECORD_AUDIO
ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:7:5-71
	android:name
		ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:7:22-68
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:8:5-9:38
	android:maxSdkVersion
		ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:9:9-35
	android:name
		ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:8:22-78
application
ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:11:5-32:19
INJECTED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:11:5-32:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2546df139fad0738ad0957958c32c5fd\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2546df139fad0738ad0957958c32c5fd\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a837338b3977ec042f451025b5b4e064\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a837338b3977ec042f451025b5b4e064\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9432865878371e9545627d83de8d1340\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9432865878371e9545627d83de8d1340\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6a89eba57c9d14e67fa63b3b7fec273e\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6a89eba57c9d14e67fa63b3b7fec273e\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\07fd601961dda2c621aae39edb69e025\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\07fd601961dda2c621aae39edb69e025\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\88f400cf962815a13a8071ea1623f0c4\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\88f400cf962815a13a8071ea1623f0c4\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from E:\health-report-tracker\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:19:9-35
	android:label
		ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:17:9-41
	android:fullBackupContent
		ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:15:9-54
	android:roundIcon
		ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:18:9-63
	tools:targetApi
		ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:21:9-29
	android:icon
		ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:16:9-58
	android:allowBackup
		ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:13:9-35
	android:theme
		ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:20:9-58
	android:dataExtractionRules
		ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:14:9-65
	android:name
		ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:12:9-47
activity#com.aiinterview.simulator.presentation.MainActivity
ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:23:9-31:20
	android:exported
		ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:25:13-36
	android:theme
		ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:26:13-62
	android:name
		ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:24:13-54
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:27:13-30:29
action#android.intent.action.MAIN
ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:28:17-69
	android:name
		ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:28:25-66
category#android.intent.category.LAUNCHER
ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:29:17-77
	android:name
		ADDED from E:\health-report-tracker\app\src\main\AndroidManifest.xml:29:27-74
uses-sdk
INJECTED from E:\health-report-tracker\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\health-report-tracker\app\src\main\AndroidManifest.xml
INJECTED from E:\health-report-tracker\app\src\main\AndroidManifest.xml
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\dbc7298f8ad072dc5569e2320bc12cad\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\dbc7298f8ad072dc5569e2320bc12cad\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\279872a76e8eac0b8ce485635b4ca7e3\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\279872a76e8eac0b8ce485635b4ca7e3\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\93a78d6058bcb9c99c165ea262084b84\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\93a78d6058bcb9c99c165ea262084b84\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\4df5696c7342ae1b311d79fd60107daa\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\4df5696c7342ae1b311d79fd60107daa\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\5d7dff009915cf0b8745a0ee84456d91\transformed\navigation-compose-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\5d7dff009915cf0b8745a0ee84456d91\transformed\navigation-compose-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\73f51c4b00c3d8b5bd0709c291d17c64\transformed\material3-1.1.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\73f51c4b00c3d8b5bd0709c291d17c64\transformed\material3-1.1.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\6d8cd924a9cf02f0063c8361a41a9ebf\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\6d8cd924a9cf02f0063c8361a41a9ebf\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\d9f98b8a42be3dabcc5485d7f3d38301\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\d9f98b8a42be3dabcc5485d7f3d38301\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a603a3b349484665dbaca8a017d3ab52\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a603a3b349484665dbaca8a017d3ab52\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\223e582811732ec84a11b69194056cf9\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\223e582811732ec84a11b69194056cf9\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0fd375897a389cb28beca43f7c262ec3\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0fd375897a389cb28beca43f7c262ec3\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\ac5fb3a29548f600959da45dba017655\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\ac5fb3a29548f600959da45dba017655\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\9dd633556f57d346bc7dea03bde86cb9\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\9dd633556f57d346bc7dea03bde86cb9\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\fec5ddd509fbf26c9bec58de814242f8\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\fec5ddd509fbf26c9bec58de814242f8\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\148f8fb8ab6020372dea6d7b43441b4f\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\148f8fb8ab6020372dea6d7b43441b4f\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a011408394204b20e30fb36a0a89a110\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a011408394204b20e30fb36a0a89a110\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\c8e3b48b3c0cb444c32a8db4d26ae137\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\c8e3b48b3c0cb444c32a8db4d26ae137\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a0a6d88befcae6d9813043c86cd75460\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a0a6d88befcae6d9813043c86cd75460\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\3769a9787634b1aba2d4b5b9fd8fcead\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\3769a9787634b1aba2d4b5b9fd8fcead\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\66dfb19e75a9256459ea38ec58cf43b1\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\66dfb19e75a9256459ea38ec58cf43b1\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a7cbe3338730503d22b9eafe3295e4b5\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a7cbe3338730503d22b9eafe3295e4b5\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\42ec0313f2a1f130cf06c535075632ba\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\42ec0313f2a1f130cf06c535075632ba\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\1eeca5c6f851b6bd9ff19e4c122b69ae\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\1eeca5c6f851b6bd9ff19e4c122b69ae\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2546df139fad0738ad0957958c32c5fd\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2546df139fad0738ad0957958c32c5fd\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a837338b3977ec042f451025b5b4e064\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a837338b3977ec042f451025b5b4e064\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\011839cb0bd20d2f74a75a2070e61a12\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\011839cb0bd20d2f74a75a2070e61a12\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9432865878371e9545627d83de8d1340\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9432865878371e9545627d83de8d1340\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\cfa62adecc1db49c7c0cd9b341afbe43\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\cfa62adecc1db49c7c0cd9b341afbe43\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d81370559aac4d4290f79c86542fb81e\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d81370559aac4d4290f79c86542fb81e\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b9bf0c7298e94e20b9cad0d8517e71ef\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b9bf0c7298e94e20b9cad0d8517e71ef\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\87a91d84630924b568dfdc40ff42c450\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\87a91d84630924b568dfdc40ff42c450\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\09ba08d641d34d6975b94f32fd87afeb\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\09ba08d641d34d6975b94f32fd87afeb\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3cfd39d7f925e50e3e34236837813144\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3cfd39d7f925e50e3e34236837813144\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\b78c762ad8b933b1fdc5e729107e312e\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\b78c762ad8b933b1fdc5e729107e312e\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\39f8f5b7af159a8b62e58a6667620ae4\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\39f8f5b7af159a8b62e58a6667620ae4\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\65ee62cea27eb63af65608500a7e672f\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\65ee62cea27eb63af65608500a7e672f\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d78fa9dcb2040782f62e575b1696aadd\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d78fa9dcb2040782f62e575b1696aadd\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d1b9f983a408e326f4b262de57832089\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d1b9f983a408e326f4b262de57832089\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\282c18d69cd69984610652f4a7d46511\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\282c18d69cd69984610652f4a7d46511\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\55d506fbcec2f9fa2b9dc4139efca67a\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\55d506fbcec2f9fa2b9dc4139efca67a\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6a89eba57c9d14e67fa63b3b7fec273e\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6a89eba57c9d14e67fa63b3b7fec273e\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1cc0dae2474ed4e4ea45ae0e309b8bb7\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1cc0dae2474ed4e4ea45ae0e309b8bb7\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f0ca55c98ea2a62f589d60191884fcd\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f0ca55c98ea2a62f589d60191884fcd\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e69ee5bb1069d43d556c21504951e2c\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e69ee5bb1069d43d556c21504951e2c\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\fca5986f27e9308a47a5e565c6571803\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\fca5986f27e9308a47a5e565c6571803\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\375939ffc0ec8eb8258be600b9626d14\transformed\datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\375939ffc0ec8eb8258be600b9626d14\transformed\datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\9992320932b03e855ba4c1c92b2bcd40\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\9992320932b03e855ba4c1c92b2bcd40\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\62b1e433706c0e55369478a34c4790bc\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\62b1e433706c0e55369478a34c4790bc\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d05685b3c5a1feb245beb615a085c857\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d05685b3c5a1feb245beb615a085c857\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\50474261e82cd20e26a2e1911ff804f4\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\50474261e82cd20e26a2e1911ff804f4\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f4644546782730ce5f8c72121d980106\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f4644546782730ce5f8c72121d980106\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\07fd601961dda2c621aae39edb69e025\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\07fd601961dda2c621aae39edb69e025\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\88f400cf962815a13a8071ea1623f0c4\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\88f400cf962815a13a8071ea1623f0c4\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f4642891ef9de1422c52e4f5c944b07b\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f4642891ef9de1422c52e4f5c944b07b\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
	android:targetSdkVersion
		INJECTED from E:\health-report-tracker\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\health-report-tracker\app\src\main\AndroidManifest.xml
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2546df139fad0738ad0957958c32c5fd\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2546df139fad0738ad0957958c32c5fd\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2546df139fad0738ad0957958c32c5fd\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a837338b3977ec042f451025b5b4e064\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a837338b3977ec042f451025b5b4e064\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a837338b3977ec042f451025b5b4e064\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9432865878371e9545627d83de8d1340\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9432865878371e9545627d83de8d1340\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\88f400cf962815a13a8071ea1623f0c4\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\88f400cf962815a13a8071ea1623f0c4\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\034ec09b57af1e5a8db95c7ba6c2bcf0\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9432865878371e9545627d83de8d1340\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9432865878371e9545627d83de8d1340\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9432865878371e9545627d83de8d1340\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.aiinterview.simulator.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.aiinterview.simulator.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd044ab7a523edb1345beec00f65097\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6a89eba57c9d14e67fa63b3b7fec273e\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6a89eba57c9d14e67fa63b3b7fec273e\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6a89eba57c9d14e67fa63b3b7fec273e\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6a89eba57c9d14e67fa63b3b7fec273e\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6a89eba57c9d14e67fa63b3b7fec273e\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af7cc468abc4c5a65442a9aa313d72b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
