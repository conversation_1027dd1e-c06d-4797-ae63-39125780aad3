package com.aiinterview.simulator.presentation.viewmodel

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import com.aiinterview.simulator.data.dto.response.EvaluationResponse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.Assert.*

/**
 * 评价报告ViewModel单元测试
 */
@OptIn(ExperimentalCoroutinesApi::class)
class EvaluationReportViewModelTest {
    
    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()
    
    private val testDispatcher = StandardTestDispatcher()
    private lateinit var viewModel: EvaluationReportViewModel
    
    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)
        viewModel = EvaluationReportViewModel()
    }
    
    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }
    
    @Test
    fun `初始状态应该正确`() = runTest {
        val initialState = viewModel.uiState.first()
        
        assertFalse(initialState.isLoading)
        assertNull(initialState.evaluation)
        assertNull(initialState.error)
        assertTrue(initialState.expandedSections.isEmpty())
    }
    
    @Test
    fun `加载评价报告应该更新状态`() = runTest {
        val sessionId = "test-session-123"
        
        // 开始加载
        viewModel.loadEvaluationReport(sessionId)
        
        // 验证加载状态
        val loadingState = viewModel.uiState.first()
        assertTrue(loadingState.isLoading)
        assertNull(loadingState.error)
        
        // 等待加载完成
        testDispatcher.scheduler.advanceUntilIdle()
        
        // 验证加载完成状态
        val completedState = viewModel.uiState.first()
        assertFalse(completedState.isLoading)
        assertNotNull(completedState.evaluation)
        assertNull(completedState.error)
        assertEquals(sessionId, completedState.evaluation?.sessionId)
        assertTrue(completedState.expandedSections.contains("overall"))
    }
    
    @Test
    fun `切换章节展开状态应该正确更新`() = runTest {
        val sectionId = "dimensions"
        
        // 初始状态不包含该章节
        val initialState = viewModel.uiState.first()
        assertFalse(initialState.expandedSections.contains(sectionId))
        
        // 展开章节
        viewModel.toggleSectionExpanded(sectionId)
        
        val expandedState = viewModel.uiState.first()
        assertTrue(expandedState.expandedSections.contains(sectionId))
        
        // 收起章节
        viewModel.toggleSectionExpanded(sectionId)
        
        val collapsedState = viewModel.uiState.first()
        assertFalse(collapsedState.expandedSections.contains(sectionId))
    }
    
    @Test
    fun `重试功能应该重新加载数据`() = runTest {
        val sessionId = "test-session-456"
        
        // 首次加载
        viewModel.loadEvaluationReport(sessionId)
        testDispatcher.scheduler.advanceUntilIdle()
        
        val firstLoadState = viewModel.uiState.first()
        assertNotNull(firstLoadState.evaluation)
        
        // 重试加载
        viewModel.retry(sessionId)
        
        // 验证重新进入加载状态
        val retryLoadingState = viewModel.uiState.first()
        assertTrue(retryLoadingState.isLoading)
        
        testDispatcher.scheduler.advanceUntilIdle()
        
        // 验证重试完成
        val retryCompletedState = viewModel.uiState.first()
        assertFalse(retryCompletedState.isLoading)
        assertNotNull(retryCompletedState.evaluation)
    }
    
    @Test
    fun `模拟评价数据应该包含所有必要字段`() = runTest {
        val sessionId = "test-session-789"
        
        viewModel.loadEvaluationReport(sessionId)
        testDispatcher.scheduler.advanceUntilIdle()
        
        val state = viewModel.uiState.first()
        val evaluation = state.evaluation
        
        assertNotNull(evaluation)
        assertEquals(sessionId, evaluation!!.sessionId)
        assertTrue(evaluation.overallScore > 0)
        assertNotNull(evaluation.overallGrade)
        assertNotNull(evaluation.dimensions)
        assertNotNull(evaluation.feedback)
        assertTrue(evaluation.suggestions.isNotEmpty())
        assertNotNull(evaluation.encouragement)
        assertNotNull(evaluation.timeAnalysis)
        assertTrue(evaluation.createdAt > 0)
        
        // 验证各维度评分
        val dimensions = evaluation.dimensions
        assertTrue(dimensions.contentCompleteness.score > 0)
        assertTrue(dimensions.logicalStructure.score > 0)
        assertTrue(dimensions.languageFluency.score > 0)
        assertTrue(dimensions.expressionClarity.score > 0)
        assertTrue(dimensions.timeManagement.score > 0)
        
        // 验证时间分析
        val timeAnalysis = evaluation.timeAnalysis
        assertTrue(timeAnalysis.totalTime > 0)
        assertTrue(timeAnalysis.timeLimit > 0)
        assertTrue(timeAnalysis.timeUtilization > 0)
        assertTrue(timeAnalysis.questionTimeBreakdown.isNotEmpty())
        assertNotNull(timeAnalysis.timeManagementFeedback)
    }
    
    @Test
    fun `多个章节可以同时展开`() = runTest {
        val section1 = "dimensions"
        val section2 = "time"
        val section3 = "suggestions"
        
        // 展开多个章节
        viewModel.toggleSectionExpanded(section1)
        viewModel.toggleSectionExpanded(section2)
        viewModel.toggleSectionExpanded(section3)
        
        val state = viewModel.uiState.first()
        assertTrue(state.expandedSections.contains(section1))
        assertTrue(state.expandedSections.contains(section2))
        assertTrue(state.expandedSections.contains(section3))
        assertEquals(3, state.expandedSections.size)
    }
    
    @Test
    fun `评价等级应该与分数匹配`() = runTest {
        viewModel.loadEvaluationReport("test-session")
        testDispatcher.scheduler.advanceUntilIdle()
        
        val evaluation = viewModel.uiState.first().evaluation!!
        
        // 验证总体评分和等级的一致性
        when (evaluation.overallGrade) {
            "优秀" -> assertTrue(evaluation.overallScore >= 90)
            "良好" -> assertTrue(evaluation.overallScore >= 80 && evaluation.overallScore < 90)
            "中等" -> assertTrue(evaluation.overallScore >= 70 && evaluation.overallScore < 80)
            "需改进" -> assertTrue(evaluation.overallScore < 70)
        }
        
        // 验证各维度评分和等级的一致性
        listOf(
            evaluation.dimensions.contentCompleteness,
            evaluation.dimensions.logicalStructure,
            evaluation.dimensions.languageFluency,
            evaluation.dimensions.expressionClarity,
            evaluation.dimensions.timeManagement
        ).forEach { dimension ->
            when (dimension.grade) {
                "优秀" -> assertTrue(dimension.score >= 90)
                "良好" -> assertTrue(dimension.score >= 80 && dimension.score < 90)
                "中等" -> assertTrue(dimension.score >= 70 && dimension.score < 80)
                "需改进" -> assertTrue(dimension.score < 70)
            }
        }
    }
}