{"logs": [{"outputFile": "com.aiinterview.simulator.app-mergeOfficialDebugResources-55:/values-ca/values-ca.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fd044ab7a523edb1345beec00f65097\\transformed\\core-1.12.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "568,664,766,865,962,1068,1173,6544", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "659,761,860,957,1063,1168,1294,6640"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\73f51c4b00c3d8b5bd0709c291d17c64\\transformed\\material3-1.1.2\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,402,518,601,695,807,945,1056,1211,1291,1385,1479,1576,1689,1814,1913,2050,2183,2330,2492,2621,2734,2852,2978,3073,3166,3283,3424,3526,3635,3745,3879,4020,4125,4227,4299,4382,4464,4546,4653,4729,4809,4906,5010,5105,5205,5288,5397,5493,5595,5710,5786,5899", "endColumns": "116,114,114,115,82,93,111,137,110,154,79,93,93,96,112,124,98,136,132,146,161,128,112,117,125,94,92,116,140,101,108,109,133,140,104,101,71,82,81,81,106,75,79,96,103,94,99,82,108,95,101,114,75,112,102", "endOffsets": "167,282,397,513,596,690,802,940,1051,1206,1286,1380,1474,1571,1684,1809,1908,2045,2178,2325,2487,2616,2729,2847,2973,3068,3161,3278,3419,3521,3630,3740,3874,4015,4120,4222,4294,4377,4459,4541,4648,4724,4804,4901,5005,5100,5200,5283,5392,5488,5590,5705,5781,5894,5997"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,337,452,1480,1563,1657,1769,1907,2018,2173,2253,2347,2441,2538,2651,2776,2875,3012,3145,3292,3454,3583,3696,3814,3940,4035,4128,4245,4386,4488,4597,4707,4841,4982,5087,5396,5557,6304,6462,6645,7025,7101,7181,7278,7382,7477,7577,7660,7769,7865,7967,8082,8158,8271", "endColumns": "116,114,114,115,82,93,111,137,110,154,79,93,93,96,112,124,98,136,132,146,161,128,112,117,125,94,92,116,140,101,108,109,133,140,104,101,71,82,81,81,106,75,79,96,103,94,99,82,108,95,101,114,75,112,102", "endOffsets": "217,332,447,563,1558,1652,1764,1902,2013,2168,2248,2342,2436,2533,2646,2771,2870,3007,3140,3287,3449,3578,3691,3809,3935,4030,4123,4240,4381,4483,4592,4702,4836,4977,5082,5184,5463,5635,6381,6539,6747,7096,7176,7273,7377,7472,7572,7655,7764,7860,7962,8077,8153,8266,8369"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d78fa9dcb2040782f62e575b1696aadd\\transformed\\ui-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,390,493,582,660,751,842,928,1000,1069,1155,1246,1322,1404,1475", "endColumns": "96,83,103,102,88,77,90,90,85,71,68,85,90,75,81,70,119", "endOffsets": "197,281,385,488,577,655,746,837,923,995,1064,1150,1241,1317,1399,1470,1590"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1299,1396,5189,5293,5468,5640,5718,5809,5900,5986,6058,6127,6213,6386,6752,6834,6905", "endColumns": "96,83,103,102,88,77,90,90,85,71,68,85,90,75,81,70,119", "endOffsets": "1391,1475,5288,5391,5552,5713,5804,5895,5981,6053,6122,6208,6299,6457,6829,6900,7020"}}]}]}