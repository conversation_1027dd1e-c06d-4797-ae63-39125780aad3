package com.aiinterview.simulator.presentation.viewmodel

import com.aiinterview.simulator.data.handler.TechnicalIssueHandler
import com.aiinterview.simulator.data.manager.InterviewSessionManager
import com.aiinterview.simulator.data.model.*
import com.aiinterview.simulator.data.service.InterviewTimerService
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.*
import org.junit.Assert.*
import java.io.File

@ExperimentalCoroutinesApi
class InterviewFlowManagementViewModelTest {
    
    @Mock
    private lateinit var sessionManager: InterviewSessionManager
    
    @Mock
    private lateinit var timerService: InterviewTimerService
    
    @Mock
    private lateinit var technicalIssueHandler: TechnicalIssueHandler
    
    @Mock
    private lateinit var mockFile: File
    
    private lateinit var viewModel: InterviewFlowManagementViewModel
    
    private val testPosition = Position(
        id = "test-position",
        name = "测试岗位",
        category = "测试分类",
        level = "测试级别",
        description = "测试描述",
        duration = 30,
        questionCount = 3,
        questionTypes = "[]",
        timeWarnings = "[]"
    )
    
    private val testSession = InterviewSession(
        id = "test-session",
        userId = "test-user",
        positionId = "test-position",
        status = InterviewStatus.STARTED,
        startTime = System.currentTimeMillis(),
        endTime = null,
        currentQuestionIndex = 0,
        questions = listOf(
            Question(
                id = "q1",
                type = QuestionType.COMPREHENSIVE_ANALYSIS,
                category = "综合分析",
                title = "测试题目1",
                content = "测试内容1",
                timeLimit = 300,
                difficulty = 3
            )
        ),
        answers = emptyList(),
        evaluation = null
    )
    
    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        
        // Setup mock flows
        whenever(sessionManager.activeSession).thenReturn(MutableStateFlow(null))
        whenever(sessionManager.sessionProgress).thenReturn(
            MutableStateFlow(InterviewSessionManager.SessionProgress())
        )
        whenever(timerService.timerState).thenReturn(
            MutableStateFlow(InterviewTimerService.TimerState.STOPPED)
        )
        whenever(timerService.totalTimeRemaining).thenReturn(MutableStateFlow(0L))
        whenever(timerService.questionTimeRemaining).thenReturn(MutableStateFlow(0L))
        whenever(timerService.timeWarnings).thenReturn(MutableStateFlow(emptyList()))
        whenever(technicalIssueHandler.currentIssue).thenReturn(MutableStateFlow(null))
        whenever(technicalIssueHandler.resolutionOptions).thenReturn(MutableStateFlow(null))
        whenever(technicalIssueHandler.isHandlingIssue).thenReturn(MutableStateFlow(false))
        
        viewModel = InterviewFlowManagementViewModel(
            sessionManager,
            timerService,
            technicalIssueHandler
        )
    }
    
    @Test
    fun `createInterviewSession should create session and start timer`() = runTest {
        // Given
        val userId = "test-user"
        whenever(sessionManager.createSession(userId, testPosition, true))
            .thenReturn(Result.success(testSession))
        
        // When
        viewModel.createInterviewSession(userId, testPosition, true)
        
        // Then
        verify(sessionManager).createSession(userId, testPosition, true)
        verify(timerService).startInterviewTimer(testPosition.duration)
    }
    
    @Test
    fun `createInterviewSession should handle failure`() = runTest {
        // Given
        val userId = "test-user"
        val error = Exception("创建失败")
        whenever(sessionManager.createSession(userId, testPosition, true))
            .thenReturn(Result.failure(error))
        
        // When
        viewModel.createInterviewSession(userId, testPosition, true)
        
        // Then
        verify(sessionManager).createSession(userId, testPosition, true)
        verify(timerService, never()).startInterviewTimer(any())
    }
    
    @Test
    fun `startInterviewSession should start session and timer`() = runTest {
        // Given
        val sessionId = "test-session"
        whenever(sessionManager.startSession(sessionId))
            .thenReturn(Result.success(testSession))
        
        // When
        viewModel.startInterviewSession(sessionId)
        
        // Then
        verify(sessionManager).startSession(sessionId)
        verify(timerService).startInterviewTimer(30) // 默认30分钟
    }
    
    @Test
    fun `proceedToNextQuestion should proceed and start question timer`() = runTest {
        // Given
        val question = Question(
            id = "q1",
            type = QuestionType.COMPREHENSIVE_ANALYSIS,
            category = "综合分析",
            title = "测试题目",
            content = "测试内容",
            timeLimit = 300,
            difficulty = 3
        )
        whenever(sessionManager.proceedToNextQuestion())
            .thenReturn(Result.success(question))
        
        // When
        viewModel.proceedToNextQuestion()
        
        // Then
        verify(sessionManager).proceedToNextQuestion()
        verify(timerService).startQuestionTimer(question.timeLimit)
    }
    
    @Test
    fun `proceedToNextQuestion should handle completion`() = runTest {
        // Given
        whenever(sessionManager.proceedToNextQuestion())
            .thenReturn(Result.failure(Exception("面试已完成")))
        
        // When
        viewModel.proceedToNextQuestion()
        
        // Then
        verify(sessionManager).proceedToNextQuestion()
        verify(timerService, never()).startQuestionTimer(any())
    }
    
    @Test
    fun `submitAnswer should submit and stop question timer`() = runTest {
        // Given
        val questionId = "q1"
        val transcription = "测试回答"
        val duration = 30
        whenever(sessionManager.submitAnswer(questionId, mockFile, transcription, duration))
            .thenReturn(Result.success(Unit))
        
        // When
        viewModel.submitAnswer(questionId, mockFile, transcription, duration)
        
        // Then
        verify(sessionManager).submitAnswer(questionId, mockFile, transcription, duration)
        verify(timerService).stopQuestionTimer()
    }
    
    @Test
    fun `pauseInterview should pause session and timers`() = runTest {
        // Given
        whenever(sessionManager.pauseSession()).thenReturn(Result.success(Unit))
        
        // When
        viewModel.pauseInterview()
        
        // Then
        verify(sessionManager).pauseSession()
        verify(timerService).pauseTimers()
    }
    
    @Test
    fun `resumeInterview should resume session and timers`() = runTest {
        // Given
        whenever(sessionManager.resumeSession()).thenReturn(Result.success(Unit))
        
        // When
        viewModel.resumeInterview()
        
        // Then
        verify(sessionManager).resumeSession()
        verify(timerService).resumeTimers()
    }
    
    @Test
    fun `completeInterview should complete session and stop timers`() = runTest {
        // Given
        whenever(sessionManager.completeSession()).thenReturn(Result.success(testSession))
        
        // When
        viewModel.completeInterview()
        
        // Then
        verify(sessionManager).completeSession()
        verify(timerService).stopAllTimers()
    }
    
    @Test
    fun `cancelInterview should cancel session and stop timers`() = runTest {
        // Given
        whenever(sessionManager.cancelSession()).thenReturn(Result.success(Unit))
        
        // When
        viewModel.cancelInterview()
        
        // Then
        verify(sessionManager).cancelSession()
        verify(timerService).stopAllTimers()
    }
    
    @Test
    fun `handleRecordingFailure should handle recording issue`() = runTest {
        // Given
        val questionId = "q1"
        val errorMessage = "录音失败"
        val options = TechnicalIssueHandler.ResolutionOptions(
            primaryActions = emptyList(),
            alternativeActions = emptyList(),
            preventiveActions = emptyList(),
            userGuidance = "测试指导",
            estimatedResolutionTime = 30000L
        )
        whenever(technicalIssueHandler.handleRecordingFailed(questionId, null, errorMessage))
            .thenReturn(Result.success(options))
        
        // When
        viewModel.handleRecordingFailure(questionId, errorMessage)
        
        // Then
        verify(technicalIssueHandler).handleRecordingFailed(questionId, null, errorMessage)
    }
    
    @Test
    fun `handleSpeechRecognitionFailure should handle recognition issue`() = runTest {
        // Given
        val questionId = "q1"
        val provider = "BAIDU"
        val errorMessage = "识别失败"
        val options = TechnicalIssueHandler.ResolutionOptions(
            primaryActions = emptyList(),
            alternativeActions = emptyList(),
            preventiveActions = emptyList(),
            userGuidance = "测试指导",
            estimatedResolutionTime = 30000L
        )
        whenever(technicalIssueHandler.handleSpeechRecognitionFailed(questionId, mockFile, provider, errorMessage))
            .thenReturn(Result.success(options))
        
        // When
        viewModel.handleSpeechRecognitionFailure(questionId, mockFile, provider, errorMessage)
        
        // Then
        verify(technicalIssueHandler).handleSpeechRecognitionFailed(questionId, mockFile, provider, errorMessage)
    }
    
    @Test
    fun `handleNetworkIssue should handle network problem`() = runTest {
        // Given
        val description = "网络连接失败"
        val questionId = "q1"
        val options = TechnicalIssueHandler.ResolutionOptions(
            primaryActions = emptyList(),
            alternativeActions = emptyList(),
            preventiveActions = emptyList(),
            userGuidance = "测试指导",
            estimatedResolutionTime = 60000L
        )
        whenever(technicalIssueHandler.handleNetworkIssue(description, questionId))
            .thenReturn(Result.success(options))
        
        // When
        viewModel.handleNetworkIssue(description, questionId)
        
        // Then
        verify(technicalIssueHandler).handleNetworkIssue(description, questionId)
    }
    
    @Test
    fun `executeResolution should execute technical issue resolution`() = runTest {
        // Given
        val actionId = "retry_recording"
        whenever(technicalIssueHandler.executeResolution(actionId))
            .thenReturn(Result.success("解决方案执行成功"))
        
        // When
        viewModel.executeResolution(actionId)
        
        // Then
        verify(technicalIssueHandler).executeResolution(actionId)
    }
    
    @Test
    fun `acknowledgeTimeWarning should acknowledge warning`() {
        // Given
        val warningId = "warning-1"
        
        // When
        viewModel.acknowledgeTimeWarning(warningId)
        
        // Then
        verify(timerService).acknowledgeWarning(warningId)
    }
    
    @Test
    fun `clearCurrentIssue should clear technical issue`() {
        // When
        viewModel.clearCurrentIssue()
        
        // Then
        verify(technicalIssueHandler).clearCurrentIssue()
    }
    
    @Test
    fun `getInterviewStatistics should return combined statistics`() {
        // Given
        val sessionStats = InterviewSessionManager.SessionStatistics(
            sessionId = "test-session",
            totalQuestions = 3,
            completedQuestions = 1,
            completionRate = 0.33f,
            totalTimeSpent = 300000L,
            averageTimePerQuestion = 300000L,
            technicalIssuesCount = 1,
            resolvedIssuesCount = 0,
            timeWarningsCount = 2
        )
        val timerStats = InterviewTimerService.TimerStatistics(
            totalTimeLimit = 1800000L,
            totalTimeElapsed = 300000L,
            totalTimeRemaining = 1500000L,
            questionTimeLimit = 300000L,
            questionTimeElapsed = 60000L,
            questionTimeRemaining = 240000L,
            warningsTriggered = 2,
            voiceWarningsTriggered = 1,
            timerState = InterviewTimerService.TimerState.RUNNING
        )
        
        whenever(sessionManager.getSessionStatistics()).thenReturn(sessionStats)
        whenever(timerService.getTimerStatistics()).thenReturn(timerStats)
        
        // When
        val result = viewModel.getInterviewStatistics()
        
        // Then
        assertEquals("test-session", result.sessionId)
        assertEquals(3, result.totalQuestions)
        assertEquals(1, result.completedQuestions)
        assertEquals(0.33f, result.completionRate, 0.01f)
        assertEquals(300000L, result.totalTimeSpent)
        assertEquals(300000L, result.averageTimePerQuestion)
        assertEquals(2, result.timeWarningsCount)
        assertEquals(1, result.technicalIssuesCount)
        assertEquals(0, result.resolvedIssuesCount)
    }
    
    @Test
    fun `canProceedToNext should return correct state`() {
        // Given
        val progress = InterviewSessionManager.SessionProgress(
            totalQuestions = 3,
            completedQuestions = 1,
            currentQuestionIndex = 1,
            totalTimeSpent = 300000L,
            averageTimePerQuestion = 300000L,
            remainingTime = 1500000L
        )
        whenever(sessionManager.sessionProgress).thenReturn(MutableStateFlow(progress))
        
        // When
        val canProceed = viewModel.canProceedToNext()
        
        // Then
        assertTrue(canProceed) // currentQuestionIndex (1) < totalQuestions (3)
    }
    
    @Test
    fun `isInterviewCompleted should return correct state`() {
        // Given
        val progress = InterviewSessionManager.SessionProgress(
            totalQuestions = 3,
            completedQuestions = 3,
            currentQuestionIndex = 3,
            totalTimeSpent = 900000L,
            averageTimePerQuestion = 300000L,
            remainingTime = 900000L
        )
        whenever(sessionManager.sessionProgress).thenReturn(MutableStateFlow(progress))
        
        // When
        val isCompleted = viewModel.isInterviewCompleted()
        
        // Then
        assertTrue(isCompleted) // completedQuestions (3) >= totalQuestions (3)
    }
    
    @Test
    fun `getFormattedTotalTime should return formatted time`() {
        // Given
        whenever(timerService.getFormattedTotalTime()).thenReturn("25:30")
        
        // When
        val formattedTime = viewModel.getFormattedTotalTime()
        
        // Then
        assertEquals("25:30", formattedTime)
        verify(timerService).getFormattedTotalTime()
    }
    
    @Test
    fun `getFormattedQuestionTime should return formatted time`() {
        // Given
        whenever(timerService.getFormattedQuestionTime()).thenReturn("04:15")
        
        // When
        val formattedTime = viewModel.getFormattedQuestionTime()
        
        // Then
        assertEquals("04:15", formattedTime)
        verify(timerService).getFormattedQuestionTime()
    }
    
    @Test
    fun `getInterviewProgress should return progress percentage`() {
        // Given
        whenever(timerService.getInterviewProgress()).thenReturn(0.75f)
        
        // When
        val progress = viewModel.getInterviewProgress()
        
        // Then
        assertEquals(0.75f, progress, 0.01f)
        verify(timerService).getInterviewProgress()
    }
    
    @Test
    fun `clearErrorMessage should clear error state`() {
        // When
        viewModel.clearErrorMessage()
        
        // Then
        // Verify error message is cleared (would need to check internal state)
        // This is more of an integration test
    }
}