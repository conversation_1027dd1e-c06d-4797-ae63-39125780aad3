package com.aiinterview.simulator.data.service

import android.content.Context
import com.aiinterview.simulator.data.api.AIQuestionGenerationApi
import com.aiinterview.simulator.data.dto.response.QuestionGenerationResponse
import com.aiinterview.simulator.data.model.Position
import com.aiinterview.simulator.data.model.QuestionType
import com.google.gson.Gson
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import org.junit.Assert.*

@ExperimentalCoroutinesApi
class AIQuestionGenerationServiceTest {
    
    @Mock
    private lateinit var context: Context
    
    @Mock
    private lateinit var aiApi: AIQuestionGenerationApi
    
    @Mock
    private lateinit var gson: Gson
    
    private lateinit var aiQuestionService: AIQuestionGenerationService
    
    private val testPosition = Position(
        id = "test-position-1",
        name = "公务员",
        category = "行政管理",
        level = "科员",
        description = "测试岗位",
        duration = 30,
        questionCount = 5,
        questionTypes = "[]",
        timeWarnings = "[]"
    )
    
    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        aiQuestionService = AIQuestionGenerationService(context, aiApi, gson)
    }
    
    @Test
    fun `generateQuestion should return success response for valid input`() = runTest {
        // Given
        val questionType = QuestionType.COMPREHENSIVE_ANALYSIS
        val difficulty = 3
        
        // When
        val result = aiQuestionService.generateQuestion(
            position = testPosition,
            questionType = questionType,
            difficulty = difficulty
        )
        
        // Then
        assertNotNull(result)
        assertTrue(result.success || result.provider == "fallback") // 可能使用降级问题
    }
    
    @Test
    fun `generateQuestion should handle different question types`() = runTest {
        // Test all question types
        val questionTypes = QuestionType.values()
        
        for (questionType in questionTypes) {
            // When
            val result = aiQuestionService.generateQuestion(
                position = testPosition,
                questionType = questionType,
                difficulty = 3
            )
            
            // Then
            assertNotNull(result)
            if (result.success && result.question != null) {
                assertEquals(questionType.name, result.question.type)
                assertEquals(questionType.displayName, result.question.category)
            }
        }
    }
    
    @Test
    fun `generateQuestion should handle different difficulty levels`() = runTest {
        // Test different difficulty levels
        val difficulties = listOf(1, 2, 3, 4, 5)
        
        for (difficulty in difficulties) {
            // When
            val result = aiQuestionService.generateQuestion(
                position = testPosition,
                questionType = QuestionType.COMPREHENSIVE_ANALYSIS,
                difficulty = difficulty
            )
            
            // Then
            assertNotNull(result)
            if (result.success && result.question != null) {
                assertEquals(difficulty, result.question.difficulty)
            }
        }
    }
    
    @Test
    fun `generateQuestion should avoid duplicate questions`() = runTest {
        // Given
        val previousQuestions = listOf(
            "请分析当前的经济形势",
            "谈谈对环保政策的看法"
        )
        
        // When
        val result = aiQuestionService.generateQuestion(
            position = testPosition,
            questionType = QuestionType.COMPREHENSIVE_ANALYSIS,
            difficulty = 3,
            previousQuestions = previousQuestions
        )
        
        // Then
        assertNotNull(result)
        if (result.success && result.question != null) {
            // 生成的问题不应该与之前的问题重复
            assertFalse(previousQuestions.contains(result.question.content))
        }
    }
    
    @Test
    fun `generateQuestion should return fallback when API fails`() = runTest {
        // Given - API调用失败的情况已经在实际实现中通过try-catch处理
        
        // When
        val result = aiQuestionService.generateQuestion(
            position = testPosition,
            questionType = QuestionType.PLANNING_ORGANIZATION,
            difficulty = 3
        )
        
        // Then
        assertNotNull(result)
        assertTrue(result.success) // 应该返回降级问题
        assertNotNull(result.question)
        assertEquals(QuestionType.PLANNING_ORGANIZATION.name, result.question?.type)
    }
    
    @Test
    fun `validateGeneratedQuestion should validate question quality`() {
        // Given
        val validQuestion = com.aiinterview.simulator.data.dto.response.GeneratedQuestion(
            id = "test-1",
            type = "COMPREHENSIVE_ANALYSIS",
            category = "综合分析",
            title = "测试题目",
            content = "这是一个有效的测试问题内容，长度足够进行分析。",
            timeLimit = 300,
            difficulty = 3
        )
        
        val invalidQuestion = com.aiinterview.simulator.data.dto.response.GeneratedQuestion(
            id = "test-2",
            type = "COMPREHENSIVE_ANALYSIS",
            category = "综合分析",
            title = "测试题目",
            content = "太短", // 内容太短
            timeLimit = 0, // 时间限制无效
            difficulty = 10 // 难度超出范围
        )
        
        // When & Then
        assertTrue(aiQuestionService.validateGeneratedQuestion(validQuestion))
        assertFalse(aiQuestionService.validateGeneratedQuestion(invalidQuestion))
    }
    
    @Test
    fun `generateQuestion should handle all AI providers`() = runTest {
        // Test AUTO provider (should try multiple providers)
        val autoResult = aiQuestionService.generateQuestion(
            position = testPosition,
            questionType = QuestionType.COMPREHENSIVE_ANALYSIS,
            difficulty = 3,
            provider = AIQuestionGenerationService.AIProvider.AUTO
        )
        
        assertNotNull(autoResult)
        assertTrue(autoResult.success)
        
        // Test specific providers
        val wenxinResult = aiQuestionService.generateQuestion(
            position = testPosition,
            questionType = QuestionType.COMPREHENSIVE_ANALYSIS,
            difficulty = 3,
            provider = AIQuestionGenerationService.AIProvider.WENXIN
        )
        
        assertNotNull(wenxinResult)
        
        val tongyiResult = aiQuestionService.generateQuestion(
            position = testPosition,
            questionType = QuestionType.COMPREHENSIVE_ANALYSIS,
            difficulty = 3,
            provider = AIQuestionGenerationService.AIProvider.TONGYI
        )
        
        assertNotNull(tongyiResult)
    }
    
    @Test
    fun `generateQuestion should create appropriate prompts for different question types`() = runTest {
        // Test that different question types generate appropriate content
        val comprehensiveResult = aiQuestionService.generateQuestion(
            position = testPosition,
            questionType = QuestionType.COMPREHENSIVE_ANALYSIS,
            difficulty = 3
        )
        
        val planningResult = aiQuestionService.generateQuestion(
            position = testPosition,
            questionType = QuestionType.PLANNING_ORGANIZATION,
            difficulty = 3
        )
        
        val interpersonalResult = aiQuestionService.generateQuestion(
            position = testPosition,
            questionType = QuestionType.INTERPERSONAL_RELATIONS,
            difficulty = 3
        )
        
        // All should be successful (either AI generated or fallback)
        assertTrue(comprehensiveResult.success)
        assertTrue(planningResult.success)
        assertTrue(interpersonalResult.success)
        
        // Questions should be different types
        if (comprehensiveResult.question != null && planningResult.question != null) {
            assertNotEquals(comprehensiveResult.question.type, planningResult.question.type)
        }
    }
}