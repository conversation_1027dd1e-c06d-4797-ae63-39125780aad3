package com.aiinterview.simulator.di

import android.content.Context
import com.aiinterview.simulator.data.audio.AudioRecorder
import com.aiinterview.simulator.data.audio.OfflineRecordingManager
import com.aiinterview.simulator.domain.util.PermissionManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object AudioModule {
    
    @Provides
    @Singleton
    fun provideAudioRecorder(@ApplicationContext context: Context): AudioRecorder {
        return AudioRecorder(context)
    }
    
    @Provides
    @Singleton
    fun providePermissionManager(@ApplicationContext context: Context): PermissionManager {
        return PermissionManager(context)
    }
    
    @Provides
    @Singleton
    fun provideOfflineRecordingManager(@ApplicationContext context: Context): OfflineRecordingManager {
        return OfflineRecordingManager(context)
    }
}