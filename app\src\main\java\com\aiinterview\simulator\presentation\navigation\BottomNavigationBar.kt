package com.aiinterview.simulator.presentation.navigation

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.History
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.TrendingUp
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.navigation.NavController
import androidx.navigation.compose.currentBackStackEntryAsState
import com.aiinterview.simulator.presentation.theme.ComponentStyles
import com.aiinterview.simulator.presentation.theme.Dimensions
import com.aiinterview.simulator.presentation.theme.ResponsiveStyles

/**
 * 底部导航栏数据类
 * 定义底部导航栏每个标签的属性
 */
data class BottomNavItem(
    val route: String,          // 导航路由
    val icon: ImageVector,      // 图标
    val label: String          // 标签文字
)

/**
 * 底部导航栏组件
 * 提供主要功能模块的快速导航
 * @param navController 导航控制器
 * @param onNavigate 导航回调函数
 */
@Composable
fun BottomNavigationBar(
    navController: NavController,
    onNavigate: (String) -> Unit
) {
    // 定义底部导航栏的所有标签项
    val bottomNavItems = listOf(
        BottomNavItem(
            route = NavigationRoutes.HOME,      // 首页路由
            icon = Icons.Default.Home,          // 首页图标
            label = "首页"                      // 首页标签
        ),
        BottomNavItem(
            route = NavigationRoutes.INTERVIEW_HISTORY, // 历史记录路由
            icon = Icons.Default.History,       // 历史图标
            label = "历史记录"                  // 历史记录标签
        ),
        BottomNavItem(
            route = NavigationRoutes.PROGRESS_ANALYSIS, // 进步分析路由
            icon = Icons.Default.TrendingUp,    // 趋势图标
            label = "进步分析"                  // 进步分析标签
        ),
        BottomNavItem(
            route = NavigationRoutes.PROFILE,   // 个人中心路由
            icon = Icons.Default.Person,        // 个人图标
            label = "个人中心"                  // 个人中心标签
        )
    )
    
    // 获取当前导航栈的状态
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route // 获取当前路由
    
    // 渲染底部导航栏 - 使用统一的组件样式
    NavigationBar(
        containerColor = MaterialTheme.colorScheme.surface,     // 设置容器背景色
        contentColor = MaterialTheme.colorScheme.onSurface,     // 设置内容颜色
        tonalElevation = Dimensions.ElevationSmall              // 设置色调阴影高度
    ) {
        bottomNavItems.forEach { item ->     // 遍历所有导航项
            NavigationBarItem(
                icon = {                     // 设置图标
                    Icon(
                        imageVector = item.icon,
                        contentDescription = item.label, // 无障碍描述
                        modifier = Modifier.size(Dimensions.IconMedium) // 使用统一的中等图标尺寸
                    )
                },
                label = {                    // 设置标签文字
                    Text(
                        text = item.label,
                        style = ResponsiveStyles.getResponsiveTextStyle(MaterialTheme.typography.labelMedium) // 使用响应式标签样式
                    )
                },
                selected = currentRoute == item.route, // 判断是否为当前选中项
                onClick = {                  // 点击事件处理
                    if (currentRoute != item.route) { // 如果不是当前路由才进行导航
                        onNavigate(item.route)        // 执行导航回调
                    }
                },
                colors = ComponentStyles.BottomNavigation.standardNavigationBarItemColors(), // 使用统一的底部导航栏颜色样式
                alwaysShowLabel = true       // 始终显示标签，提高可用性
            )
        }
    }
}

/**
 * 判断当前路由是否应该显示底部导航栏
 * @param currentRoute 当前路由
 * @return 是否显示底部导航栏
 */
fun shouldShowBottomBar(currentRoute: String?): Boolean {
    // 定义需要显示底部导航栏的路由列表
    val routesWithBottomBar = listOf(
        NavigationRoutes.HOME,              // 首页
        NavigationRoutes.INTERVIEW_HISTORY, // 历史记录
        NavigationRoutes.PROGRESS_ANALYSIS, // 进步分析
        NavigationRoutes.PROFILE           // 个人中心
    )
    
    // 检查当前路由是否在显示列表中
    return currentRoute in routesWithBottomBar
}