package com.aiinterview.simulator.di

import android.content.Context
import com.aiinterview.simulator.data.error.CrashReportingManager
import com.aiinterview.simulator.data.error.ErrorHandler
import com.aiinterview.simulator.data.offline.DegradationManager
import com.aiinterview.simulator.data.offline.OfflineModeManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 错误处理依赖注入模块
 * 提供错误处理和离线模式相关的单例对象
 */
@Module
@InstallIn(SingletonComponent::class)
object ErrorHandlingModule {
    
    /**
     * 提供错误处理器单例
     * @param context 应用上下文
     * @return ErrorHandler实例
     */
    @Provides
    @Singleton
    fun provideErrorHandler(@ApplicationContext context: Context): ErrorHandler {
        return ErrorHandler(context) // 创建错误处理器实例
    }
    
    /**
     * 提供崩溃报告管理器单例
     * @param context 应用上下文
     * @return CrashReportingManager实例
     */
    @Provides
    @Singleton
    fun provideCrashReportingManager(@ApplicationContext context: Context): CrashReportingManager {
        return CrashReportingManager(context) // 创建崩溃报告管理器实例
    }
    
    /**
     * 提供离线模式管理器单例
     * @param context 应用上下文
     * @return OfflineModeManager实例
     */
    @Provides
    @Singleton
    fun provideOfflineModeManager(@ApplicationContext context: Context): OfflineModeManager {
        return OfflineModeManager(context) // 创建离线模式管理器实例
    }
    
    /**
     * 提供降级管理器单例
     * @param context 应用上下文
     * @param offlineModeManager 离线模式管理器
     * @return DegradationManager实例
     */
    @Provides
    @Singleton
    fun provideDegradationManager(
        @ApplicationContext context: Context,
        offlineModeManager: OfflineModeManager
    ): DegradationManager {
        return DegradationManager(context, offlineModeManager) // 创建降级管理器实例
    }
}