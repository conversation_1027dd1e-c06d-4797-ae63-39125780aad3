package com.aiinterview.simulator.presentation.screen.security

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.aiinterview.simulator.data.security.*
import com.aiinterview.simulator.presentation.viewmodel.SecuritySettingsViewModel

/**
 * 安全设置屏幕
 * 允许用户管理安全和隐私相关设置
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SecuritySettingsScreen(
    onNavigateBack: () -> Unit,                                     // 返回导航回调
    viewModel: SecuritySettingsViewModel = hiltViewModel()         // 安全设置ViewModel
) {
    // 收集安全设置状态
    val securityState by viewModel.securityState.collectAsStateWithLifecycle()
    // 收集隐私状态
    val privacyState by viewModel.privacyState.collectAsStateWithLifecycle()
    // 收集清理统计信息
    val cleanupStats by viewModel.cleanupStats.collectAsStateWithLifecycle()
    
    // 在组件首次加载时刷新状态
    LaunchedEffect(Unit) {
        viewModel.refreshSecuritySettings()
    }
    
    // 主界面布局
    Scaffold(
        topBar = {
            // 顶部应用栏
            TopAppBar(
                title = { 
                    Text("安全与隐私") 
                },
                navigationIcon = {
                    // 返回按钮
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        // 主内容区域
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 隐私设置部分
            item {
                PrivacySettingsSection(
                    privacyState = privacyState,
                    onDataCollectionPreferenceChanged = { preference ->
                        viewModel.updateDataCollectionPreference(preference)
                    },
                    onDataRetentionPeriodChanged = { period ->
                        viewModel.updateDataRetentionPeriod(period)
                    },
                    onOpenPrivacyPolicy = {
                        viewModel.openPrivacyPolicy()
                    },
                    onOpenUserAgreement = {
                        viewModel.openUserAgreement()
                    }
                )
            }
            
            // 数据管理部分
            item {
                DataManagementSection(
                    cleanupStats = cleanupStats,
                    onPerformCleanup = {
                        viewModel.performManualCleanup()
                    },
                    onExportUserData = {
                        viewModel.exportUserData()
                    },
                    onDeleteAllData = {
                        viewModel.deleteAllUserData("用户手动删除")
                    }
                )
            }
            
            // 安全功能部分
            item {
                SecurityFeaturesSection(
                    securityState = securityState,
                    onToggleApiSecurity = { enabled ->
                        viewModel.toggleApiSecurity(enabled)
                    },
                    onToggleDataEncryption = { enabled ->
                        viewModel.toggleDataEncryption(enabled)
                    }
                )
            }
            
            // 数据访问日志部分
            item {
                DataAccessLogsSection(
                    onViewAccessLogs = {
                        viewModel.loadDataAccessLogs()
                    }
                )
            }
        }
    }
    
    // 显示加载状态
    if (securityState.isLoading) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator()
        }
    }
    
    // 显示错误信息
    securityState.error?.let { error ->
        LaunchedEffect(error) {
            // 这里可以显示Snackbar或其他错误提示
        }
    }
}

/**
 * 隐私设置部分
 */
@Composable
private fun PrivacySettingsSection(
    privacyState: PrivacyState,
    onDataCollectionPreferenceChanged: (DataCollectionPreference) -> Unit,
    onDataRetentionPeriodChanged: (DataRetentionPeriod) -> Unit,
    onOpenPrivacyPolicy: () -> Unit,
    onOpenUserAgreement: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 部分标题
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Privacy,
                    contentDescription = "隐私设置",
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "隐私设置",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 数据收集偏好设置
            Text(
                text = "数据收集偏好",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            // 数据收集偏好选择器
            DataCollectionPreferenceSelector(
                currentPreference = privacyState.dataCollectionPreference,
                onPreferenceChanged = onDataCollectionPreferenceChanged
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 数据保留期限设置
            Text(
                text = "数据保留期限",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            // 数据保留期限选择器
            DataRetentionPeriodSelector(
                currentPeriod = privacyState.dataRetentionPeriod,
                onPeriodChanged = onDataRetentionPeriodChanged
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 隐私政策和用户协议链接
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedButton(
                    onClick = onOpenPrivacyPolicy,
                    modifier = Modifier.weight(1f)
                ) {
                    Text("隐私政策")
                }
                
                OutlinedButton(
                    onClick = onOpenUserAgreement,
                    modifier = Modifier.weight(1f)
                ) {
                    Text("用户协议")
                }
            }
        }
    }
}

/**
 * 数据收集偏好选择器
 */
@Composable
private fun DataCollectionPreferenceSelector(
    currentPreference: DataCollectionPreference,
    onPreferenceChanged: (DataCollectionPreference) -> Unit
) {
    var expanded by remember { mutableStateOf(false) }
    
    ExposedDropdownMenuBox(
        expanded = expanded,
        onExpandedChange = { expanded = !expanded }
    ) {
        OutlinedTextField(
            value = currentPreference.displayName,
            onValueChange = { },
            readOnly = true,
            label = { Text("数据收集偏好") },
            trailingIcon = {
                ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded)
            },
            modifier = Modifier
                .fillMaxWidth()
                .menuAnchor()
        )
        
        ExposedDropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false }
        ) {
            DataCollectionPreference.values().forEach { preference ->
                DropdownMenuItem(
                    text = {
                        Column {
                            Text(preference.displayName)
                            Text(
                                text = preference.description,
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    },
                    onClick = {
                        onPreferenceChanged(preference)
                        expanded = false
                    }
                )
            }
        }
    }
}

/**
 * 数据保留期限选择器
 */
@Composable
private fun DataRetentionPeriodSelector(
    currentPeriod: DataRetentionPeriod,
    onPeriodChanged: (DataRetentionPeriod) -> Unit
) {
    var expanded by remember { mutableStateOf(false) }
    
    ExposedDropdownMenuBox(
        expanded = expanded,
        onExpandedChange = { expanded = !expanded }
    ) {
        OutlinedTextField(
            value = currentPeriod.displayName,
            onValueChange = { },
            readOnly = true,
            label = { Text("数据保留期限") },
            trailingIcon = {
                ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded)
            },
            modifier = Modifier
                .fillMaxWidth()
                .menuAnchor()
        )
        
        ExposedDropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false }
        ) {
            DataRetentionPeriod.values().forEach { period ->
                DropdownMenuItem(
                    text = { Text(period.displayName) },
                    onClick = {
                        onPeriodChanged(period)
                        expanded = false
                    }
                )
            }
        }
    }
}

/**
 * 数据管理部分
 */
@Composable
private fun DataManagementSection(
    cleanupStats: CleanupStats?,
    onPerformCleanup: () -> Unit,
    onExportUserData: () -> Unit,
    onDeleteAllData: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 部分标题
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Storage,
                    contentDescription = "数据管理",
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "数据管理",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 存储统计信息
            cleanupStats?.let { stats ->
                Text(
                    text = "存储使用情况",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "总文件数：${stats.totalFileCount}",
                    style = MaterialTheme.typography.bodySmall
                )
                Text(
                    text = "总大小：${stats.getFormattedTotalSize()}",
                    style = MaterialTheme.typography.bodySmall
                )
                
                Spacer(modifier = Modifier.height(16.dp))
            }
            
            // 数据管理操作按钮
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = onPerformCleanup,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(
                        imageVector = Icons.Default.CleaningServices,
                        contentDescription = "清理数据"
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("清理临时数据")
                }
                
                OutlinedButton(
                    onClick = onExportUserData,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(
                        imageVector = Icons.Default.Download,
                        contentDescription = "导出数据"
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("导出我的数据")
                }
                
                OutlinedButton(
                    onClick = onDeleteAllData,
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Icon(
                        imageVector = Icons.Default.DeleteForever,
                        contentDescription = "删除所有数据"
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("删除所有数据")
                }
            }
        }
    }
}

/**
 * 安全功能部分
 */
@Composable
private fun SecurityFeaturesSection(
    securityState: SecuritySettingsState,
    onToggleApiSecurity: (Boolean) -> Unit,
    onToggleDataEncryption: (Boolean) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 部分标题
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Security,
                    contentDescription = "安全功能",
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "安全功能",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // API安全开关
            SecurityToggleItem(
                title = "API请求安全",
                description = "启用API请求签名验证和加密传输",
                checked = securityState.apiSecurityEnabled,
                onCheckedChange = onToggleApiSecurity,
                icon = Icons.Default.VpnKey
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 数据加密开关
            SecurityToggleItem(
                title = "本地数据加密",
                description = "启用本地敏感数据加密存储",
                checked = securityState.dataEncryptionEnabled,
                onCheckedChange = onToggleDataEncryption,
                icon = Icons.Default.Lock
            )
        }
    }
}

/**
 * 安全开关项组件
 */
@Composable
private fun SecurityToggleItem(
    title: String,
    description: String,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    icon: ImageVector
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = title,
            modifier = Modifier.size(24.dp),
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.width(12.dp))
        
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = description,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange
        )
    }
}

/**
 * 数据访问日志部分
 */
@Composable
private fun DataAccessLogsSection(
    onViewAccessLogs: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 部分标题
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.History,
                    contentDescription = "访问日志",
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "数据访问记录",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "查看应用对您数据的访问记录，了解数据使用情况",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            OutlinedButton(
                onClick = onViewAccessLogs,
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(
                    imageVector = Icons.Default.Visibility,
                    contentDescription = "查看日志"
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("查看访问日志")
            }
        }
    }
}