package com.aiinterview.simulator.data.handler

import com.aiinterview.simulator.data.manager.InterviewSessionManager
import com.aiinterview.simulator.data.service.TextToSpeechService
import com.aiinterview.simulator.data.speech.SpeechRecognitionService
import com.aiinterview.simulator.data.audio.OfflineRecordingManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class TechnicalIssueHandler @Inject constructor(
    private val sessionManager: InterviewSessionManager,
    private val ttsService: TextToSpeechService,
    private val speechRecognitionService: SpeechRecognitionService,
    private val offlineRecordingManager: OfflineRecordingManager
) {
    
    private val _currentIssue = MutableStateFlow<TechnicalIssue?>(null)
    val currentIssue: StateFlow<TechnicalIssue?> = _currentIssue.asStateFlow()
    
    private val _resolutionOptions = MutableStateFlow<ResolutionOptions?>(null)
    val resolutionOptions: StateFlow<ResolutionOptions?> = _resolutionOptions.asStateFlow()
    
    private val _isHandlingIssue = MutableStateFlow(false)
    val isHandlingIssue: StateFlow<Boolean> = _isHandlingIssue.asStateFlow()
    
    data class TechnicalIssue(
        val id: String,
        val type: IssueType,
        val severity: IssueSeverity,
        val description: String,
        val context: IssueContext,
        val occurredAt: Long,
        val autoRetryAttempts: Int = 0,
        val maxAutoRetryAttempts: Int = 3
    )
    
    enum class IssueType {
        RECORDING_PERMISSION_DENIED,
        RECORDING_DEVICE_UNAVAILABLE,
        RECORDING_FAILED,
        SPEECH_RECOGNITION_FAILED,
        TTS_PLAYBACK_FAILED,
        NETWORK_CONNECTION_LOST,
        FILE_UPLOAD_FAILED,
        QUESTION_GENERATION_FAILED,
        AUDIO_QUALITY_POOR,
        SYSTEM_RESOURCE_LOW,
        UNEXPECTED_ERROR
    }
    
    enum class IssueSeverity {
        LOW,      // 不影响主流程，可以继续
        MEDIUM,   // 影响体验，建议处理
        HIGH,     // 影响功能，需要处理
        CRITICAL  // 阻塞流程，必须处理
    }
    
    data class IssueContext(
        val questionId: String? = null,
        val sessionId: String? = null,
        val audioFile: File? = null,
        val errorMessage: String? = null,
        val stackTrace: String? = null
    )
    
    data class ResolutionOptions(
        val primaryActions: List<ResolutionAction>,
        val alternativeActions: List<ResolutionAction>,
        val preventiveActions: List<ResolutionAction>,
        val userGuidance: String,
        val estimatedResolutionTime: Long
    )
    
    data class ResolutionAction(
        val id: String,
        val title: String,
        val description: String,
        val type: ActionType,
        val isRecommended: Boolean = false,
        val requiresUserInput: Boolean = false,
        val estimatedTime: Long = 0L
    )
    
    enum class ActionType {
        RETRY_OPERATION,
        SWITCH_PROVIDER,
        USE_ALTERNATIVE_METHOD,
        SKIP_STEP,
        MANUAL_INPUT,
        RESTART_COMPONENT,
        CHECK_PERMISSIONS,
        OFFLINE_MODE,
        CONTACT_SUPPORT
    )
    
    /**
     * 处理录音权限被拒绝
     */
    suspend fun handleRecordingPermissionDenied(questionId: String): ResolutionOptions {
        val issue = TechnicalIssue(
            id = generateIssueId(),
            type = IssueType.RECORDING_PERMISSION_DENIED,
            severity = IssueSeverity.CRITICAL,
            description = "录音权限被拒绝，无法进行语音录制",
            context = IssueContext(questionId = questionId),
            occurredAt = System.currentTimeMillis()
        )
        
        _currentIssue.value = issue
        
        val options = ResolutionOptions(
            primaryActions = listOf(
                ResolutionAction(
                    id = "grant_permission",
                    title = "授予录音权限",
                    description = "前往设置页面授予应用录音权限",
                    type = ActionType.CHECK_PERMISSIONS,
                    isRecommended = true,
                    requiresUserInput = true
                )
            ),
            alternativeActions = listOf(
                ResolutionAction(
                    id = "text_input",
                    title = "文字输入",
                    description = "使用文字输入方式回答问题",
                    type = ActionType.USE_ALTERNATIVE_METHOD
                ),
                ResolutionAction(
                    id = "skip_question",
                    title = "跳过问题",
                    description = "跳过当前问题，继续下一题",
                    type = ActionType.SKIP_STEP
                )
            ),
            preventiveActions = listOf(
                ResolutionAction(
                    id = "check_permissions_startup",
                    title = "启动时检查权限",
                    description = "在面试开始前确保所有必要权限已授予",
                    type = ActionType.CHECK_PERMISSIONS
                )
            ),
            userGuidance = "录音权限是面试必需的功能。请前往设置页面授予录音权限，或选择其他回答方式。",
            estimatedResolutionTime = 60000L // 1分钟
        )
        
        _resolutionOptions.value = options
        return options
    }
    
    /**
     * 处理录音失败
     */
    suspend fun handleRecordingFailed(
        questionId: String,
        audioFile: File?,
        errorMessage: String
    ): ResolutionOptions {
        val issue = TechnicalIssue(
            id = generateIssueId(),
            type = IssueType.RECORDING_FAILED,
            severity = IssueSeverity.HIGH,
            description = "录音过程中发生错误: $errorMessage",
            context = IssueContext(
                questionId = questionId,
                audioFile = audioFile,
                errorMessage = errorMessage
            ),
            occurredAt = System.currentTimeMillis()
        )
        
        _currentIssue.value = issue
        
        val options = ResolutionOptions(
            primaryActions = listOf(
                ResolutionAction(
                    id = "retry_recording",
                    title = "重新录制",
                    description = "重新开始录制回答",
                    type = ActionType.RETRY_OPERATION,
                    isRecommended = true
                ),
                ResolutionAction(
                    id = "check_microphone",
                    title = "检查麦克风",
                    description = "检查麦克风是否正常工作",
                    type = ActionType.CHECK_PERMISSIONS,
                    requiresUserInput = true
                )
            ),
            alternativeActions = listOf(
                ResolutionAction(
                    id = "text_input",
                    title = "文字输入",
                    description = "改用文字方式输入回答",
                    type = ActionType.USE_ALTERNATIVE_METHOD
                ),
                ResolutionAction(
                    id = "offline_recording",
                    title = "离线录制",
                    description = "使用离线模式录制，稍后上传",
                    type = ActionType.OFFLINE_MODE
                )
            ),
            preventiveActions = listOf(
                ResolutionAction(
                    id = "test_recording",
                    title = "录音测试",
                    description = "面试前进行录音功能测试",
                    type = ActionType.CHECK_PERMISSIONS
                )
            ),
            userGuidance = "录音功能出现问题。建议重新尝试录制，或检查设备麦克风是否正常。",
            estimatedResolutionTime = 30000L // 30秒
        )
        
        _resolutionOptions.value = options
        return options
    }
    
    /**
     * 处理语音识别失败
     */
    suspend fun handleSpeechRecognitionFailed(
        questionId: String,
        audioFile: File,
        provider: String,
        errorMessage: String
    ): ResolutionOptions {
        val issue = TechnicalIssue(
            id = generateIssueId(),
            type = IssueType.SPEECH_RECOGNITION_FAILED,
            severity = IssueSeverity.MEDIUM,
            description = "语音识别失败 ($provider): $errorMessage",
            context = IssueContext(
                questionId = questionId,
                audioFile = audioFile,
                errorMessage = errorMessage
            ),
            occurredAt = System.currentTimeMillis()
        )
        
        _currentIssue.value = issue
        
        val options = ResolutionOptions(
            primaryActions = listOf(
                ResolutionAction(
                    id = "retry_recognition",
                    title = "重新识别",
                    description = "使用相同服务重新进行语音识别",
                    type = ActionType.RETRY_OPERATION,
                    isRecommended = true
                ),
                ResolutionAction(
                    id = "switch_provider",
                    title = "切换服务商",
                    description = "尝试使用其他语音识别服务",
                    type = ActionType.SWITCH_PROVIDER,
                    isRecommended = true
                )
            ),
            alternativeActions = listOf(
                ResolutionAction(
                    id = "manual_edit",
                    title = "手动编辑",
                    description = "手动输入或编辑识别结果",
                    type = ActionType.MANUAL_INPUT
                ),
                ResolutionAction(
                    id = "re_record",
                    title = "重新录制",
                    description = "重新录制更清晰的音频",
                    type = ActionType.RETRY_OPERATION
                ),
                ResolutionAction(
                    id = "offline_mode",
                    title = "离线处理",
                    description = "保存录音，稍后处理",
                    type = ActionType.OFFLINE_MODE
                )
            ),
            preventiveActions = listOf(
                ResolutionAction(
                    id = "improve_audio_quality",
                    title = "改善录音质量",
                    description = "在安静环境中清晰录制",
                    type = ActionType.USE_ALTERNATIVE_METHOD
                )
            ),
            userGuidance = "语音识别遇到问题。建议尝试其他识别服务或手动编辑结果。",
            estimatedResolutionTime = 45000L // 45秒
        )
        
        _resolutionOptions.value = options
        return options
    }
    
    /**
     * 处理TTS播放失败
     */
    suspend fun handleTTSPlaybackFailed(
        questionId: String,
        text: String,
        errorMessage: String
    ): ResolutionOptions {
        val issue = TechnicalIssue(
            id = generateIssueId(),
            type = IssueType.TTS_PLAYBACK_FAILED,
            severity = IssueSeverity.LOW,
            description = "语音播放失败: $errorMessage",
            context = IssueContext(
                questionId = questionId,
                errorMessage = errorMessage
            ),
            occurredAt = System.currentTimeMillis()
        )
        
        _currentIssue.value = issue
        
        val options = ResolutionOptions(
            primaryActions = listOf(
                ResolutionAction(
                    id = "retry_tts",
                    title = "重新播放",
                    description = "重新尝试语音播放",
                    type = ActionType.RETRY_OPERATION,
                    isRecommended = true
                ),
                ResolutionAction(
                    id = "switch_tts_provider",
                    title = "切换语音服务",
                    description = "使用其他TTS服务商",
                    type = ActionType.SWITCH_PROVIDER
                )
            ),
            alternativeActions = listOf(
                ResolutionAction(
                    id = "text_only",
                    title = "仅显示文字",
                    description = "跳过语音播放，直接显示文字",
                    type = ActionType.USE_ALTERNATIVE_METHOD
                ),
                ResolutionAction(
                    id = "continue_without_audio",
                    title = "继续面试",
                    description = "继续面试流程，不影响答题",
                    type = ActionType.SKIP_STEP
                )
            ),
            preventiveActions = listOf(
                ResolutionAction(
                    id = "test_audio_output",
                    title = "测试音频输出",
                    description = "面试前测试设备音频播放功能",
                    type = ActionType.CHECK_PERMISSIONS
                )
            ),
            userGuidance = "语音播放功能出现问题，但不影响面试进行。您可以直接阅读题目内容。",
            estimatedResolutionTime = 15000L // 15秒
        )
        
        _resolutionOptions.value = options
        return options
    }
    
    /**
     * 处理网络连接丢失
     */
    suspend fun handleNetworkConnectionLost(
        questionId: String? = null,
        operation: String = "未知操作"
    ): ResolutionOptions {
        val issue = TechnicalIssue(
            id = generateIssueId(),
            type = IssueType.NETWORK_CONNECTION_LOST,
            severity = IssueSeverity.HIGH,
            description = "网络连接丢失，影响操作: $operation",
            context = IssueContext(
                questionId = questionId,
                errorMessage = "网络连接不可用"
            ),
            occurredAt = System.currentTimeMillis()
        )
        
        _currentIssue.value = issue
        
        val options = ResolutionOptions(
            primaryActions = listOf(
                ResolutionAction(
                    id = "check_network",
                    title = "检查网络",
                    description = "检查网络连接状态",
                    type = ActionType.CHECK_PERMISSIONS,
                    isRecommended = true,
                    requiresUserInput = true
                ),
                ResolutionAction(
                    id = "retry_connection",
                    title = "重试连接",
                    description = "重新尝试网络连接",
                    type = ActionType.RETRY_OPERATION
                )
            ),
            alternativeActions = listOf(
                ResolutionAction(
                    id = "offline_mode",
                    title = "离线模式",
                    description = "切换到离线模式继续面试",
                    type = ActionType.OFFLINE_MODE,
                    isRecommended = true
                ),
                ResolutionAction(
                    id = "save_progress",
                    title = "保存进度",
                    description = "保存当前面试进度",
                    type = ActionType.USE_ALTERNATIVE_METHOD
                )
            ),
            preventiveActions = listOf(
                ResolutionAction(
                    id = "stable_network",
                    title = "确保网络稳定",
                    description = "面试前确保网络连接稳定",
                    type = ActionType.CHECK_PERMISSIONS
                )
            ),
            userGuidance = "网络连接出现问题。建议切换到离线模式继续面试，数据将在网络恢复后自动同步。",
            estimatedResolutionTime = 120000L // 2分钟
        )
        
        _resolutionOptions.value = options
        return options
    }
    
    /**
     * 执行解决方案
     */
    suspend fun executeResolution(actionId: String): Result<String> {
        _isHandlingIssue.value = true
        
        return try {
            val result = when (actionId) {
                "retry_recording" -> handleRetryRecording()
                "retry_recognition" -> handleRetryRecognition()
                "switch_provider" -> handleSwitchProvider()
                "text_input" -> handleTextInput()
                "offline_mode" -> handleOfflineMode()
                "skip_question" -> handleSkipQuestion()
                "manual_edit" -> handleManualEdit()
                "retry_tts" -> handleRetryTTS()
                else -> Result.failure(Exception("未知的解决方案: $actionId"))
            }
            
            if (result.isSuccess) {
                markIssueResolved(actionId)
            }
            
            result
        } finally {
            _isHandlingIssue.value = false
        }
    }
    
    /**
     * 处理重新录制
     */
    private suspend fun handleRetryRecording(): Result<String> {
        return try {
            // 清理之前的录音文件
            _currentIssue.value?.context?.audioFile?.delete()
            
            Result.success("已准备重新录制，请点击录音按钮开始")
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 处理重新识别
     */
    private suspend fun handleRetryRecognition(): Result<String> {
        val audioFile = _currentIssue.value?.context?.audioFile
            ?: return Result.failure(Exception("没有可用的音频文件"))
        
        return try {
            val result = speechRecognitionService.recognizeAudio(
                audioFile,
                SpeechRecognitionService.Provider.AUTO
            )
            
            if (result.success) {
                Result.success("语音识别成功: ${result.text}")
            } else {
                Result.failure(Exception("重新识别失败: ${result.errorMessage}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 处理切换服务商
     */
    private suspend fun handleSwitchProvider(): Result<String> {
        return Result.success("已切换到备用服务商，请重试操作")
    }
    
    /**
     * 处理文字输入
     */
    private suspend fun handleTextInput(): Result<String> {
        return Result.success("已切换到文字输入模式")
    }
    
    /**
     * 处理离线模式
     */
    private suspend fun handleOfflineMode(): Result<String> {
        return try {
            // 这里可以保存当前状态到离线存储
            Result.success("已切换到离线模式，数据将在网络恢复后同步")
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 处理跳过问题
     */
    private suspend fun handleSkipQuestion(): Result<String> {
        return Result.success("已跳过当前问题")
    }
    
    /**
     * 处理手动编辑
     */
    private suspend fun handleManualEdit(): Result<String> {
        return Result.success("已启用手动编辑模式")
    }
    
    /**
     * 处理重新TTS
     */
    private suspend fun handleRetryTTS(): Result<String> {
        return try {
            // 停止当前播放
            ttsService.stopAudio()
            Result.success("已准备重新播放语音")
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 标记问题已解决
     */
    private fun markIssueResolved(resolution: String) {
        val currentIssue = _currentIssue.value
        if (currentIssue != null) {
            sessionManager.resolveTechnicalIssue(currentIssue.id, resolution)
        }
        _currentIssue.value = null
        _resolutionOptions.value = null
    }
    
    /**
     * 生成问题ID
     */
    private fun generateIssueId(): String {
        return "issue_${System.currentTimeMillis()}_${(1000..9999).random()}"
    }
    
    /**
     * 获取问题严重程度描述
     */
    fun getSeverityDescription(severity: IssueSeverity): String {
        return when (severity) {
            IssueSeverity.LOW -> "轻微问题"
            IssueSeverity.MEDIUM -> "一般问题"
            IssueSeverity.HIGH -> "严重问题"
            IssueSeverity.CRITICAL -> "关键问题"
        }
    }
    
    /**
     * 获取问题类型描述
     */
    fun getIssueTypeDescription(type: IssueType): String {
        return when (type) {
            IssueType.RECORDING_PERMISSION_DENIED -> "录音权限被拒绝"
            IssueType.RECORDING_DEVICE_UNAVAILABLE -> "录音设备不可用"
            IssueType.RECORDING_FAILED -> "录音失败"
            IssueType.SPEECH_RECOGNITION_FAILED -> "语音识别失败"
            IssueType.TTS_PLAYBACK_FAILED -> "语音播放失败"
            IssueType.NETWORK_CONNECTION_LOST -> "网络连接丢失"
            IssueType.FILE_UPLOAD_FAILED -> "文件上传失败"
            IssueType.QUESTION_GENERATION_FAILED -> "问题生成失败"
            IssueType.AUDIO_QUALITY_POOR -> "音频质量差"
            IssueType.SYSTEM_RESOURCE_LOW -> "系统资源不足"
            IssueType.UNEXPECTED_ERROR -> "意外错误"
        }
    }
    
    /**
     * 清除当前问题
     */
    fun clearCurrentIssue() {
        _currentIssue.value = null
        _resolutionOptions.value = null
    }
}