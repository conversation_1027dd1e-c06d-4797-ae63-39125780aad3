package com.aiinterview.simulator.data.security

import android.content.Context
import android.content.Intent
import android.net.Uri
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 隐私保护管理器
 * 负责管理用户隐私保护相关功能
 */
@Singleton
class PrivacyManager @Inject constructor(
    @ApplicationContext private val context: Context,           // 注入应用上下文
    private val encryptedStorageManager: EncryptedStorageManager // 注入加密存储管理器
) {
    
    companion object {
        // 隐私政策版本
        private const val PRIVACY_POLICY_VERSION = "1.0.0"
        // 用户协议版本
        private const val USER_AGREEMENT_VERSION = "1.0.0"
        // 隐私政策URL
        private const val PRIVACY_POLICY_URL = "https://aiinterview.com/privacy"
        // 用户协议URL
        private const val USER_AGREEMENT_URL = "https://aiinterview.com/agreement"
    }
    
    // 隐私状态的私有可变状态流
    private val _privacyState = MutableStateFlow(PrivacyState())
    // 对外暴露的隐私状态只读状态流
    val privacyState: StateFlow<PrivacyState> = _privacyState.asStateFlow()
    
    init {
        // 初始化时加载隐私设置
        loadPrivacySettings()
    }
    
    /**
     * 检查用户是否已同意隐私政策
     * @return true表示已同意，false表示未同意
     */
    fun hasUserAcceptedPrivacyPolicy(): Boolean {
        val acceptedVersion = encryptedStorageManager.getString("privacy_policy_accepted_version")
        return acceptedVersion == PRIVACY_POLICY_VERSION
    }
    
    /**
     * 检查用户是否已同意用户协议
     * @return true表示已同意，false表示未同意
     */
    fun hasUserAcceptedUserAgreement(): Boolean {
        val acceptedVersion = encryptedStorageManager.getString("user_agreement_accepted_version")
        return acceptedVersion == USER_AGREEMENT_VERSION
    }
    
    /**
     * 用户同意隐私政策
     */
    fun acceptPrivacyPolicy() {
        val timestamp = System.currentTimeMillis()
        encryptedStorageManager.saveString("privacy_policy_accepted_version", PRIVACY_POLICY_VERSION)
        encryptedStorageManager.saveLong("privacy_policy_accepted_time", timestamp)
        
        // 更新状态
        updatePrivacyState()
    }
    
    /**
     * 用户同意用户协议
     */
    fun acceptUserAgreement() {
        val timestamp = System.currentTimeMillis()
        encryptedStorageManager.saveString("user_agreement_accepted_version", USER_AGREEMENT_VERSION)
        encryptedStorageManager.saveLong("user_agreement_accepted_time", timestamp)
        
        // 更新状态
        updatePrivacyState()
    }
    
    /**
     * 用户撤销隐私政策同意
     */
    fun revokePrivacyPolicyConsent() {
        encryptedStorageManager.remove("privacy_policy_accepted_version")
        encryptedStorageManager.remove("privacy_policy_accepted_time")
        
        // 更新状态
        updatePrivacyState()
    }
    
    /**
     * 用户撤销用户协议同意
     */
    fun revokeUserAgreementConsent() {
        encryptedStorageManager.remove("user_agreement_accepted_version")
        encryptedStorageManager.remove("user_agreement_accepted_time")
        
        // 更新状态
        updatePrivacyState()
    }
    
    /**
     * 设置数据收集偏好
     * @param preference 数据收集偏好
     */
    fun setDataCollectionPreference(preference: DataCollectionPreference) {
        encryptedStorageManager.saveString("data_collection_preference", preference.name)
        encryptedStorageManager.saveLong("data_collection_preference_time", System.currentTimeMillis())
        
        // 更新状态
        updatePrivacyState()
    }
    
    /**
     * 获取数据收集偏好
     * @return 数据收集偏好
     */
    fun getDataCollectionPreference(): DataCollectionPreference {
        val preferenceName = encryptedStorageManager.getString("data_collection_preference")
        return try {
            DataCollectionPreference.valueOf(preferenceName ?: DataCollectionPreference.NECESSARY_ONLY.name)
        } catch (e: IllegalArgumentException) {
            DataCollectionPreference.NECESSARY_ONLY
        }
    }
    
    /**
     * 设置数据保留期限
     * @param period 数据保留期限
     */
    fun setDataRetentionPeriod(period: DataRetentionPeriod) {
        encryptedStorageManager.saveString("data_retention_period", period.name)
        encryptedStorageManager.saveLong("data_retention_period_time", System.currentTimeMillis())
        
        // 更新状态
        updatePrivacyState()
    }
    
    /**
     * 获取数据保留期限
     * @return 数据保留期限
     */
    fun getDataRetentionPeriod(): DataRetentionPeriod {
        val periodName = encryptedStorageManager.getString("data_retention_period")
        return try {
            DataRetentionPeriod.valueOf(periodName ?: DataRetentionPeriod.ONE_YEAR.name)
        } catch (e: IllegalArgumentException) {
            DataRetentionPeriod.ONE_YEAR
        }
    }
    
    /**
     * 记录数据访问日志
     * @param dataType 数据类型
     * @param accessType 访问类型
     * @param purpose 访问目的
     */
    fun logDataAccess(dataType: String, accessType: DataAccessType, purpose: String) {
        val logEntry = DataAccessLog(
            id = UUID.randomUUID().toString(),
            dataType = dataType,
            accessType = accessType,
            purpose = purpose,
            timestamp = System.currentTimeMillis(),
            userId = encryptedStorageManager.getUserId()
        )
        
        // 将日志保存到加密存储中
        val logKey = "data_access_log_${logEntry.id}"
        val logJson = logEntry.toJson()
        encryptedStorageManager.saveString(logKey, logJson)
        
        // 清理过期的日志（保留最近30天）
        cleanupOldAccessLogs()
    }
    
    /**
     * 获取数据访问日志
     * @param limit 返回的日志数量限制
     * @return 数据访问日志列表
     */
    fun getDataAccessLogs(limit: Int = 100): List<DataAccessLog> {
        val logs = mutableListOf<DataAccessLog>()
        val allKeys = getAllStorageKeys()
        
        // 筛选出日志键
        val logKeys = allKeys.filter { it.startsWith("data_access_log_") }
        
        // 获取日志并按时间排序
        logKeys.forEach { key ->
            val logJson = encryptedStorageManager.getString(key)
            if (logJson != null) {
                try {
                    val log = DataAccessLog.fromJson(logJson)
                    logs.add(log)
                } catch (e: Exception) {
                    // 忽略无法解析的日志
                }
            }
        }
        
        // 按时间倒序排列并限制数量
        return logs.sortedByDescending { it.timestamp }.take(limit)
    }
    
    /**
     * 清理过期的访问日志
     */
    private fun cleanupOldAccessLogs() {
        val thirtyDaysAgo = System.currentTimeMillis() - (30 * 24 * 60 * 60 * 1000L)
        val allKeys = getAllStorageKeys()
        val logKeys = allKeys.filter { it.startsWith("data_access_log_") }
        
        logKeys.forEach { key ->
            val logJson = encryptedStorageManager.getString(key)
            if (logJson != null) {
                try {
                    val log = DataAccessLog.fromJson(logJson)
                    if (log.timestamp < thirtyDaysAgo) {
                        encryptedStorageManager.remove(key)
                    }
                } catch (e: Exception) {
                    // 删除无法解析的日志
                    encryptedStorageManager.remove(key)
                }
            }
        }
    }
    
    /**
     * 导出用户数据
     * @return 用户数据导出结果
     */
    fun exportUserData(): UserDataExport {
        val userId = encryptedStorageManager.getUserId()
        val phoneNumber = encryptedStorageManager.getPhoneNumber()
        val dataCollectionPreference = getDataCollectionPreference()
        val dataRetentionPeriod = getDataRetentionPeriod()
        val accessLogs = getDataAccessLogs()
        
        return UserDataExport(
            userId = userId,
            phoneNumber = phoneNumber,
            dataCollectionPreference = dataCollectionPreference,
            dataRetentionPeriod = dataRetentionPeriod,
            accessLogs = accessLogs,
            exportTime = System.currentTimeMillis()
        )
    }
    
    /**
     * 删除用户所有数据
     * @param reason 删除原因
     */
    fun deleteAllUserData(reason: String) {
        // 记录数据删除日志
        logDataAccess("all_user_data", DataAccessType.DELETE, "用户请求删除所有数据：$reason")
        
        // 清除所有存储的数据
        encryptedStorageManager.clearAllStoredData()
        
        // 更新状态
        updatePrivacyState()
    }
    
    /**
     * 打开隐私政策页面
     */
    fun openPrivacyPolicy() {
        try {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(PRIVACY_POLICY_URL)).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            // 如果无法打开浏览器，可以显示内置的隐私政策
        }
    }
    
    /**
     * 打开用户协议页面
     */
    fun openUserAgreement() {
        try {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(USER_AGREEMENT_URL)).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            // 如果无法打开浏览器，可以显示内置的用户协议
        }
    }
    
    /**
     * 加载隐私设置
     */
    private fun loadPrivacySettings() {
        updatePrivacyState()
    }
    
    /**
     * 更新隐私状态
     */
    private fun updatePrivacyState() {
        val newState = PrivacyState(
            hasAcceptedPrivacyPolicy = hasUserAcceptedPrivacyPolicy(),
            hasAcceptedUserAgreement = hasUserAcceptedUserAgreement(),
            dataCollectionPreference = getDataCollectionPreference(),
            dataRetentionPeriod = getDataRetentionPeriod(),
            privacyPolicyVersion = PRIVACY_POLICY_VERSION,
            userAgreementVersion = USER_AGREEMENT_VERSION
        )
        _privacyState.value = newState
    }
    
    /**
     * 获取所有存储键（这是一个简化的实现，实际项目中可能需要更复杂的逻辑）
     */
    private fun getAllStorageKeys(): List<String> {
        // 这里返回一个空列表，实际实现中需要从SharedPreferences获取所有键
        return emptyList()
    }
}

/**
 * 隐私状态数据类
 */
data class PrivacyState(
    val hasAcceptedPrivacyPolicy: Boolean = false,                         // 是否已同意隐私政策
    val hasAcceptedUserAgreement: Boolean = false,                         // 是否已同意用户协议
    val dataCollectionPreference: DataCollectionPreference = DataCollectionPreference.NECESSARY_ONLY, // 数据收集偏好
    val dataRetentionPeriod: DataRetentionPeriod = DataRetentionPeriod.ONE_YEAR, // 数据保留期限
    val privacyPolicyVersion: String = "",                                  // 隐私政策版本
    val userAgreementVersion: String = ""                                   // 用户协议版本
)

/**
 * 数据收集偏好枚举
 */
enum class DataCollectionPreference(val displayName: String, val description: String) {
    NECESSARY_ONLY("仅必要数据", "只收集应用正常运行所必需的数据"),
    FUNCTIONAL("功能性数据", "收集用于改善应用功能的数据"),
    ANALYTICS("分析数据", "收集用于分析和优化的数据"),
    ALL("所有数据", "收集所有类型的数据以提供最佳体验")
}

/**
 * 数据保留期限枚举
 */
enum class DataRetentionPeriod(val displayName: String, val days: Int) {
    THREE_MONTHS("3个月", 90),
    SIX_MONTHS("6个月", 180),
    ONE_YEAR("1年", 365),
    TWO_YEARS("2年", 730),
    INDEFINITE("无限期", -1)
}

/**
 * 数据访问类型枚举
 */
enum class DataAccessType {
    READ,       // 读取
    WRITE,      // 写入
    UPDATE,     // 更新
    DELETE      // 删除
}

/**
 * 数据访问日志数据类
 */
data class DataAccessLog(
    val id: String,                     // 日志ID
    val dataType: String,               // 数据类型
    val accessType: DataAccessType,     // 访问类型
    val purpose: String,                // 访问目的
    val timestamp: Long,                // 时间戳
    val userId: String?                 // 用户ID
) {
    /**
     * 转换为JSON字符串
     */
    fun toJson(): String {
        return """
            {
                "id": "$id",
                "dataType": "$dataType",
                "accessType": "${accessType.name}",
                "purpose": "$purpose",
                "timestamp": $timestamp,
                "userId": ${if (userId != null) "\"$userId\"" else "null"}
            }
        """.trimIndent()
    }
    
    companion object {
        /**
         * 从JSON字符串创建实例
         */
        fun fromJson(json: String): DataAccessLog {
            // 这里是一个简化的JSON解析实现
            // 实际项目中应该使用Gson或其他JSON库
            val id = extractJsonValue(json, "id")
            val dataType = extractJsonValue(json, "dataType")
            val accessType = DataAccessType.valueOf(extractJsonValue(json, "accessType"))
            val purpose = extractJsonValue(json, "purpose")
            val timestamp = extractJsonValue(json, "timestamp").toLong()
            val userId = extractJsonValue(json, "userId").takeIf { it != "null" }
            
            return DataAccessLog(id, dataType, accessType, purpose, timestamp, userId)
        }
        
        private fun extractJsonValue(json: String, key: String): String {
            val pattern = "\"$key\":\\s*\"?([^,}\"]+)\"?".toRegex()
            return pattern.find(json)?.groupValues?.get(1) ?: ""
        }
    }
}

/**
 * 用户数据导出数据类
 */
data class UserDataExport(
    val userId: String?,                                    // 用户ID
    val phoneNumber: String?,                               // 手机号
    val dataCollectionPreference: DataCollectionPreference, // 数据收集偏好
    val dataRetentionPeriod: DataRetentionPeriod,          // 数据保留期限
    val accessLogs: List<DataAccessLog>,                   // 访问日志
    val exportTime: Long                                   // 导出时间
)