@echo off
REM AI面试模拟器 - 发布版本打包脚本
REM 此脚本用于自动化构建发布版本的APK文件

echo ========================================
echo AI面试模拟器 - 发布版本打包
echo ========================================

REM 检查是否存在密钥库配置文件
if not exist "keystore.properties" (
    echo 错误：未找到 keystore.properties 文件
    echo 请先配置签名密钥库信息
    pause
    exit /b 1
)

REM 清理之前的构建文件
echo 正在清理之前的构建文件...
call gradlew clean

REM 构建所有渠道的发布版本
echo 正在构建发布版本APK...
call gradlew assembleRelease

REM 检查构建是否成功
if %ERRORLEVEL% neq 0 (
    echo 构建失败！请检查错误信息。
    pause
    exit /b 1
)

echo ========================================
echo 构建完成！
echo ========================================

REM 显示生成的APK文件位置
echo 生成的APK文件位置：
dir /b app\build\outputs\apk\*\release\*.apk

echo.
echo 构建成功完成！
pause