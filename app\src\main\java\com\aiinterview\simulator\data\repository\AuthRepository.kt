package com.aiinterview.simulator.data.repository

import com.aiinterview.simulator.data.api.AuthApi
import com.aiinterview.simulator.data.api.TokenManager
import com.aiinterview.simulator.data.dao.UserDao
import com.aiinterview.simulator.data.dto.request.LoginRequest
import com.aiinterview.simulator.data.dto.request.RegisterRequest
import com.aiinterview.simulator.data.dto.request.RefreshTokenRequest
import com.aiinterview.simulator.data.dto.response.AuthResponse
import com.aiinterview.simulator.data.model.User
import com.aiinterview.simulator.domain.util.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import javax.inject.Singleton

interface AuthRepository {
    suspend fun register(phoneNumber: String, verificationCode: String): Flow<Resource<AuthResponse>>
    suspend fun login(phoneNumber: String, password: String? = null, verificationCode: String? = null): Flow<Resource<AuthResponse>>
    suspend fun refreshToken(): Flow<Resource<AuthResponse>>
    suspend fun logout()
    suspend fun getUserById(userId: String): User?
    suspend fun getUserByPhone(phoneNumber: String): User?
    fun isLoggedIn(): Flow<Boolean>
    fun getCurrentUserId(): Flow<String?>
}

@Singleton
class AuthRepositoryImpl @Inject constructor(
    private val authApi: AuthApi,
    private val userDao: UserDao,
    private val tokenManager: TokenManager
) : AuthRepository {
    override suspend fun register(phoneNumber: String, verificationCode: String): Flow<Resource<AuthResponse>> = flow {
        try {
            emit(Resource.Loading())
            val request = RegisterRequest(phoneNumber, verificationCode)
            val response = authApi.register(request)
            
            if (response.success && response.data != null) {
                // Save user to local database
                userDao.insertUser(response.data.user)
                // Save tokens
                tokenManager.saveTokens(
                    accessToken = response.data.accessToken,
                    refreshToken = response.data.refreshToken,
                    userId = response.data.user.id,
                    phoneNumber = response.data.user.phoneNumber
                )
                emit(Resource.Success(response.data))
            } else {
                emit(Resource.Error(response.message ?: "注册失败"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "网络错误"))
        }
    }
    
    override suspend fun login(phoneNumber: String, password: String? = null, verificationCode: String? = null): Flow<Resource<AuthResponse>> = flow {
        try {
            emit(Resource.Loading())
            val request = LoginRequest(phoneNumber, password, verificationCode)
            val response = authApi.login(request)
            
            if (response.success && response.data != null) {
                // Save user to local database
                userDao.insertUser(response.data.user)
                // Save tokens
                tokenManager.saveTokens(
                    accessToken = response.data.accessToken,
                    refreshToken = response.data.refreshToken,
                    userId = response.data.user.id,
                    phoneNumber = response.data.user.phoneNumber
                )
                emit(Resource.Success(response.data))
            } else {
                emit(Resource.Error(response.message ?: "登录失败"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "网络错误"))
        }
    }
    
    override suspend fun refreshToken(): Flow<Resource<AuthResponse>> = flow {
        try {
            emit(Resource.Loading())
            val refreshToken = tokenManager.getRefreshToken().first()
            if (refreshToken == null) {
                emit(Resource.Error("No refresh token available"))
                return@flow
            }
            
            val request = RefreshTokenRequest(refreshToken)
            val response = authApi.refreshToken(request)
            
            if (response.success && response.data != null) {
                // Update tokens
                tokenManager.saveTokens(
                    accessToken = response.data.accessToken,
                    refreshToken = response.data.refreshToken,
                    userId = response.data.user.id,
                    phoneNumber = response.data.user.phoneNumber
                )
                emit(Resource.Success(response.data))
            } else {
                emit(Resource.Error(response.message ?: "Token刷新失败"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "网络错误"))
        }
    }
    
    override suspend fun logout() {
        tokenManager.clearTokens()
    }
    
    override suspend fun getUserById(userId: String): User? {
        return userDao.getUserById(userId)
    }
    
    override suspend fun getUserByPhone(phoneNumber: String): User? {
        return userDao.getUserByPhone(phoneNumber)
    }
    
    override fun isLoggedIn(): Flow<Boolean> {
        return tokenManager.isLoggedIn()
    }
    
    override fun getCurrentUserId(): Flow<String?> {
        return tokenManager.getUserId()
    }
}