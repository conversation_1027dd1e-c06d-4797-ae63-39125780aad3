package com.aiinterview.simulator.presentation.theme

import androidx.compose.ui.unit.dp

/**
 * 应用尺寸定义
 * 统一管理应用中使用的所有尺寸常量，确保设计一致性
 */
object Dimensions {
    
    // 间距定义 - 用于组件之间的间距
    val SpaceExtraSmall = 4.dp          // 超小间距 - 紧密相关元素
    val SpaceSmall = 8.dp               // 小间距 - 相关元素
    val SpaceMedium = 16.dp             // 中等间距 - 标准组件间距
    val SpaceLarge = 24.dp              // 大间距 - 不同区域间距
    val SpaceExtraLarge = 32.dp         // 超大间距 - 主要区域分隔
    val SpaceHuge = 48.dp               // 巨大间距 - 页面级分隔
    
    // 内边距定义 - 用于组件内部间距
    val PaddingExtraSmall = 4.dp        // 超小内边距 - 紧凑组件
    val PaddingSmall = 8.dp             // 小内边距 - 小型组件
    val PaddingMedium = 16.dp           // 中等内边距 - 标准组件
    val PaddingLarge = 24.dp            // 大内边距 - 大型组件
    val PaddingExtraLarge = 32.dp       // 超大内边距 - 特殊组件
    
    // 圆角定义 - 用于组件圆角
    val CornerExtraSmall = 4.dp         // 超小圆角 - 小型元素
    val CornerSmall = 8.dp              // 小圆角 - 按钮、输入框
    val CornerMedium = 12.dp            // 中等圆角 - 卡片
    val CornerLarge = 16.dp             // 大圆角 - 大型卡片
    val CornerExtraLarge = 24.dp        // 超大圆角 - 对话框
    val CornerFull = 50                 // 全圆角 - 圆形按钮
    
    // 高度定义 - 用于组件高度
    val HeightSmall = 32.dp             // 小高度 - 紧凑按钮
    val HeightMedium = 48.dp            // 中等高度 - 标准按钮
    val HeightLarge = 56.dp             // 大高度 - 重要按钮
    val HeightExtraLarge = 72.dp        // 超大高度 - 特殊按钮
    
    // 宽度定义 - 用于组件宽度
    val WidthSmall = 64.dp              // 小宽度 - 图标按钮
    val WidthMedium = 120.dp            // 中等宽度 - 标准按钮
    val WidthLarge = 200.dp             // 大宽度 - 宽按钮
    val WidthExtraLarge = 280.dp        // 超大宽度 - 特殊组件
    
    // 图标尺寸定义
    val IconExtraSmall = 16.dp          // 超小图标 - 内联图标
    val IconSmall = 20.dp               // 小图标 - 按钮图标
    val IconMedium = 24.dp              // 中等图标 - 标准图标
    val IconLarge = 32.dp               // 大图标 - 重要图标
    val IconExtraLarge = 48.dp          // 超大图标 - 主要图标
    val IconHuge = 64.dp                // 巨大图标 - 装饰图标
    
    // 头像尺寸定义
    val AvatarSmall = 32.dp             // 小头像 - 列表项
    val AvatarMedium = 48.dp            // 中等头像 - 卡片
    val AvatarLarge = 64.dp             // 大头像 - 个人资料
    val AvatarExtraLarge = 96.dp        // 超大头像 - 主要展示
    
    // 分割线尺寸定义
    val DividerThin = 1.dp              // 细分割线 - 列表分隔
    val DividerMedium = 2.dp            // 中等分割线 - 区域分隔
    val DividerThick = 4.dp             // 粗分割线 - 强调分隔
    
    // 阴影高度定义
    val ElevationNone = 0.dp            // 无阴影 - 平面元素
    val ElevationSmall = 2.dp           // 小阴影 - 轻微浮起
    val ElevationMedium = 4.dp          // 中等阴影 - 标准浮起
    val ElevationLarge = 8.dp           // 大阴影 - 明显浮起
    val ElevationExtraLarge = 16.dp     // 超大阴影 - 强烈浮起
    
    // 最小触摸目标尺寸 - 确保可访问性
    val MinTouchTarget = 48.dp          // 最小触摸目标 - 符合无障碍标准
    
    // 应用栏高度定义
    val AppBarHeight = 56.dp            // 应用栏高度 - 标准高度
    val AppBarHeightLarge = 64.dp       // 大应用栏高度 - 突出显示
    
    // 底部导航栏高度定义
    val BottomNavHeight = 80.dp         // 底部导航栏高度 - 标准高度
    
    // 浮动操作按钮尺寸定义
    val FabSmall = 40.dp                // 小浮动按钮 - 次要操作
    val FabMedium = 56.dp               // 中等浮动按钮 - 标准操作
    val FabLarge = 96.dp                // 大浮动按钮 - 主要操作
    
    // 进度条尺寸定义
    val ProgressBarThin = 4.dp          // 细进度条 - 线性进度
    val ProgressBarMedium = 8.dp        // 中等进度条 - 标准进度
    val ProgressBarThick = 12.dp        // 粗进度条 - 突出进度
    
    // 输入框尺寸定义
    val TextFieldHeight = 56.dp         // 输入框高度 - 标准高度
    val TextFieldHeightSmall = 40.dp    // 小输入框高度 - 紧凑布局
    
    // 对话框尺寸定义
    val DialogMinWidth = 280.dp         // 对话框最小宽度
    val DialogMaxWidth = 560.dp         // 对话框最大宽度
    val DialogMinHeight = 140.dp        // 对话框最小高度
    
    // 卡片尺寸定义
    val CardMinHeight = 72.dp           // 卡片最小高度
    val CardImageHeight = 200.dp        // 卡片图片高度
    
    // 列表项尺寸定义
    val ListItemHeight = 56.dp          // 标准列表项高度
    val ListItemHeightSmall = 48.dp     // 小列表项高度
    val ListItemHeightLarge = 72.dp     // 大列表项高度
    
    // 网格间距定义
    val GridSpacing = 8.dp              // 网格间距 - 标准间距
    val GridSpacingSmall = 4.dp         // 小网格间距 - 紧密布局
    val GridSpacingLarge = 16.dp        // 大网格间距 - 宽松布局
}

/**
 * 响应式尺寸定义
 * 根据屏幕尺寸提供不同的尺寸值
 */
object ResponsiveDimensions {
    // 屏幕断点定义
    val CompactWidth = 600.dp           // 紧凑屏幕宽度断点
    val MediumWidth = 840.dp            // 中等屏幕宽度断点
    val ExpandedWidth = 1200.dp         // 扩展屏幕宽度断点
    
    // 内容最大宽度定义
    val ContentMaxWidth = 1200.dp       // 内容最大宽度 - 防止过宽
    val ContentMaxWidthMedium = 800.dp  // 中等内容最大宽度
    val ContentMaxWidthSmall = 600.dp   // 小内容最大宽度
    
    // 侧边栏宽度定义
    val SidebarWidth = 280.dp           // 侧边栏宽度 - 标准宽度
    val SidebarWidthCompact = 240.dp    // 紧凑侧边栏宽度
    val SidebarWidthExpanded = 320.dp   // 扩展侧边栏宽度
}