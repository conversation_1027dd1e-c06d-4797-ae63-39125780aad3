package com.aiinterview.simulator.util

import com.aiinterview.simulator.data.model.*

/**
 * UI测试用的Mock数据工厂
 * 为UI测试提供一致的测试数据
 */
object MockTestData {
    
    /**
     * 创建测试用户数据
     */
    fun createTestUser(
        id: String = "test_user_001",
        phoneNumber: String = "13800138000",
        nickname: String = "测试用户"
    ): User {
        return User(
            id = id,
            phoneNumber = phoneNumber,
            nickname = nickname,
            avatar = null,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis()
        )
    }
    
    /**
     * 创建测试岗位列表
     */
    fun createTestPositions(): List<Position> {
        return listOf(
            Position(
                id = "position_001",
                name = "公务员-行政管理",
                category = "公务员",
                level = "中级",
                description = "负责行政管理相关工作，要求具备良好的组织协调能力",
                requirements = listOf("本科及以上学历", "2年以上工作经验", "良好的沟通能力"),
                interviewConfig = InterviewConfig(
                    duration = 20,
                    questionCount = 4,
                    questionTypes = listOf(
                        QuestionType.COMPREHENSIVE_ANALYSIS,
                        QuestionType.PLANNING_ORGANIZATION,
                        QuestionType.INTERPERSONAL_RELATIONS,
                        QuestionType.EMERGENCY_RESPONSE
                    ),
                    timeWarnings = listOf(5, 2)
                )
            ),
            Position(
                id = "position_002",
                name = "公务员-财务管理",
                category = "公务员",
                level = "高级",
                description = "负责财务管理和预算编制工作",
                requirements = listOf("财务相关专业", "3年以上工作经验", "持有会计师证书"),
                interviewConfig = InterviewConfig(
                    duration = 25,
                    questionCount = 5,
                    questionTypes = listOf(
                        QuestionType.COMPREHENSIVE_ANALYSIS,
                        QuestionType.PLANNING_ORGANIZATION,
                        QuestionType.VERBAL_EXPRESSION
                    ),
                    timeWarnings = listOf(5, 2)
                )
            ),
            Position(
                id = "position_003",
                name = "公务员-人力资源",
                category = "公务员",
                level = "初级",
                description = "负责人力资源管理和招聘工作",
                requirements = listOf("人力资源相关专业", "1年以上工作经验"),
                interviewConfig = InterviewConfig(
                    duration = 15,
                    questionCount = 3,
                    questionTypes = listOf(
                        QuestionType.INTERPERSONAL_RELATIONS,
                        QuestionType.PLANNING_ORGANIZATION,
                        QuestionType.VERBAL_EXPRESSION
                    ),
                    timeWarnings = listOf(3)
                )
            )
        )
    }
    
    /**
     * 创建测试问题列表
     */
    fun createTestQuestions(): List<Question> {
        return listOf(
            Question(
                id = "question_001",
                type = QuestionType.COMPREHENSIVE_ANALYSIS,
                category = "综合分析",
                content = "请谈谈你对当前数字化政务服务发展的看法，以及如何提升政务服务效率？",
                backgroundInfo = "这是一道综合分析题，主要考查考生对时事政策的理解和分析能力",
                keyPoints = listOf(
                    "数字化政务服务的重要意义",
                    "当前存在的问题和挑战",
                    "具体的改进措施和建议",
                    "个人的思考和见解"
                ),
                timeLimit = 5,
                difficulty = 3
            ),
            Question(
                id = "question_002",
                type = QuestionType.PLANNING_ORGANIZATION,
                category = "计划组织",
                content = "假如你是某部门的负责人，需要组织一次面向市民的政策宣传活动，你会如何策划和实施？",
                backgroundInfo = "这是一道计划组织题，考查考生的组织协调和统筹规划能力",
                keyPoints = listOf(
                    "活动目标和意义",
                    "前期准备工作",
                    "活动实施方案",
                    "效果评估和总结"
                ),
                timeLimit = 5,
                difficulty = 3
            ),
            Question(
                id = "question_003",
                type = QuestionType.INTERPERSONAL_RELATIONS,
                category = "人际关系",
                content = "在工作中，你发现同事经常迟到早退，影响了团队工作效率，你会如何处理这种情况？",
                backgroundInfo = "这是一道人际关系题，考查考生处理人际关系和解决问题的能力",
                keyPoints = listOf(
                    "正确认识问题的性质",
                    "与同事的沟通方式",
                    "寻求合适的解决方案",
                    "维护团队和谐"
                ),
                timeLimit = 5,
                difficulty = 2
            ),
            Question(
                id = "question_004",
                type = QuestionType.EMERGENCY_RESPONSE,
                category = "应急应变",
                content = "在一次重要会议中，突然停电了，会议无法正常进行，作为会议组织者，你会如何应对？",
                backgroundInfo = "这是一道应急应变题，考查考生在突发情况下的应变能力和处理能力",
                keyPoints = listOf(
                    "快速评估情况",
                    "制定应急方案",
                    "有效沟通协调",
                    "确保会议目标达成"
                ),
                timeLimit = 5,
                difficulty = 4
            )
        )
    }
    
    /**
     * 创建测试面试会话
     */
    fun createTestInterviewSession(
        userId: String = "test_user_001",
        positionId: String = "position_001"
    ): InterviewSession {
        return InterviewSession(
            id = "session_001",
            userId = userId,
            positionId = positionId,
            status = InterviewStatus.IN_PROGRESS,
            startTime = System.currentTimeMillis() - 300000, // 5分钟前开始
            endTime = null,
            currentQuestionIndex = 1,
            questions = createTestQuestions(),
            answers = listOf(
                Answer(
                    questionId = "question_001",
                    audioUrl = "https://example.com/audio/answer_001.mp3",
                    transcription = "我认为数字化政务服务是提升政府服务效率的重要手段...",
                    duration = 180,
                    submittedAt = System.currentTimeMillis() - 240000
                )
            ),
            evaluation = null
        )
    }
    
    /**
     * 创建测试面试评价
     */
    fun createTestEvaluation(sessionId: String = "session_001"): InterviewEvaluation {
        return InterviewEvaluation(
            sessionId = sessionId,
            overallScore = 85.5,
            dimensions = mapOf(
                "内容完整性" to DimensionScore(82.0, 100.0, "回答内容较为完整，涵盖了主要要点"),
                "逻辑性" to DimensionScore(88.0, 100.0, "逻辑清晰，条理分明"),
                "语言流畅度" to DimensionScore(90.0, 100.0, "表达流畅，语言规范"),
                "时间掌控" to DimensionScore(83.0, 100.0, "时间控制基本合理，可以更加精准")
            ),
            feedback = "整体表现良好，回答内容充实，逻辑清晰。建议在内容深度和时间掌控方面进一步提升。",
            suggestions = listOf(
                "加强对热点问题的深入分析和思考",
                "提高论证的说服力和针对性",
                "注意回答的结构化和层次性",
                "合理分配各部分内容的时间"
            ),
            createdAt = System.currentTimeMillis()
        )
    }
    
    /**
     * 创建测试面试记录列表
     */
    fun createTestInterviewRecords(userId: String = "test_user_001"): List<InterviewRecord> {
        val now = System.currentTimeMillis()
        return listOf(
            InterviewRecord(
                id = "record_001",
                userId = userId,
                positionName = "公务员-行政管理",
                interviewDate = now - 86400000, // 1天前
                overallScore = 85.5,
                status = "已完成",
                duration = 1200 // 20分钟
            ),
            InterviewRecord(
                id = "record_002",
                userId = userId,
                positionName = "公务员-财务管理",
                interviewDate = now - 172800000, // 2天前
                overallScore = 78.0,
                status = "已完成",
                duration = 1500 // 25分钟
            ),
            InterviewRecord(
                id = "record_003",
                userId = userId,
                positionName = "公务员-人力资源",
                interviewDate = now - 259200000, // 3天前
                overallScore = 82.5,
                status = "已完成",
                duration = 900 // 15分钟
            )
        )
    }
    
    /**
     * 创建测试进步分析数据
     */
    fun createTestProgressAnalysis(userId: String = "test_user_001"): ProgressAnalysis {
        return ProgressAnalysis(
            userId = userId,
            totalInterviews = 10,
            averageScore = 82.5,
            improvementRate = 15.2,
            strongPoints = listOf("逻辑清晰", "表达流畅", "时间控制良好"),
            weakPoints = listOf("内容深度有待提升", "论证说服力需要加强"),
            scoreHistory = listOf(70.0, 72.5, 75.0, 78.0, 80.5, 82.0, 83.5, 81.0, 84.0, 85.5),
            lastUpdated = System.currentTimeMillis()
        )
    }
    
    /**
     * 创建测试错误消息
     */
    object ErrorMessages {
        const val NETWORK_ERROR = "网络连接失败，请检查网络设置"
        const val LOGIN_FAILED = "登录失败，请检查手机号和验证码"
        const val INVALID_PHONE = "请输入正确的手机号码"
        const val INVALID_CODE = "请输入6位验证码"
        const val PERMISSION_DENIED = "需要录音权限才能进行面试"
        const val AUDIO_RECORD_FAILED = "录音失败，请重试"
        const val UPLOAD_FAILED = "上传失败，请检查网络连接"
    }
    
    /**
     * 创建测试成功消息
     */
    object SuccessMessages {
        const val LOGIN_SUCCESS = "登录成功"
        const val REGISTER_SUCCESS = "注册成功"
        const val INTERVIEW_STARTED = "面试已开始"
        const val ANSWER_SUBMITTED = "答案提交成功"
        const val INTERVIEW_COMPLETED = "面试完成"
    }
}