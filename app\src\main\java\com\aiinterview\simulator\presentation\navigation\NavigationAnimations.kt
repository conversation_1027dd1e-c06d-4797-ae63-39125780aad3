package com.aiinterview.simulator.presentation.navigation

import androidx.compose.animation.*
import androidx.compose.animation.core.tween
import androidx.navigation.NavBackStackEntry

/**
 * 导航动画配置类
 * 定义页面切换时的动画效果
 */
object NavigationAnimations {
    
    // 动画持续时间常量
    private const val ANIMATION_DURATION = 300         // 标准动画持续时间（毫秒）
    private const val FAST_ANIMATION_DURATION = 200    // 快速动画持续时间（毫秒）
    
    /**
     * 获取进入动画
     * 页面进入时的动画效果
     * @param targetState 目标状态
     * @param initialState 初始状态
     * @return 进入动画规范
     */
    fun enterTransition(
        targetState: NavBackStackEntry,
        initialState: NavBackStackEntry
    ): EnterTransition {
        val targetRoute = targetState.destination.route    // 获取目标路由
        val initialRoute = initialState.destination.route  // 获取初始路由
        
        return when {
            // 从登录/注册页面进入主页面，使用淡入效果
            isAuthToMain(initialRoute, targetRoute) -> {
                fadeIn(animationSpec = tween(ANIMATION_DURATION)) // 淡入动画
            }
            // 进入面试流程相关页面，使用从右滑入效果
            isInterviewFlow(targetRoute) -> {
                slideInHorizontally(                          // 水平滑入动画
                    initialOffsetX = { it },                  // 从右侧开始
                    animationSpec = tween(ANIMATION_DURATION)
                ) + fadeIn(animationSpec = tween(ANIMATION_DURATION)) // 结合淡入效果
            }
            // 底部导航栏切换，使用淡入效果
            isBottomNavigation(targetRoute) -> {
                fadeIn(animationSpec = tween(FAST_ANIMATION_DURATION)) // 快速淡入
            }
            // 详情页面，使用从下滑入效果
            isDetailPage(targetRoute) -> {
                slideInVertically(                            // 垂直滑入动画
                    initialOffsetY = { it },                  // 从下方开始
                    animationSpec = tween(ANIMATION_DURATION)
                ) + fadeIn(animationSpec = tween(ANIMATION_DURATION)) // 结合淡入效果
            }
            // 默认使用从右滑入效果
            else -> {
                slideInHorizontally(                          // 默认水平滑入
                    initialOffsetX = { it },
                    animationSpec = tween(ANIMATION_DURATION)
                ) + fadeIn(animationSpec = tween(ANIMATION_DURATION))
            }
        }
    }
    
    /**
     * 获取退出动画
     * 页面退出时的动画效果
     * @param targetState 目标状态
     * @param initialState 初始状态
     * @return 退出动画规范
     */
    fun exitTransition(
        targetState: NavBackStackEntry,
        initialState: NavBackStackEntry
    ): ExitTransition {
        val targetRoute = targetState.destination.route    // 获取目标路由
        val initialRoute = initialState.destination.route  // 获取初始路由
        
        return when {
            // 从主页面退出到登录/注册页面，使用淡出效果
            isMainToAuth(initialRoute, targetRoute) -> {
                fadeOut(animationSpec = tween(ANIMATION_DURATION)) // 淡出动画
            }
            // 退出面试流程相关页面，使用向左滑出效果
            isInterviewFlow(initialRoute) -> {
                slideOutHorizontally(                         // 水平滑出动画
                    targetOffsetX = { -it },                  // 向左滑出
                    animationSpec = tween(ANIMATION_DURATION)
                ) + fadeOut(animationSpec = tween(ANIMATION_DURATION)) // 结合淡出效果
            }
            // 底部导航栏切换，使用淡出效果
            isBottomNavigation(initialRoute) -> {
                fadeOut(animationSpec = tween(FAST_ANIMATION_DURATION)) // 快速淡出
            }
            // 详情页面退出，使用向下滑出效果
            isDetailPage(initialRoute) -> {
                slideOutVertically(                           // 垂直滑出动画
                    targetOffsetY = { it },                   // 向下滑出
                    animationSpec = tween(ANIMATION_DURATION)
                ) + fadeOut(animationSpec = tween(ANIMATION_DURATION)) // 结合淡出效果
            }
            // 默认使用向左滑出效果
            else -> {
                slideOutHorizontally(                         // 默认水平滑出
                    targetOffsetX = { -it },
                    animationSpec = tween(ANIMATION_DURATION)
                ) + fadeOut(animationSpec = tween(ANIMATION_DURATION))
            }
        }
    }
    
    /**
     * 获取弹出进入动画
     * 从栈中弹出页面时的进入动画
     * @param targetState 目标状态
     * @param initialState 初始状态
     * @return 弹出进入动画规范
     */
    fun popEnterTransition(
        targetState: NavBackStackEntry,
        initialState: NavBackStackEntry
    ): EnterTransition {
        val targetRoute = targetState.destination.route    // 获取目标路由
        val initialRoute = initialState.destination.route  // 获取初始路由
        
        return when {
            // 返回到底部导航页面，使用淡入效果
            isBottomNavigation(targetRoute) -> {
                fadeIn(animationSpec = tween(FAST_ANIMATION_DURATION)) // 快速淡入
            }
            // 默认使用从左滑入效果（返回效果）
            else -> {
                slideInHorizontally(                          // 水平滑入动画
                    initialOffsetX = { -it },                 // 从左侧开始
                    animationSpec = tween(ANIMATION_DURATION)
                ) + fadeIn(animationSpec = tween(ANIMATION_DURATION)) // 结合淡入效果
            }
        }
    }
    
    /**
     * 获取弹出退出动画
     * 从栈中弹出页面时的退出动画
     * @param targetState 目标状态
     * @param initialState 初始状态
     * @return 弹出退出动画规范
     */
    fun popExitTransition(
        targetState: NavBackStackEntry,
        initialState: NavBackStackEntry
    ): ExitTransition {
        val targetRoute = targetState.destination.route    // 获取目标路由
        val initialRoute = initialState.destination.route  // 获取初始路由
        
        return when {
            // 从底部导航页面弹出，使用淡出效果
            isBottomNavigation(initialRoute) -> {
                fadeOut(animationSpec = tween(FAST_ANIMATION_DURATION)) // 快速淡出
            }
            // 详情页面弹出，使用向下滑出效果
            isDetailPage(initialRoute) -> {
                slideOutVertically(                           // 垂直滑出动画
                    targetOffsetY = { it },                   // 向下滑出
                    animationSpec = tween(ANIMATION_DURATION)
                ) + fadeOut(animationSpec = tween(ANIMATION_DURATION)) // 结合淡出效果
            }
            // 默认使用向右滑出效果（返回效果）
            else -> {
                slideOutHorizontally(                         // 水平滑出动画
                    targetOffsetX = { it },                   // 向右滑出
                    animationSpec = tween(ANIMATION_DURATION)
                ) + fadeOut(animationSpec = tween(ANIMATION_DURATION)) // 结合淡出效果
            }
        }
    }
    
    // 辅助函数：判断是否从认证页面到主页面
    private fun isAuthToMain(initialRoute: String?, targetRoute: String?): Boolean {
        val authRoutes = listOf(NavigationRoutes.LOGIN, NavigationRoutes.REGISTER) // 认证路由列表
        val mainRoutes = listOf(NavigationRoutes.HOME)                             // 主页面路由列表
        return initialRoute in authRoutes && targetRoute in mainRoutes             // 判断路由转换
    }
    
    // 辅助函数：判断是否从主页面到认证页面
    private fun isMainToAuth(initialRoute: String?, targetRoute: String?): Boolean {
        val authRoutes = listOf(NavigationRoutes.LOGIN, NavigationRoutes.REGISTER) // 认证路由列表
        val mainRoutes = listOf(NavigationRoutes.HOME)                             // 主页面路由列表
        return initialRoute in mainRoutes && targetRoute in authRoutes             // 判断路由转换
    }
    
    // 辅助函数：判断是否为面试流程相关页面
    private fun isInterviewFlow(route: String?): Boolean {
        val interviewRoutes = listOf(                                              // 面试流程路由列表
            NavigationRoutes.POSITION_SELECTION,
            NavigationRoutes.INTERVIEW_FLOW,
            "interview_question",           // 匹配带参数的路由前缀
            "speech_recognition",           // 匹配带参数的路由前缀
            "answer_processing"             // 匹配带参数的路由前缀
        )
        return interviewRoutes.any { route?.startsWith(it) == true }               // 检查路由是否匹配
    }
    
    // 辅助函数：判断是否为底部导航页面
    private fun isBottomNavigation(route: String?): Boolean {
        val bottomNavRoutes = listOf(                                              // 底部导航路由列表
            NavigationRoutes.HOME,
            NavigationRoutes.INTERVIEW_HISTORY,
            NavigationRoutes.PROGRESS_ANALYSIS,
            NavigationRoutes.PROFILE
        )
        return route in bottomNavRoutes                                            // 检查是否为底部导航路由
    }
    
    // 辅助函数：判断是否为详情页面
    private fun isDetailPage(route: String?): Boolean {
        val detailRoutes = listOf(                                                 // 详情页面路由前缀列表
            "evaluation_detail",            // 评价详情
            "interview_record_detail",      // 面试记录详情
            "evaluation_report"             // 评价报告
        )
        return detailRoutes.any { route?.startsWith(it) == true }                  // 检查路由是否匹配详情页面
    }
}