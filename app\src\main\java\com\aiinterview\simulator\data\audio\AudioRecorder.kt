package com.aiinterview.simulator.data.audio

import android.content.Context
import android.media.MediaRecorder
import android.os.Build
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.File
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 音频录制器类，负责处理音频录制功能
 * 使用MediaRecorder实现音频录制，支持暂停、恢复、停止等操作
 */
@Singleton
class AudioRecorder @Inject constructor(
    private val context: Context // 应用上下文，用于获取文件路径
) {
    
    // MediaRecorder实例，用于实际的音频录制
    private var mediaRecorder: MediaRecorder? = null
    
    // 当前录音文件
    private var currentAudioFile: File? = null
    
    // 录音状态的StateFlow，用于UI观察录音状态变化
    private val _recordingState = MutableStateFlow(RecordingState.IDLE)
    val recordingState: StateFlow<RecordingState> = _recordingState.asStateFlow()
    
    // 录音时长的StateFlow，以毫秒为单位
    private val _recordingDuration = MutableStateFlow(0L)
    val recordingDuration: StateFlow<Long> = _recordingDuration.asStateFlow()
    
    // 当前录音文件的StateFlow
    private val _currentRecordingFile = MutableStateFlow<File?>(null)
    val currentRecordingFile: StateFlow<File?> = _currentRecordingFile.asStateFlow()
    
    // 错误消息的StateFlow
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    // 录音开始时间，用于计算录音时长
    private var recordingStartTime: Long = 0
    
    // 暂停前的累计时长
    private var pausedDuration: Long = 0
    
    /**
     * 录音状态枚举
     */
    enum class RecordingState {
        IDLE,       // 空闲状态
        RECORDING,  // 正在录音
        PAUSED,     // 暂停录音
        STOPPED,    // 录音已停止
        ERROR       // 录音出错
    }
    
    /**
     * 开始录音
     * @param questionId 问题ID，用于生成录音文件名
     * @return 是否成功开始录音
     */
    suspend fun startRecording(questionId: String): Boolean {
        try {
            // 清除之前的错误消息
            _errorMessage.value = null
            
            // 创建录音文件
            currentAudioFile = createAudioFile(questionId)
            
            // 初始化MediaRecorder
            mediaRecorder = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                MediaRecorder(context) // Android 12及以上版本
            } else {
                @Suppress("DEPRECATION")
                MediaRecorder() // Android 12以下版本
            }
            
            // 配置MediaRecorder
            mediaRecorder?.apply {
                setAudioSource(MediaRecorder.AudioSource.MIC) // 设置音频源为麦克风
                setOutputFormat(MediaRecorder.OutputFormat.MPEG_4) // 设置输出格式为MP4
                setAudioEncoder(MediaRecorder.AudioEncoder.AAC) // 设置音频编码器为AAC
                setAudioSamplingRate(16000) // 设置采样率为16kHz
                setAudioEncodingBitRate(64000) // 设置音频编码比特率
                setOutputFile(currentAudioFile?.absolutePath) // 设置输出文件路径
                
                prepare() // 准备录音
                start() // 开始录音
            }
            
            // 更新状态
            _recordingState.value = RecordingState.RECORDING
            _currentRecordingFile.value = currentAudioFile
            recordingStartTime = System.currentTimeMillis()
            pausedDuration = 0
            
            // 开始计时
            startDurationTimer()
            
            return true
            
        } catch (e: IOException) {
            // IO异常处理
            _errorMessage.value = "录音初始化失败: ${e.message}"
            _recordingState.value = RecordingState.ERROR
            releaseRecorder()
            return false
        } catch (e: Exception) {
            // 其他异常处理
            _errorMessage.value = "录音启动失败: ${e.message}"
            _recordingState.value = RecordingState.ERROR
            releaseRecorder()
            return false
        }
    }
    
    /**
     * 暂停录音
     * 注意：MediaRecorder在某些Android版本上不支持暂停，这里提供接口但实际效果可能有限
     */
    fun pauseRecording() {
        try {
            if (_recordingState.value == RecordingState.RECORDING) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    mediaRecorder?.pause() // Android 7.0及以上支持暂停
                    _recordingState.value = RecordingState.PAUSED
                    
                    // 记录暂停前的累计时长
                    pausedDuration += System.currentTimeMillis() - recordingStartTime
                } else {
                    // 低版本Android不支持暂停，显示提示信息
                    _errorMessage.value = "当前系统版本不支持暂停录音功能"
                }
            }
        } catch (e: Exception) {
            _errorMessage.value = "暂停录音失败: ${e.message}"
            _recordingState.value = RecordingState.ERROR
        }
    }
    
    /**
     * 恢复录音
     */
    fun resumeRecording() {
        try {
            if (_recordingState.value == RecordingState.PAUSED) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    mediaRecorder?.resume() // 恢复录音
                    _recordingState.value = RecordingState.RECORDING
                    recordingStartTime = System.currentTimeMillis() // 重新记录开始时间
                } else {
                    _errorMessage.value = "当前系统版本不支持恢复录音功能"
                }
            }
        } catch (e: Exception) {
            _errorMessage.value = "恢复录音失败: ${e.message}"
            _recordingState.value = RecordingState.ERROR
        }
    }
    
    /**
     * 停止录音
     * @return 录音文件，如果录音失败则返回null
     */
    fun stopRecording(): File? {
        try {
            if (_recordingState.value == RecordingState.RECORDING || 
                _recordingState.value == RecordingState.PAUSED) {
                
                mediaRecorder?.apply {
                    stop() // 停止录音
                    release() // 释放资源
                }
                mediaRecorder = null
                
                _recordingState.value = RecordingState.STOPPED
                
                // 检查录音文件是否有效
                val audioFile = currentAudioFile
                if (audioFile != null && audioFile.exists() && audioFile.length() > 0) {
                    return audioFile
                } else {
                    _errorMessage.value = "录音文件无效或为空"
                    return null
                }
            }
            return null
            
        } catch (e: Exception) {
            _errorMessage.value = "停止录音失败: ${e.message}"
            _recordingState.value = RecordingState.ERROR
            releaseRecorder()
            return null
        }
    }
    
    /**
     * 取消录音
     * 停止录音并删除录音文件
     */
    fun cancelRecording() {
        try {
            // 停止录音
            if (_recordingState.value == RecordingState.RECORDING || 
                _recordingState.value == RecordingState.PAUSED) {
                mediaRecorder?.apply {
                    stop()
                    release()
                }
                mediaRecorder = null
            }
            
            // 删除录音文件
            currentAudioFile?.let { file ->
                if (file.exists()) {
                    file.delete()
                }
            }
            
            // 重置状态
            _recordingState.value = RecordingState.IDLE
            _currentRecordingFile.value = null
            _recordingDuration.value = 0L
            _errorMessage.value = null
            currentAudioFile = null
            
        } catch (e: Exception) {
            _errorMessage.value = "取消录音失败: ${e.message}"
            _recordingState.value = RecordingState.ERROR
        }
    }
    
    /**
     * 重置录音器状态
     */
    fun resetRecording() {
        releaseRecorder()
        _recordingState.value = RecordingState.IDLE
        _currentRecordingFile.value = null
        _recordingDuration.value = 0L
        _errorMessage.value = null
        currentAudioFile = null
        pausedDuration = 0
    }
    
    /**
     * 获取录音时长（格式化为字符串）
     * @param duration 时长（毫秒）
     * @return 格式化的时长字符串（MM:SS）
     */
    fun formatDuration(duration: Long): String {
        val totalSeconds = duration / 1000
        val minutes = totalSeconds / 60
        val seconds = totalSeconds % 60
        return String.format("%02d:%02d", minutes, seconds)
    }
    
    /**
     * 创建录音文件
     * @param questionId 问题ID
     * @return 录音文件
     */
    private fun createAudioFile(questionId: String): File {
        // 创建录音文件目录
        val audioDir = File(context.filesDir, "recordings")
        if (!audioDir.exists()) {
            audioDir.mkdirs()
        }
        
        // 生成录音文件名
        val timestamp = System.currentTimeMillis()
        val fileName = "recording_${questionId}_${timestamp}.m4a"
        
        return File(audioDir, fileName)
    }
    
    /**
     * 开始计时器，更新录音时长
     */
    private fun startDurationTimer() {
        // 这里应该使用协程或定时器来更新录音时长
        // 为了简化，这里只是提供接口，实际实现需要在ViewModel中处理
    }
    
    /**
     * 释放MediaRecorder资源
     */
    private fun releaseRecorder() {
        try {
            mediaRecorder?.apply {
                release()
            }
            mediaRecorder = null
        } catch (e: Exception) {
            // 忽略释放时的异常
        }
    }
    
    /**
     * 检查录音权限是否已授予
     * @return 是否有录音权限
     */
    fun hasRecordingPermission(): Boolean {
        return android.Manifest.permission.RECORD_AUDIO.let { permission ->
            androidx.core.content.ContextCompat.checkSelfPermission(
                context, 
                permission
            ) == android.content.pm.PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 获取录音文件目录
     * @return 录音文件目录
     */
    fun getRecordingsDirectory(): File {
        val audioDir = File(context.filesDir, "recordings")
        if (!audioDir.exists()) {
            audioDir.mkdirs()
        }
        return audioDir
    }
    
    /**
     * 清理旧的录音文件
     * @param maxAgeMillis 文件最大保存时间（毫秒）
     */
    fun cleanupOldRecordings(maxAgeMillis: Long = 7 * 24 * 60 * 60 * 1000L) { // 默认7天
        try {
            val recordingsDir = getRecordingsDirectory()
            val currentTime = System.currentTimeMillis()
            
            recordingsDir.listFiles()?.forEach { file ->
                if (file.isFile && (currentTime - file.lastModified()) > maxAgeMillis) {
                    file.delete()
                }
            }
        } catch (e: Exception) {
            // 忽略清理时的异常
        }
    }
}