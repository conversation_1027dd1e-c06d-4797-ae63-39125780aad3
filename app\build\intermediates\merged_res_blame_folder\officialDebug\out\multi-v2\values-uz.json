{"logs": [{"outputFile": "com.aiinterview.simulator.app-mergeOfficialDebugResources-55:/values-uz/values-uz.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\73f51c4b00c3d8b5bd0709c291d17c64\\transformed\\material3-1.1.2\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,279,389,499,577,671,783,912,1017,1152,1232,1327,1417,1511,1621,1738,1843,1964,2083,2209,2373,2494,2611,2732,2850,2941,3035,3148,3270,3370,3476,3579,3697,3821,3930,4029,4109,4185,4269,4351,4448,4524,4604,4700,4800,4892,4987,5071,5175,5271,5369,5504,5580,5692", "endColumns": "112,110,109,109,77,93,111,128,104,134,79,94,89,93,109,116,104,120,118,125,163,120,116,120,117,90,93,112,121,99,105,102,117,123,108,98,79,75,83,81,96,75,79,95,99,91,94,83,103,95,97,134,75,111,98", "endOffsets": "163,274,384,494,572,666,778,907,1012,1147,1227,1322,1412,1506,1616,1733,1838,1959,2078,2204,2368,2489,2606,2727,2845,2936,3030,3143,3265,3365,3471,3574,3692,3816,3925,4024,4104,4180,4264,4346,4443,4519,4599,4695,4795,4887,4982,5066,5170,5266,5364,5499,5575,5687,5786"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,329,439,1469,1547,1641,1753,1882,1987,2122,2202,2297,2387,2481,2591,2708,2813,2934,3053,3179,3343,3464,3581,3702,3820,3911,4005,4118,4240,4340,4446,4549,4667,4791,4900,5210,5386,6110,6268,6451,6816,6892,6972,7068,7168,7260,7355,7439,7543,7639,7737,7872,7948,8060", "endColumns": "112,110,109,109,77,93,111,128,104,134,79,94,89,93,109,116,104,120,118,125,163,120,116,120,117,90,93,112,121,99,105,102,117,123,108,98,79,75,83,81,96,75,79,95,99,91,94,83,103,95,97,134,75,111,98", "endOffsets": "213,324,434,544,1542,1636,1748,1877,1982,2117,2197,2292,2382,2476,2586,2703,2808,2929,3048,3174,3338,3459,3576,3697,3815,3906,4000,4113,4235,4335,4441,4544,4662,4786,4895,4994,5285,5457,6189,6345,6543,6887,6967,7063,7163,7255,7350,7434,7538,7634,7732,7867,7943,8055,8154"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d78fa9dcb2040782f62e575b1696aadd\\transformed\\ui-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,289,393,500,596,679,769,862,945,1013,1080,1161,1244,1318,1401,1469", "endColumns": "98,84,103,106,95,82,89,92,82,67,66,80,82,73,82,67,116", "endOffsets": "199,284,388,495,591,674,764,857,940,1008,1075,1156,1239,1313,1396,1464,1581"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1285,1384,4999,5103,5290,5462,5545,5635,5728,5811,5879,5946,6027,6194,6548,6631,6699", "endColumns": "98,84,103,106,95,82,89,92,82,67,66,80,82,73,82,67,116", "endOffsets": "1379,1464,5098,5205,5381,5540,5630,5723,5806,5874,5941,6022,6105,6263,6626,6694,6811"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fd044ab7a523edb1345beec00f65097\\transformed\\core-1.12.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "549,651,753,854,954,1062,1166,6350", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "646,748,849,949,1057,1161,1280,6446"}}]}]}