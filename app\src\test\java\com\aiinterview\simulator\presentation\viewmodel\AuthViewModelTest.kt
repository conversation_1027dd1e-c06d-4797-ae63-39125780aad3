package com.aiinterview.simulator.presentation.viewmodel

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import app.cash.turbine.test
import com.aiinterview.simulator.data.repository.AuthRepository
import com.aiinterview.simulator.data.dto.response.AuthResponse
import com.aiinterview.simulator.domain.util.Resource
import com.aiinterview.simulator.util.MainCoroutineRule
import com.aiinterview.simulator.util.MockDataFactory
import com.aiinterview.simulator.util.TestUtils
import com.google.common.truth.Truth.assertThat
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.*

/**
 * AuthViewModel单元测试类
 * 测试用户认证相关的ViewModel功能
 */
@ExperimentalCoroutinesApi
class AuthViewModelTest {

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    @get:Rule
    val mainCoroutineRule = MainCoroutineRule()

    @Mock
    private lateinit var authRepository: AuthRepository

    private lateinit var authViewModel: AuthViewModel

    // 测试数据
    private val testUser = MockDataFactory.createMockUser()
    private val testAuthResponse = AuthResponse(
        user = testUser,
        accessToken = "test_access_token",
        refreshToken = "test_refresh_token"
    )

    @Before
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        authViewModel = AuthViewModel(authRepository)
    }

    /**
     * 测试用户注册成功场景
     */
    @Test
    fun `register should update authState and isLoggedIn when successful`() = runTest {
        // Given - 准备成功的注册响应
        val phoneNumber = "13800138000"
        val verificationCode = "123456"
        val successResource = Resource.Success(testAuthResponse)
        
        whenever(authRepository.register(phoneNumber, verificationCode))
            .thenReturn(flowOf(Resource.Loading(), successResource))

        // When - 执行注册
        authViewModel.register(phoneNumber, verificationCode)

        // Then - 验证状态变化
        authViewModel.authState.test {
            // 应该先收到Loading状态
            val loadingState = awaitItem()
            assertThat(loadingState).isInstanceOf(Resource.Loading::class.java)

            // 然后收到Success状态
            val successState = awaitItem()
            assertThat(successState).isInstanceOf(Resource.Success::class.java)
            assertThat(successState.data).isEqualTo(testAuthResponse)

            cancelAndIgnoreRemainingEvents()
        }

        // 验证登录状态更新
        authViewModel.isLoggedIn.test {
            val isLoggedIn = awaitItem()
            assertThat(isLoggedIn).isTrue()
            cancelAndIgnoreRemainingEvents()
        }

        // 验证Repository调用
        verify(authRepository).register(phoneNumber, verificationCode)
    }

    /**
     * 测试用户注册失败场景
     */
    @Test
    fun `register should update authState with error when failed`() = runTest {
        // Given - 准备失败的注册响应
        val phoneNumber = "13800138000"
        val verificationCode = "123456"
        val errorMessage = "验证码错误"
        val errorResource = Resource.Error<AuthResponse>(errorMessage)
        
        whenever(authRepository.register(phoneNumber, verificationCode))
            .thenReturn(flowOf(Resource.Loading(), errorResource))

        // When - 执行注册
        authViewModel.register(phoneNumber, verificationCode)

        // Then - 验证状态变化
        authViewModel.authState.test {
            // 应该先收到Loading状态
            val loadingState = awaitItem()
            assertThat(loadingState).isInstanceOf(Resource.Loading::class.java)

            // 然后收到Error状态
            val errorState = awaitItem()
            assertThat(errorState).isInstanceOf(Resource.Error::class.java)
            assertThat(errorState.message).isEqualTo(errorMessage)

            cancelAndIgnoreRemainingEvents()
        }

        // 验证登录状态没有变化（应该保持false）
        authViewModel.isLoggedIn.test {
            val isLoggedIn = awaitItem()
            assertThat(isLoggedIn).isFalse()
            cancelAndIgnoreRemainingEvents()
        }
    }

    /**
     * 测试用户登录成功场景
     */
    @Test
    fun `login should update authState and isLoggedIn when successful`() = runTest {
        // Given - 准备成功的登录响应
        val phoneNumber = "13800138000"
        val verificationCode = "123456"
        val successResource = Resource.Success(testAuthResponse)
        
        whenever(authRepository.login(phoneNumber, null, verificationCode))
            .thenReturn(flowOf(Resource.Loading(), successResource))

        // When - 执行登录
        authViewModel.login(phoneNumber, verificationCode = verificationCode)

        // Then - 验证状态变化
        authViewModel.authState.test {
            val loadingState = awaitItem()
            assertThat(loadingState).isInstanceOf(Resource.Loading::class.java)

            val successState = awaitItem()
            assertThat(successState).isInstanceOf(Resource.Success::class.java)
            assertThat(successState.data).isEqualTo(testAuthResponse)

            cancelAndIgnoreRemainingEvents()
        }

        // 验证登录状态更新
        authViewModel.isLoggedIn.test {
            val isLoggedIn = awaitItem()
            assertThat(isLoggedIn).isTrue()
            cancelAndIgnoreRemainingEvents()
        }

        verify(authRepository).login(phoneNumber, null, verificationCode)
    }

    /**
     * 测试密码登录场景
     */
    @Test
    fun `login with password should call repository with correct parameters`() = runTest {
        // Given - 准备密码登录数据
        val phoneNumber = "13800138000"
        val password = "password123"
        val successResource = Resource.Success(testAuthResponse)
        
        whenever(authRepository.login(phoneNumber, password, null))
            .thenReturn(flowOf(successResource))

        // When - 执行密码登录
        authViewModel.login(phoneNumber, password = password)

        // Then - 验证Repository调用参数
        verify(authRepository).login(phoneNumber, password, null)
    }

    /**
     * 测试Token刷新成功场景
     */
    @Test
    fun `refreshToken should update authState when successful`() = runTest {
        // Given - 准备成功的刷新响应
        val successResource = Resource.Success(testAuthResponse)
        
        whenever(authRepository.refreshToken())
            .thenReturn(flowOf(Resource.Loading(), successResource))

        // When - 执行Token刷新
        authViewModel.refreshToken()

        // Then - 验证状态变化
        authViewModel.authState.test {
            val loadingState = awaitItem()
            assertThat(loadingState).isInstanceOf(Resource.Loading::class.java)

            val successState = awaitItem()
            assertThat(successState).isInstanceOf(Resource.Success::class.java)
            assertThat(successState.data).isEqualTo(testAuthResponse)

            cancelAndIgnoreRemainingEvents()
        }

        verify(authRepository).refreshToken()
    }

    /**
     * 测试Token刷新失败场景
     */
    @Test
    fun `refreshToken should update isLoggedIn to false when failed`() = runTest {
        // Given - 准备失败的刷新响应
        val errorMessage = "Token已过期"
        val errorResource = Resource.Error<AuthResponse>(errorMessage)
        
        whenever(authRepository.refreshToken())
            .thenReturn(flowOf(Resource.Loading(), errorResource))

        // When - 执行Token刷新
        authViewModel.refreshToken()

        // Then - 验证登录状态更新为false
        authViewModel.isLoggedIn.test {
            val isLoggedIn = awaitItem()
            assertThat(isLoggedIn).isFalse()
            cancelAndIgnoreRemainingEvents()
        }

        // 验证错误状态
        authViewModel.authState.test {
            val loadingState = awaitItem()
            assertThat(loadingState).isInstanceOf(Resource.Loading::class.java)

            val errorState = awaitItem()
            assertThat(errorState).isInstanceOf(Resource.Error::class.java)
            assertThat(errorState.message).isEqualTo(errorMessage)

            cancelAndIgnoreRemainingEvents()
        }
    }

    /**
     * 测试用户登出功能
     */
    @Test
    fun `logout should clear authState and set isLoggedIn to false`() = runTest {
        // Given - 设置初始登录状态
        authViewModel.login("13800138000", verificationCode = "123456")
        
        // When - 执行登出
        authViewModel.logout()

        // Then - 验证状态清除
        authViewModel.isLoggedIn.test {
            val isLoggedIn = awaitItem()
            assertThat(isLoggedIn).isFalse()
            cancelAndIgnoreRemainingEvents()
        }

        authViewModel.authState.test {
            val authState = awaitItem()
            assertThat(authState).isNull()
            cancelAndIgnoreRemainingEvents()
        }

        verify(authRepository).logout()
    }

    /**
     * 测试检查登录状态功能
     */
    @Test
    fun `checkLoginStatus should update isLoggedIn from repository`() = runTest {
        // Given - 准备Repository返回的登录状态
        val isLoggedIn = true
        whenever(authRepository.isLoggedIn()).thenReturn(flowOf(isLoggedIn))

        // When - 检查登录状态
        authViewModel.checkLoginStatus()

        // Then - 验证状态更新
        authViewModel.isLoggedIn.test {
            val loginStatus = awaitItem()
            assertThat(loginStatus).isEqualTo(isLoggedIn)
            cancelAndIgnoreRemainingEvents()
        }

        verify(authRepository).isLoggedIn()
    }

    /**
     * 测试清除认证状态功能
     */
    @Test
    fun `clearAuthState should set authState to null`() = runTest {
        // Given - 设置初始认证状态
        val successResource = Resource.Success(testAuthResponse)
        whenever(authRepository.register("13800138000", "123456"))
            .thenReturn(flowOf(successResource))
        
        authViewModel.register("13800138000", "123456")

        // When - 清除认证状态
        authViewModel.clearAuthState()

        // Then - 验证状态被清除
        authViewModel.authState.test {
            val authState = awaitItem()
            assertThat(authState).isNull()
            cancelAndIgnoreRemainingEvents()
        }
    }

    /**
     * 测试ViewModel初始状态
     */
    @Test
    fun `initial state should be correct`() = runTest {
        // Then - 验证初始状态
        authViewModel.authState.test {
            val initialAuthState = awaitItem()
            assertThat(initialAuthState).isNull()
            cancelAndIgnoreRemainingEvents()
        }

        authViewModel.isLoggedIn.test {
            val initialLoginState = awaitItem()
            assertThat(initialLoginState).isFalse()
            cancelAndIgnoreRemainingEvents()
        }
    }

    /**
     * 测试多次连续操作的状态管理
     */
    @Test
    fun `multiple operations should update state correctly`() = runTest {
        // Given - 准备多个操作的响应
        val phoneNumber = "13800138000"
        val verificationCode = "123456"
        val successResource = Resource.Success(testAuthResponse)
        val errorResource = Resource.Error<AuthResponse>("网络错误")
        
        whenever(authRepository.register(phoneNumber, verificationCode))
            .thenReturn(flowOf(successResource))
        whenever(authRepository.login(phoneNumber, null, verificationCode))
            .thenReturn(flowOf(errorResource))

        // When - 执行多个操作
        authViewModel.register(phoneNumber, verificationCode)
        authViewModel.login(phoneNumber, verificationCode = verificationCode)

        // Then - 验证最终状态
        authViewModel.authState.test {
            // 应该是最后一次操作的结果
            val finalState = awaitItem()
            assertThat(finalState).isInstanceOf(Resource.Error::class.java)
            assertThat(finalState.message).isEqualTo("网络错误")
            cancelAndIgnoreRemainingEvents()
        }
    }
}