package com.aiinterview.simulator.data.service

import com.aiinterview.simulator.data.api.EvaluationSyncApi
import com.aiinterview.simulator.data.dao.EvaluationRecordDao
import com.aiinterview.simulator.data.dto.request.SyncEvaluationRecordsRequest
import com.aiinterview.simulator.data.model.EvaluationRecordModel
import com.aiinterview.simulator.domain.util.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import javax.inject.Singleton

interface EvaluationSyncService {
    suspend fun syncEvaluationRecords(userId: String): Flow<Resource<Unit>>
    suspend fun uploadEvaluationRecords(userId: String, records: List<EvaluationRecordModel>): Flow<Resource<Unit>>
    suspend fun downloadEvaluationRecords(userId: String, lastSyncTime: Long? = null): Flow<Resource<List<EvaluationRecordModel>>>
    suspend fun deleteCloudEvaluationRecord(recordId: String): Flow<Resource<Unit>>
    suspend fun batchDeleteCloudEvaluationRecords(recordIds: List<String>): Flow<Resource<Unit>>
}

@Singleton
class EvaluationSyncServiceImpl @Inject constructor(
    private val evaluationSyncApi: EvaluationSyncApi,
    private val evaluationRecordDao: EvaluationRecordDao
) : EvaluationSyncService {

    override suspend fun syncEvaluationRecords(userId: String): Flow<Resource<Unit>> = flow {
        try {
            emit(Resource.Loading())
            
            // 1. 获取本地未同步的记录
            val unsyncedRecords = evaluationRecordDao.getUnsyncedEvaluationRecords()
                .filter { it.userId == userId }
            
            if (unsyncedRecords.isNotEmpty()) {
                // 2. 上传未同步的记录
                val recordModels = unsyncedRecords.map { it.toModel() }
                val uploadRequest = SyncEvaluationRecordsRequest(
                    userId = userId,
                    records = recordModels
                )
                
                val uploadResponse = evaluationSyncApi.uploadEvaluationRecords(uploadRequest)
                
                if (uploadResponse.success && uploadResponse.data != null) {
                    // 3. 标记成功上传的记录为已同步
                    val syncedIds = uploadResponse.data.syncedRecordIds
                    val serverTimestamp = uploadResponse.data.serverTimestamp
                    
                    if (syncedIds.isNotEmpty()) {
                        evaluationRecordDao.markMultipleAsSynced(syncedIds, serverTimestamp)
                    }
                    
                    // 4. 处理上传失败的记录
                    val failedIds = uploadResponse.data.failedRecordIds
                    if (failedIds.isNotEmpty()) {
                        // 可以记录日志或者通知用户
                        android.util.Log.w("EvaluationSync", "Failed to sync records: $failedIds")
                    }
                } else {
                    emit(Resource.Error(uploadResponse.message ?: "上传评价记录失败"))
                    return@flow
                }
            }
            
            // 5. 下载云端的新记录（可选，根据需求决定是否实现）
            // downloadAndMergeCloudRecords(userId)
            
            emit(Resource.Success(Unit))
            
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "同步评价记录失败"))
        }
    }

    override suspend fun uploadEvaluationRecords(
        userId: String,
        records: List<EvaluationRecordModel>
    ): Flow<Resource<Unit>> = flow {
        try {
            emit(Resource.Loading())
            
            if (records.isEmpty()) {
                emit(Resource.Success(Unit))
                return@flow
            }
            
            val request = SyncEvaluationRecordsRequest(
                userId = userId,
                records = records
            )
            
            val response = evaluationSyncApi.uploadEvaluationRecords(request)
            
            if (response.success && response.data != null) {
                // 标记成功上传的记录为已同步
                val syncedIds = response.data.syncedRecordIds
                val serverTimestamp = response.data.serverTimestamp
                
                if (syncedIds.isNotEmpty()) {
                    evaluationRecordDao.markMultipleAsSynced(syncedIds, serverTimestamp)
                }
                
                emit(Resource.Success(Unit))
            } else {
                emit(Resource.Error(response.message ?: "上传评价记录失败"))
            }
            
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "上传评价记录失败"))
        }
    }

    override suspend fun downloadEvaluationRecords(
        userId: String,
        lastSyncTime: Long?
    ): Flow<Resource<List<EvaluationRecordModel>>> = flow {
        try {
            emit(Resource.Loading())
            
            val response = evaluationSyncApi.downloadEvaluationRecords(userId, lastSyncTime)
            
            if (response.success && response.data != null) {
                // 将下载的记录保存到本地数据库
                val cloudRecords = response.data
                if (cloudRecords.isNotEmpty()) {
                    val recordEntities = cloudRecords.map { it.toEntity() }
                    evaluationRecordDao.insertEvaluationRecords(recordEntities)
                }
                
                emit(Resource.Success(cloudRecords))
            } else {
                emit(Resource.Error(response.message ?: "下载评价记录失败"))
            }
            
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "下载评价记录失败"))
        }
    }

    override suspend fun deleteCloudEvaluationRecord(recordId: String): Flow<Resource<Unit>> = flow {
        try {
            emit(Resource.Loading())
            
            val response = evaluationSyncApi.deleteCloudEvaluationRecord(recordId)
            
            if (response.success) {
                emit(Resource.Success(Unit))
            } else {
                emit(Resource.Error(response.message ?: "删除云端评价记录失败"))
            }
            
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "删除云端评价记录失败"))
        }
    }

    override suspend fun batchDeleteCloudEvaluationRecords(recordIds: List<String>): Flow<Resource<Unit>> = flow {
        try {
            emit(Resource.Loading())
            
            if (recordIds.isEmpty()) {
                emit(Resource.Success(Unit))
                return@flow
            }
            
            val response = evaluationSyncApi.batchDeleteCloudEvaluationRecords(recordIds)
            
            if (response.success) {
                emit(Resource.Success(Unit))
            } else {
                emit(Resource.Error(response.message ?: "批量删除云端评价记录失败"))
            }
            
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "批量删除云端评价记录失败"))
        }
    }

    /**
     * 扩展函数：将Entity转换为Model
     */
    private fun com.aiinterview.simulator.data.model.EvaluationRecord.toModel(): EvaluationRecordModel {
        val gson = com.google.gson.Gson()
        val dimensionScoresType = object : com.google.gson.reflect.TypeToken<Map<String, com.aiinterview.simulator.data.model.DimensionScore>>() {}.type
        val suggestionsType = object : com.google.gson.reflect.TypeToken<List<String>>() {}.type
        
        return EvaluationRecordModel(
            id = id,
            sessionId = sessionId,
            userId = userId,
            positionId = positionId,
            positionName = positionName,
            overallScore = overallScore,
            dimensionScores = gson.fromJson(dimensionScoresJson, dimensionScoresType),
            feedback = feedback,
            suggestions = gson.fromJson(suggestionsJson, suggestionsType),
            createdAt = createdAt,
            isSynced = isSynced,
            syncedAt = syncedAt
        )
    }

    /**
     * 扩展函数：将Model转换为Entity
     */
    private fun EvaluationRecordModel.toEntity(): com.aiinterview.simulator.data.model.EvaluationRecord {
        val gson = com.google.gson.Gson()
        
        return com.aiinterview.simulator.data.model.EvaluationRecord(
            id = id,
            sessionId = sessionId,
            userId = userId,
            positionId = positionId,
            positionName = positionName,
            overallScore = overallScore,
            dimensionScoresJson = gson.toJson(dimensionScores),
            feedback = feedback,
            suggestionsJson = gson.toJson(suggestions),
            createdAt = createdAt,
            isSynced = isSynced,
            syncedAt = syncedAt
        )
    }
}