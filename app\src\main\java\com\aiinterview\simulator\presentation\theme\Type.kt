package com.aiinterview.simulator.presentation.theme

import androidx.compose.material3.Typography
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp

/**
 * 应用字体排版系统
 * 基于Material Design 3规范，提供完整的字体层次结构
 * 针对中文显示进行优化
 */
val Typography = Typography(
    // 显示级别字体 - 用于大标题和重要信息展示
    displayLarge = TextStyle(
        fontFamily = FontFamily.Default,    // 使用系统默认字体，确保中文显示效果
        fontWeight = FontWeight.Normal,     // 正常字重
        fontSize = 57.sp,                   // 大字号，用于主要标题
        lineHeight = 64.sp,                 // 行高，确保可读性
        letterSpacing = (-0.25).sp          // 字符间距，紧凑显示
    ),
    displayMedium = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Normal,
        fontSize = 45.sp,                   // 中等大字号
        lineHeight = 52.sp,
        letterSpacing = 0.sp                // 标准字符间距
    ),
    displaySmall = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Normal,
        fontSize = 36.sp,                   // 小显示字号
        lineHeight = 44.sp,
        letterSpacing = 0.sp
    ),
    
    // 标题级别字体 - 用于页面标题和重要内容标题
    headlineLarge = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Normal,
        fontSize = 32.sp,                   // 大标题字号
        lineHeight = 40.sp,
        letterSpacing = 0.sp
    ),
    headlineMedium = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Normal,
        fontSize = 28.sp,                   // 中等标题字号
        lineHeight = 36.sp,
        letterSpacing = 0.sp
    ),
    headlineSmall = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Normal,
        fontSize = 24.sp,                   // 小标题字号
        lineHeight = 32.sp,
        letterSpacing = 0.sp
    ),
    
    // 标题级别字体 - 用于卡片标题和组件标题
    titleLarge = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Normal,
        fontSize = 22.sp,                   // 大标题字号
        lineHeight = 28.sp,
        letterSpacing = 0.sp
    ),
    titleMedium = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Medium,     // 中等字重，增强视觉层次
        fontSize = 16.sp,                   // 中等标题字号
        lineHeight = 24.sp,
        letterSpacing = 0.15.sp             // 适当字符间距
    ),
    titleSmall = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Medium,
        fontSize = 14.sp,                   // 小标题字号
        lineHeight = 20.sp,
        letterSpacing = 0.1.sp
    ),
    
    // 正文级别字体 - 用于主要内容文本
    bodyLarge = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Normal,
        fontSize = 16.sp,                   // 大正文字号，适合长文本阅读
        lineHeight = 24.sp,                 // 舒适的行高
        letterSpacing = 0.5.sp              // 适当字符间距，提高可读性
    ),
    bodyMedium = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Normal,
        fontSize = 14.sp,                   // 标准正文字号
        lineHeight = 20.sp,
        letterSpacing = 0.25.sp
    ),
    bodySmall = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Normal,
        fontSize = 12.sp,                   // 小正文字号，用于辅助信息
        lineHeight = 16.sp,
        letterSpacing = 0.4.sp
    ),
    
    // 标签级别字体 - 用于按钮、标签和小型组件
    labelLarge = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Medium,     // 中等字重，突出重要性
        fontSize = 14.sp,                   // 大标签字号
        lineHeight = 20.sp,
        letterSpacing = 0.1.sp
    ),
    labelMedium = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Medium,
        fontSize = 12.sp,                   // 标准标签字号
        lineHeight = 16.sp,
        letterSpacing = 0.5.sp
    ),
    labelSmall = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Medium,
        fontSize = 11.sp,                   // 小标签字号，用于密集信息
        lineHeight = 16.sp,
        letterSpacing = 0.5.sp
    )
)

/**
 * 自定义字体样式
 * 为特定场景提供专用的字体样式
 */
object CustomTypography {
    // 面试问题文本样式 - 清晰易读
    val InterviewQuestion = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Normal,
        fontSize = 18.sp,                   // 较大字号，确保问题清晰可读
        lineHeight = 26.sp,                 // 舒适行高
        letterSpacing = 0.15.sp
    )
    
    // 评分数字样式 - 突出显示
    val ScoreNumber = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Bold,       // 粗体，突出分数
        fontSize = 32.sp,                   // 大字号，醒目显示
        lineHeight = 40.sp,
        letterSpacing = 0.sp
    )
    
    // 计时器样式 - 等宽字体效果
    val Timer = TextStyle(
        fontFamily = FontFamily.Monospace,  // 等宽字体，数字对齐
        fontWeight = FontWeight.Medium,
        fontSize = 20.sp,
        lineHeight = 24.sp,
        letterSpacing = 0.sp
    )
    
    // 代码样式 - 等宽字体
    val Code = TextStyle(
        fontFamily = FontFamily.Monospace,
        fontWeight = FontWeight.Normal,
        fontSize = 14.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.sp
    )
    
    // 强调文本样式 - 重要信息
    val Emphasis = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.SemiBold,   // 半粗体，适度强调
        fontSize = 16.sp,
        lineHeight = 24.sp,
        letterSpacing = 0.15.sp
    )
    
    // 提示文本样式 - 辅助信息
    val Hint = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Normal,
        fontSize = 13.sp,                   // 稍小字号
        lineHeight = 18.sp,
        letterSpacing = 0.25.sp
    )
    
    // 错误信息样式 - 错误提示
    val Error = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Medium,
        fontSize = 14.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.25.sp
    )
    
    // 成功信息样式 - 成功提示
    val Success = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Medium,
        fontSize = 14.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.25.sp
    )
    
    // 警告信息样式 - 警告提示
    val Warning = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Medium,
        fontSize = 14.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.25.sp
    )
}