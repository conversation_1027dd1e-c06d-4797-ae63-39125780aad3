package com.aiinterview.simulator.data.speech

import android.content.Context
import com.aiinterview.simulator.data.api.BaiduASRApi
import com.aiinterview.simulator.data.api.SpeechRecognitionApi
import com.aiinterview.simulator.data.dto.response.BaiduASRResponse
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.whenever
import org.junit.Assert.*
import java.io.File

class SpeechRecognitionServiceTest {
    
    @Mock
    private lateinit var context: Context
    
    @Mock
    private lateinit var speechRecognitionApi: SpeechRecognitionApi
    
    @Mock
    private lateinit var baiduASRApi: BaiduASRApi
    
    @Mock
    private lateinit var audioFile: File
    
    private lateinit var speechRecognitionService: SpeechRecognitionService
    
    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        speechRecognitionService = SpeechRecognitionService(context, speechRecognition<PERSON>pi, baiduAS<PERSON>pi)
    }
    
    @Test
    fun `validateRecognitionResult should return true for good result`() {
        // Given
        val goodResult = com.aiinterview.simulator.data.dto.response.RecognitionResult(
            text = "这是一个很好的识别结果",
            confidence = 0.8,
            duration = 5000L,
            provider = "BAIDU",
            success = true
        )
        
        // When
        val isValid = speechRecognitionService.validateRecognitionResult(goodResult)
        
        // Then
        assertTrue(isValid)
    }
    
    @Test
    fun `validateRecognitionResult should return false for poor result`() {
        // Given
        val poorResult = com.aiinterview.simulator.data.dto.response.RecognitionResult(
            text = "",
            confidence = 0.3,
            duration = 5000L,
            provider = "BAIDU",
            success = false
        )
        
        // When
        val isValid = speechRecognitionService.validateRecognitionResult(poorResult)
        
        // Then
        assertFalse(isValid)
    }
    
    @Test
    fun `getConfidenceLevel should return correct description`() {
        // Test cases for different confidence levels
        assertEquals("很高", speechRecognitionService.getConfidenceLevel(0.95))
        assertEquals("高", speechRecognitionService.getConfidenceLevel(0.85))
        assertEquals("中等", speechRecognitionService.getConfidenceLevel(0.75))
        assertEquals("较低", speechRecognitionService.getConfidenceLevel(0.65))
        assertEquals("很低", speechRecognitionService.getConfidenceLevel(0.45))
    }
    
    @Test
    fun `createOfflineFallbackResult should return offline result`() {
        // Given
        whenever(audioFile.length()).thenReturn(5000L)
        
        // When
        val result = speechRecognitionService.createOfflineFallbackResult(audioFile)
        
        // Then
        assertEquals("[语音识别失败，请手动输入回答内容]", result.text)
        assertEquals(0.0, result.confidence, 0.01)
        assertEquals(5000L, result.duration)
        assertEquals("OFFLINE", result.provider)
        assertFalse(result.success)
        assertNotNull(result.errorMessage)
    }
    
    @Test
    fun `recognizeAudio should handle file not exists`() = runTest {
        // Given
        whenever(audioFile.exists()).thenReturn(false)
        
        // When
        val result = speechRecognitionService.recognizeAudio(audioFile)
        
        // Then
        assertFalse(result.success)
        assertNotNull(result.errorMessage)
    }
}