package com.aiinterview.simulator.data.permission

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.platform.LocalContext

/**
 * 权限工具类
 * 提供权限相关的实用方法和扩展函数
 */
object PermissionUtils {
    
    /**
     * 检查是否为关键权限
     * @param permission 权限名称
     * @return true表示是关键权限，false表示非关键权限
     */
    fun isCriticalPermission(permission: String): <PERSON><PERSON><PERSON> {
        return when (permission) {
            android.Manifest.permission.RECORD_AUDIO -> true    // 录音权限是关键权限
            android.Manifest.permission.INTERNET -> true        // 网络权限是关键权限
            else -> false
        }
    }
    
    /**
     * 获取权限的中文名称
     * @param permission 权限名称
     * @return 权限的中文名称
     */
    fun getPermissionChineseName(permission: String): String {
        return when (permission) {
            android.Manifest.permission.RECORD_AUDIO -> "录音权限"
            android.Manifest.permission.WRITE_EXTERNAL_STORAGE -> "存储权限"
            android.Manifest.permission.ACCESS_NETWORK_STATE -> "网络状态权限"
            android.Manifest.permission.INTERNET -> "网络访问权限"
            android.Manifest.permission.CAMERA -> "相机权限"
            android.Manifest.permission.READ_EXTERNAL_STORAGE -> "读取存储权限"
            else -> "未知权限"
        }
    }
    
    /**
     * 获取权限的详细说明
     * @param permission 权限名称
     * @return 权限的详细说明
     */
    fun getPermissionDetailedDescription(permission: String): String {
        return when (permission) {
            android.Manifest.permission.RECORD_AUDIO -> 
                "录音权限用于录制您的面试回答。应用会在您点击录音按钮时开始录制，录制完成后会停止。录音文件仅用于语音识别和AI评价，不会被用于其他用途。"
            
            android.Manifest.permission.WRITE_EXTERNAL_STORAGE -> 
                "存储权限用于保存录音文件和面试记录到设备存储中。这些文件会被加密存储，确保您的隐私安全。您可以随时在应用中删除这些文件。"
            
            android.Manifest.permission.ACCESS_NETWORK_STATE -> 
                "网络状态权限用于检查设备的网络连接状态。应用会根据网络状态调整功能，例如在网络不佳时提供离线录音功能。"
            
            android.Manifest.permission.INTERNET -> 
                "网络访问权限用于连接服务器获取面试题目、上传录音文件进行语音识别、获取AI评价结果等。所有网络传输都经过加密处理。"
            
            else -> "此权限用于应用的正常运行，我们承诺只在必要时使用此权限。"
        }
    }
    
    /**
     * 打开应用设置页面
     * @param context 上下文
     * @return true表示成功打开设置页面，false表示失败
     */
    fun openAppSettings(context: Context): Boolean {
        return try {
            // 创建前往应用详情设置的Intent
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.fromParts("package", context.packageName, null)
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
            true
        } catch (e: Exception) {
            // 如果无法打开应用设置，尝试打开通用设置
            try {
                val intent = Intent(Settings.ACTION_SETTINGS).apply {
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                context.startActivity(intent)
                true
            } catch (e2: Exception) {
                false
            }
        }
    }
    
    /**
     * 打开权限设置页面
     * @param context 上下文
     * @return true表示成功打开设置页面，false表示失败
     */
    fun openPermissionSettings(context: Context): Boolean {
        return try {
            // Android 11及以上版本的权限设置页面
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                    data = Uri.fromParts("package", context.packageName, null)
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                context.startActivity(intent)
            } else {
                // 较低版本的权限设置页面
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                    data = Uri.fromParts("package", context.packageName, null)
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                context.startActivity(intent)
            }
            true
        } catch (e: Exception) {
            openAppSettings(context)
        }
    }
    
    /**
     * 获取权限被拒绝的原因分析
     * @param permission 权限名称
     * @param isPermanentlyDenied 是否永久拒绝
     * @return 拒绝原因分析
     */
    fun getPermissionDeniedReason(permission: String, isPermanentlyDenied: Boolean): String {
        val permissionName = getPermissionChineseName(permission)
        
        return if (isPermanentlyDenied) {
            "您已选择不再询问${permissionName}。这可能是因为：\n" +
            "1. 您担心隐私安全问题\n" +
            "2. 您不确定权限的用途\n" +
            "3. 您误操作选择了拒绝\n\n" +
            "请放心，我们严格遵守隐私保护原则，${permissionName}仅用于应用的核心功能。"
        } else {
            "您拒绝了${permissionName}。如果您改变主意，可以：\n" +
            "1. 点击重新请求权限\n" +
            "2. 前往设置中手动开启\n" +
            "3. 了解更多权限用途说明"
        }
    }
    
    /**
     * 检查Android版本是否需要特定权限
     * @param permission 权限名称
     * @return true表示当前Android版本需要此权限，false表示不需要
     */
    fun isPermissionRequiredForCurrentVersion(permission: String): Boolean {
        return when (permission) {
            android.Manifest.permission.WRITE_EXTERNAL_STORAGE -> {
                // Android 10及以上版本不需要存储权限
                android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.Q
            }
            android.Manifest.permission.READ_EXTERNAL_STORAGE -> {
                // Android 13及以上版本有新的媒体权限
                android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.TIRAMISU
            }
            else -> true // 其他权限都需要
        }
    }
    
    /**
     * 获取权限的重要性等级描述
     * @param permissionType 权限类型
     * @return 重要性等级描述
     */
    fun getPermissionImportanceDescription(permissionType: PermissionType): String {
        return when (permissionType.importance) {
            5 -> "必需权限 - 应用核心功能必须使用"
            4 -> "重要权限 - 影响主要功能的正常使用"
            3 -> "一般权限 - 用于优化用户体验"
            2 -> "可选权限 - 用于增强功能"
            1 -> "辅助权限 - 用于提供额外服务"
            else -> "未知重要性"
        }
    }
}

/**
 * Activity扩展函数：检查是否有权限
 * @param permission 权限名称
 * @return true表示有权限，false表示没有权限
 */
fun Activity.hasPermission(permission: String): Boolean {
    return androidx.core.content.ContextCompat.checkSelfPermission(
        this, 
        permission
    ) == android.content.pm.PackageManager.PERMISSION_GRANTED
}

/**
 * Activity扩展函数：检查是否应该显示权限说明
 * @param permission 权限名称
 * @return true表示应该显示说明，false表示不需要
 */
fun Activity.shouldShowPermissionRationale(permission: String): Boolean {
    return androidx.core.app.ActivityCompat.shouldShowRequestPermissionRationale(this, permission)
}

/**
 * Context扩展函数：检查是否有权限
 * @param permission 权限名称
 * @return true表示有权限，false表示没有权限
 */
fun Context.hasPermission(permission: String): Boolean {
    return androidx.core.content.ContextCompat.checkSelfPermission(
        this, 
        permission
    ) == android.content.pm.PackageManager.PERMISSION_GRANTED
}

/**
 * Compose函数：权限检查效果
 * 在Compose中方便地检查权限状态
 * @param permission 权限名称
 * @param onPermissionResult 权限检查结果回调
 */
@Composable
fun PermissionCheckEffect(
    permission: String,
    onPermissionResult: (Boolean) -> Unit
) {
    val context = LocalContext.current
    
    LaunchedEffect(permission) {
        val hasPermission = context.hasPermission(permission)
        onPermissionResult(hasPermission)
    }
}