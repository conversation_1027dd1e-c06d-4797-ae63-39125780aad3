package com.aiinterview.simulator

import android.app.Application
import com.aiinterview.simulator.data.error.CrashReportingManager
import com.aiinterview.simulator.data.error.ErrorHandler
import com.aiinterview.simulator.data.offline.DegradationManager
import com.aiinterview.simulator.data.offline.OfflineModeManager
import com.aiinterview.simulator.data.performance.MemoryOptimizationManager
import com.aiinterview.simulator.data.performance.StartupOptimizationManager
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * AI面试模拟应用程序主类
 * 负责应用程序的全局初始化、性能优化和错误处理
 */
@HiltAndroidApp
class AIInterviewApplication : Application() {
    
    // 注入启动优化管理器
    @Inject
    lateinit var startupOptimizationManager: StartupOptimizationManager
    
    // 注入内存优化管理器
    @Inject
    lateinit var memoryOptimizationManager: MemoryOptimizationManager
    
    // 注入错误处理器
    @Inject
    lateinit var errorHandler: ErrorHandler
    
    // 注入崩溃报告管理器
    @Inject
    lateinit var crashReportingManager: CrashReportingManager
    
    // 注入离线模式管理器
    @Inject
    lateinit var offlineModeManager: OfflineModeManager
    
    // 注入降级管理器
    @Inject
    lateinit var degradationManager: DegradationManager
    
    // 应用程序级协程作用域
    private val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
    
    /**
     * 应用程序创建时的回调
     */
    override fun onCreate() {
        super.onCreate()
        
        // 初始化崩溃报告管理器（必须最先初始化）
        crashReportingManager.initialize()
        
        // 开始启动优化
        startupOptimizationManager.startOptimization()
        
        // 初始化离线模式管理器
        offlineModeManager.initialize()
        
        // 初始化降级管理器
        degradationManager.initialize()
        
        // 启动内存监控
        startMemoryMonitoring()
        
        // 注册应用程序生命周期回调
        registerActivityLifecycleCallbacks(AppLifecycleCallbacks())
    }
    
    /**
     * 应用程序内存不足时的回调
     */
    override fun onLowMemory() {
        super.onLowMemory()
        
        // 执行紧急内存清理
        applicationScope.launch {
            memoryOptimizationManager.performEmergencyMemoryCleanup()
        }
    }
    
    /**
     * 应用程序配置改变时的回调
     * @param level 内存级别
     */
    override fun onTrimMemory(level: Int) {
        super.onTrimMemory(level)
        
        // 根据内存级别执行相应的内存清理
        applicationScope.launch {
            when (level) {
                TRIM_MEMORY_RUNNING_CRITICAL, // 运行时内存严重不足
                TRIM_MEMORY_COMPLETE -> { // 内存完全不足
                    memoryOptimizationManager.performEmergencyMemoryCleanup()
                }
                TRIM_MEMORY_RUNNING_LOW, // 运行时内存不足
                TRIM_MEMORY_RUNNING_MODERATE, // 运行时内存中等不足
                TRIM_MEMORY_BACKGROUND, // 后台内存不足
                TRIM_MEMORY_MODERATE -> { // 中等内存不足
                    memoryOptimizationManager.performMemoryCleanup()
                }
                TRIM_MEMORY_UI_HIDDEN -> { // UI隐藏时
                    // 清理UI相关的缓存
                    memoryOptimizationManager.performMemoryCleanup()
                }
            }
        }
    }
    
    /**
     * 启动内存监控
     */
    private fun startMemoryMonitoring() {
        applicationScope.launch {
            // 定期监控内存使用情况
            while (true) {
                try {
                    memoryOptimizationManager.monitorMemoryUsage() // 监控内存使用
                    kotlinx.coroutines.delay(30000) // 每30秒检查一次
                } catch (e: Exception) {
                    e.printStackTrace() // 打印异常信息
                    kotlinx.coroutines.delay(60000) // 出错时延长检查间隔
                }
            }
        }
    }
    
    /**
     * 应用程序终止时的清理工作
     */
    override fun onTerminate() {
        super.onTerminate()
        
        // 清理启动优化管理器资源
        startupOptimizationManager.cleanup()
        
        // 清理离线模式管理器资源
        offlineModeManager.cleanup()
        
        // 取消应用程序级协程作用域
        applicationScope.cancel()
    }
}