package com.aiinterview.simulator.presentation.screen.evaluation

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.aiinterview.simulator.presentation.viewmodel.EvaluationHistoryViewModel
import com.aiinterview.simulator.presentation.viewmodel.SortOrder
import com.aiinterview.simulator.data.model.EvaluationRecordSummary
import com.aiinterview.simulator.data.dao.EvaluationStatistics
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EvaluationHistoryScreen(
    onNavigateToDetail: (String) -> Unit,
    onNavigateBack: () -> Unit,
    viewModel: EvaluationHistoryViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    var showFilterDialog by remember { mutableStateOf(false) }
    var showDeleteAllDialog by remember { mutableStateOf(false) }

    // 处理消息显示
    LaunchedEffect(uiState.errorMessage) {
        uiState.errorMessage?.let {
            // 这里可以显示Snackbar或Toast
            viewModel.clearErrorMessage()
        }
    }

    LaunchedEffect(uiState.successMessage) {
        uiState.successMessage?.let {
            // 这里可以显示Snackbar或Toast
            viewModel.clearSuccessMessage()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("评价历史") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    // 同步按钮
                    if (uiState.unsyncedCount > 0) {
                        IconButton(
                            onClick = { viewModel.syncEvaluationRecords() },
                            enabled = !uiState.isSyncing
                        ) {
                            if (uiState.isSyncing) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(20.dp),
                                    strokeWidth = 2.dp
                                )
                            } else {
                                Badge(
                                    containerColor = MaterialTheme.colorScheme.error
                                ) {
                                    Text(uiState.unsyncedCount.toString())
                                }
                                Icon(Icons.Default.CloudUpload, contentDescription = "同步")
                            }
                        }
                    }
                    
                    // 筛选按钮
                    IconButton(onClick = { showFilterDialog = true }) {
                        Icon(Icons.Default.FilterList, contentDescription = "筛选")
                    }
                    
                    // 更多操作
                    var showMenu by remember { mutableStateOf(false) }
                    Box {
                        IconButton(onClick = { showMenu = true }) {
                            Icon(Icons.Default.MoreVert, contentDescription = "更多")
                        }
                        DropdownMenu(
                            expanded = showMenu,
                            onDismissRequest = { showMenu = false }
                        ) {
                            DropdownMenuItem(
                                text = { Text("删除所有记录") },
                                onClick = {
                                    showMenu = false
                                    showDeleteAllDialog = true
                                },
                                leadingIcon = {
                                    Icon(Icons.Default.DeleteSweep, contentDescription = null)
                                }
                            )
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 统计信息卡片
            uiState.statistics?.let { statistics ->
                StatisticsCard(
                    statistics = statistics,
                    modifier = Modifier.padding(16.dp)
                )
            }

            // 评价记录列表
            if (uiState.isLoading && uiState.filteredRecords.isEmpty()) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            } else if (uiState.filteredRecords.isEmpty()) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            Icons.Default.Assessment,
                            contentDescription = null,
                            modifier = Modifier.size(64.dp),
                            tint = MaterialTheme.colorScheme.outline
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "暂无评价记录",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.outline
                        )
                    }
                }
            } else {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(uiState.filteredRecords) { record ->
                        EvaluationRecordItem(
                            record = record,
                            onClick = { onNavigateToDetail(record.id) },
                            onDelete = { viewModel.deleteEvaluationRecord(record.id) }
                        )
                    }
                }
            }
        }
    }

    // 筛选对话框
    if (showFilterDialog) {
        FilterDialog(
            currentPositionFilter = uiState.filterPosition,
            currentScoreRange = uiState.filterScoreRange,
            currentSortOrder = uiState.sortOrder,
            onPositionFilterChange = viewModel::setPositionFilter,
            onScoreRangeChange = { min, max -> viewModel.setScoreRangeFilter(min, max) },
            onSortOrderChange = viewModel::setSortOrder,
            onClearFilters = viewModel::clearFilters,
            onDismiss = { showFilterDialog = false }
        )
    }

    // 删除所有记录确认对话框
    if (showDeleteAllDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteAllDialog = false },
            title = { Text("确认删除") },
            text = { Text("确定要删除所有评价记录吗？此操作不可撤销。") },
            confirmButton = {
                TextButton(
                    onClick = {
                        showDeleteAllDialog = false
                        viewModel.deleteAllEvaluationRecords()
                    }
                ) {
                    Text("删除", color = MaterialTheme.colorScheme.error)
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteAllDialog = false }) {
                    Text("取消")
                }
            }
        )
    }
}

@Composable
private fun StatisticsCard(
    statistics: EvaluationStatistics,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "统计信息",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(12.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                StatisticItem(
                    label = "总次数",
                    value = statistics.totalCount.toString(),
                    icon = Icons.Default.Assessment
                )
                StatisticItem(
                    label = "平均分",
                    value = String.format("%.1f", statistics.averageScore),
                    icon = Icons.Default.TrendingUp
                )
                StatisticItem(
                    label = "最高分",
                    value = String.format("%.1f", statistics.bestScore),
                    icon = Icons.Default.Star
                )
            }
        }
    }
}

@Composable
private fun StatisticItem(
    label: String,
    value: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            icon,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(24.dp)
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = value,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.outline
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun EvaluationRecordItem(
    record: EvaluationRecordSummary,
    onClick: () -> Unit,
    onDelete: () -> Unit
) {
    var showDeleteDialog by remember { mutableStateOf(false) }
    val dateFormat = remember { SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()) }

    Card(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 分数圆圈
            Box(
                modifier = Modifier
                    .size(48.dp),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(
                    progress = (record.overallScore / 100.0).toFloat(),
                    modifier = Modifier.fillMaxSize(),
                    strokeWidth = 4.dp,
                    color = when {
                        record.overallScore >= 80 -> Color.Green
                        record.overallScore >= 60 -> Color.Orange
                        else -> Color.Red
                    }
                )
                Text(
                    text = "${record.overallScore.toInt()}",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Bold
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // 记录信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = record.positionName,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = dateFormat.format(Date(record.createdAt)),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.outline
                )
                
                // 同步状态
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        if (record.isSynced) Icons.Default.CloudDone else Icons.Default.CloudOff,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp),
                        tint = if (record.isSynced) Color.Green else MaterialTheme.colorScheme.outline
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = if (record.isSynced) "已同步" else "未同步",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.outline
                    )
                }
            }

            // 删除按钮
            IconButton(onClick = { showDeleteDialog = true }) {
                Icon(
                    Icons.Default.Delete,
                    contentDescription = "删除",
                    tint = MaterialTheme.colorScheme.outline
                )
            }
        }
    }

    // 删除确认对话框
    if (showDeleteDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteDialog = false },
            title = { Text("确认删除") },
            text = { Text("确定要删除这条评价记录吗？") },
            confirmButton = {
                TextButton(
                    onClick = {
                        showDeleteDialog = false
                        onDelete()
                    }
                ) {
                    Text("删除", color = MaterialTheme.colorScheme.error)
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteDialog = false }) {
                    Text("取消")
                }
            }
        )
    }
}

@Composable
private fun FilterDialog(
    currentPositionFilter: String,
    currentScoreRange: Pair<Double, Double>,
    currentSortOrder: SortOrder,
    onPositionFilterChange: (String) -> Unit,
    onScoreRangeChange: (Double, Double) -> Unit,
    onSortOrderChange: (SortOrder) -> Unit,
    onClearFilters: () -> Unit,
    onDismiss: () -> Unit
) {
    var positionFilter by remember { mutableStateOf(currentPositionFilter) }
    var minScore by remember { mutableStateOf(currentScoreRange.first) }
    var maxScore by remember { mutableStateOf(currentScoreRange.second) }
    var sortOrder by remember { mutableStateOf(currentSortOrder) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("筛选和排序") },
        text = {
            Column {
                // 岗位筛选
                OutlinedTextField(
                    value = positionFilter,
                    onValueChange = { positionFilter = it },
                    label = { Text("岗位名称") },
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 分数范围筛选
                Text("分数范围", style = MaterialTheme.typography.labelMedium)
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    OutlinedTextField(
                        value = minScore.toInt().toString(),
                        onValueChange = { 
                            it.toDoubleOrNull()?.let { value -> 
                                minScore = value.coerceIn(0.0, 100.0)
                            }
                        },
                        label = { Text("最低分") },
                        modifier = Modifier.weight(1f)
                    )
                    Text(" - ", modifier = Modifier.padding(horizontal = 8.dp))
                    OutlinedTextField(
                        value = maxScore.toInt().toString(),
                        onValueChange = { 
                            it.toDoubleOrNull()?.let { value -> 
                                maxScore = value.coerceIn(0.0, 100.0)
                            }
                        },
                        label = { Text("最高分") },
                        modifier = Modifier.weight(1f)
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 排序方式
                Text("排序方式", style = MaterialTheme.typography.labelMedium)
                Column {
                    SortOrder.values().forEach { order ->
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = sortOrder == order,
                                onClick = { sortOrder = order }
                            )
                            Text(
                                text = when (order) {
                                    SortOrder.DATE_DESC -> "按日期降序"
                                    SortOrder.DATE_ASC -> "按日期升序"
                                    SortOrder.SCORE_DESC -> "按分数降序"
                                    SortOrder.SCORE_ASC -> "按分数升序"
                                }
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    onPositionFilterChange(positionFilter)
                    onScoreRangeChange(minScore, maxScore)
                    onSortOrderChange(sortOrder)
                    onDismiss()
                }
            ) {
                Text("应用")
            }
        },
        dismissButton = {
            Row {
                TextButton(
                    onClick = {
                        onClearFilters()
                        onDismiss()
                    }
                ) {
                    Text("清除")
                }
                TextButton(onClick = onDismiss) {
                    Text("取消")
                }
            }
        }
    )
}