package com.aiinterview.simulator.presentation.screen.settings

import android.os.Build
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Brightness4
import androidx.compose.material.icons.filled.Brightness6
import androidx.compose.material.icons.filled.BrightnessHigh
import androidx.compose.material.icons.filled.Contrast
import androidx.compose.material.icons.filled.FormatSize
import androidx.compose.material.icons.filled.Palette
import androidx.compose.material.icons.filled.RoundedCorner
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.aiinterview.simulator.presentation.theme.*
import com.aiinterview.simulator.presentation.viewmodel.ThemeSettingsViewModel
import kotlinx.coroutines.launch

/**
 * 主题设置屏幕
 * 允许用户自定义应用的主题外观
 * @param onNavigateBack 返回上一页的回调函数
 * @param viewModel 主题设置视图模型
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ThemeSettingsScreen(
    onNavigateBack: () -> Unit,                 // 返回上一页的回调函数
    viewModel: ThemeSettingsViewModel = hiltViewModel() // 主题设置视图模型
) {
    // 获取当前主题配置状态
    val themeConfig by viewModel.themeConfig.collectAsState() // 收集主题配置状态
    val coroutineScope = rememberCoroutineScope()      // 协程作用域，用于异步操作
    val scrollState = rememberScrollState()             // 滚动状态，用于长内容滚动
    val context = LocalContext.current                  // 当前上下文
    
    // 检查是否支持动态颜色（Android 12+）
    val supportsDynamicColors = Build.VERSION.SDK_INT >= Build.VERSION_CODES.S
    
    Column(
        modifier = Modifier
            .fillMaxSize()                              // 填充整个屏幕
            .verticalScroll(scrollState)                // 启用垂直滚动
    ) {
        // 顶部应用栏
        TopAppBar(
            title = {
                Text(
                    text = "主题设置",                   // 页面标题
                    style = ResponsiveStyles.getResponsiveTextStyle(MaterialTheme.typography.titleLarge) // 使用响应式大标题样式
                )
            },
            navigationIcon = {                          // 导航图标（返回按钮）
                IconButton(onClick = onNavigateBack) {  // 点击返回上一页
                    Icon(
                        imageVector = Icons.Default.ArrowBack, // 返回箭头图标
                        contentDescription = "返回",    // 无障碍描述
                        modifier = Modifier.size(Dimensions.IconMedium) // 使用统一的中等图标尺寸
                    )
                }
            },
            colors = ComponentStyles.AppBar.topAppBarColors() // 使用统一的顶部应用栏颜色样式
        )
        
        // 设置内容区域
        Column(
            modifier = Modifier
                .fillMaxWidth()                         // 填充宽度
                .padding(ResponsiveStyles.getResponsivePadding()) // 使用响应式内边距
        ) {
            // 深色模式设置卡片
            SettingsCard(
                title = "外观模式",                      // 卡片标题
                icon = Icons.Default.Brightness6,       // 卡片图标
                description = "选择浅色或深色主题"        // 卡片描述
            ) {
                Column {
                    // 浅色模式选项
                    ThemeOptionItem(
                        title = "浅色模式",              // 选项标题
                        description = "明亮清晰的界面风格", // 选项描述
                        icon = Icons.Default.BrightnessHigh, // 选项图标
                        selected = !themeConfig.isDarkTheme, // 是否选中
                        onClick = {                      // 点击事件
                            coroutineScope.launch {
                                viewModel.setDarkTheme(false) // 设置为浅色主题
                            }
                        }
                    )
                    
                    // 深色模式选项
                    ThemeOptionItem(
                        title = "深色模式",              // 选项标题
                        description = "护眼舒适的夜间风格", // 选项描述
                        icon = Icons.Default.Brightness4, // 选项图标
                        selected = themeConfig.isDarkTheme, // 是否选中
                        onClick = {                      // 点击事件
                            coroutineScope.launch {
                                viewModel.setDarkTheme(true) // 设置为深色主题
                            }
                        }
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(ResponsiveStyles.getResponsiveSpacing())) // 卡片间距
            
            // 动态颜色设置卡片（仅Android 12+显示）
            if (supportsDynamicColors) {
                SettingsCard(
                    title = "动态颜色",                  // 卡片标题
                    icon = Icons.Default.Palette,       // 卡片图标
                    description = "根据壁纸自动调整应用颜色" // 卡片描述
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()              // 填充宽度
                            .padding(vertical = Dimensions.PaddingSmall), // 垂直内边距
                        horizontalArrangement = Arrangement.SpaceBetween, // 两端对齐
                        verticalAlignment = Alignment.CenterVertically    // 垂直居中
                    ) {
                        Column(modifier = Modifier.weight(1f)) { // 占据剩余空间
                            Text(
                                text = "启用动态颜色",    // 开关标题
                                style = ResponsiveStyles.getResponsiveTextStyle(MaterialTheme.typography.bodyLarge) // 使用响应式大正文样式
                            )
                            Text(
                                text = "跟随系统壁纸颜色变化", // 开关描述
                                style = ResponsiveStyles.getResponsiveTextStyle(MaterialTheme.typography.bodySmall), // 使用响应式小正文样式
                                color = MaterialTheme.colorScheme.onSurfaceVariant // 设置描述文字颜色
                            )
                        }
                        Switch(
                            checked = themeConfig.useDynamicColors, // 开关状态
                            onCheckedChange = { enabled ->       // 开关变化事件
                                coroutineScope.launch {
                                    viewModel.setDynamicColors(enabled) // 设置动态颜色开关
                                }
                            }
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(ResponsiveStyles.getResponsiveSpacing())) // 卡片间距
            }
            
            // 高对比度设置卡片
            SettingsCard(
                title = "高对比度",                      // 卡片标题
                icon = Icons.Default.Contrast,          // 卡片图标
                description = "提高文字和背景的对比度，改善可读性" // 卡片描述
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()                  // 填充宽度
                        .padding(vertical = Dimensions.PaddingSmall), // 垂直内边距
                    horizontalArrangement = Arrangement.SpaceBetween, // 两端对齐
                    verticalAlignment = Alignment.CenterVertically    // 垂直居中
                ) {
                    Column(modifier = Modifier.weight(1f)) { // 占据剩余空间
                        Text(
                            text = "启用高对比度",        // 开关标题
                            style = ResponsiveStyles.getResponsiveTextStyle(MaterialTheme.typography.bodyLarge) // 使用响应式大正文样式
                        )
                        Text(
                            text = "增强视觉对比度，提升可访问性", // 开关描述
                            style = ResponsiveStyles.getResponsiveTextStyle(MaterialTheme.typography.bodySmall), // 使用响应式小正文样式
                            color = MaterialTheme.colorScheme.onSurfaceVariant // 设置描述文字颜色
                        )
                    }
                    Switch(
                        checked = themeConfig.useHighContrast, // 开关状态
                        onCheckedChange = { enabled ->       // 开关变化事件
                            coroutineScope.launch {
                                viewModel.setHighContrast(enabled) // 设置高对比度开关
                            }
                        }
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(ResponsiveStyles.getResponsiveSpacing())) // 卡片间距
            
            // 字体大小设置卡片
            SettingsCard(
                title = "字体大小",                      // 卡片标题
                icon = Icons.Default.FormatSize,        // 卡片图标
                description = "调整应用中文字的大小"      // 卡片描述
            ) {
                Column(modifier = Modifier.selectableGroup()) { // 单选组
                    FontSize.values().forEach { fontSize ->     // 遍历所有字体大小选项
                        ThemeOptionItem(
                            title = fontSize.displayName,       // 选项标题
                            description = "字体缩放 ${(fontSize.scale * 100).toInt()}%", // 选项描述
                            selected = themeConfig.fontSize == fontSize, // 是否选中
                            onClick = {                          // 点击事件
                                coroutineScope.launch {
                                    viewModel.setFontSize(fontSize) // 设置字体大小
                                }
                            }
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(ResponsiveStyles.getResponsiveSpacing())) // 卡片间距
            
            // 圆角大小设置卡片
            SettingsCard(
                title = "圆角样式",                      // 卡片标题
                icon = Icons.Default.RoundedCorner,     // 卡片图标
                description = "调整界面元素的圆角程度"    // 卡片描述
            ) {
                Column(modifier = Modifier.selectableGroup()) { // 单选组
                    CornerRadius.values().forEach { cornerRadius -> // 遍历所有圆角大小选项
                        ThemeOptionItem(
                            title = cornerRadius.displayName,   // 选项标题
                            description = "圆角缩放 ${(cornerRadius.scale * 100).toInt()}%", // 选项描述
                            selected = themeConfig.cornerRadius == cornerRadius, // 是否选中
                            onClick = {                          // 点击事件
                                coroutineScope.launch {
                                    viewModel.setCornerRadius(cornerRadius) // 设置圆角大小
                                }
                            }
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(ResponsiveStyles.getResponsiveSpacing())) // 卡片间距
            
            // 重置设置按钮
            Card(
                modifier = Modifier.fillMaxWidth(),     // 填充宽度
                colors = ComponentStyles.Card.outlinedCardColors(), // 使用轮廓卡片颜色样式
                border = ComponentStyles.Card.thinBorder // 使用细边框
            ) {
                Column(
                    modifier = Modifier.padding(ResponsiveStyles.getResponsivePadding()) // 使用响应式内边距
                ) {
                    Text(
                        text = "重置设置",                // 重置标题
                        style = ResponsiveStyles.getResponsiveTextStyle(MaterialTheme.typography.titleMedium), // 使用响应式中等标题样式
                        color = MaterialTheme.colorScheme.onSurface // 设置文字颜色
                    )
                    Spacer(modifier = Modifier.height(Dimensions.SpaceSmall)) // 标题下方间距
                    Text(
                        text = "将所有主题设置恢复为默认值", // 重置描述
                        style = ResponsiveStyles.getResponsiveTextStyle(MaterialTheme.typography.bodyMedium), // 使用响应式中等正文样式
                        color = MaterialTheme.colorScheme.onSurfaceVariant // 设置描述文字颜色
                    )
                    Spacer(modifier = Modifier.height(Dimensions.SpaceMedium)) // 描述下方间距
                    Button(
                        onClick = {                          // 点击重置按钮
                            coroutineScope.launch {
                                viewModel.resetThemeSettings() // 重置所有主题设置
                            }
                        },
                        colors = ComponentStyles.Button.outlinedButtonColors(), // 使用轮廓按钮颜色样式
                        modifier = Modifier.fillMaxWidth()  // 填充宽度
                    ) {
                        Text(
                            text = "重置为默认设置",          // 按钮文字
                            style = ResponsiveStyles.getResponsiveTextStyle(MaterialTheme.typography.labelLarge) // 使用响应式大标签样式
                        )
                    }
                }
            }
            
            // 底部间距，避免被底部导航栏遮挡
            Spacer(modifier = Modifier.height(Dimensions.BottomNavHeight + Dimensions.SpaceMedium))
        }
    }
}

/**
 * 设置卡片组件
 * 用于包装设置项的卡片容器
 * @param title 卡片标题
 * @param icon 卡片图标
 * @param description 卡片描述
 * @param content 卡片内容
 */
@Composable
private fun SettingsCard(
    title: String,                              // 卡片标题
    icon: ImageVector,                          // 卡片图标
    description: String,                        // 卡片描述
    content: @Composable () -> Unit             // 卡片内容
) {
    Card(
        modifier = Modifier.fillMaxWidth(),     // 填充宽度
        colors = ComponentStyles.Card.standardCardColors(), // 使用标准卡片颜色样式
        elevation = ComponentStyles.Card.lowElevation       // 使用低阴影高度
    ) {
        Column(
            modifier = Modifier.padding(ResponsiveStyles.getResponsivePadding()) // 使用响应式内边距
        ) {
            // 卡片标题行
            Row(
                verticalAlignment = Alignment.CenterVertically, // 垂直居中
                modifier = Modifier.padding(bottom = Dimensions.SpaceSmall) // 底部间距
            ) {
                Icon(
                    imageVector = icon,                  // 卡片图标
                    contentDescription = null,           // 装饰性图标，无需描述
                    modifier = Modifier
                        .size(Dimensions.IconMedium)     // 使用统一的中等图标尺寸
                        .padding(end = Dimensions.SpaceSmall), // 右侧间距
                    tint = MaterialTheme.colorScheme.primary // 使用主色调着色
                )
                Column {
                    Text(
                        text = title,                    // 卡片标题
                        style = ResponsiveStyles.getResponsiveTextStyle(MaterialTheme.typography.titleMedium), // 使用响应式中等标题样式
                        color = MaterialTheme.colorScheme.onSurface // 设置文字颜色
                    )
                    Text(
                        text = description,              // 卡片描述
                        style = ResponsiveStyles.getResponsiveTextStyle(MaterialTheme.typography.bodySmall), // 使用响应式小正文样式
                        color = MaterialTheme.colorScheme.onSurfaceVariant // 设置描述文字颜色
                    )
                }
            }
            
            // 卡片内容
            content()                                    // 渲染传入的内容
        }
    }
}

/**
 * 主题选项项组件
 * 用于显示可选择的主题选项
 * @param title 选项标题
 * @param description 选项描述（可选）
 * @param icon 选项图标（可选）
 * @param selected 是否选中
 * @param onClick 点击事件回调
 */
@Composable
private fun ThemeOptionItem(
    title: String,                              // 选项标题
    description: String? = null,                // 选项描述（可选）
    icon: ImageVector? = null,                  // 选项图标（可选）
    selected: Boolean,                          // 是否选中
    onClick: () -> Unit                         // 点击事件回调
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()                      // 填充宽度
            .selectable(                         // 可选择行为
                selected = selected,             // 选中状态
                onClick = onClick,               // 点击事件
                role = Role.RadioButton          // 无障碍角色为单选按钮
            )
            .padding(vertical = Dimensions.PaddingSmall), // 垂直内边距
        verticalAlignment = Alignment.CenterVertically    // 垂直居中
    ) {
        // 单选按钮
        RadioButton(
            selected = selected,                 // 选中状态
            onClick = null,                      // 点击事件由父组件处理
            modifier = Modifier.padding(end = Dimensions.SpaceSmall) // 右侧间距
        )
        
        // 选项图标（如果提供）
        if (icon != null) {
            Icon(
                imageVector = icon,              // 选项图标
                contentDescription = null,       // 装饰性图标，无需描述
                modifier = Modifier
                    .size(Dimensions.IconSmall)  // 使用统一的小图标尺寸
                    .padding(end = Dimensions.SpaceSmall), // 右侧间距
                tint = if (selected) {           // 根据选中状态设置图标颜色
                    MaterialTheme.colorScheme.primary     // 选中时使用主色调
                } else {
                    MaterialTheme.colorScheme.onSurfaceVariant // 未选中时使用表面变体色
                }
            )
        }
        
        // 选项文字内容
        Column {
            Text(
                text = title,                    // 选项标题
                style = ResponsiveStyles.getResponsiveTextStyle(MaterialTheme.typography.bodyLarge), // 使用响应式大正文样式
                color = if (selected) {          // 根据选中状态设置文字颜色
                    MaterialTheme.colorScheme.primary     // 选中时使用主色调
                } else {
                    MaterialTheme.colorScheme.onSurface   // 未选中时使用表面色
                }
            )
            
            // 选项描述（如果提供）
            if (description != null) {
                Text(
                    text = description,          // 选项描述
                    style = ResponsiveStyles.getResponsiveTextStyle(MaterialTheme.typography.bodySmall), // 使用响应式小正文样式
                    color = MaterialTheme.colorScheme.onSurfaceVariant // 设置描述文字颜色
                )
            }
        }
    }
}