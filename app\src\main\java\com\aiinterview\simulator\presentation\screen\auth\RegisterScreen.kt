package com.aiinterview.simulator.presentation.screen.auth

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.aiinterview.simulator.presentation.viewmodel.AuthViewModel
import com.aiinterview.simulator.domain.util.Resource

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RegisterScreen(
    onNavigateToHome: () -> Unit,
    onNavigateToLogin: () -> Unit,
    viewModel: AuthViewModel = hiltViewModel()
) {
    var phoneNumber by remember { mutableStateOf(TextFieldValue()) }
    var verificationCode by remember { mutableStateOf(TextFieldValue()) }
    
    val authState by viewModel.authState.collectAsState()
    val isLoggedIn by viewModel.isLoggedIn.collectAsState()
    
    LaunchedEffect(isLoggedIn) {
        if (isLoggedIn) {
            onNavigateToHome()
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "注册账号",
            style = MaterialTheme.typography.headlineMedium,
            modifier = Modifier.padding(bottom = 32.dp)
        )
        
        OutlinedTextField(
            value = phoneNumber,
            onValueChange = { phoneNumber = it },
            label = { Text("手机号") },
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp)
        )
        
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 24.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            OutlinedTextField(
                value = verificationCode,
                onValueChange = { verificationCode = it },
                label = { Text("验证码") },
                modifier = Modifier.weight(1f)
            )
            
            Button(
                onClick = {
                    // TODO: 实现发送验证码功能
                },
                modifier = Modifier.height(56.dp),
                enabled = phoneNumber.text.isNotBlank()
            ) {
                Text("发送验证码")
            }
        }
        
        Button(
            onClick = {
                viewModel.register(
                    phoneNumber = phoneNumber.text,
                    verificationCode = verificationCode.text
                )
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(50.dp),
            enabled = phoneNumber.text.isNotBlank() && verificationCode.text.isNotBlank()
        ) {
            Text("注册")
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        TextButton(onClick = onNavigateToLogin) {
            Text("已有账号？立即登录")
        }
        
        when (authState) {
            is Resource.Loading -> {
                CircularProgressIndicator(
                    modifier = Modifier.padding(top = 16.dp)
                )
            }
            is Resource.Error -> {
                Text(
                    text = authState.message ?: "注册失败",
                    color = MaterialTheme.colorScheme.error,
                    modifier = Modifier.padding(top = 16.dp)
                )
            }
            else -> {}
        }
    }
}