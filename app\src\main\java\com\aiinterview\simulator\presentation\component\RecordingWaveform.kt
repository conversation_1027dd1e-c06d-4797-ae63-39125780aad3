package com.aiinterview.simulator.presentation.component

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.*
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import kotlin.math.sin
import kotlin.random.Random

/**
 * 录音波形显示组件
 * 在录音过程中显示动态的音频波形效果
 */
@Composable
fun RecordingWaveform(
    modifier: Modifier = Modifier, // 修饰符
    isRecording: Boolean = true, // 是否正在录音
    volumeLevel: Float = 0.5f, // 音量级别 (0.0 - 1.0)
    waveColor: Color = MaterialTheme.colorScheme.primary, // 波形颜色
    backgroundColor: Color = MaterialTheme.colorScheme.surfaceVariant, // 背景颜色
    waveCount: Int = 20, // 波形条数
    minHeight: Dp = 4.dp, // 最小高度
    maxHeight: Dp = 40.dp // 最大高度
) {
    val density = LocalDensity.current
    
    // 动画状态
    val infiniteTransition = rememberInfiniteTransition(label = "waveform")
    
    // 波形动画
    val animatedValues = remember { mutableStateListOf<Float>() }
    
    // 初始化波形数据
    LaunchedEffect(waveCount) {
        animatedValues.clear()
        repeat(waveCount) {
            animatedValues.add(Random.nextFloat())
        }
    }
    
    // 动画更新
    LaunchedEffect(isRecording, volumeLevel) {
        if (isRecording) {
            // 录音时更新波形数据
            while (isRecording) {
                for (i in animatedValues.indices) {
                    // 根据音量级别和随机因子生成波形高度
                    val baseHeight = volumeLevel * 0.7f + 0.3f // 基础高度
                    val randomFactor = Random.nextFloat() * 0.4f - 0.2f // 随机因子
                    animatedValues[i] = (baseHeight + randomFactor).coerceIn(0.1f, 1.0f)
                }
                kotlinx.coroutines.delay(100) // 每100ms更新一次
            }
        } else {
            // 不录音时逐渐降低波形
            for (i in animatedValues.indices) {
                animatedValues[i] = animatedValues[i] * 0.9f
            }
        }
    }
    
    // 绘制波形
    Canvas(
        modifier = modifier
            .fillMaxWidth()
            .height(maxHeight)
    ) {
        val canvasWidth = size.width // 画布宽度
        val canvasHeight = size.height // 画布高度
        val waveWidth = canvasWidth / waveCount // 每个波形条的宽度
        val strokeWidth = with(density) { 3.dp.toPx() } // 线条宽度
        
        // 绘制背景
        drawRect(
            color = backgroundColor,
            size = size
        )
        
        // 绘制波形条
        for (i in 0 until waveCount) {
            val x = i * waveWidth + waveWidth / 2 // X坐标
            val waveHeight = if (animatedValues.size > i) {
                // 根据动画值计算波形高度
                val animatedHeight = animatedValues[i]
                val minHeightPx = with(density) { minHeight.toPx() }
                val maxHeightPx = canvasHeight
                minHeightPx + (maxHeightPx - minHeightPx) * animatedHeight
            } else {
                with(density) { minHeight.toPx() }
            }
            
            // 计算起始和结束Y坐标（居中显示）
            val startY = (canvasHeight - waveHeight) / 2
            val endY = startY + waveHeight
            
            // 绘制波形条
            drawLine(
                color = waveColor,
                start = Offset(x, startY),
                end = Offset(x, endY),
                strokeWidth = strokeWidth,
                cap = StrokeCap.Round
            )
        }
    }
}

/**
 * 简化版录音波形组件
 * 显示静态的波形效果
 */
@Composable
fun StaticWaveform(
    modifier: Modifier = Modifier,
    waveColor: Color = MaterialTheme.colorScheme.primary,
    waveCount: Int = 15,
    heights: List<Float> = listOf(0.3f, 0.7f, 0.5f, 0.9f, 0.4f, 0.8f, 0.6f, 1.0f, 0.5f, 0.7f, 0.3f, 0.6f, 0.8f, 0.4f, 0.5f)
) {
    val density = LocalDensity.current
    
    Canvas(
        modifier = modifier
            .fillMaxWidth()
            .height(32.dp)
    ) {
        val canvasWidth = size.width
        val canvasHeight = size.height
        val waveWidth = canvasWidth / waveCount
        val strokeWidth = with(density) { 2.dp.toPx() }
        
        for (i in 0 until minOf(waveCount, heights.size)) {
            val x = i * waveWidth + waveWidth / 2
            val waveHeight = canvasHeight * heights[i]
            val startY = (canvasHeight - waveHeight) / 2
            val endY = startY + waveHeight
            
            drawLine(
                color = waveColor,
                start = Offset(x, startY),
                end = Offset(x, endY),
                strokeWidth = strokeWidth,
                cap = StrokeCap.Round
            )
        }
    }
}

/**
 * 圆形录音指示器
 * 显示录音状态的圆形动画
 */
@Composable
fun RecordingIndicator(
    modifier: Modifier = Modifier,
    isRecording: Boolean = false,
    color: Color = MaterialTheme.colorScheme.error,
    size: Dp = 16.dp
) {
    // 脉冲动画
    val infiniteTransition = rememberInfiniteTransition(label = "recording_indicator")
    
    val scale by infiniteTransition.animateFloat(
        initialValue = 1f,
        targetValue = if (isRecording) 1.3f else 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = EaseInOut),
            repeatMode = RepeatMode.Reverse
        ),
        label = "scale"
    )
    
    val alpha by infiniteTransition.animateFloat(
        initialValue = 1f,
        targetValue = if (isRecording) 0.6f else 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = EaseInOut),
            repeatMode = RepeatMode.Reverse
        ),
        label = "alpha"
    )
    
    Canvas(
        modifier = modifier.size(size)
    ) {
        val radius = size.toPx() / 2 * scale
        
        drawCircle(
            color = color.copy(alpha = alpha),
            radius = radius,
            center = center
        )
    }
}

/**
 * 音量级别指示器
 * 显示当前录音的音量级别
 */
@Composable
fun VolumeIndicator(
    modifier: Modifier = Modifier,
    volumeLevel: Float = 0f, // 音量级别 (0.0 - 1.0)
    color: Color = MaterialTheme.colorScheme.primary,
    backgroundColor: Color = MaterialTheme.colorScheme.surfaceVariant
) {
    val animatedVolume by animateFloatAsState(
        targetValue = volumeLevel,
        animationSpec = tween(durationMillis = 200),
        label = "volume"
    )
    
    Canvas(
        modifier = modifier
            .width(200.dp)
            .height(8.dp)
    ) {
        val canvasWidth = size.width
        val canvasHeight = size.height
        
        // 绘制背景
        drawRect(
            color = backgroundColor,
            size = size
        )
        
        // 绘制音量条
        val volumeWidth = canvasWidth * animatedVolume
        drawRect(
            color = color,
            size = androidx.compose.ui.geometry.Size(volumeWidth, canvasHeight)
        )
    }
}