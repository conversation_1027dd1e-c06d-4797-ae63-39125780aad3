<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\health-report-tracker\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\health-report-tracker\app\src\main\res"><file path="E:\health-report-tracker\app\src\main\res\values\app_store_strings.xml" qualifiers=""><string name="app_store_name">AI面试模拟器</string><string name="app_store_short_description">智能面试练习，AI评价指导</string><string name="app_store_tagline">让面试不再紧张，让成功触手可及</string><string name="app_store_description">
        AI面试模拟器是一款专业的面试练习应用，通过先进的AI技术为求职者提供真实的面试模拟体验。
        
        【核心功能】
        ✓ 智能面试问题生成 - 根据岗位类型生成专业面试题目
        ✓ 语音回答识别 - 高精度语音识别，支持中文普通话
        ✓ AI综合评价 - 从多个维度分析回答质量，提供改进建议
        ✓ 多种岗位选择 - 涵盖公务员、企业、事业单位等多种岗位
        ✓ 面试记录管理 - 完整保存面试历史，追踪学习进度
        ✓ 进步趋势分析 - 可视化展示能力提升轨迹
        
        【产品特色】
        • 专业的面试题库，覆盖结构化面试各个类型
        • 智能AI评价系统，提供个性化学习建议
        • 完整的面试流程模拟，还原真实面试场景
        • 简洁易用的界面设计，专注提升用户体验
        
        【适用人群】
        • 准备公务员面试的考生
        • 求职面试的应届毕业生
        • 需要提升面试技能的职场人士
        • 想要练习口语表达的用户
        
        立即下载，开启您的面试成功之路！
    </string><string name="app_store_changelog_v1_0_0">
        【新功能】
        • 全新的AI面试模拟体验
        • 支持多种面试岗位类型选择
        • 智能语音识别与分析功能
        • 专业的AI评价系统
        • 完整的面试记录管理
        • 个人进步趋势分析
        
        【特色亮点】
        • 高精度语音识别，准确率达90%以上
        • 多维度AI评价，提供专业改进建议
        • 丰富的面试题库，持续更新
        • 简洁直观的用户界面
        
        【优化改进】
        • 优化应用启动速度
        • 提升语音识别准确率
        • 改进用户界面体验
        • 增强应用稳定性
    </string><string name="app_store_keywords">面试,AI,模拟,练习,求职,公务员,语音识别,评价,学习,职场</string><string name="app_store_category">教育学习</string><string name="app_store_subcategory">职业培训</string><string name="app_store_developer">AI面试模拟器团队</string><string name="app_store_developer_website">https://www.aiinterview.com</string><string name="app_store_support_email"><EMAIL></string><string name="app_store_privacy_policy_url">https://www.aiinterview.com/privacy</string><string name="permission_microphone_description">用于录制面试回答语音</string><string name="permission_storage_description">用于保存面试记录和音频文件</string><string name="permission_network_description">用于获取面试题目和AI评价服务</string><string name="app_store_age_rating">12+</string><string name="app_store_age_rating_description">适合12岁以上用户使用</string><string name="screenshot_1_description">主界面 - 选择面试岗位类型</string><string name="screenshot_2_description">面试进行中 - 语音回答录制</string><string name="screenshot_3_description">AI评价报告 - 详细分析和建议</string><string name="screenshot_4_description">历史记录 - 面试记录管理</string><string name="screenshot_5_description">进步分析 - 能力提升趋势</string><string name="huawei_app_description">专业AI面试模拟器，助力求职成功。支持语音识别、智能评价，提供个性化学习建议。</string><string name="xiaomi_app_description">智能面试练习工具，AI技术驱动，帮助用户提升面试技能，增强求职竞争力。</string><string name="oppo_app_description">AI驱动的面试模拟应用，提供真实面试体验，专业评价指导，让面试更有把握。</string><string name="vivo_app_description">面试神器！AI智能评价，语音识别技术，全方位提升面试表现，求职必备工具。</string><string name="tencent_app_description">专业面试模拟器，AI技术加持，语音识别评价，助力求职者提升面试技能。</string><string name="qihoo_app_description">AI面试练习工具，智能语音识别，专业评价建议，让面试准备更高效。</string><string name="baidu_app_description">智能面试模拟器，AI评价系统，语音识别技术，全面提升面试能力。</string><string name="update_available_title">发现新版本</string><string name="update_available_message">新版本包含功能改进和bug修复，建议立即更新。</string><string name="update_now">立即更新</string><string name="update_later">稍后更新</string><string name="update_ignore">忽略此版本</string><string name="rate_app_title">喜欢AI面试模拟器吗？</string><string name="rate_app_message">您的评价是我们前进的动力，请为我们打分并留下宝贵建议！</string><string name="rate_now">立即评价</string><string name="rate_later">稍后提醒</string><string name="rate_never">不再提醒</string><string name="share_app_title">推荐一款面试神器</string><string name="share_app_message">AI面试模拟器，智能练习，专业评价，让面试不再紧张！快来试试吧：%s</string></file><file path="E:\health-report-tracker\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">AI面试模拟器</string><string name="error_network">网络连接失败，请检查网络设置</string><string name="error_timeout">请求超时，请稍后重试</string><string name="error_unauthorized">请先登录</string><string name="error_token_expired">登录已过期，请重新登录</string><string name="error_speech_recognition">语音识别失败，请重新录制</string><string name="error_microphone_permission">需要麦克风权限才能录音，请在设置中开启</string><string name="error_server">服务器内部错误，请稍后重试</string><string name="error_no_internet">无网络连接，请检查网络设置</string><string name="error_unknown">发生未知错误，请重试</string><string name="network_offline">当前处于离线模式</string><string name="network_poor">网络信号较弱</string><string name="network_connecting">正在连接网络…</string><string name="network_connected">网络已连接</string><string name="degradation_offline_mode">当前处于离线模式，部分功能可能受限</string><string name="degradation_lightweight">网络信号较弱，已启用轻量级模式</string><string name="degradation_cache_only">服务暂时不可用，正在使用缓存数据</string><string name="degradation_simplified">服务性能降级，已启用简化模式</string><string name="degradation_basic">网络质量一般，已启用基础模式</string><string name="memory_low">内存不足，正在清理缓存</string><string name="memory_critical">内存严重不足，请关闭其他应用</string><string name="startup_optimizing">正在优化启动速度…</string><string name="cache_clearing">正在清理缓存…</string><string name="crash_report_title">应用崩溃报告</string><string name="crash_report_message">应用遇到了问题，是否发送崩溃报告帮助我们改进？</string><string name="crash_report_send">发送报告</string><string name="crash_report_cancel">取消</string><string name="retry">重试</string><string name="cancel">取消</string><string name="ok">确定</string><string name="close">关闭</string><string name="settings">设置</string></file><file path="E:\health-report-tracker\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.AIInterviewSimulator" parent="android:Theme.Material.Light.NoActionBar"/></file><file name="backup_rules" path="E:\health-report-tracker\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="E:\health-report-tracker\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="official$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\health-report-tracker\app\src\official\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="official" generated-set="official$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\health-report-tracker\app\src\official\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\health-report-tracker\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\health-report-tracker\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\health-report-tracker\app\build\generated\res\resValues\official\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\health-report-tracker\app\build\generated\res\resValues\official\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="variant$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\health-report-tracker\app\src\officialDebug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="variant" generated-set="variant$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\health-report-tracker\app\src\officialDebug\res"/></dataSet><mergedItems/></merger>