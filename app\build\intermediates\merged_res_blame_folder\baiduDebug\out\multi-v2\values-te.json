{"logs": [{"outputFile": "com.aiinterview.simulator.app-mergeBaiduDebugResources-55:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\73f51c4b00c3d8b5bd0709c291d17c64\\transformed\\material3-1.1.2\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,180,307,417,543,624,719,832,968,1076,1215,1295,1394,1484,1578,1690,1816,1920,2065,2207,2344,2536,2668,2780,2898,3035,3128,3223,3344,3468,3570,3672,3774,3912,4058,4162,4261,4333,4416,4506,4594,4697,4773,4852,4949,5050,5143,5241,5325,5432,5530,5627,5746,5822,5926", "endColumns": "124,126,109,125,80,94,112,135,107,138,79,98,89,93,111,125,103,144,141,136,191,131,111,117,136,92,94,120,123,101,101,101,137,145,103,98,71,82,89,87,102,75,78,96,100,92,97,83,106,97,96,118,75,103,92", "endOffsets": "175,302,412,538,619,714,827,963,1071,1210,1290,1389,1479,1573,1685,1811,1915,2060,2202,2339,2531,2663,2775,2893,3030,3123,3218,3339,3463,3565,3667,3769,3907,4053,4157,4256,4328,4411,4501,4589,4692,4768,4847,4944,5045,5138,5236,5320,5427,5525,5622,5741,5817,5921,6014"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,230,357,467,1526,1607,1702,1815,1951,2059,2198,2278,2377,2467,2561,2673,2799,2903,3048,3190,3327,3519,3651,3763,3881,4018,4111,4206,4327,4451,4553,4655,4757,4895,5041,5145,5441,5602,6362,6529,6718,7084,7160,7239,7336,7437,7530,7628,7712,7819,7917,8014,8133,8209,8313", "endColumns": "124,126,109,125,80,94,112,135,107,138,79,98,89,93,111,125,103,144,141,136,191,131,111,117,136,92,94,120,123,101,101,101,137,145,103,98,71,82,89,87,102,75,78,96,100,92,97,83,106,97,96,118,75,103,92", "endOffsets": "225,352,462,588,1602,1697,1810,1946,2054,2193,2273,2372,2462,2556,2668,2794,2898,3043,3185,3322,3514,3646,3758,3876,4013,4106,4201,4322,4446,4548,4650,4752,4890,5036,5140,5239,5508,5680,6447,6612,6816,7155,7234,7331,7432,7525,7623,7707,7814,7912,8009,8128,8204,8308,8401"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d78fa9dcb2040782f62e575b1696aadd\\transformed\\ui-release\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,288,385,485,574,663,759,847,931,1004,1077,1161,1251,1328,1405,1474", "endColumns": "95,86,96,99,88,88,95,87,83,72,72,83,89,76,76,68,116", "endOffsets": "196,283,380,480,569,658,754,842,926,999,1072,1156,1246,1323,1400,1469,1586"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1343,1439,5244,5341,5513,5685,5774,5870,5958,6042,6115,6188,6272,6452,6821,6898,6967", "endColumns": "95,86,96,99,88,88,95,87,83,72,72,83,89,76,76,68,116", "endOffsets": "1434,1521,5336,5436,5597,5769,5865,5953,6037,6110,6183,6267,6357,6524,6893,6962,7079"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fd044ab7a523edb1345beec00f65097\\transformed\\core-1.12.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "593,695,803,905,1006,1112,1219,6617", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "690,798,900,1001,1107,1214,1338,6713"}}]}]}