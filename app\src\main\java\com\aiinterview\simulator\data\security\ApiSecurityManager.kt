package com.aiinterview.simulator.data.security

import android.util.Base64
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response
import java.nio.charset.StandardCharsets
import java.security.MessageDigest
import java.security.SecureRandom
import java.util.*
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec
import javax.inject.Inject
import javax.inject.Singleton

/**
 * API安全管理器
 * 负责API请求的签名验证和安全传输
 */
@Singleton
class ApiSecurityManager @Inject constructor(
    private val encryptedStorageManager: EncryptedStorageManager // 注入加密存储管理器
) {
    
    companion object {
        // HMAC算法名称
        private const val HMAC_SHA256 = "HmacSHA256"
        // 签名头名称
        private const val HEADER_SIGNATURE = "X-Signature"
        // 时间戳头名称
        private const val HEADER_TIMESTAMP = "X-Timestamp"
        // 随机数头名称
        private const val HEADER_NONCE = "X-Nonce"
        // 请求ID头名称
        private const val HEADER_REQUEST_ID = "X-Request-ID"
        // 客户端版本头名称
        private const val HEADER_CLIENT_VERSION = "X-Client-Version"
        // 设备ID头名称
        private const val HEADER_DEVICE_ID = "X-Device-ID"
        // 应用密钥（实际项目中应该从安全的地方获取）
        private const val APP_SECRET = "ai_interview_app_secret_key_2024"
        // 签名有效期（5分钟）
        private const val SIGNATURE_VALIDITY_PERIOD = 5 * 60 * 1000L
    }
    
    // 安全随机数生成器
    private val secureRandom = SecureRandom()
    
    /**
     * 创建API安全拦截器
     * @return API安全拦截器
     */
    fun createSecurityInterceptor(): Interceptor {
        return ApiSecurityInterceptor()
    }
    
    /**
     * 生成请求签名
     * @param method HTTP方法
     * @param url 请求URL
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @param body 请求体（可选）
     * @return 签名字符串
     */
    fun generateSignature(
        method: String,
        url: String,
        timestamp: Long,
        nonce: String,
        body: String? = null
    ): String {
        // 构建签名字符串
        val signatureString = buildString {
            append(method.uppercase())
            append("\n")
            append(url)
            append("\n")
            append(timestamp)
            append("\n")
            append(nonce)
            append("\n")
            if (!body.isNullOrEmpty()) {
                append(calculateBodyHash(body))
            }
        }
        
        // 使用HMAC-SHA256生成签名
        return generateHmacSha256(signatureString, APP_SECRET)
    }
    
    /**
     * 验证请求签名
     * @param signature 收到的签名
     * @param method HTTP方法
     * @param url 请求URL
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @param body 请求体（可选）
     * @return true表示签名有效，false表示签名无效
     */
    fun verifySignature(
        signature: String,
        method: String,
        url: String,
        timestamp: Long,
        nonce: String,
        body: String? = null
    ): Boolean {
        // 检查时间戳是否在有效期内
        val currentTime = System.currentTimeMillis()
        if (Math.abs(currentTime - timestamp) > SIGNATURE_VALIDITY_PERIOD) {
            return false
        }
        
        // 生成期望的签名
        val expectedSignature = generateSignature(method, url, timestamp, nonce, body)
        
        // 比较签名
        return signature == expectedSignature
    }
    
    /**
     * 生成随机数
     * @return 随机数字符串
     */
    fun generateNonce(): String {
        val bytes = ByteArray(16)
        secureRandom.nextBytes(bytes)
        return Base64.encodeToString(bytes, Base64.NO_WRAP)
    }
    
    /**
     * 生成请求ID
     * @return 唯一的请求ID
     */
    fun generateRequestId(): String {
        return UUID.randomUUID().toString()
    }
    
    /**
     * 获取设备ID
     * @return 设备ID
     */
    fun getDeviceId(): String {
        // 从加密存储中获取设备ID，如果不存在则生成新的
        var deviceId = encryptedStorageManager.getString("device_id")
        if (deviceId == null) {
            deviceId = generateDeviceId()
            encryptedStorageManager.saveString("device_id", deviceId)
        }
        return deviceId
    }
    
    /**
     * 生成设备ID
     * @return 新的设备ID
     */
    private fun generateDeviceId(): String {
        val timestamp = System.currentTimeMillis()
        val random = secureRandom.nextLong()
        val deviceString = "${timestamp}_${random}"
        return calculateSha256Hash(deviceString)
    }
    
    /**
     * 计算请求体的哈希值
     * @param body 请求体
     * @return 哈希值
     */
    private fun calculateBodyHash(body: String): String {
        return calculateSha256Hash(body)
    }
    
    /**
     * 计算SHA256哈希值
     * @param input 输入字符串
     * @return SHA256哈希值
     */
    private fun calculateSha256Hash(input: String): String {
        val digest = MessageDigest.getInstance("SHA-256")
        val hashBytes = digest.digest(input.toByteArray(StandardCharsets.UTF_8))
        return Base64.encodeToString(hashBytes, Base64.NO_WRAP)
    }
    
    /**
     * 生成HMAC-SHA256签名
     * @param data 要签名的数据
     * @param key 签名密钥
     * @return HMAC-SHA256签名
     */
    private fun generateHmacSha256(data: String, key: String): String {
        val mac = Mac.getInstance(HMAC_SHA256)
        val secretKeySpec = SecretKeySpec(key.toByteArray(StandardCharsets.UTF_8), HMAC_SHA256)
        mac.init(secretKeySpec)
        val hashBytes = mac.doFinal(data.toByteArray(StandardCharsets.UTF_8))
        return Base64.encodeToString(hashBytes, Base64.NO_WRAP)
    }
    
    /**
     * API安全拦截器内部类
     * 负责在请求中添加安全头信息
     */
    private inner class ApiSecurityInterceptor : Interceptor {
        
        override fun intercept(chain: Interceptor.Chain): Response {
            val originalRequest = chain.request()
            
            // 获取请求信息
            val method = originalRequest.method
            val url = originalRequest.url.toString()
            val timestamp = System.currentTimeMillis()
            val nonce = generateNonce()
            val requestId = generateRequestId()
            val deviceId = getDeviceId()
            
            // 获取请求体内容
            val body = originalRequest.body?.let { requestBody ->
                val buffer = okio.Buffer()
                requestBody.writeTo(buffer)
                buffer.readUtf8()
            }
            
            // 生成签名
            val signature = generateSignature(method, url, timestamp, nonce, body)
            
            // 构建新的请求，添加安全头
            val newRequest = originalRequest.newBuilder()
                .addHeader(HEADER_SIGNATURE, signature)
                .addHeader(HEADER_TIMESTAMP, timestamp.toString())
                .addHeader(HEADER_NONCE, nonce)
                .addHeader(HEADER_REQUEST_ID, requestId)
                .addHeader(HEADER_CLIENT_VERSION, getClientVersion())
                .addHeader(HEADER_DEVICE_ID, deviceId)
                .addHeader("User-Agent", getUserAgent())
                .build()
            
            return chain.proceed(newRequest)
        }
        
        /**
         * 获取客户端版本
         * @return 客户端版本号
         */
        private fun getClientVersion(): String {
            return "1.0.0" // 实际项目中应该从BuildConfig获取
        }
        
        /**
         * 获取用户代理字符串
         * @return 用户代理字符串
         */
        private fun getUserAgent(): String {
            return "AIInterviewSimulator/1.0.0 (Android)"
        }
    }
}

/**
 * API安全配置数据类
 * 用于配置API安全相关参数
 */
data class ApiSecurityConfig(
    val enableSignature: Boolean = true,           // 是否启用签名验证
    val enableTimestampCheck: Boolean = true,      // 是否启用时间戳检查
    val signatureValidityPeriod: Long = 5 * 60 * 1000L, // 签名有效期（毫秒）
    val enableRequestIdCheck: Boolean = true,      // 是否启用请求ID检查
    val enableDeviceIdCheck: Boolean = true        // 是否启用设备ID检查
)

/**
 * API安全异常类
 * 用于表示API安全相关的异常
 */
sealed class ApiSecurityException(message: String) : Exception(message) {
    object InvalidSignature : ApiSecurityException("请求签名无效")
    object ExpiredTimestamp : ApiSecurityException("请求时间戳已过期")
    object InvalidNonce : ApiSecurityException("请求随机数无效")
    object InvalidDeviceId : ApiSecurityException("设备ID无效")
    object MissingSecurityHeaders : ApiSecurityException("缺少安全头信息")
}