package com.aiinterview.simulator.util

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.TestDispatcher
import kotlinx.coroutines.test.UnconfinedTestDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.setMain
import org.junit.rules.TestWatcher
import org.junit.runner.Description

/**
 * 测试工具类
 * 提供通用的测试辅助方法和工具
 */
object TestUtils {
    
    /**
     * 创建测试用的用户ID
     */
    fun createTestUserId(): String = "test_user_${System.currentTimeMillis()}"
    
    /**
     * 创建测试用的会话ID
     */
    fun createTestSessionId(): String = "test_session_${System.currentTimeMillis()}"
    
    /**
     * 创建测试用的问题ID
     */
    fun createTestQuestionId(): String = "test_question_${System.currentTimeMillis()}"
    
    /**
     * 等待指定时间（毫秒）
     */
    suspend fun waitFor(millis: Long) {
        kotlinx.coroutines.delay(millis)
    }
    
    /**
     * 验证字符串是否为有效的UUID格式
     */
    fun isValidUUID(uuid: String): Boolean {
        return try {
            java.util.UUID.fromString(uuid)
            true
        } catch (e: IllegalArgumentException) {
            false
        }
    }
    
    /**
     * 创建测试用的时间戳
     */
    fun createTestTimestamp(): Long = System.currentTimeMillis()
    
    /**
     * 验证两个时间戳是否在指定范围内
     */
    fun isTimestampInRange(timestamp: Long, expected: Long, toleranceMs: Long = 1000): Boolean {
        return kotlin.math.abs(timestamp - expected) <= toleranceMs
    }
}

/**
 * 协程测试规则
 * 用于在测试中设置和清理测试调度器
 */
@ExperimentalCoroutinesApi
class MainCoroutineRule(
    private val testDispatcher: TestDispatcher = UnconfinedTestDispatcher()
) : TestWatcher() {
    
    override fun starting(description: Description) {
        super.starting(description)
        // 设置主调度器为测试调度器
        Dispatchers.setMain(testDispatcher)
    }
    
    override fun finished(description: Description) {
        super.finished(description)
        // 重置主调度器
        Dispatchers.resetMain()
    }
}

/**
 * 测试数据验证工具
 */
object TestDataValidator {
    
    /**
     * 验证用户数据是否有效
     */
    fun isValidUserData(
        id: String?,
        phoneNumber: String?,
        nickname: String?
    ): Boolean {
        return !id.isNullOrBlank() && 
               !phoneNumber.isNullOrBlank() && 
               phoneNumber.matches(Regex("^1[3-9]\\d{9}$"))
    }
    
    /**
     * 验证面试会话数据是否有效
     */
    fun isValidInterviewSession(
        id: String?,
        userId: String?,
        positionId: String?,
        status: String?
    ): Boolean {
        return !id.isNullOrBlank() && 
               !userId.isNullOrBlank() && 
               !positionId.isNullOrBlank() && 
               !status.isNullOrBlank()
    }
    
    /**
     * 验证评价数据是否有效
     */
    fun isValidEvaluation(
        sessionId: String?,
        overallScore: Double?,
        feedback: String?
    ): Boolean {
        return !sessionId.isNullOrBlank() && 
               overallScore != null && 
               overallScore >= 0.0 && 
               overallScore <= 100.0 && 
               !feedback.isNullOrBlank()
    }
}