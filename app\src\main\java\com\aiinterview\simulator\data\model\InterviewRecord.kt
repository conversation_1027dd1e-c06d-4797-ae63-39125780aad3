package com.aiinterview.simulator.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 面试记录实体类 - 用于Room数据库存储
 * 包含完整的面试信息，包括音频文件和文本记录的关联
 */
@Entity(tableName = "interview_records")
data class InterviewRecord(
    @PrimaryKey
    val id: String,
    val sessionId: String,
    val userId: String,
    val positionId: String,
    val positionName: String,
    val positionCategory: String,
    val startTime: Long,
    val endTime: Long,
    val duration: Int, // 面试总时长（秒）
    val status: String, // COMPLETED, CANCELLED
    val questionsJson: String, // JSON格式存储问题列表
    val answersJson: String, // JSON格式存储答案列表（包含音频文件路径和文本）
    val audioFilesJson: String, // JSON格式存储音频文件路径列表
    val evaluationId: String? = null, // 关联的评价记录ID
    val overallScore: Double? = null, // 总体评分
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val isSynced: Boolean = false, // 是否已同步到云端
    val syncedAt: Long? = null // 同步时间
)

/**
 * 面试记录业务模型
 */
data class InterviewRecordModel(
    val id: String,
    val sessionId: String,
    val userId: String,
    val positionId: String,
    val positionName: String,
    val positionCategory: String,
    val startTime: Long,
    val endTime: Long,
    val duration: Int,
    val status: InterviewRecordStatus,
    val questions: List<Question>,
    val answers: List<AnswerWithAudio>,
    val audioFiles: List<AudioFileInfo>,
    val evaluationId: String? = null,
    val overallScore: Double? = null,
    val createdAt: Long,
    val updatedAt: Long,
    val isSynced: Boolean = false,
    val syncedAt: Long? = null
)

/**
 * 面试记录摘要 - 用于列表显示
 */
data class InterviewRecordSummary(
    val id: String,
    val sessionId: String,
    val positionName: String,
    val positionCategory: String,
    val startTime: Long,
    val duration: Int,
    val overallScore: Double?,
    val status: InterviewRecordStatus,
    val isSynced: Boolean
)

/**
 * 带音频信息的答案
 */
data class AnswerWithAudio(
    val questionId: String,
    val questionContent: String,
    val audioFilePath: String,
    val audioUrl: String? = null, // 云端音频URL
    val transcription: String,
    val duration: Int, // 录音时长（秒）
    val submittedAt: Long,
    val audioFileSize: Long = 0L // 音频文件大小（字节）
)

/**
 * 音频文件信息
 */
data class AudioFileInfo(
    val filePath: String,
    val fileName: String,
    val fileSize: Long,
    val duration: Int,
    val questionId: String,
    val createdAt: Long,
    val isUploaded: Boolean = false,
    val uploadUrl: String? = null
)

/**
 * 面试记录状态
 */
enum class InterviewRecordStatus {
    COMPLETED, // 已完成
    CANCELLED, // 已取消
    INTERRUPTED // 中断（异常结束）
}