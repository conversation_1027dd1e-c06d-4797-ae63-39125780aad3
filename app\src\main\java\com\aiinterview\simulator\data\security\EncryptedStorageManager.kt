package com.aiinterview.simulator.data.security

import android.content.Context
import android.content.SharedPreferences
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 加密存储管理器
 * 负责管理应用中的敏感数据加密存储
 */
@Singleton
class EncryptedStorageManager @Inject constructor(
    @ApplicationContext private val context: Context // 注入应用上下文
) {
    
    companion object {
        // 加密SharedPreferences文件名
        private const val ENCRYPTED_PREFS_FILE = "encrypted_prefs"
        // 用户认证信息存储文件名
        private const val AUTH_PREFS_FILE = "auth_encrypted_prefs"
        // 面试数据存储文件名
        private const val INTERVIEW_PREFS_FILE = "interview_encrypted_prefs"
    }
    
    // 主密钥，用于加密SharedPreferences
    private val masterKey: MasterKey by lazy {
        MasterKey.Builder(context)
            .setKeyScheme(MasterKey.KeyScheme.AES256_GCM) // 使用AES256-GCM加密算法
            .build()
    }
    
    // 通用加密SharedPreferences
    private val encryptedPrefs: SharedPreferences by lazy {
        createEncryptedSharedPreferences(ENCRYPTED_PREFS_FILE)
    }
    
    // 用户认证信息加密SharedPreferences
    private val authPrefs: SharedPreferences by lazy {
        createEncryptedSharedPreferences(AUTH_PREFS_FILE)
    }
    
    // 面试数据加密SharedPreferences
    private val interviewPrefs: SharedPreferences by lazy {
        createEncryptedSharedPreferences(INTERVIEW_PREFS_FILE)
    }
    
    /**
     * 创建加密的SharedPreferences实例
     * @param fileName 文件名
     * @return 加密的SharedPreferences实例
     */
    private fun createEncryptedSharedPreferences(fileName: String): SharedPreferences {
        return EncryptedSharedPreferences.create(
            context,
            fileName,
            masterKey,
            EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,    // 键加密方案
            EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM   // 值加密方案
        )
    }
    
    // ==================== 用户认证数据存储 ====================
    
    /**
     * 保存用户访问令牌
     * @param token 访问令牌
     */
    fun saveAccessToken(token: String) {
        authPrefs.edit()
            .putString("access_token", token)
            .apply()
    }
    
    /**
     * 获取用户访问令牌
     * @return 访问令牌，如果不存在则返回null
     */
    fun getAccessToken(): String? {
        return authPrefs.getString("access_token", null)
    }
    
    /**
     * 保存用户刷新令牌
     * @param token 刷新令牌
     */
    fun saveRefreshToken(token: String) {
        authPrefs.edit()
            .putString("refresh_token", token)
            .apply()
    }
    
    /**
     * 获取用户刷新令牌
     * @return 刷新令牌，如果不存在则返回null
     */
    fun getRefreshToken(): String? {
        return authPrefs.getString("refresh_token", null)
    }
    
    /**
     * 保存用户ID
     * @param userId 用户ID
     */
    fun saveUserId(userId: String) {
        authPrefs.edit()
            .putString("user_id", userId)
            .apply()
    }
    
    /**
     * 获取用户ID
     * @return 用户ID，如果不存在则返回null
     */
    fun getUserId(): String? {
        return authPrefs.getString("user_id", null)
    }
    
    /**
     * 保存用户手机号（加密存储）
     * @param phoneNumber 手机号
     */
    fun savePhoneNumber(phoneNumber: String) {
        authPrefs.edit()
            .putString("phone_number", phoneNumber)
            .apply()
    }
    
    /**
     * 获取用户手机号
     * @return 手机号，如果不存在则返回null
     */
    fun getPhoneNumber(): String? {
        return authPrefs.getString("phone_number", null)
    }
    
    /**
     * 清除所有用户认证数据
     */
    fun clearAuthData() {
        authPrefs.edit()
            .clear()
            .apply()
    }
    
    // ==================== 面试数据存储 ====================
    
    /**
     * 保存当前面试会话ID
     * @param sessionId 面试会话ID
     */
    fun saveCurrentInterviewSessionId(sessionId: String) {
        interviewPrefs.edit()
            .putString("current_session_id", sessionId)
            .apply()
    }
    
    /**
     * 获取当前面试会话ID
     * @return 面试会话ID，如果不存在则返回null
     */
    fun getCurrentInterviewSessionId(): String? {
        return interviewPrefs.getString("current_session_id", null)
    }
    
    /**
     * 保存面试临时数据
     * @param key 数据键
     * @param value 数据值
     */
    fun saveInterviewTempData(key: String, value: String) {
        interviewPrefs.edit()
            .putString("temp_$key", value)
            .apply()
    }
    
    /**
     * 获取面试临时数据
     * @param key 数据键
     * @return 数据值，如果不存在则返回null
     */
    fun getInterviewTempData(key: String): String? {
        return interviewPrefs.getString("temp_$key", null)
    }
    
    /**
     * 清除面试临时数据
     * @param key 数据键
     */
    fun clearInterviewTempData(key: String) {
        interviewPrefs.edit()
            .remove("temp_$key")
            .apply()
    }
    
    /**
     * 清除所有面试临时数据
     */
    fun clearAllInterviewTempData() {
        val editor = interviewPrefs.edit()
        val allKeys = interviewPrefs.all.keys
        
        // 删除所有以"temp_"开头的键
        allKeys.filter { it.startsWith("temp_") }.forEach { key ->
            editor.remove(key)
        }
        
        editor.apply()
    }
    
    // ==================== 通用数据存储 ====================
    
    /**
     * 保存字符串数据
     * @param key 数据键
     * @param value 数据值
     */
    fun saveString(key: String, value: String) {
        encryptedPrefs.edit()
            .putString(key, value)
            .apply()
    }
    
    /**
     * 获取字符串数据
     * @param key 数据键
     * @param defaultValue 默认值
     * @return 数据值
     */
    fun getString(key: String, defaultValue: String? = null): String? {
        return encryptedPrefs.getString(key, defaultValue)
    }
    
    /**
     * 保存整数数据
     * @param key 数据键
     * @param value 数据值
     */
    fun saveInt(key: String, value: Int) {
        encryptedPrefs.edit()
            .putInt(key, value)
            .apply()
    }
    
    /**
     * 获取整数数据
     * @param key 数据键
     * @param defaultValue 默认值
     * @return 数据值
     */
    fun getInt(key: String, defaultValue: Int = 0): Int {
        return encryptedPrefs.getInt(key, defaultValue)
    }
    
    /**
     * 保存长整数数据
     * @param key 数据键
     * @param value 数据值
     */
    fun saveLong(key: String, value: Long) {
        encryptedPrefs.edit()
            .putLong(key, value)
            .apply()
    }
    
    /**
     * 获取长整数数据
     * @param key 数据键
     * @param defaultValue 默认值
     * @return 数据值
     */
    fun getLong(key: String, defaultValue: Long = 0L): Long {
        return encryptedPrefs.getLong(key, defaultValue)
    }
    
    /**
     * 保存布尔值数据
     * @param key 数据键
     * @param value 数据值
     */
    fun saveBoolean(key: String, value: Boolean) {
        encryptedPrefs.edit()
            .putBoolean(key, value)
            .apply()
    }
    
    /**
     * 获取布尔值数据
     * @param key 数据键
     * @param defaultValue 默认值
     * @return 数据值
     */
    fun getBoolean(key: String, defaultValue: Boolean = false): Boolean {
        return encryptedPrefs.getBoolean(key, defaultValue)
    }
    
    /**
     * 保存浮点数数据
     * @param key 数据键
     * @param value 数据值
     */
    fun saveFloat(key: String, value: Float) {
        encryptedPrefs.edit()
            .putFloat(key, value)
            .apply()
    }
    
    /**
     * 获取浮点数数据
     * @param key 数据键
     * @param defaultValue 默认值
     * @return 数据值
     */
    fun getFloat(key: String, defaultValue: Float = 0f): Float {
        return encryptedPrefs.getFloat(key, defaultValue)
    }
    
    /**
     * 删除指定键的数据
     * @param key 数据键
     */
    fun remove(key: String) {
        encryptedPrefs.edit()
            .remove(key)
            .apply()
    }
    
    /**
     * 检查是否包含指定键
     * @param key 数据键
     * @return true表示包含，false表示不包含
     */
    fun contains(key: String): Boolean {
        return encryptedPrefs.contains(key)
    }
    
    /**
     * 清除所有通用数据
     */
    fun clearAllData() {
        encryptedPrefs.edit()
            .clear()
            .apply()
    }
    
    /**
     * 清除所有存储的数据（包括认证和面试数据）
     * 用于用户注销或重置应用时
     */
    fun clearAllStoredData() {
        clearAuthData()
        clearAllInterviewTempData()
        clearAllData()
    }
    
    /**
     * 获取存储数据的统计信息
     * @return 存储统计信息
     */
    fun getStorageStats(): StorageStats {
        val authDataCount = authPrefs.all.size
        val interviewDataCount = interviewPrefs.all.size
        val generalDataCount = encryptedPrefs.all.size
        
        return StorageStats(
            authDataCount = authDataCount,
            interviewDataCount = interviewDataCount,
            generalDataCount = generalDataCount,
            totalDataCount = authDataCount + interviewDataCount + generalDataCount
        )
    }
}

/**
 * 存储统计信息数据类
 * 用于统计加密存储中的数据量
 */
data class StorageStats(
    val authDataCount: Int,         // 认证数据数量
    val interviewDataCount: Int,    // 面试数据数量
    val generalDataCount: Int,      // 通用数据数量
    val totalDataCount: Int         // 总数据数量
)