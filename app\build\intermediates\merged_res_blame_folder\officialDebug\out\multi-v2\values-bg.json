{"logs": [{"outputFile": "com.aiinterview.simulator.app-mergeOfficialDebugResources-55:/values-bg/values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d78fa9dcb2040782f62e575b1696aadd\\transformed\\ui-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,301,404,507,591,667,758,849,933,1000,1066,1150,1238,1310,1393,1462", "endColumns": "102,92,102,102,83,75,90,90,83,66,65,83,87,71,82,68,120", "endOffsets": "203,296,399,502,586,662,753,844,928,995,1061,1145,1233,1305,1388,1457,1578"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1326,1429,5215,5318,5504,5665,5741,5832,5923,6007,6074,6140,6224,6396,6760,6843,6912", "endColumns": "102,92,102,102,83,75,90,90,83,66,65,83,87,71,82,68,120", "endOffsets": "1424,1517,5313,5416,5583,5736,5827,5918,6002,6069,6135,6219,6307,6463,6838,6907,7028"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\73f51c4b00c3d8b5bd0709c291d17c64\\transformed\\material3-1.1.2\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,287,420,535,609,701,811,937,1054,1197,1277,1372,1464,1556,1665,1790,1889,2024,2159,2300,2484,2620,2743,2870,3001,3094,3187,3320,3451,3546,3647,3748,3884,4026,4130,4228,4311,4388,4472,4558,4663,4739,4818,4914,5015,5107,5201,5285,5391,5487,5586,5701,5777,5883", "endColumns": "114,116,132,114,73,91,109,125,116,142,79,94,91,91,108,124,98,134,134,140,183,135,122,126,130,92,92,132,130,94,100,100,135,141,103,97,82,76,83,85,104,75,78,95,100,91,93,83,105,95,98,114,75,105,92", "endOffsets": "165,282,415,530,604,696,806,932,1049,1192,1272,1367,1459,1551,1660,1785,1884,2019,2154,2295,2479,2615,2738,2865,2996,3089,3182,3315,3446,3541,3642,3743,3879,4021,4125,4223,4306,4383,4467,4553,4658,4734,4813,4909,5010,5102,5196,5280,5386,5482,5581,5696,5772,5878,5971"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,337,470,1522,1596,1688,1798,1924,2041,2184,2264,2359,2451,2543,2652,2777,2876,3011,3146,3287,3471,3607,3730,3857,3988,4081,4174,4307,4438,4533,4634,4735,4871,5013,5117,5421,5588,6312,6468,6655,7033,7109,7188,7284,7385,7477,7571,7655,7761,7857,7956,8071,8147,8253", "endColumns": "114,116,132,114,73,91,109,125,116,142,79,94,91,91,108,124,98,134,134,140,183,135,122,126,130,92,92,132,130,94,100,100,135,141,103,97,82,76,83,85,104,75,78,95,100,91,93,83,105,95,98,114,75,105,92", "endOffsets": "215,332,465,580,1591,1683,1793,1919,2036,2179,2259,2354,2446,2538,2647,2772,2871,3006,3141,3282,3466,3602,3725,3852,3983,4076,4169,4302,4433,4528,4629,4730,4866,5008,5112,5210,5499,5660,6391,6549,6755,7104,7183,7279,7380,7472,7566,7650,7756,7852,7951,8066,8142,8248,8341"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fd044ab7a523edb1345beec00f65097\\transformed\\core-1.12.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "585,682,792,894,995,1102,1207,6554", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "677,787,889,990,1097,1202,1321,6650"}}]}]}