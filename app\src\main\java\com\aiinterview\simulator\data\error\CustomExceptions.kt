package com.aiinterview.simulator.data.error

/**
 * 业务逻辑异常
 * 用于表示业务层面的错误
 */
class BusinessException(
    val errorCode: BusinessErrorCode, // 业务错误代码
    message: String, // 错误信息
    cause: Throwable? = null // 原因异常
) : Exception(message, cause)

/**
 * 业务错误代码枚举
 */
enum class BusinessErrorCode {
    USER_NOT_FOUND, // 用户不存在
    INTERVIEW_NOT_FOUND, // 面试会话不存在
    INVALID_CREDENTIALS, // 无效凭据
    TOKEN_EXPIRED, // Token过期
    INSUFFICIENT_PERMISSIONS, // 权限不足
    RESOURCE_LOCKED, // 资源被锁定
    QUOTA_EXCEEDED, // 配额超限
    INVALID_OPERATION, // 无效操作
    DATA_VALIDATION_FAILED, // 数据验证失败
    DUPLICATE_RESOURCE // 资源重复
}

/**
 * 语音识别异常
 * 用于表示语音识别相关的错误
 */
class SpeechRecognitionException(
    val errorType: SpeechErrorType, // 语音错误类型
    message: String, // 错误信息
    cause: Throwable? = null // 原因异常
) : Exception(message, cause)

/**
 * 语音错误类型枚举
 */
enum class SpeechErrorType {
    MICROPHONE_PERMISSION_DENIED, // 麦克风权限被拒绝
    AUDIO_RECORDING_FAILED, // 音频录制失败
    SPEECH_RECOGNITION_FAILED, // 语音识别失败
    AUDIO_FORMAT_NOT_SUPPORTED, // 音频格式不支持
    NETWORK_ERROR, // 网络错误
    SERVICE_UNAVAILABLE, // 服务不可用
    TIMEOUT, // 超时
    INVALID_AUDIO_DATA, // 无效音频数据
    RECOGNITION_CANCELLED, // 识别被取消
    INSUFFICIENT_AUDIO_LENGTH // 音频长度不足
}

/**
 * 数据库异常
 * 用于表示数据库操作相关的错误
 */
class DatabaseException(
    val errorType: DatabaseErrorType, // 数据库错误类型
    message: String, // 错误信息
    cause: Throwable? = null // 原因异常
) : Exception(message, cause)

/**
 * 数据库错误类型枚举
 */
enum class DatabaseErrorType {
    CONNECTION_FAILED, // 连接失败
    QUERY_FAILED, // 查询失败
    INSERT_FAILED, // 插入失败
    UPDATE_FAILED, // 更新失败
    DELETE_FAILED, // 删除失败
    CONSTRAINT_VIOLATION, // 约束违反
    DISK_FULL, // 磁盘空间不足
    CORRUPTION, // 数据库损坏
    MIGRATION_FAILED, // 迁移失败
    TRANSACTION_FAILED // 事务失败
}

/**
 * 权限异常
 * 用于表示权限相关的错误
 */
class PermissionException(
    val permission: String, // 权限名称
    message: String, // 错误信息
    cause: Throwable? = null // 原因异常
) : Exception(message, cause)

/**
 * AI服务异常
 * 用于表示AI服务相关的错误
 */
class AIServiceException(
    val serviceType: AIServiceType, // AI服务类型
    val errorCode: String, // 错误代码
    message: String, // 错误信息
    cause: Throwable? = null // 原因异常
) : Exception(message, cause)

/**
 * AI服务类型枚举
 */
enum class AIServiceType {
    QUESTION_GENERATION, // 问题生成
    ANSWER_EVALUATION, // 答案评价
    TEXT_TO_SPEECH, // 文本转语音
    SPEECH_TO_TEXT, // 语音转文本
    CONTENT_ANALYSIS, // 内容分析
    SENTIMENT_ANALYSIS // 情感分析
}

/**
 * 文件操作异常
 * 用于表示文件操作相关的错误
 */
class FileOperationException(
    val operation: FileOperation, // 文件操作类型
    val filePath: String, // 文件路径
    message: String, // 错误信息
    cause: Throwable? = null // 原因异常
) : Exception(message, cause)

/**
 * 文件操作类型枚举
 */
enum class FileOperation {
    READ, // 读取
    WRITE, // 写入
    DELETE, // 删除
    COPY, // 复制
    MOVE, // 移动
    COMPRESS, // 压缩
    DECOMPRESS, // 解压
    UPLOAD, // 上传
    DOWNLOAD // 下载
}

/**
 * 网络服务异常
 * 用于表示网络服务相关的错误
 */
class NetworkServiceException(
    val serviceUrl: String, // 服务URL
    val httpStatusCode: Int?, // HTTP状态码
    message: String, // 错误信息
    cause: Throwable? = null // 原因异常
) : Exception(message, cause)

/**
 * 配置异常
 * 用于表示配置相关的错误
 */
class ConfigurationException(
    val configKey: String, // 配置键
    message: String, // 错误信息
    cause: Throwable? = null // 原因异常
) : Exception(message, cause)

/**
 * 验证异常
 * 用于表示数据验证相关的错误
 */
class ValidationException(
    val field: String, // 验证失败的字段
    val validationRule: String, // 验证规则
    message: String, // 错误信息
    cause: Throwable? = null // 原因异常
) : Exception(message, cause)

/**
 * 缓存异常
 * 用于表示缓存操作相关的错误
 */
class CacheException(
    val cacheKey: String, // 缓存键
    val operation: CacheOperation, // 缓存操作
    message: String, // 错误信息
    cause: Throwable? = null // 原因异常
) : Exception(message, cause)

/**
 * 缓存操作类型枚举
 */
enum class CacheOperation {
    GET, // 获取
    PUT, // 存储
    REMOVE, // 移除
    CLEAR, // 清空
    EXPIRE // 过期
}

/**
 * 安全异常
 * 用于表示安全相关的错误
 */
class SecurityException(
    val securityIssue: SecurityIssue, // 安全问题类型
    message: String, // 错误信息
    cause: Throwable? = null // 原因异常
) : Exception(message, cause)

/**
 * 安全问题类型枚举
 */
enum class SecurityIssue {
    ENCRYPTION_FAILED, // 加密失败
    DECRYPTION_FAILED, // 解密失败
    SIGNATURE_VERIFICATION_FAILED, // 签名验证失败
    CERTIFICATE_INVALID, // 证书无效
    TOKEN_TAMPERED, // Token被篡改
    UNAUTHORIZED_ACCESS, // 未授权访问
    SUSPICIOUS_ACTIVITY, // 可疑活动
    DATA_INTEGRITY_VIOLATION // 数据完整性违反
}

/**
 * 性能异常
 * 用于表示性能相关的错误
 */
class PerformanceException(
    val performanceIssue: PerformanceIssue, // 性能问题类型
    val threshold: Long, // 阈值
    val actualValue: Long, // 实际值
    message: String, // 错误信息
    cause: Throwable? = null // 原因异常
) : Exception(message, cause)

/**
 * 性能问题类型枚举
 */
enum class PerformanceIssue {
    RESPONSE_TIME_EXCEEDED, // 响应时间超限
    MEMORY_USAGE_EXCEEDED, // 内存使用超限
    CPU_USAGE_EXCEEDED, // CPU使用超限
    DISK_USAGE_EXCEEDED, // 磁盘使用超限
    NETWORK_BANDWIDTH_EXCEEDED, // 网络带宽超限
    CONCURRENT_REQUESTS_EXCEEDED, // 并发请求超限
    QUEUE_SIZE_EXCEEDED, // 队列大小超限
    THREAD_POOL_EXHAUSTED // 线程池耗尽
}