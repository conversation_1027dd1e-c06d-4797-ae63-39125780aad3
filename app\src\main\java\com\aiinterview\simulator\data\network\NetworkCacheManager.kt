package com.aiinterview.simulator.data.network

import android.content.Context
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.Cache
import okhttp3.CacheControl
import okhttp3.Interceptor
import okhttp3.Response
import java.io.File
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 网络缓存管理器
 * 负责管理HTTP请求的缓存策略和缓存清理
 */
@Singleton
class NetworkCacheManager @Inject constructor(
    private val context: Context // 注入应用上下文
) {
    
    companion object {
        private const val CACHE_SIZE = 50 * 1024 * 1024L // 缓存大小：50MB
        private const val CACHE_DIR_NAME = "http_cache" // 缓存目录名称
        private const val CACHE_MAX_AGE = 5 // 缓存最大存活时间（分钟）
        private const val CACHE_MAX_STALE = 7 // 离线时缓存最大过期时间（天）
    }
    
    // 创建HTTP缓存对象
    val httpCache: Cache by lazy {
        val cacheDir = File(context.cacheDir, CACHE_DIR_NAME) // 创建缓存目录
        Cache(cacheDir, CACHE_SIZE) // 创建指定大小的缓存
    }
    
    /**
     * 创建缓存拦截器
     * 根据网络状态决定缓存策略
     */
    fun createCacheInterceptor(): Interceptor {
        return Interceptor { chain ->
            var request = chain.request() // 获取原始请求
            
            // 检查网络连接状态
            if (!isNetworkAvailable()) {
                // 无网络时使用缓存，即使已过期
                val cacheControl = CacheControl.Builder()
                    .maxStale(CACHE_MAX_STALE, TimeUnit.DAYS) // 设置最大过期时间
                    .build()
                
                request = request.newBuilder()
                    .cacheControl(cacheControl) // 应用缓存控制策略
                    .build()
            }
            
            chain.proceed(request) // 继续执行请求
        }
    }
    
    /**
     * 创建网络缓存拦截器
     * 为响应添加缓存头信息
     */
    fun createNetworkCacheInterceptor(): Interceptor {
        return Interceptor { chain ->
            val response = chain.proceed(chain.request()) // 执行请求获取响应
            
            // 为响应添加缓存控制头
            val cacheControl = CacheControl.Builder()
                .maxAge(CACHE_MAX_AGE, TimeUnit.MINUTES) // 设置缓存最大存活时间
                .build()
            
            response.newBuilder()
                .header("Cache-Control", cacheControl.toString()) // 添加缓存控制头
                .build()
        }
    }
    
    /**
     * 清理过期缓存
     */
    suspend fun clearExpiredCache() = withContext(Dispatchers.IO) {
        try {
            httpCache.evictAll() // 清理所有缓存
        } catch (e: Exception) {
            e.printStackTrace() // 打印异常信息
        }
    }
    
    /**
     * 获取缓存大小
     * @return 当前缓存大小（字节）
     */
    suspend fun getCacheSize(): Long = withContext(Dispatchers.IO) {
        try {
            httpCache.size() // 返回缓存大小
        } catch (e: Exception) {
            e.printStackTrace() // 打印异常信息
            0L // 获取失败返回0
        }
    }
    
    /**
     * 获取缓存命中率统计
     * @return 缓存统计信息
     */
    suspend fun getCacheStats(): CacheStats = withContext(Dispatchers.IO) {
        try {
            val snapshot = httpCache.urls() // 获取缓存URL快照
            val hitCount = httpCache.hitCount() // 获取缓存命中次数
            val requestCount = httpCache.requestCount() // 获取总请求次数
            val networkCount = httpCache.networkCount() // 获取网络请求次数
            
            CacheStats(
                hitCount = hitCount,
                requestCount = requestCount,
                networkCount = networkCount,
                hitRate = if (requestCount > 0) hitCount.toDouble() / requestCount else 0.0 // 计算命中率
            )
        } catch (e: Exception) {
            e.printStackTrace() // 打印异常信息
            CacheStats() // 返回默认统计信息
        }
    }
    
    /**
     * 检查网络是否可用
     * @return 网络是否可用
     */
    private fun isNetworkAvailable(): Boolean {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) 
                as android.net.ConnectivityManager // 获取连接管理器
            
            val activeNetwork = connectivityManager.activeNetworkInfo // 获取活动网络信息
            activeNetwork?.isConnectedOrConnecting == true // 检查是否已连接或正在连接
        } catch (e: Exception) {
            e.printStackTrace() // 打印异常信息
            false // 检查失败默认返回false
        }
    }
}

/**
 * 缓存统计信息数据类
 */
data class CacheStats(
    val hitCount: Int = 0, // 缓存命中次数
    val requestCount: Int = 0, // 总请求次数
    val networkCount: Int = 0, // 网络请求次数
    val hitRate: Double = 0.0 // 缓存命中率
)