package com.aiinterview.simulator.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.aiinterview.simulator.data.repository.AuthRepository
import com.aiinterview.simulator.data.dto.response.AuthResponse
import com.aiinterview.simulator.domain.util.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class AuthViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {
    
    private val _authState = MutableStateFlow<Resource<AuthResponse>?>(null)
    val authState: StateFlow<Resource<AuthResponse>?> = _authState.asStateFlow()
    
    private val _isLoggedIn = MutableStateFlow(false)
    val isLoggedIn: StateFlow<Boolean> = _isLoggedIn.asStateFlow()
    
    fun register(phoneNumber: String, verificationCode: String) {
        viewModelScope.launch {
            authRepository.register(phoneNumber, verificationCode).collect { result ->
                _authState.value = result
                if (result is Resource.Success) {
                    _isLoggedIn.value = true
                }
            }
        }
    }
    
    fun login(phoneNumber: String, password: String? = null, verificationCode: String? = null) {
        viewModelScope.launch {
            authRepository.login(phoneNumber, password, verificationCode).collect { result ->
                _authState.value = result
                if (result is Resource.Success) {
                    _isLoggedIn.value = true
                }
            }
        }
    }
    
    fun refreshToken() {
        viewModelScope.launch {
            authRepository.refreshToken().collect { result ->
                _authState.value = result
                if (result is Resource.Error) {
                    _isLoggedIn.value = false
                }
            }
        }
    }
    
    fun logout() {
        viewModelScope.launch {
            authRepository.logout()
            _isLoggedIn.value = false
            _authState.value = null
        }
    }
    
    fun checkLoginStatus() {
        viewModelScope.launch {
            authRepository.isLoggedIn().collect { isLoggedIn ->
                _isLoggedIn.value = isLoggedIn
            }
        }
    }
    
    fun clearAuthState() {
        _authState.value = null
    }
}