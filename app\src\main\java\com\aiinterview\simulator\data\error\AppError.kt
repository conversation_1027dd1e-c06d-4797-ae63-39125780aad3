package com.aiinterview.simulator.data.error

/**
 * 应用错误基类
 * 定义了应用中所有可能出现的错误类型
 */
sealed class AppError(
    val code: String, // 错误代码
    val message: String, // 技术错误信息
    val userMessage: String // 用户友好的错误信息
) {
    
    // ========== 网络相关错误 ==========
    
    /**
     * 网络连接错误
     */
    object NetworkError : AppError(
        code = "NETWORK_ERROR",
        message = "Network connection failed",
        userMessage = "网络连接失败，请检查网络设置"
    )
    
    /**
     * 请求超时错误
     */
    object TimeoutError : AppError(
        code = "TIMEOUT_ERROR",
        message = "Request timeout",
        userMessage = "请求超时，请稍后重试"
    )
    
    /**
     * 无网络连接错误
     */
    object NoInternetError : AppError(
        code = "NO_INTERNET_ERROR",
        message = "No internet connection",
        userMessage = "无网络连接，请检查网络设置"
    )
    
    /**
     * 连接被重置错误
     */
    data class ConnectionResetError(val details: String) : AppError(
        code = "CONNECTION_RESET_ERROR",
        message = "Connection reset: $details",
        userMessage = "连接被重置，请重试"
    )
    
    /**
     * 连接被拒绝错误
     */
    data class ConnectionRefusedError(val details: String) : AppError(
        code = "CONNECTION_REFUSED_ERROR",
        message = "Connection refused: $details",
        userMessage = "连接被拒绝，请检查网络设置"
    )
    
    /**
     * 网络不可达错误
     */
    data class NetworkUnreachableError(val details: String) : AppError(
        code = "NETWORK_UNREACHABLE_ERROR",
        message = "Network unreachable: $details",
        userMessage = "网络不可达，请检查网络连接"
    )
    
    // ========== HTTP状态码相关错误 ==========
    
    /**
     * 400 Bad Request错误
     */
    data class BadRequestError(val details: String) : AppError(
        code = "BAD_REQUEST_ERROR",
        message = "Bad request: $details",
        userMessage = "请求参数错误，请检查输入信息"
    )
    
    /**
     * 401 Unauthorized错误
     */
    object UnauthorizedError : AppError(
        code = "UNAUTHORIZED_ERROR",
        message = "Unauthorized access",
        userMessage = "请先登录"
    )
    
    /**
     * 403 Forbidden错误
     */
    data class ForbiddenError(val details: String) : AppError(
        code = "FORBIDDEN_ERROR",
        message = "Forbidden: $details",
        userMessage = "访问被拒绝，权限不足"
    )
    
    /**
     * 404 Not Found错误
     */
    data class NotFoundError(val details: String) : AppError(
        code = "NOT_FOUND_ERROR",
        message = "Resource not found: $details",
        userMessage = "请求的资源不存在"
    )
    
    /**
     * 429 Too Many Requests错误
     */
    data class TooManyRequestsError(val details: String) : AppError(
        code = "TOO_MANY_REQUESTS_ERROR",
        message = "Too many requests: $details",
        userMessage = "请求过于频繁，请稍后重试"
    )
    
    /**
     * 500 Internal Server Error错误
     */
    object ServerError : AppError(
        code = "SERVER_ERROR",
        message = "Internal server error",
        userMessage = "服务器内部错误，请稍后重试"
    )
    
    /**
     * 502 Bad Gateway错误
     */
    data class BadGatewayError(val details: String) : AppError(
        code = "BAD_GATEWAY_ERROR",
        message = "Bad gateway: $details",
        userMessage = "网关错误，请稍后重试"
    )
    
    /**
     * 503 Service Unavailable错误
     */
    data class ServiceUnavailableError(val details: String) : AppError(
        code = "SERVICE_UNAVAILABLE_ERROR",
        message = "Service unavailable: $details",
        userMessage = "服务暂时不可用，请稍后重试"
    )
    
    /**
     * 504 Gateway Timeout错误
     */
    data class GatewayTimeoutError(val details: String) : AppError(
        code = "GATEWAY_TIMEOUT_ERROR",
        message = "Gateway timeout: $details",
        userMessage = "网关超时，请稍后重试"
    )
    
    // ========== 认证相关错误 ==========
    
    /**
     * Token过期错误
     */
    object TokenExpiredError : AppError(
        code = "TOKEN_EXPIRED_ERROR",
        message = "Authentication token expired",
        userMessage = "登录已过期，请重新登录"
    )
    
    /**
     * 无效凭据错误
     */
    data class InvalidCredentialsError(val details: String) : AppError(
        code = "INVALID_CREDENTIALS_ERROR",
        message = "Invalid credentials: $details",
        userMessage = "用户名或密码错误"
    )
    
    /**
     * 权限不足错误
     */
    data class InsufficientPermissionsError(val details: String) : AppError(
        code = "INSUFFICIENT_PERMISSIONS_ERROR",
        message = "Insufficient permissions: $details",
        userMessage = "权限不足，无法执行此操作"
    )
    
    // ========== 业务逻辑相关错误 ==========
    
    /**
     * 用户不存在错误
     */
    object UserNotFoundError : AppError(
        code = "USER_NOT_FOUND_ERROR",
        message = "User not found",
        userMessage = "用户不存在"
    )
    
    /**
     * 面试会话不存在错误
     */
    object InterviewNotFoundError : AppError(
        code = "INTERVIEW_NOT_FOUND_ERROR",
        message = "Interview session not found",
        userMessage = "面试会话不存在"
    )
    
    /**
     * 资源被锁定错误
     */
    data class ResourceLockedError(val details: String) : AppError(
        code = "RESOURCE_LOCKED_ERROR",
        message = "Resource locked: $details",
        userMessage = "资源被锁定，请稍后重试"
    )
    
    /**
     * 配额超限错误
     */
    data class QuotaExceededError(val details: String) : AppError(
        code = "QUOTA_EXCEEDED_ERROR",
        message = "Quota exceeded: $details",
        userMessage = "使用配额已超限，请稍后重试"
    )
    
    /**
     * 业务逻辑错误
     */
    data class BusinessLogicError(val details: String) : AppError(
        code = "BUSINESS_LOGIC_ERROR",
        message = "Business logic error: $details",
        userMessage = "操作失败，请检查输入信息"
    )
    
    // ========== 语音识别相关错误 ==========
    
    /**
     * 语音识别失败错误
     */
    object SpeechRecognitionError : AppError(
        code = "SPEECH_RECOGNITION_ERROR",
        message = "Speech recognition failed",
        userMessage = "语音识别失败，请重新录制"
    )
    
    /**
     * 麦克风权限错误
     */
    object MicrophonePermissionError : AppError(
        code = "MICROPHONE_PERMISSION_ERROR",
        message = "Microphone permission denied",
        userMessage = "需要麦克风权限才能录音，请在设置中开启"
    )
    
    /**
     * 音频录制错误
     */
    data class AudioRecordingError(val details: String) : AppError(
        code = "AUDIO_RECORDING_ERROR",
        message = "Audio recording failed: $details",
        userMessage = "录音失败，请检查麦克风设置"
    )
    
    /**
     * 音频格式错误
     */
    data class AudioFormatError(val details: String) : AppError(
        code = "AUDIO_FORMAT_ERROR",
        message = "Audio format error: $details",
        userMessage = "音频格式不支持，请重新录制"
    )
    
    /**
     * 语音服务不可用错误
     */
    data class SpeechServiceUnavailableError(val details: String) : AppError(
        code = "SPEECH_SERVICE_UNAVAILABLE_ERROR",
        message = "Speech service unavailable: $details",
        userMessage = "语音服务暂时不可用，请稍后重试"
    )
    
    // ========== 数据库相关错误 ==========
    
    /**
     * 数据库连接错误
     */
    data class DatabaseConnectionError(val details: String) : AppError(
        code = "DATABASE_CONNECTION_ERROR",
        message = "Database connection failed: $details",
        userMessage = "数据库连接失败，请重启应用"
    )
    
    /**
     * 数据库查询错误
     */
    data class DatabaseQueryError(val details: String) : AppError(
        code = "DATABASE_QUERY_ERROR",
        message = "Database query failed: $details",
        userMessage = "数据查询失败，请重试"
    )
    
    /**
     * 数据库约束错误
     */
    data class DatabaseConstraintError(val details: String) : AppError(
        code = "DATABASE_CONSTRAINT_ERROR",
        message = "Database constraint violation: $details",
        userMessage = "数据保存失败，请检查输入信息"
    )
    
    /**
     * 磁盘空间不足错误
     */
    data class DiskFullError(val details: String) : AppError(
        code = "DISK_FULL_ERROR",
        message = "Disk full: $details",
        userMessage = "存储空间不足，请清理设备存储空间"
    )
    
    /**
     * 数据库损坏错误
     */
    data class DatabaseCorruptionError(val details: String) : AppError(
        code = "DATABASE_CORRUPTION_ERROR",
        message = "Database corruption: $details",
        userMessage = "数据库损坏，请重新安装应用"
    )
    
    /**
     * 通用数据库错误
     */
    data class DatabaseError(val details: String) : AppError(
        code = "DATABASE_ERROR",
        message = "Database error: $details",
        userMessage = "数据库操作失败，请重试"
    )
    
    // ========== 权限相关错误 ==========
    
    /**
     * 存储权限错误
     */
    data class StoragePermissionError(val details: String) : AppError(
        code = "STORAGE_PERMISSION_ERROR",
        message = "Storage permission denied: $details",
        userMessage = "需要存储权限才能保存文件，请在设置中开启"
    )
    
    /**
     * 相机权限错误
     */
    data class CameraPermissionError(val details: String) : AppError(
        code = "CAMERA_PERMISSION_ERROR",
        message = "Camera permission denied: $details",
        userMessage = "需要相机权限才能拍照，请在设置中开启"
    )
    
    /**
     * 网络权限错误
     */
    data class NetworkPermissionError(val details: String) : AppError(
        code = "NETWORK_PERMISSION_ERROR",
        message = "Network permission denied: $details",
        userMessage = "需要网络权限才能联网，请在设置中开启"
    )
    
    /**
     * 通用权限被拒绝错误
     */
    data class PermissionDeniedError(val details: String) : AppError(
        code = "PERMISSION_DENIED_ERROR",
        message = "Permission denied: $details",
        userMessage = "权限被拒绝，请在设置中开启相应权限"
    )
    
    // ========== 其他错误 ==========
    
    /**
     * 未知错误
     */
    data class UnknownError(val details: String) : AppError(
        code = "UNKNOWN_ERROR",
        message = "Unknown error: $details",
        userMessage = "发生未知错误，请重试"
    )
    
    /**
     * 操作取消错误
     */
    data class OperationCancelledError(val details: String) : AppError(
        code = "OPERATION_CANCELLED_ERROR",
        message = "Operation cancelled: $details",
        userMessage = "操作已取消"
    )
    
    /**
     * 配置错误
     */
    data class ConfigurationError(val details: String) : AppError(
        code = "CONFIGURATION_ERROR",
        message = "Configuration error: $details",
        userMessage = "配置错误，请联系技术支持"
    )
    
    /**
     * 版本不兼容错误
     */
    data class VersionIncompatibleError(val details: String) : AppError(
        code = "VERSION_INCOMPATIBLE_ERROR",
        message = "Version incompatible: $details",
        userMessage = "版本不兼容，请更新应用"
    )
}