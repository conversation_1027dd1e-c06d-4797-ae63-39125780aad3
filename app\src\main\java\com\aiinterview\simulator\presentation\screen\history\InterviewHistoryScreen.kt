package com.aiinterview.simulator.presentation.screen.history

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.aiinterview.simulator.data.model.InterviewRecordStatus
import com.aiinterview.simulator.data.model.InterviewRecordSummary
import com.aiinterview.simulator.presentation.viewmodel.InterviewHistoryViewModel
import com.aiinterview.simulator.presentation.viewmodel.SortOrder
import com.aiinterview.simulator.presentation.viewmodel.TimeRange
import java.text.SimpleDateFormat
import java.util.*

/**
 * 面试历史记录主界面
 * @param onNavigateToDetail 导航到详情页面的回调函数，传入记录ID
 * @param onNavigateBack 返回上一页的回调函数
 * @param viewModel 面试历史ViewModel，使用Hilt注入
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun InterviewHistoryScreen(
    onNavigateToDetail: (String) -> Unit,
    onNavigateBack: () -> Unit,
    viewModel: InterviewHistoryViewModel = hiltViewModel()
) {
    // 收集ViewModel中的状态数据
    val uiState by viewModel.uiState.collectAsState()
    val searchQuery by viewModel.searchQuery.collectAsState()
    val filterCategory by viewModel.filterCategory.collectAsState()
    val timeRange by viewModel.timeRange.collectAsState()
    val sortOrder by viewModel.sortOrder.collectAsState()
    
    // 控制对话框显示状态的本地状态
    var showFilterDialog by remember { mutableStateOf(false) }
    var showSortDialog by remember { mutableStateOf(false) }
    var showStatistics by remember { mutableStateOf(false) }
    
    // 主界面布局：垂直排列的列布局
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
    ) {
        // 顶部应用栏，包含标题、返回按钮和功能按钮
        TopAppBar(
            title = { Text("面试历史") },
            navigationIcon = {
                // 返回按钮
                IconButton(onClick = onNavigateBack) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            },
            actions = {
                // 搜索按钮：触发高级搜索功能
                IconButton(
                    onClick = { 
                        viewModel.performAdvancedSearch(
                            keyword = searchQuery,
                            category = filterCategory,
                            timeRange = timeRange
                        )
                    }
                ) {
                    Icon(Icons.Default.Search, contentDescription = "高级搜索")
                }
                
                // 进步分析按钮：导航到进步分析界面
                IconButton(onClick = { 
                    // 这里应该导航到进步分析界面
                    // onNavigateToProgressAnalysis()
                }) {
                    Icon(Icons.Default.TrendingUp, contentDescription = "进步分析")
                }
                
                // 统计按钮：显示统计信息对话框
                IconButton(onClick = { showStatistics = true }) {
                    Icon(Icons.Default.Analytics, contentDescription = "统计")
                }
                
                // 筛选按钮：如果有筛选条件则高亮显示
                IconButton(onClick = { showFilterDialog = true }) {
                    Icon(
                        Icons.Default.FilterList,
                        contentDescription = "筛选",
                        tint = if (filterCategory != null || timeRange != TimeRange.ALL) {
                            MaterialTheme.colorScheme.primary // 有筛选条件时使用主色调
                        } else {
                            MaterialTheme.colorScheme.onSurface // 无筛选条件时使用默认颜色
                        }
                    )
                }
                
                // 排序按钮：显示排序选项对话框
                IconButton(onClick = { showSortDialog = true }) {
                    Icon(Icons.Default.Sort, contentDescription = "排序")
                }
                
                // 更多操作按钮：显示下拉菜单
                var showMoreMenu by remember { mutableStateOf(false) }
                Box {
                    IconButton(onClick = { showMoreMenu = true }) {
                        Icon(Icons.Default.MoreVert, contentDescription = "更多")
                    }
                    
                    // 更多操作下拉菜单
                    DropdownMenu(
                        expanded = showMoreMenu,
                        onDismissRequest = { showMoreMenu = false }
                    ) {
                        // 刷新数据选项
                        DropdownMenuItem(
                            text = { Text("刷新数据") },
                            onClick = {
                                viewModel.refreshHistory() // 刷新历史记录
                                showMoreMenu = false // 关闭菜单
                            },
                            leadingIcon = {
                                Icon(Icons.Default.Refresh, contentDescription = null)
                            }
                        )
                        
                        // 清除筛选选项
                        DropdownMenuItem(
                            text = { Text("清除筛选") },
                            onClick = {
                                viewModel.clearFilters() // 清除所有筛选条件
                                showMoreMenu = false // 关闭菜单
                            },
                            leadingIcon = {
                                Icon(Icons.Default.ClearAll, contentDescription = null)
                            }
                        )
                    }
                }
            }
        )
        
        // 搜索栏：用户可以输入关键词搜索面试记录
        SearchBar(
            query = searchQuery,
            onQueryChange = viewModel::setSearchQuery,
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        )
        
        // 筛选标签：显示当前生效的筛选条件，用户可以点击清除
        FilterChips(
            filterCategory = filterCategory,
            timeRange = timeRange,
            onClearCategory = { viewModel.setFilterCategory(null) },
            onClearTimeRange = { viewModel.setTimeRange(TimeRange.ALL) },
            modifier = Modifier.padding(horizontal = 16.dp)
        )
        
        // 主内容区域：根据不同状态显示不同内容
        Box(modifier = Modifier.fillMaxSize()) {
            when {
                // 加载状态：显示加载指示器
                uiState.isLoading -> {
                    CircularProgressIndicator(
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                // 错误状态：显示错误信息和重试按钮
                uiState.error != null -> {
                    ErrorMessage(
                        message = uiState.error,
                        onRetry = viewModel::refreshHistory,
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                // 空状态：没有面试记录时显示空状态提示
                uiState.filteredRecords.isEmpty() -> {
                    EmptyState(
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                // 正常状态：显示面试记录列表
                else -> {
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(16.dp),
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        // 为每个面试记录创建卡片
                        items(uiState.filteredRecords) { record ->
                            InterviewRecordCard(
                                record = record,
                                onClick = { onNavigateToDetail(record.id) }, // 点击查看详情
                                onDelete = { viewModel.deleteRecord(record.id) } // 删除记录
                            )
                        }
                    }
                }
            }
        }
    }
    
    // 筛选对话框：用户可以选择类别和时间范围进行筛选
    if (showFilterDialog) {
        FilterDialog(
            availableCategories = viewModel.getAvailableCategories(),
            selectedCategory = filterCategory,
            selectedTimeRange = timeRange,
            onCategorySelected = viewModel::setFilterCategory,
            onTimeRangeSelected = viewModel::setTimeRange,
            onDismiss = { showFilterDialog = false },
            onClearAll = {
                viewModel.clearFilters()
                showFilterDialog = false
            }
        )
    }
    
    // 排序对话框：用户可以选择不同的排序方式
    if (showSortDialog) {
        SortDialog(
            selectedSortOrder = sortOrder,
            onSortOrderSelected = { 
                viewModel.setSortOrder(it)
                showSortDialog = false
            },
            onDismiss = { showSortDialog = false }
        )
    }
    
    // 统计对话框：显示面试记录的统计信息
    if (showStatistics) {
        StatisticsDialog(
            statistics = viewModel.getStatistics(),
            onDismiss = { showStatistics = false }
        )
    }
    
    // 错误提示处理：当有错误时可以显示Snackbar等提示
    uiState.error?.let { error ->
        LaunchedEffect(error) {
            // 可以显示Snackbar或其他错误提示
        }
    }
}

/**
 * 搜索栏组件
 * @param query 当前搜索关键词
 * @param onQueryChange 搜索关键词变化的回调函数
 * @param modifier 修饰符
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SearchBar(
    query: String,
    onQueryChange: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    OutlinedTextField(
        value = query,
        onValueChange = onQueryChange,
        modifier = modifier,
        placeholder = { Text("搜索岗位名称或类别...") },
        leadingIcon = {
            // 搜索图标
            Icon(Icons.Default.Search, contentDescription = "搜索")
        },
        trailingIcon = {
            // 当有输入内容时显示清除按钮
            if (query.isNotEmpty()) {
                IconButton(onClick = { onQueryChange("") }) {
                    Icon(Icons.Default.Clear, contentDescription = "清除")
                }
            }
        },
        singleLine = true, // 单行输入
        shape = RoundedCornerShape(12.dp) // 圆角边框
    )
}

/**
 * 筛选标签组件：显示当前生效的筛选条件
 * @param filterCategory 当前选中的类别筛选
 * @param timeRange 当前选中的时间范围筛选
 * @param onClearCategory 清除类别筛选的回调函数
 * @param onClearTimeRange 清除时间筛选的回调函数
 * @param modifier 修饰符
 */
@Composable
private fun FilterChips(
    filterCategory: String?,
    timeRange: TimeRange,
    onClearCategory: () -> Unit,
    onClearTimeRange: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(8.dp) // 标签之间的间距
    ) {
        // 如果有类别筛选，显示类别筛选标签
        if (filterCategory != null) {
            FilterChip(
                onClick = onClearCategory, // 点击清除类别筛选
                label = { Text(filterCategory) },
                selected = true, // 选中状态
                trailingIcon = {
                    // 清除图标
                    Icon(
                        Icons.Default.Close,
                        contentDescription = "清除类别筛选",
                        modifier = Modifier.size(16.dp)
                    )
                }
            )
        }
        
        // 如果有时间范围筛选，显示时间筛选标签
        if (timeRange != TimeRange.ALL) {
            FilterChip(
                onClick = onClearTimeRange, // 点击清除时间筛选
                label = { Text(timeRange.displayName) },
                selected = true, // 选中状态
                trailingIcon = {
                    // 清除图标
                    Icon(
                        Icons.Default.Close,
                        contentDescription = "清除时间筛选",
                        modifier = Modifier.size(16.dp)
                    )
                }
            )
        }
    }
}

/**
 * 面试记录卡片组件：显示单个面试记录的摘要信息
 * @param record 面试记录摘要数据
 * @param onClick 点击卡片的回调函数
 * @param onDelete 删除记录的回调函数
 * @param modifier 修饰符
 */
@Composable
private fun InterviewRecordCard(
    record: InterviewRecordSummary,
    onClick: () -> Unit,
    onDelete: () -> Unit,
    modifier: Modifier = Modifier
) {
    // 控制删除确认对话框的显示状态
    var showDeleteDialog by remember { mutableStateOf(false) }
    
    // 卡片容器
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() }, // 点击卡片触发详情查看
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp), // 阴影效果
        shape = RoundedCornerShape(12.dp) // 圆角
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题行：岗位名称和删除按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 岗位名称
                Text(
                    text = record.positionName,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    maxLines = 1, // 限制单行显示
                    overflow = TextOverflow.Ellipsis, // 超出部分显示省略号
                    modifier = Modifier.weight(1f) // 占据剩余空间
                )
                
                // 删除按钮
                IconButton(
                    onClick = { showDeleteDialog = true },
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        Icons.Default.Delete,
                        contentDescription = "删除",
                        tint = MaterialTheme.colorScheme.error, // 使用错误颜色
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 第二行：岗位类别和面试状态
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 岗位类别
                Text(
                    text = record.positionCategory,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.primary // 使用主色调突出显示
                )
                
                // 面试状态标签
                StatusChip(status = record.status)
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 第三行：面试时间和总体评分
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 面试开始时间
                Text(
                    text = formatDateTime(record.startTime),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                // 总体评分（如果有的话）
                if (record.overallScore != null) {
                    ScoreChip(score = record.overallScore)
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 第四行：面试时长和云端同步状态
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 面试时长
                Text(
                    text = "时长: ${formatDuration(record.duration)}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                // 云端同步状态图标
                if (record.isSynced) {
                    Icon(
                        Icons.Default.CloudDone,
                        contentDescription = "已同步",
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(16.dp)
                    )
                } else {
                    Icon(
                        Icons.Default.CloudOff,
                        contentDescription = "未同步",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
    
    // 删除确认对话框：防止用户误删除
    if (showDeleteDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteDialog = false }, // 点击外部区域关闭对话框
            title = { Text("删除面试记录") },
            text = { Text("确定要删除这条面试记录吗？此操作无法撤销。") },
            confirmButton = {
                // 确认删除按钮
                TextButton(
                    onClick = {
                        onDelete() // 执行删除操作
                        showDeleteDialog = false // 关闭对话框
                    }
                ) {
                    Text("删除", color = MaterialTheme.colorScheme.error) // 使用错误颜色
                }
            },
            dismissButton = {
                // 取消按钮
                TextButton(onClick = { showDeleteDialog = false }) {
                    Text("取消")
                }
            }
        )
    }
}

/**
 * 状态标签组件：显示面试记录的状态
 * @param status 面试记录状态
 * @param modifier 修饰符
 */
@Composable
private fun StatusChip(
    status: InterviewRecordStatus,
    modifier: Modifier = Modifier
) {
    // 根据状态确定显示文本和颜色
    val (text, color) = when (status) {
        InterviewRecordStatus.COMPLETED -> "已完成" to MaterialTheme.colorScheme.primary
        InterviewRecordStatus.CANCELLED -> "已取消" to MaterialTheme.colorScheme.error
        InterviewRecordStatus.INTERRUPTED -> "中断" to MaterialTheme.colorScheme.tertiary
    }
    
    // 带背景色的标签
    Surface(
        modifier = modifier,
        shape = RoundedCornerShape(12.dp), // 圆角
        color = color.copy(alpha = 0.1f) // 半透明背景
    ) {
        Text(
            text = text,
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
            style = MaterialTheme.typography.labelSmall,
            color = color // 使用对应的颜色
        )
    }
}

/**
 * 评分标签组件：显示面试评分，根据分数使用不同颜色
 * @param score 面试评分
 * @param modifier 修饰符
 */
@Composable
private fun ScoreChip(
    score: Double,
    modifier: Modifier = Modifier
) {
    // 根据分数确定颜色：优秀(绿)、良好(蓝)、一般(橙)、较差(红)
    val color = when {
        score >= 90 -> Color(0xFF4CAF50) // 绿色：优秀
        score >= 80 -> Color(0xFF2196F3) // 蓝色：良好
        score >= 70 -> Color(0xFFFF9800) // 橙色：一般
        else -> Color(0xFFF44336) // 红色：较差
    }
    
    // 带背景色的评分标签
    Surface(
        modifier = modifier,
        shape = RoundedCornerShape(12.dp), // 圆角
        color = color.copy(alpha = 0.1f) // 半透明背景
    ) {
        Text(
            text = "${score.toInt()}分", // 显示整数分数
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
            style = MaterialTheme.typography.labelSmall,
            color = color, // 使用对应的颜色
            fontWeight = FontWeight.Bold // 粗体显示
        )
    }
}

/**
 * 空状态组件：当没有面试记录时显示的提示界面
 * @param modifier 修饰符
 */
@Composable
private fun EmptyState(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally // 居中对齐
    ) {
        // 历史记录图标
        Icon(
            Icons.Default.History,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.onSurfaceVariant // 使用次要颜色
        )
        Spacer(modifier = Modifier.height(16.dp))
        
        // 主要提示文本
        Text(
            text = "暂无面试记录",
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Spacer(modifier = Modifier.height(8.dp))
        
        // 辅助说明文本
        Text(
            text = "完成面试后，记录会显示在这里",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 错误信息组件：当加载失败时显示错误信息和重试按钮
 * @param message 错误信息文本
 * @param onRetry 重试操作的回调函数
 * @param modifier 修饰符
 */
@Composable
private fun ErrorMessage(
    message: String,
    onRetry: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally // 居中对齐
    ) {
        // 错误图标
        Icon(
            Icons.Default.Error,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.error // 使用错误颜色
        )
        Spacer(modifier = Modifier.height(16.dp))
        
        // 错误信息文本
        Text(
            text = message,
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.error
        )
        Spacer(modifier = Modifier.height(16.dp))
        
        // 重试按钮
        Button(onClick = onRetry) {
            Text("重试")
        }
    }
}

// 工具函数

/**
 * 格式化时间戳为可读的日期时间字符串
 * @param timestamp 时间戳（毫秒）
 * @return 格式化后的时间字符串，格式为"MM-dd HH:mm"
 */
private fun formatDateTime(timestamp: Long): String {
    val formatter = SimpleDateFormat("MM-dd HH:mm", Locale.getDefault())
    return formatter.format(Date(timestamp))
}

/**
 * 格式化时长为可读的时间字符串
 * @param seconds 时长（秒）
 * @return 格式化后的时长字符串，如"5分30秒"或"30秒"
 */
private fun formatDuration(seconds: Int): String {
    val minutes = seconds / 60 // 计算分钟数
    val remainingSeconds = seconds % 60 // 计算剩余秒数
    return if (minutes > 0) {
        "${minutes}分${remainingSeconds}秒"
    } else {
        "${remainingSeconds}秒"
    }
}

/**
 * 筛选对话框组件
 * 允许用户选择类别和时间范围进行筛选
 */
@Composable
private fun FilterDialog(
    availableCategories: List<String>,
    selectedCategory: String?,
    selectedTimeRange: TimeRange,
    onCategorySelected: (String?) -> Unit,
    onTimeRangeSelected: (TimeRange) -> Unit,
    onDismiss: () -> Unit,
    onClearAll: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("筛选条件") },
        text = {
            Column {
                // 类别筛选
                Text(
                    text = "岗位类别",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 全部类别选项
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { onCategorySelected(null) }
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = selectedCategory == null,
                        onClick = { onCategorySelected(null) }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("全部类别")
                }
                
                // 具体类别选项
                availableCategories.forEach { category ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onCategorySelected(category) }
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = selectedCategory == category,
                            onClick = { onCategorySelected(category) }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(category)
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 时间范围筛选
                Text(
                    text = "时间范围",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                TimeRange.values().forEach { timeRange ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onTimeRangeSelected(timeRange) }
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = selectedTimeRange == timeRange,
                            onClick = { onTimeRangeSelected(timeRange) }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(timeRange.displayName)
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("确定")
            }
        },
        dismissButton = {
            TextButton(onClick = onClearAll) {
                Text("清除全部")
            }
        }
    )
}

/**
 * 排序对话框组件
 * 允许用户选择不同的排序方式
 */
@Composable
private fun SortDialog(
    selectedSortOrder: SortOrder,
    onSortOrderSelected: (SortOrder) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("排序方式") },
        text = {
            Column {
                SortOrder.values().forEach { sortOrder ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onSortOrderSelected(sortOrder) }
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = selectedSortOrder == sortOrder,
                            onClick = { onSortOrderSelected(sortOrder) }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(sortOrder.displayName)
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("确定")
            }
        }
    )
}

/**
 * 统计对话框组件
 * 显示面试记录的统计信息
 */
@Composable
private fun StatisticsDialog(
    statistics: InterviewHistoryStatistics,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("统计信息") },
        text = {
            Column {
                // 总体统计
                StatisticRow("总面试次数", "${statistics.totalInterviews}次")
                StatisticRow("完成面试", "${statistics.completedInterviews}次")
                StatisticRow("平均分数", "${String.format("%.1f", statistics.averageScore)}分")
                StatisticRow("最高分数", "${String.format("%.1f", statistics.bestScore)}分")
                StatisticRow("总练习时长", "${statistics.totalDuration / 60}分钟")
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 分类别统计
                if (statistics.categoryCounts.isNotEmpty()) {
                    Text(
                        text = "分类别统计",
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    statistics.categoryCounts.forEach { (category, count) ->
                        StatisticRow(category, "${count}次")
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("确定")
            }
        }
    )
}

/**
 * 统计行组件
 * 显示单行统计信息
 */
@Composable
private fun StatisticRow(
    label: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Bold
        )
    }
}