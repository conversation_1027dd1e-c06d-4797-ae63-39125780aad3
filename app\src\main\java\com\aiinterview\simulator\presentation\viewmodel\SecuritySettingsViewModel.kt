package com.aiinterview.simulator.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.aiinterview.simulator.data.security.*
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 安全设置ViewModel
 * 负责管理安全设置相关的业务逻辑和状态
 */
@HiltViewModel
class SecuritySettingsViewModel @Inject constructor(
    private val encryptedStorageManager: EncryptedStorageManager,   // 注入加密存储管理器
    private val apiSecurityManager: ApiSecurityManager,            // 注入API安全管理器
    private val privacyManager: PrivacyManager,                    // 注入隐私管理器
    private val dataCleanupManager: DataCleanupManager             // 注入数据清理管理器
) : ViewModel() {
    
    // 安全设置状态的私有可变状态流
    private val _securityState = MutableStateFlow(SecuritySettingsState())
    // 对外暴露的安全设置状态只读状态流
    val securityState: StateFlow<SecuritySettingsState> = _securityState.asStateFlow()
    
    // 隐私状态的只读状态流（从隐私管理器获取）
    val privacyState: StateFlow<PrivacyState> = privacyManager.privacyState
    
    // 清理统计信息的私有可变状态流
    private val _cleanupStats = MutableStateFlow<CleanupStats?>(null)
    // 对外暴露的清理统计信息只读状态流
    val cleanupStats: StateFlow<CleanupStats?> = _cleanupStats.asStateFlow()
    
    init {
        // 初始化时刷新设置
        refreshSecuritySettings()
        loadCleanupStats()
    }
    
    /**
     * 刷新安全设置
     */
    fun refreshSecuritySettings() {
        viewModelScope.launch {
            try {
                _securityState.value = _securityState.value.copy(isLoading = true)
                
                // 加载安全设置
                val apiSecurityEnabled = encryptedStorageManager.getBoolean("api_security_enabled", true)
                val dataEncryptionEnabled = encryptedStorageManager.getBoolean("data_encryption_enabled", true)
                
                _securityState.value = _securityState.value.copy(
                    isLoading = false,
                    apiSecurityEnabled = apiSecurityEnabled,
                    dataEncryptionEnabled = dataEncryptionEnabled,
                    error = null
                )
                
            } catch (e: Exception) {
                _securityState.value = _securityState.value.copy(
                    isLoading = false,
                    error = "加载安全设置失败：${e.message}"
                )
            }
        }
    }
    
    /**
     * 加载清理统计信息
     */
    fun loadCleanupStats() {
        viewModelScope.launch {
            try {
                val stats = dataCleanupManager.getCleanupStats()
                _cleanupStats.value = stats
            } catch (e: Exception) {
                _securityState.value = _securityState.value.copy(
                    error = "加载存储统计失败：${e.message}"
                )
            }
        }
    }
    
    /**
     * 切换API安全功能
     * @param enabled 是否启用
     */
    fun toggleApiSecurity(enabled: Boolean) {
        viewModelScope.launch {
            try {
                encryptedStorageManager.saveBoolean("api_security_enabled", enabled)
                
                // 记录设置变更
                privacyManager.logDataAccess(
                    "security_settings", 
                    DataAccessType.UPDATE, 
                    "API安全功能${if (enabled) "启用" else "禁用"}"
                )
                
                _securityState.value = _securityState.value.copy(
                    apiSecurityEnabled = enabled
                )
                
            } catch (e: Exception) {
                _securityState.value = _securityState.value.copy(
                    error = "更新API安全设置失败：${e.message}"
                )
            }
        }
    }
    
    /**
     * 切换数据加密功能
     * @param enabled 是否启用
     */
    fun toggleDataEncryption(enabled: Boolean) {
        viewModelScope.launch {
            try {
                encryptedStorageManager.saveBoolean("data_encryption_enabled", enabled)
                
                // 记录设置变更
                privacyManager.logDataAccess(
                    "security_settings", 
                    DataAccessType.UPDATE, 
                    "数据加密功能${if (enabled) "启用" else "禁用"}"
                )
                
                _securityState.value = _securityState.value.copy(
                    dataEncryptionEnabled = enabled
                )
                
            } catch (e: Exception) {
                _securityState.value = _securityState.value.copy(
                    error = "更新数据加密设置失败：${e.message}"
                )
            }
        }
    }
    
    /**
     * 更新数据收集偏好
     * @param preference 数据收集偏好
     */
    fun updateDataCollectionPreference(preference: DataCollectionPreference) {
        viewModelScope.launch {
            try {
                privacyManager.setDataCollectionPreference(preference)
                
                // 记录设置变更
                privacyManager.logDataAccess(
                    "privacy_settings", 
                    DataAccessType.UPDATE, 
                    "数据收集偏好更新为：${preference.displayName}"
                )
                
            } catch (e: Exception) {
                _securityState.value = _securityState.value.copy(
                    error = "更新数据收集偏好失败：${e.message}"
                )
            }
        }
    }
    
    /**
     * 更新数据保留期限
     * @param period 数据保留期限
     */
    fun updateDataRetentionPeriod(period: DataRetentionPeriod) {
        viewModelScope.launch {
            try {
                privacyManager.setDataRetentionPeriod(period)
                
                // 记录设置变更
                privacyManager.logDataAccess(
                    "privacy_settings", 
                    DataAccessType.UPDATE, 
                    "数据保留期限更新为：${period.displayName}"
                )
                
            } catch (e: Exception) {
                _securityState.value = _securityState.value.copy(
                    error = "更新数据保留期限失败：${e.message}"
                )
            }
        }
    }
    
    /**
     * 执行手动清理
     */
    fun performManualCleanup() {
        viewModelScope.launch {
            try {
                _securityState.value = _securityState.value.copy(isLoading = true)
                
                // 执行清理
                dataCleanupManager.performFullCleanup(CleanupType.MANUAL)
                
                // 重新加载统计信息
                loadCleanupStats()
                
                _securityState.value = _securityState.value.copy(
                    isLoading = false,
                    message = "数据清理完成"
                )
                
            } catch (e: Exception) {
                _securityState.value = _securityState.value.copy(
                    isLoading = false,
                    error = "数据清理失败：${e.message}"
                )
            }
        }
    }
    
    /**
     * 导出用户数据
     */
    fun exportUserData() {
        viewModelScope.launch {
            try {
                _securityState.value = _securityState.value.copy(isLoading = true)
                
                // 导出用户数据
                val exportData = privacyManager.exportUserData()
                
                // 记录导出操作
                privacyManager.logDataAccess(
                    "user_data", 
                    DataAccessType.READ, 
                    "用户数据导出"
                )
                
                _securityState.value = _securityState.value.copy(
                    isLoading = false,
                    message = "用户数据导出完成",
                    exportedData = exportData
                )
                
            } catch (e: Exception) {
                _securityState.value = _securityState.value.copy(
                    isLoading = false,
                    error = "用户数据导出失败：${e.message}"
                )
            }
        }
    }
    
    /**
     * 删除所有用户数据
     * @param reason 删除原因
     */
    fun deleteAllUserData(reason: String) {
        viewModelScope.launch {
            try {
                _securityState.value = _securityState.value.copy(isLoading = true)
                
                // 删除所有用户数据
                privacyManager.deleteAllUserData(reason)
                
                // 重新加载统计信息
                loadCleanupStats()
                
                _securityState.value = _securityState.value.copy(
                    isLoading = false,
                    message = "所有用户数据已删除"
                )
                
            } catch (e: Exception) {
                _securityState.value = _securityState.value.copy(
                    isLoading = false,
                    error = "删除用户数据失败：${e.message}"
                )
            }
        }
    }
    
    /**
     * 加载数据访问日志
     */
    fun loadDataAccessLogs() {
        viewModelScope.launch {
            try {
                _securityState.value = _securityState.value.copy(isLoading = true)
                
                // 获取访问日志
                val logs = privacyManager.getDataAccessLogs(50)
                
                _securityState.value = _securityState.value.copy(
                    isLoading = false,
                    accessLogs = logs
                )
                
            } catch (e: Exception) {
                _securityState.value = _securityState.value.copy(
                    isLoading = false,
                    error = "加载访问日志失败：${e.message}"
                )
            }
        }
    }
    
    /**
     * 打开隐私政策
     */
    fun openPrivacyPolicy() {
        viewModelScope.launch {
            try {
                privacyManager.openPrivacyPolicy()
                
                // 记录访问
                privacyManager.logDataAccess(
                    "privacy_policy", 
                    DataAccessType.READ, 
                    "用户查看隐私政策"
                )
                
            } catch (e: Exception) {
                _securityState.value = _securityState.value.copy(
                    error = "打开隐私政策失败：${e.message}"
                )
            }
        }
    }
    
    /**
     * 打开用户协议
     */
    fun openUserAgreement() {
        viewModelScope.launch {
            try {
                privacyManager.openUserAgreement()
                
                // 记录访问
                privacyManager.logDataAccess(
                    "user_agreement", 
                    DataAccessType.READ, 
                    "用户查看用户协议"
                )
                
            } catch (e: Exception) {
                _securityState.value = _securityState.value.copy(
                    error = "打开用户协议失败：${e.message}"
                )
            }
        }
    }
    
    /**
     * 清除错误信息
     */
    fun clearError() {
        _securityState.value = _securityState.value.copy(error = null)
    }
    
    /**
     * 清除消息
     */
    fun clearMessage() {
        _securityState.value = _securityState.value.copy(message = null)
    }
    
    /**
     * 清除导出数据
     */
    fun clearExportedData() {
        _securityState.value = _securityState.value.copy(exportedData = null)
    }
}

/**
 * 安全设置状态数据类
 * 封装安全设置相关的UI状态信息
 */
data class SecuritySettingsState(
    val isLoading: Boolean = false,                             // 是否正在加载
    val apiSecurityEnabled: Boolean = true,                     // API安全是否启用
    val dataEncryptionEnabled: Boolean = true,                  // 数据加密是否启用
    val accessLogs: List<DataAccessLog> = emptyList(),         // 数据访问日志
    val exportedData: UserDataExport? = null,                  // 导出的用户数据
    val message: String? = null,                                // 成功消息
    val error: String? = null                                   // 错误信息
)