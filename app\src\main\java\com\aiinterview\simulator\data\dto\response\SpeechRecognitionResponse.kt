package com.aiinterview.simulator.data.dto.response

data class SpeechRecognitionResponse(
    val success: Boolean,
    val text: String,
    val confidence: Double, // 识别置信度 0-1
    val duration: Long, // 音频时长（毫秒）
    val provider: String, // 服务提供商：baidu, tencent
    val errorCode: String? = null,
    val errorMessage: String? = null,
    val rawResponse: String? = null // 原始响应数据
)

data class BaiduASRResponse(
    val err_no: Int,
    val err_msg: String,
    val corpus_no: String? = null,
    val sn: String? = null,
    val result: List<String>? = null
)

data class TencentASRResponse(
    val Response: TencentASRResponseData
)

data class TencentASRResponseData(
    val RequestId: String,
    val Data: TencentASRData? = null,
    val Error: TencentError? = null
)

data class TencentASRData(
    val TaskId: Long,
    val Url: String,
    val Text: String,
    val AudioTime: Long
)

data class TencentError(
    val Code: String,
    val Message: String
)

// 统一的语音识别结果
data class RecognitionResult(
    val text: String,
    val confidence: Double,
    val duration: Long,
    val provider: String,
    val success: <PERSON>olean,
    val errorMessage: String? = null
)