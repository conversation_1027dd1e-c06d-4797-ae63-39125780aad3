package com.aiinterview.simulator.presentation.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat

/**
 * 深色主题颜色方案
 * 适用于深色模式的完整颜色定义，提供舒适的夜间使用体验
 */
private val DarkColorScheme = darkColorScheme(
    primary = Primary80,                    // 主色调 - 浅蓝色，在深色背景下更柔和
    onPrimary = Primary10,                  // 主色调上的文字 - 深蓝色，确保对比度
    primaryContainer = Primary30,           // 主色调容器 - 中深蓝色，提供层次感
    onPrimaryContainer = Primary90,         // 主色调容器上的文字 - 极浅蓝色，保持可读性
    
    secondary = Secondary80,                // 次要色调 - 浅青色，与主色调协调
    onSecondary = Secondary10,              // 次要色调上的文字 - 深青色，确保对比度
    secondaryContainer = Secondary30,       // 次要色调容器 - 中深青色，提供层次感
    onSecondaryContainer = Secondary90,     // 次要色调容器上的文字 - 极浅青色，保持可读性
    
    tertiary = Tertiary80,                  // 第三色调 - 浅紫色，用于强调元素
    onTertiary = Tertiary10,                // 第三色调上的文字 - 深紫色，确保对比度
    tertiaryContainer = Tertiary30,         // 第三色调容器 - 中深紫色，提供层次感
    onTertiaryContainer = Tertiary90,       // 第三色调容器上的文字 - 极浅紫色，保持可读性
    
    error = Error80,                        // 错误色 - 浅红色，在深色背景下更温和
    onError = Error10,                      // 错误色上的文字 - 深红色，确保对比度
    errorContainer = Error30,               // 错误容器 - 中深红色，提供层次感
    onErrorContainer = Error90,             // 错误容器上的文字 - 极浅红色，保持可读性
    
    background = Neutral10,                 // 背景色 - 最深灰色，减少眼部疲劳
    onBackground = Neutral90,               // 背景上的文字 - 极浅灰色，确保可读性
    surface = Neutral10,                    // 表面色 - 最深灰色，与背景一致
    onSurface = Neutral90,                  // 表面上的文字 - 极浅灰色，确保对比度
    surfaceVariant = NeutralVariant30,      // 表面变体 - 深色边框，提供微妙分隔
    onSurfaceVariant = NeutralVariant80,    // 表面变体上的文字 - 很浅边框，保持可读性
    
    outline = NeutralVariant60,             // 轮廓线 - 浅色边框，提供清晰分界
    outlineVariant = NeutralVariant30,      // 轮廓线变体 - 深色边框，用于微妙分隔
    scrim = Neutral10,                      // 遮罩色 - 最深灰色，用于模态覆盖
    inverseSurface = Neutral90,             // 反转表面色 - 极浅灰色，用于对比元素
    inverseOnSurface = Neutral20,           // 反转表面上的文字 - 深灰色，确保对比度
    inversePrimary = Primary40,             // 反转主色调 - 主要蓝色，用于反转元素
    surfaceDim = Neutral10,                 // 暗淡表面色 - 最深灰色，用于低优先级表面
    surfaceBright = Neutral20,              // 明亮表面色 - 深灰色，用于高优先级表面
    surfaceContainerLowest = Neutral10,     // 最低容器表面色 - 与背景融合
    surfaceContainerLow = Neutral20,        // 低容器表面色 - 轻微提升
    surfaceContainer = Neutral20,           // 容器表面色 - 标准容器
    surfaceContainerHigh = Neutral30,       // 高容器表面色 - 明显提升
    surfaceContainerHighest = Neutral40     // 最高容器表面色 - 最大提升
)

/**
 * 浅色主题颜色方案
 * 适用于浅色模式的完整颜色定义，提供清晰明亮的日间使用体验
 */
private val LightColorScheme = lightColorScheme(
    primary = Primary40,                    // 主色调 - 主要蓝色，提供强烈的视觉焦点
    onPrimary = Primary99,                  // 主色调上的文字 - 纯白色，确保最佳对比度
    primaryContainer = Primary90,           // 主色调容器 - 极浅蓝色，提供柔和背景
    onPrimaryContainer = Primary10,         // 主色调容器上的文字 - 深蓝色，确保可读性
    
    secondary = Secondary40,                // 次要色调 - 主要青色，与主色调形成和谐搭配
    onSecondary = Secondary99,              // 次要色调上的文字 - 纯白色，确保最佳对比度
    secondaryContainer = Secondary90,       // 次要色调容器 - 极浅青色，提供柔和背景
    onSecondaryContainer = Secondary10,     // 次要色调容器上的文字 - 深青色，确保可读性
    
    tertiary = Tertiary40,                  // 第三色调 - 主要紫色，用于强调和装饰
    onTertiary = Tertiary99,                // 第三色调上的文字 - 纯白色，确保最佳对比度
    tertiaryContainer = Tertiary90,         // 第三色调容器 - 极浅紫色，提供柔和背景
    onTertiaryContainer = Tertiary10,       // 第三色调容器上的文字 - 深紫色，确保可读性
    
    error = Error40,                        // 错误色 - 主要错误红色，清晰传达错误状态
    onError = Error99,                      // 错误色上的文字 - 纯白色，确保最佳对比度
    errorContainer = Error90,               // 错误容器 - 极浅错误红色，提供柔和错误背景
    onErrorContainer = Error10,             // 错误容器上的文字 - 深错误红色，确保可读性
    
    background = Neutral99,                 // 背景色 - 纯白色，提供清洁明亮的基础
    onBackground = Neutral10,               // 背景上的文字 - 最深灰色，确保最佳可读性
    surface = Neutral99,                    // 表面色 - 纯白色，与背景保持一致
    onSurface = Neutral10,                  // 表面上的文字 - 最深灰色，确保清晰对比
    surfaceVariant = NeutralVariant90,      // 表面变体 - 极浅边框，提供微妙分隔
    onSurfaceVariant = NeutralVariant30,    // 表面变体上的文字 - 深色边框，确保可读性
    
    outline = NeutralVariant50,             // 轮廓线 - 标准边框，提供清晰分界
    outlineVariant = NeutralVariant80,      // 轮廓线变体 - 很浅边框，用于微妙分隔
    scrim = Neutral10,                      // 遮罩色 - 最深灰色，用于模态覆盖
    inverseSurface = Neutral20,             // 反转表面色 - 深灰色，用于对比元素
    inverseOnSurface = Neutral95,           // 反转表面上的文字 - 接近白色，确保对比度
    inversePrimary = Primary80,             // 反转主色调 - 浅蓝色，用于反转元素
    surfaceDim = Neutral90,                 // 暗淡表面色 - 极浅灰色，用于低优先级表面
    surfaceBright = Neutral99,              // 明亮表面色 - 纯白色，用于高优先级表面
    surfaceContainerLowest = Neutral99,     // 最低容器表面色 - 与背景融合
    surfaceContainerLow = Neutral95,        // 低容器表面色 - 轻微区分
    surfaceContainer = Neutral90,           // 容器表面色 - 标准容器背景
    surfaceContainerHigh = Neutral80,       // 高容器表面色 - 明显区分
    surfaceContainerHighest = Neutral70     // 最高容器表面色 - 最大区分
)

/**
 * 高对比度深色主题颜色方案
 * 适用于需要更高对比度的深色模式，提升可访问性
 */
private val HighContrastDarkColorScheme = darkColorScheme(
    primary = Primary90,                    // 主色调 - 更亮的蓝色，提高对比度
    onPrimary = Primary0,                   // 主色调上的文字 - 纯黑色，最大对比度
    primaryContainer = Primary20,           // 主色调容器 - 较深蓝色，保持层次
    onPrimaryContainer = Primary95,         // 主色调容器上的文字 - 接近白色，高对比度
    
    secondary = Secondary90,                // 次要色调 - 更亮的青色，提高对比度
    onSecondary = Secondary0,               // 次要色调上的文字 - 纯黑色，最大对比度
    secondaryContainer = Secondary20,       // 次要色调容器 - 较深青色，保持层次
    onSecondaryContainer = Secondary95,     // 次要色调容器上的文字 - 接近白色，高对比度
    
    tertiary = Tertiary90,                  // 第三色调 - 更亮的紫色，提高对比度
    onTertiary = Tertiary0,                 // 第三色调上的文字 - 纯黑色，最大对比度
    tertiaryContainer = Tertiary20,         // 第三色调容器 - 较深紫色，保持层次
    onTertiaryContainer = Tertiary95,       // 第三色调容器上的文字 - 接近白色，高对比度
    
    error = Error90,                        // 错误色 - 更亮的红色，提高对比度
    onError = Error0,                       // 错误色上的文字 - 纯黑色，最大对比度
    errorContainer = Error20,               // 错误容器 - 较深红色，保持层次
    onErrorContainer = Error95,             // 错误容器上的文字 - 接近白色，高对比度
    
    background = Neutral0,                  // 背景色 - 纯黑色，最大对比度
    onBackground = Neutral99,               // 背景上的文字 - 纯白色，最大对比度
    surface = Neutral0,                     // 表面色 - 纯黑色，与背景一致
    onSurface = Neutral99,                  // 表面上的文字 - 纯白色，最大对比度
    surfaceVariant = NeutralVariant20,      // 表面变体 - 很深边框，高对比度
    onSurfaceVariant = NeutralVariant90,    // 表面变体上的文字 - 很浅边框，高对比度
    
    outline = NeutralVariant70,             // 轮廓线 - 较亮边框，提高可见性
    outlineVariant = NeutralVariant20,      // 轮廓线变体 - 很深边框，保持层次
    scrim = Neutral0,                       // 遮罩色 - 纯黑色，最大覆盖效果
    inverseSurface = Neutral99,             // 反转表面色 - 纯白色，最大对比度
    inverseOnSurface = Neutral10,           // 反转表面上的文字 - 很深灰色，高对比度
    inversePrimary = Primary30,             // 反转主色调 - 较深蓝色，保持识别性
    surfaceDim = Neutral0,                  // 暗淡表面色 - 纯黑色，最深背景
    surfaceBright = Neutral30,              // 明亮表面色 - 较深灰色，提供层次
    surfaceContainerLowest = Neutral0,      // 最低容器表面色 - 与背景融合
    surfaceContainerLow = Neutral10,        // 低容器表面色 - 轻微提升
    surfaceContainer = Neutral20,           // 容器表面色 - 标准容器
    surfaceContainerHigh = Neutral30,       // 高容器表面色 - 明显提升
    surfaceContainerHighest = Neutral40     // 最高容器表面色 - 最大提升
)

/**
 * 高对比度浅色主题颜色方案
 * 适用于需要更高对比度的浅色模式，提升可访问性
 */
private val HighContrastLightColorScheme = lightColorScheme(
    primary = Primary30,                    // 主色调 - 更深的蓝色，提高对比度
    onPrimary = Primary100,                 // 主色调上的文字 - 纯白色，最大对比度
    primaryContainer = Primary95,           // 主色调容器 - 接近白色的蓝色，保持层次
    onPrimaryContainer = Primary0,          // 主色调容器上的文字 - 纯黑色，最大对比度
    
    secondary = Secondary30,                // 次要色调 - 更深的青色，提高对比度
    onSecondary = Secondary100,             // 次要色调上的文字 - 纯白色，最大对比度
    secondaryContainer = Secondary95,       // 次要色调容器 - 接近白色的青色，保持层次
    onSecondaryContainer = Secondary0,      // 次要色调容器上的文字 - 纯黑色，最大对比度
    
    tertiary = Tertiary30,                  // 第三色调 - 更深的紫色，提高对比度
    onTertiary = Tertiary100,               // 第三色调上的文字 - 纯白色，最大对比度
    tertiaryContainer = Tertiary95,         // 第三色调容器 - 接近白色的紫色，保持层次
    onTertiaryContainer = Tertiary0,        // 第三色调容器上的文字 - 纯黑色，最大对比度
    
    error = Error30,                        // 错误色 - 更深的红色，提高对比度
    onError = Error100,                     // 错误色上的文字 - 纯白色，最大对比度
    errorContainer = Error95,               // 错误容器 - 接近白色的红色，保持层次
    onErrorContainer = Error0,              // 错误容器上的文字 - 纯黑色，最大对比度
    
    background = Neutral100,                // 背景色 - 纯白色，最大对比度
    onBackground = Neutral0,                // 背景上的文字 - 纯黑色，最大对比度
    surface = Neutral100,                   // 表面色 - 纯白色，与背景一致
    onSurface = Neutral0,                   // 表面上的文字 - 纯黑色，最大对比度
    surfaceVariant = NeutralVariant95,      // 表面变体 - 接近白色，高对比度
    onSurfaceVariant = NeutralVariant20,    // 表面变体上的文字 - 很深边框，高对比度
    
    outline = NeutralVariant40,             // 轮廓线 - 较深边框，提高可见性
    outlineVariant = NeutralVariant90,      // 轮廓线变体 - 很浅边框，保持层次
    scrim = Neutral0,                       // 遮罩色 - 纯黑色，最大覆盖效果
    inverseSurface = Neutral10,             // 反转表面色 - 很深灰色，最大对比度
    inverseOnSurface = Neutral99,           // 反转表面上的文字 - 接近白色，高对比度
    inversePrimary = Primary80,             // 反转主色调 - 浅蓝色，保持识别性
    surfaceDim = Neutral80,                 // 暗淡表面色 - 较浅灰色，提供层次
    surfaceBright = Neutral100,             // 明亮表面色 - 纯白色，最亮背景
    surfaceContainerLowest = Neutral100,    // 最低容器表面色 - 与背景融合
    surfaceContainerLow = Neutral99,        // 低容器表面色 - 轻微区分
    surfaceContainer = Neutral95,           // 容器表面色 - 标准容器背景
    surfaceContainerHigh = Neutral90,       // 高容器表面色 - 明显区分
    surfaceContainerHighest = Neutral80     // 最高容器表面色 - 最大区分
)

/**
 * AI面试应用主题组件
 * 提供完整的Material Design 3主题支持，包括动态颜色、深色模式和高对比度模式
 * @param config 主题配置，包含深色模式、动态颜色、高对比度等设置
 * @param content 主题包装的内容
 */
@Composable
fun AIInterviewTheme(
    config: ThemeConfig = ThemeConfig(      // 默认主题配置
        isDarkTheme = isSystemInDarkTheme(), // 跟随系统深色模式设置
        useDynamicColors = true,            // 启用动态颜色（Android 12+）
        useHighContrast = false,            // 默认不使用高对比度
        fontSize = FontSize.MEDIUM,         // 默认中等字体大小
        cornerRadius = CornerRadius.MEDIUM  // 默认中等圆角
    ),
    content: @Composable () -> Unit         // 子组件内容
) {
    // 根据配置选择合适的颜色方案
    val colorScheme = when {
        // Android 12+ 支持动态颜色且用户启用
        config.useDynamicColors && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current  // 获取当前上下文
            if (config.isDarkTheme) {           // 根据深色模式选择动态颜色方案
                dynamicDarkColorScheme(context) // 动态深色颜色方案
            } else {
                dynamicLightColorScheme(context) // 动态浅色颜色方案
            }
        }
        // 高对比度深色主题
        config.useHighContrast && config.isDarkTheme -> HighContrastDarkColorScheme
        // 高对比度浅色主题
        config.useHighContrast && !config.isDarkTheme -> HighContrastLightColorScheme
        // 标准深色主题
        config.isDarkTheme -> DarkColorScheme
        // 标准浅色主题
        else -> LightColorScheme
    }
    
    // 根据配置调整字体排版
    val adjustedTypography = Typography.copy(
        displayLarge = Typography.displayLarge.copy(
            fontSize = Typography.displayLarge.fontSize * config.fontSize.scale // 应用字体缩放
        ),
        displayMedium = Typography.displayMedium.copy(
            fontSize = Typography.displayMedium.fontSize * config.fontSize.scale // 应用字体缩放
        ),
        displaySmall = Typography.displaySmall.copy(
            fontSize = Typography.displaySmall.fontSize * config.fontSize.scale // 应用字体缩放
        ),
        headlineLarge = Typography.headlineLarge.copy(
            fontSize = Typography.headlineLarge.fontSize * config.fontSize.scale // 应用字体缩放
        ),
        headlineMedium = Typography.headlineMedium.copy(
            fontSize = Typography.headlineMedium.fontSize * config.fontSize.scale // 应用字体缩放
        ),
        headlineSmall = Typography.headlineSmall.copy(
            fontSize = Typography.headlineSmall.fontSize * config.fontSize.scale // 应用字体缩放
        ),
        titleLarge = Typography.titleLarge.copy(
            fontSize = Typography.titleLarge.fontSize * config.fontSize.scale // 应用字体缩放
        ),
        titleMedium = Typography.titleMedium.copy(
            fontSize = Typography.titleMedium.fontSize * config.fontSize.scale // 应用字体缩放
        ),
        titleSmall = Typography.titleSmall.copy(
            fontSize = Typography.titleSmall.fontSize * config.fontSize.scale // 应用字体缩放
        ),
        bodyLarge = Typography.bodyLarge.copy(
            fontSize = Typography.bodyLarge.fontSize * config.fontSize.scale // 应用字体缩放
        ),
        bodyMedium = Typography.bodyMedium.copy(
            fontSize = Typography.bodyMedium.fontSize * config.fontSize.scale // 应用字体缩放
        ),
        bodySmall = Typography.bodySmall.copy(
            fontSize = Typography.bodySmall.fontSize * config.fontSize.scale // 应用字体缩放
        ),
        labelLarge = Typography.labelLarge.copy(
            fontSize = Typography.labelLarge.fontSize * config.fontSize.scale // 应用字体缩放
        ),
        labelMedium = Typography.labelMedium.copy(
            fontSize = Typography.labelMedium.fontSize * config.fontSize.scale // 应用字体缩放
        ),
        labelSmall = Typography.labelSmall.copy(
            fontSize = Typography.labelSmall.fontSize * config.fontSize.scale // 应用字体缩放
        )
    )
    
    // 根据配置调整形状定义
    val adjustedShapes = Shapes.copy(
        extraSmall = RoundedCornerShape((4 * config.cornerRadius.scale).dp), // 应用圆角缩放
        small = RoundedCornerShape((8 * config.cornerRadius.scale).dp),      // 应用圆角缩放
        medium = RoundedCornerShape((12 * config.cornerRadius.scale).dp),    // 应用圆角缩放
        large = RoundedCornerShape((16 * config.cornerRadius.scale).dp),     // 应用圆角缩放
        extraLarge = RoundedCornerShape((24 * config.cornerRadius.scale).dp) // 应用圆角缩放
    )
    
    // 获取当前视图，用于设置状态栏样式
    val view = LocalView.current
    if (!view.isInEditMode) {               // 确保不在编辑模式下
        // 设置状态栏样式，确保在不同主题下的可读性
        SideEffect {
            val window = (view.context as Activity).window // 获取窗口对象
            // 设置状态栏颜色为表面色，提供更好的视觉连续性
            window.statusBarColor = colorScheme.surface.toArgb()
            // 根据主题设置状态栏图标和文字颜色
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = !config.isDarkTheme
        }
    }

    // 提供主题配置给子组件
    ThemeConfigProvider(config = config) {
        // 应用Material Design 3主题
        MaterialTheme(
            colorScheme = colorScheme,      // 应用选定的颜色方案
            typography = adjustedTypography, // 应用调整后的字体排版
            shapes = adjustedShapes,        // 应用调整后的形状定义
            content = content               // 渲染主题包装的内容
        )
    }
}