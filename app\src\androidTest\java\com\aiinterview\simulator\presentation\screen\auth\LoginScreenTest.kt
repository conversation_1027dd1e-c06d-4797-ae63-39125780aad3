package com.aiinterview.simulator.presentation.screen.auth

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.aiinterview.simulator.presentation.theme.AIInterviewSimulatorTheme
import com.aiinterview.simulator.util.ComposeTestUtils
import com.aiinterview.simulator.util.ComposeTestUtils.assertButtonClickable
import com.aiinterview.simulator.util.ComposeTestUtils.assertButtonNotClickable
import com.aiinterview.simulator.util.ComposeTestUtils.assertErrorMessage
import com.aiinterview.simulator.util.ComposeTestUtils.assertSuccessMessage
import com.aiinterview.simulator.util.ComposeTestUtils.assertTextExists
import com.aiinterview.simulator.util.ComposeTestUtils.clickButton
import com.aiinterview.simulator.util.ComposeTestUtils.inputText
import com.aiinterview.simulator.util.ComposeTestUtils.waitForLoadingToComplete
import com.aiinterview.simulator.util.MockTestData
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * 登录界面UI测试类
 * 测试登录界面的各种交互场景和状态变化
 */
@RunWith(AndroidJUnit4::class)
class LoginScreenTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    /**
     * 测试登录界面初始状态
     */
    @Test
    fun loginScreen_initialState_displaysCorrectly() {
        // Given - 设置登录界面
        composeTestRule.setContent {
            AIInterviewSimulatorTheme {
                // 这里应该是实际的LoginScreen组件
                // LoginScreen(onLoginSuccess = {}, onNavigateToRegister = {})
            }
        }

        // Then - 验证初始状态
        composeTestRule.assertTextExists("用户登录")
        composeTestRule.assertTextExists("手机号")
        composeTestRule.assertTextExists("验证码")
        composeTestRule.assertTextExists("获取验证码")
        composeTestRule.assertTextExists("登录")
        composeTestRule.assertTextExists("还没有账号？立即注册")
        
        // 验证登录按钮初始状态为不可点击
        composeTestRule.assertButtonNotClickable("登录")
    }

    /**
     * 测试手机号输入验证
     */
    @Test
    fun loginScreen_phoneNumberInput_validatesCorrectly() {
        composeTestRule.setContent {
            AIInterviewSimulatorTheme {
                // LoginScreen(onLoginSuccess = {}, onNavigateToRegister = {})
            }
        }

        // When - 输入无效手机号
        composeTestRule.inputText("手机号", "123")
        
        // Then - 验证错误提示
        composeTestRule.assertErrorMessage("请输入正确的手机号码")
        composeTestRule.assertButtonNotClickable("获取验证码")

        // When - 输入有效手机号
        composeTestRule.onNodeWithText("手机号").performTextClearance()
        composeTestRule.inputText("手机号", "13800138000")
        
        // Then - 验证获取验证码按钮可点击
        composeTestRule.assertButtonClickable("获取验证码")
    }

    /**
     * 测试验证码输入验证
     */
    @Test
    fun loginScreen_verificationCodeInput_validatesCorrectly() {
        composeTestRule.setContent {
            AIInterviewSimulatorTheme {
                // LoginScreen(onLoginSuccess = {}, onNavigateToRegister = {})
            }
        }

        // Given - 输入有效手机号
        composeTestRule.inputText("手机号", "13800138000")
        
        // When - 输入无效验证码
        composeTestRule.inputText("验证码", "123")
        
        // Then - 验证错误提示
        composeTestRule.assertErrorMessage("请输入6位验证码")
        composeTestRule.assertButtonNotClickable("登录")

        // When - 输入有效验证码
        composeTestRule.onNodeWithText("验证码").performTextClearance()
        composeTestRule.inputText("验证码", "123456")
        
        // Then - 验证登录按钮可点击
        composeTestRule.assertButtonClickable("登录")
    }

    /**
     * 测试获取验证码功能
     */
    @Test
    fun loginScreen_getVerificationCode_worksCorrectly() {
        composeTestRule.setContent {
            AIInterviewSimulatorTheme {
                // LoginScreen(onLoginSuccess = {}, onNavigateToRegister = {})
            }
        }

        // Given - 输入有效手机号
        composeTestRule.inputText("手机号", "13800138000")
        
        // When - 点击获取验证码
        composeTestRule.clickButton("获取验证码")
        
        // Then - 验证按钮状态变化
        composeTestRule.waitForLoadingToComplete()
        composeTestRule.assertTextExists("60s后重新获取")
        composeTestRule.assertButtonNotClickable("60s后重新获取")
        
        // 验证成功提示
        composeTestRule.assertSuccessMessage("验证码已发送")
    }

    /**
     * 测试获取验证码失败场景
     */
    @Test
    fun loginScreen_getVerificationCodeFailed_showsError() {
        composeTestRule.setContent {
            AIInterviewSimulatorTheme {
                // LoginScreen(
                //     onLoginSuccess = {},
                //     onNavigateToRegister = {},
                //     // 模拟网络错误
                //     mockNetworkError = true
                // )
            }
        }

        // Given - 输入有效手机号
        composeTestRule.inputText("手机号", "13800138000")
        
        // When - 点击获取验证码（模拟失败）
        composeTestRule.clickButton("获取验证码")
        
        // Then - 验证错误提示
        composeTestRule.waitForLoadingToComplete()
        composeTestRule.assertErrorMessage(MockTestData.ErrorMessages.NETWORK_ERROR)
        composeTestRule.assertButtonClickable("获取验证码")
    }

    /**
     * 测试登录成功场景
     */
    @Test
    fun loginScreen_loginSuccess_navigatesToHome() {
        var loginSuccessCalled = false
        
        composeTestRule.setContent {
            AIInterviewSimulatorTheme {
                // LoginScreen(
                //     onLoginSuccess = { loginSuccessCalled = true },
                //     onNavigateToRegister = {}
                // )
            }
        }

        // Given - 输入有效登录信息
        composeTestRule.inputText("手机号", "13800138000")
        composeTestRule.inputText("验证码", "123456")
        
        // When - 点击登录
        composeTestRule.clickButton("登录")
        
        // Then - 验证登录流程
        composeTestRule.waitForLoadingToComplete()
        composeTestRule.assertSuccessMessage(MockTestData.SuccessMessages.LOGIN_SUCCESS)
        
        // 验证导航回调被调用
        assert(loginSuccessCalled) { "登录成功回调应该被调用" }
    }

    /**
     * 测试登录失败场景
     */
    @Test
    fun loginScreen_loginFailed_showsError() {
        composeTestRule.setContent {
            AIInterviewSimulatorTheme {
                // LoginScreen(
                //     onLoginSuccess = {},
                //     onNavigateToRegister = {},
                //     // 模拟登录失败
                //     mockLoginError = true
                // )
            }
        }

        // Given - 输入登录信息
        composeTestRule.inputText("手机号", "13800138000")
        composeTestRule.inputText("验证码", "123456")
        
        // When - 点击登录（模拟失败）
        composeTestRule.clickButton("登录")
        
        // Then - 验证错误提示
        composeTestRule.waitForLoadingToComplete()
        composeTestRule.assertErrorMessage(MockTestData.ErrorMessages.LOGIN_FAILED)
        composeTestRule.assertButtonClickable("登录")
    }

    /**
     * 测试导航到注册页面
     */
    @Test
    fun loginScreen_navigateToRegister_worksCorrectly() {
        var navigateToRegisterCalled = false
        
        composeTestRule.setContent {
            AIInterviewSimulatorTheme {
                // LoginScreen(
                //     onLoginSuccess = {},
                //     onNavigateToRegister = { navigateToRegisterCalled = true }
                // )
            }
        }

        // When - 点击注册链接
        composeTestRule.clickButton("还没有账号？立即注册")
        
        // Then - 验证导航回调被调用
        assert(navigateToRegisterCalled) { "导航到注册页面的回调应该被调用" }
    }

    /**
     * 测试加载状态显示
     */
    @Test
    fun loginScreen_loadingState_displaysCorrectly() {
        composeTestRule.setContent {
            AIInterviewSimulatorTheme {
                // LoginScreen(
                //     onLoginSuccess = {},
                //     onNavigateToRegister = {},
                //     // 模拟加载状态
                //     isLoading = true
                // )
            }
        }

        // Then - 验证加载状态
        composeTestRule.onNode(hasProgressBarRangeInfo(ProgressBarRangeInfo.Indeterminate))
            .assertExists()
            .assertIsDisplayed()
        
        // 验证按钮在加载时不可点击
        composeTestRule.assertButtonNotClickable("登录")
        composeTestRule.assertButtonNotClickable("获取验证码")
    }

    /**
     * 测试界面响应式布局
     */
    @Test
    fun loginScreen_responsiveLayout_adaptsCorrectly() {
        composeTestRule.setContent {
            AIInterviewSimulatorTheme {
                // LoginScreen(onLoginSuccess = {}, onNavigateToRegister = {})
            }
        }

        // 验证关键元素都可见
        composeTestRule.assertTextExists("用户登录")
        composeTestRule.onNodeWithText("手机号").assertIsDisplayed()
        composeTestRule.onNodeWithText("验证码").assertIsDisplayed()
        composeTestRule.onNodeWithText("登录").assertIsDisplayed()
        
        // 验证输入框可以获得焦点
        composeTestRule.onNodeWithText("手机号").assertIsFocusable()
        composeTestRule.onNodeWithText("验证码").assertIsFocusable()
    }

    /**
     * 测试键盘交互
     */
    @Test
    fun loginScreen_keyboardInteraction_worksCorrectly() {
        composeTestRule.setContent {
            AIInterviewSimulatorTheme {
                // LoginScreen(onLoginSuccess = {}, onNavigateToRegister = {})
            }
        }

        // When - 点击手机号输入框
        composeTestRule.onNodeWithText("手机号").performClick()
        
        // Then - 验证输入框获得焦点
        composeTestRule.onNodeWithText("手机号").assertIsFocused()
        
        // When - 输入文本并按Tab键切换到下一个输入框
        composeTestRule.onNodeWithText("手机号").performTextInput("13800138000")
        composeTestRule.onNodeWithText("手机号").performImeAction()
        
        // Then - 验证焦点切换到验证码输入框
        composeTestRule.onNodeWithText("验证码").assertIsFocused()
    }

    /**
     * 测试无障碍功能
     */
    @Test
    fun loginScreen_accessibility_worksCorrectly() {
        composeTestRule.setContent {
            AIInterviewSimulatorTheme {
                // LoginScreen(onLoginSuccess = {}, onNavigateToRegister = {})
            }
        }

        // 验证重要元素有正确的内容描述
        composeTestRule.onNodeWithContentDescription("手机号输入框").assertExists()
        composeTestRule.onNodeWithContentDescription("验证码输入框").assertExists()
        composeTestRule.onNodeWithContentDescription("获取验证码按钮").assertExists()
        composeTestRule.onNodeWithContentDescription("登录按钮").assertExists()
        
        // 验证错误提示有正确的语义
        composeTestRule.inputText("手机号", "123")
        composeTestRule.onNode(hasText("请输入正确的手机号码") and hasRole(Role.Text))
            .assertExists()
    }
}